variables:
  workingDirectory: $workingDirectory
  deploymentEnv: $deploymentEnv

stages:
  - code-push-release

job-code-push-release-POS-RN72:
  stage: code-push-release
  only:
  - build_dev
  when: on_success
  script:
    - cd $workingDirectory
    - git checkout build_dev
    - git pull
    - code-push release-react xpos-ios ios -d "$deploymentEnv" --targetBinaryVersion "1.0.2"
    - code-push release-react xpos-android android -d "$deploymentEnv" --targetBinaryVersion "1.0.2"
    - |
      curl \
      -X POST \
      -H 'Authorization: Bearer W4UpVqJYoa38rnlFHAJtY4ZVESc3Tiy8JTDpb5nQbhp' \
      -F 'message=  `[ POS ]` - `BETA`  Xinchucmung!!! TanNgoc d3ptry sj3u k4p vjp pr0 đã up code rùi ạk, bạn thử lại nhé! >🎉🎉🎉' \
      -F 'stickerPackageId=11537' \
      -F 'stickerId=52002734' \
      https://notify-api.line.me/api/notify
    - chown -R litruong $workingDirectory
  tags:
  - POS-RN72
  retry: 2