<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleGetInfoString</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleURLName</key>
			<string>mwgpos-staging</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>mwgpos-staging</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CodePushDeploymentKey</key>
	<string>$(CODEPUSH_KEY_IOS)</string>
	<key>CodePushServerURL</key>
	<string>$(CODEPUSH_URL)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>xmpos</string>
		<string>mwgapp</string>
		<string>itms-apps</string>
		<string>xmanager</string>
		<string>msale</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>codepush.azurewebsites.net</key>
			<true/>
			<key>codepush.blob.core.windows.net</key>
			<true/>
			<key>codepushupdates.azureedge.net</key>
			<true/>
			<key>https://zuulbbpublic.tgdd.vn/codepushv2</key>
			<true/>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào âm nhạc của bạn.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần sử dụng bluetooth để tìm, kết nối và truyền dữ liệu giữa các thiết bị khác nhau</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào bluetooth của bạn.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào calendar của bạn.</string>
	<key>NSCameraUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào camera của bạn để có thể thuận tiện cho việc chụp ảnh trong khi đăng ký thông tin.</string>
	<key>NSContactsUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào danh bạ của bạn.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập Face ID để xác thực và bảo mật quyền tài khoản của bạn.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào vị trí của bạn để có thể cập nhật liên tục vị trí của bạn </string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào vị trí của bạn để có thể cập nhật liên tục vị trí của bạn</string>
	<key>NSLocationUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào vị trí của bạn để có thể cập nhật liên tục vị trí của bạn</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào vị trí của bạn để có thể cập nhật liên tục vị trí của bạn</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào microphone của bạn.</string>
	<key>NSMotionUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào motion của bạn.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào hình ảnh của bạn</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào hình ảnh của bạn</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Ứng dụng $(APP_DISPLAY_NAME) cần truy cập vào speed recognition của bạn.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Roboto-Black.ttf</string>
		<string>Roboto-BlackItalic.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>Roboto-BoldItalic.ttf</string>
		<string>Roboto-Italic.ttf</string>
		<string>Roboto-Light.ttf</string>
		<string>Roboto-LightItalic.ttf</string>
		<string>Roboto-Medium.ttf</string>
		<string>Roboto-MediumItalic.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>Roboto-Thin.ttf</string>
		<string>Roboto-ThinItalic.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Fontisto.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>location</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>Light Content</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
