fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios pod

```sh
[bundle exec] fastlane ios pod
```

Create ipa

### ios buildDev

```sh
[bundle exec] fastlane ios buildDev
```



### ios buildStag

```sh
[bundle exec] fastlane ios buildStag
```



### ios buildProd

```sh
[bundle exec] fastlane ios buildProd
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
