fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## Android

### android preBuild

```sh
[bundle exec] fastlane android preBuild
```

Build App

### android buildDev

```sh
[bundle exec] fastlane android buildDev
```



### android buildStag

```sh
[bundle exec] fastlane android buildStag
```



### android buildProd

```sh
[bundle exec] fastlane android buildProd
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
