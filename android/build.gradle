// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 34
        googlePlayServicesVersion = "+"
        firebaseMessagingVersion = "+"
        ndkVersion = "23.1.7779620"
        kotlinVersion= "1.8.22"
        pdfViewer = "3.1.0-beta.1"
        dotVersion = "7.2.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.3.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath('com.google.gms:google-services:4.4.0')
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://maven.innovatrics.com/releases'
        }
    }
}