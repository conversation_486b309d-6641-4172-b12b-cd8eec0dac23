import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, Alert, Image, StyleSheet } from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import {
    MyText,
    showBlock<PERSON>,
    hideBlockUI,
    CaptureCamera,
    Icon,
    ImageURI,
    ImageCDN
} from '@components';
import { COLORS } from '@styles';
import { useDispatch, useSelector } from 'react-redux';
import ImageProcess from './ImageProcess';
import { helper } from '@common';
import { constants, ENUM, API_CONST } from '@constants';
import { translate } from '@translate';
import { getImageCDN, removeImageProduct } from '../action';
import ModalZoom from './ModalZoom';
import moment from 'moment';
import { EXCLUDE_PROD_LIST, NO_CHECK_STATUS_CATE_LIST } from '../constans';

const { URL_OLD_PRODUCT } = constants;
const { FILE_PATH: { OLD_IMAGE } } = ENUM
function ImageOldProduct(props) {
    const {
        item,
        onChange,
        handleGetOldProductImage,
        OldProductInfo,
        indexImage,
        getOldProductInfo,
        excludeAccessoryCategory } = props;
    const [isVisibleCamera, setIsVisibleCamera] = useState(false);
    const [currentPicture, setCurrentPicture] = useState('');
    const [scratchedFront, setScratchedFront] = useState('');

    const { brandID } = useSelector((state) => state.userReducer);
    const isDienMay = getBrandID(brandID)
    const date = new Date();
    const nowDate = moment(date).format("YYYY-MM-DD");
    const dispatch = useDispatch();
    const [visible, setVisible] = useState(false);
    const [urlImageBase, setUrlImageBase] = useState(item.ImageMobile ? item.ImageMobile : null);
    const [imageZoom, setImageZoom] = useState("");
    const [rotationBase, setRotationBase] = useState(0);

    const switchModal = (value) => {
        setVisible(value);
    }

    const sampleImage = helper.IsNonEmptyString(item.SampleImage)
        ? item.SampleImage.replaceAll("\\", "/")
        : "";

    const conFirmDelete = (idImage) => {
        Alert.alert("", translate("oldProduct.confirm_delete_image"),
            [
                {
                    text: translate('common.btn_skip'),
                    style: "cancel",
                },
                {
                    text: translate('common.btn_continue'),
                    style: "default",
                    onPress: () => {
                        handleDeleteImage(idImage)
                    }
                }
            ]
        )
    }
    const handleDeleteImage = (id) => {
        showBlockUI();
        dispatch(removeImageProduct(id))
            .then((response) => {
                const { object } = response
                const { Message } = object
                hideBlockUI();
                setScratchedFront('');
                Alert.alert(
                    translate('common.notification_uppercase'),
                    Message,
                    [
                        {
                            text: translate('common.btn_accept'),
                            onPress: () => {
                                getOldProductInfo();
                                handleGetOldProductImage();
                            }
                        }
                    ]
                );
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_accept'),
                            onPress: hideBlockUI
                        }
                    ]
                );
            });
    };

    const takePicture = (photo) => {
        setIsVisibleCamera(false);
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper
                .resizeImage(photo)
                .then(({ path, uri, size }) => {
                    const newNameProduct = (helper.removeAccent(OldProductInfo.ProductName)).replace(/[^a-zA-Z0-9_.]/g, "");
                    const name = indexImage + "_" + OldProductInfo.OldID + "_" + newNameProduct.toLowerCase() + "_" + new Date().getTime() + ".jpg"

                    const body = helper.createFormData({ uri, type: 'image/jpg', name: name, path: OLD_IMAGE });

                    getImageCDN(body)
                        .then((response) => {
                            hideBlockUI();
                            const remoteURI =
                                API_CONST.API_GET_UPLOAD_OLD_IMAGE_CDN_NEW + response;
                            setScratchedFront(remoteURI);
                            onChange(remoteURI);
                        })
                        .catch((error) => {
                            Alert.alert(translate("common.notification_uppercase"), error.msgError,
                                [
                                    {
                                        text: translate('common.btn_accept'),
                                        onPress: hideBlockUI
                                    }
                                ]
                            )
                        });
                })
                .catch((error) => {
                    Alert.alert(translate("common.notification_uppercase"), translate('oldProduct.error_take'),
                        [
                            {
                                text: translate('common.btn_accept'),
                                onPress: hideBlockUI
                            }
                        ]
                    )

                });
        } else {
            hideBlockUI();
        }
    };

    const selectPicture = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                setIsVisibleCamera(false);
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper
                        .resizeImage(response)
                        .then(({ path, uri, size }) => {
                            const newNameProduct = (helper.removeAccent(OldProductInfo.ProductName)).replace(/[^a-zA-Z0-9_.]/g, "");
                            const name = indexImage + "_" + OldProductInfo.OldID + "_" + newNameProduct.toLowerCase() + "_" + new Date().getTime() + ".jpg"
                            const body = helper.createFormData({ uri, type: 'image/jpg', name: name, path: OLD_IMAGE });
                            body.append('folder', nowDate);
                            body.append('dienmay', false);
                            getImageCDN(body)
                                .then((response) => {
                                    const remoteURI =
                                        API_CONST.API_GET_UPLOAD_OLD_IMAGE_CDN_NEW + response;
                                    setScratchedFront(remoteURI);
                                    onChange(remoteURI);
                                    hideBlockUI();
                                })
                                .catch((error) => {
                                    Alert.alert(translate("common.notification_uppercase"), error.msgError,
                                        [
                                            {
                                                text: translate('common.btn_accept'),
                                                onPress: hideBlockUI
                                            }
                                        ]
                                    )
                                });
                        })
                        .catch((error) => {
                            Alert.alert(translate("common.notification_uppercase"), translate('oldProduct.error_select'),
                                [
                                    {
                                        text: translate('common.btn_accept'),
                                        onPress: hideBlockUI
                                    }
                                ]
                            )
                        });
                } else {
                    hideBlockUI();
                }
            }
        );
    };

    const turnImage = (itemImage) => {
        let rotationTmp = 0;
        if (rotationBase == 270) {
            rotationTmp = 0;
        } else {
            rotationTmp = rotationBase + 90;
        }
        setRotationBase(rotationTmp);

        let urlImage = "";
        if (urlImageBase) {
            urlImage = urlImageBase;
        } else {
            urlImage = itemImage.ImageMobile;
            setUrlImageBase(urlImage);
        }

        showBlockUI();
        return new Promise(
            (resolve, reject) => {
                Image.getSize(urlImage, (width, height) => {
                    let w = 0, h = 0;
                    if (rotationTmp == 0 || rotationTmp == 180) {
                        w = width;
                        h = height;
                    } else {
                        w = height;
                        h = width;
                    }

                    const resResize = {
                        uri: urlImage,
                        width: w,
                        height: h,
                        rotation: rotationTmp
                    }

                    helper
                        .rotateImage(resResize)
                        .then(({ path, uri, size }) => {
                            const newNameProduct = (helper.removeAccent(OldProductInfo.ProductName)).replace(/[^a-zA-Z0-9_.]/g, "");
                            const name = indexImage + "_" + OldProductInfo.OldID + "_" + newNameProduct.toLowerCase() + "_" + new Date().getTime() + ".jpg"
                            const body = helper.createFormData({ uri, type: 'image/jpg', name: name, path: OLD_IMAGE });

                            getImageCDN(body)
                                .then((response) => {
                                    const remoteURI =
                                        API_CONST.API_GET_UPLOAD_OLD_IMAGE_CDN_NEW + response;
                                    setScratchedFront(remoteURI);
                                    dispatch(removeImageProduct(itemImage.ImageID))
                                        .then((resDelete) => {
                                            onChange(remoteURI);
                                        })
                                        .catch((msgError) => {
                                            Alert.alert(
                                                translate('common.notification_uppercase'),
                                                translate('oldProduct.error_turn_1') + ": " + msgError,
                                                [
                                                    {
                                                        text: translate('common.btn_accept'),
                                                        onPress: hideBlockUI
                                                    }
                                                ]
                                            );
                                        });
                                })
                                .catch((error) => {
                                    Alert.alert(translate("common.notification_uppercase"), error.msgError,
                                        [
                                            {
                                                text: translate('common.btn_accept'),
                                                onPress: hideBlockUI
                                            }
                                        ]
                                    )
                                });
                        })
                        .catch((error) => {
                            Alert.alert(translate("common.notification_uppercase"), translate('oldProduct.error_edit'),
                                [
                                    {
                                        text: translate('common.btn_accept'),
                                        onPress: hideBlockUI
                                    }
                                ]
                            )
                        });
                },
                    error => {
                        Alert.alert(translate("common.notification_uppercase"), translate('oldProduct.error_getinfo'),
                            [
                                {
                                    text: translate('common.btn_accept'),
                                    onPress: hideBlockUI
                                }
                            ]
                        )
                    }
                );
            }
        );
    }

    const isImposibleToUploadImage = () => {
        return (
            (OldProductInfo.IMEIStatusID == 0 && !NO_CHECK_STATUS_CATE_LIST.includes(OldProductInfo.CategoryID))// chưa chọn tình trạng
            || (OldProductInfo.StatusID == 3 && // Trạng thái chưa up web
                (OldProductInfo.CategoryID == 2162 && !EXCLUDE_PROD_LIST.includes(OldProductInfo.ProductCode) // không thuộc list sản phẩm không cần phụ kiện
                    || !excludeAccessoryCategory.includes(OldProductInfo.CategoryID) // Không thuộc list ngành hàng không cần phụ kiện
                )
            )
        )
            && OldProductInfo.InventoryStatusID != 7 // Sản phẩm có trạng thái trưng bày bỏ mẫu không cần kiểm tra tình trạng sp và phụ kiện kèm theo

    }

    const onOpenCamera = () => {
        if (isImposibleToUploadImage()) {
            Alert.alert("", translate("oldProduct.condition"),
                [
                    {
                        text: translate('common.btn_accept'),
                        style: "default",
                    }
                ]
            )
            return;
        }
        setIsVisibleCamera(true);
        setCurrentPicture(item.productName);
    }

    const onCopyImage = (value) => {
        if (isImposibleToUploadImage()) {
            Alert.alert("", translate("oldProduct.condition_copy"),
                [
                    {
                        text: translate('common.btn_accept'),
                        style: "default",
                    }
                ]
            )
            return;
        }
        onChange(value);
    }

    return (
        <View >
            <MyText
                text={`${item.DisplayOrder}. ${item.ImageTypeName}`}
                style={styles.imageType}>
                {item.IsRequired && (
                    <MyText
                        text={' *'}
                        style={styles.require}
                    />
                )}
            </MyText>
            <View style={styles.imageArea}>
                <View
                    style={styles.imageSampleContainer}>
                    <ImageURI
                        uri={sampleImage}
                        style={styles.imageSample}
                        resizeMode={'contain'}
                    />
                </View>
                {
                    OldProductInfo.PreOldID && item.PreImageID ?
                        <View
                            style={styles.imageUploadContainer}>
                            <View style={styles.preOldItemContainer}>
                                <View style={styles.titleImageContainer}>
                                    <MyText text={translate("oldProduct.old_image_title")} style={styles.titleImage} />
                                </View>
                                {
                                    item.PreImageMobile &&
                                    <View style={styles.preOldImageManipulation}>
                                        <TouchableOpacity
                                            style={styles.oldItem}
                                            onPress={() => {
                                                switchModal(true);
                                                setImageZoom(item.PreImageMobile);
                                            }}>
                                            <ImageURI
                                                uri={item.PreImageMobile}
                                                style={styles.oldImage}
                                                resizeMode={'contain'}
                                            />
                                        </TouchableOpacity>
                                        {
                                            OldProductInfo.StatusID != 2 && !item.ImageID &&
                                            <View >
                                                <TouchableOpacity
                                                    style={{
                                                        padding: 5,
                                                    }}
                                                    onPress={() => onCopyImage(item.PreImageMobile)}>
                                                    <View >
                                                        <Icon
                                                            iconSet={'Ionicons'}
                                                            name={'copy-outline'}
                                                            color={COLORS.ic147EFB}
                                                            size={20}
                                                        />

                                                    </View>

                                                </TouchableOpacity>
                                            </View>
                                        }
                                    </View>
                                }

                            </View>
                            <View style={styles.preOldItemContainer}>
                                <View style={styles.titleImageContainer}>
                                    <MyText text={translate("oldProduct.new_image_title")} style={styles.titleImage} />
                                </View >
                                {
                                    <OldProductImageManipulation
                                        switchModal={switchModal}
                                        setImageZoom={setImageZoom}
                                        item={item}
                                        scratchedFront={scratchedFront}
                                        turnImage={turnImage}
                                        OldProductInfo={OldProductInfo}
                                        conFirmDelete={conFirmDelete}
                                        onOpenCamera={onOpenCamera}
                                        heightImageProcess={65}
                                        styleContainer={styles.preOldImageManipulation}
                                        styleImageProcess={styles.preOldIDProcessContainer}
                                    />
                                }
                            </View>
                        </View>
                        :
                        <View
                            style={styles.oldItemContainer}>
                            <OldProductImageManipulation
                                switchModal={switchModal}
                                setImageZoom={setImageZoom}
                                item={item}
                                scratchedFront={scratchedFront}
                                turnImage={turnImage}
                                OldProductInfo={OldProductInfo}
                                conFirmDelete={conFirmDelete}
                                onOpenCamera={onOpenCamera}
                                heightImageProcess={130}
                                styleContainer={styles.oldImageManipulation}
                                styleImageProcess={styles.oldIDProcessContainer}

                            />
                        </View>
                }
                <CaptureCamera
                    isVisibleCamera={isVisibleCamera}
                    takePicture={takePicture}
                    closeCamera={() => {
                        setIsVisibleCamera(false);
                    }}
                    selectPicture={selectPicture}
                />
                <ModalZoom
                    isVisible={visible}
                    hideModal={() => switchModal(false)}
                    urlImage={imageZoom}
                    urlFastImage={scratchedFront}
                    check={imageZoom}
                />
            </View>
        </View >
    );
}

export default ImageOldProduct;

const OldProductImageManipulation = (props) => {
    const { switchModal,
        item,
        scratchedFront,
        turnImage,
        styleContainer,
        OldProductInfo,
        conFirmDelete,
        setImageZoom,
        heightImageProcess,
        styleImageProcess,
        onOpenCamera
    } = props;

    return (
        item.ImageID ?
            <View style={styleContainer}>
                {
                    item.ImageMobile ?
                        <TouchableOpacity
                            style={styles.oldItem}
                            onPress={() => {
                                setImageZoom(item.ImageMobile);
                                switchModal(true)
                            }}>
                            <ImageURI
                                uri={item.ImageMobile}
                                style={styles.oldImage}
                                resizeMode={'contain'}
                            />

                        </TouchableOpacity> :
                        <TouchableOpacity
                            style={styles.oldItem}
                            onPress={() => {
                                setImageZoom("");
                                switchModal(true)
                            }}>
                            <ImageURI
                                style={styles.oldImage}
                                uri={scratchedFront}
                                resizeMode={"contain"}
                            />


                        </TouchableOpacity>
                }
                {
                    OldProductInfo.StatusID != 2 &&
                    <View style={styles.iconManipulation}>
                        <TouchableOpacity
                            style={{
                                padding: 5,
                            }}
                            onPress={() => conFirmDelete(item.ImageID)}>
                            <View >
                                <Icon
                                    iconSet={'Ionicons'}
                                    name={'trash-outline'}
                                    color={COLORS.txtD0021B}
                                    size={20}
                                />

                            </View>

                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => turnImage(item)}
                            style={{
                                padding: 5,
                            }}
                        >
                            <Icon
                                iconSet={'Ionicons'}
                                name={'refresh-outline'}
                                color={COLORS.ic147EFB}
                                size={20}
                            />
                        </TouchableOpacity>
                    </View>
                }
            </View>
            : <ImageProcess
                onCamera={onOpenCamera}
                urlImageLocal={scratchedFront}
                urlImageRemote={scratchedFront}
                styleContainer={styleImageProcess}
                heightImage={heightImageProcess}
            />
    )
}



const getBrandID = (brandID) => {
    if (brandID == 2) {
        return true
    }
    return false;
}

const styles = StyleSheet.create({
    imageArea: {
        flexDirection: 'row',
        flex: 1,
        height: 150
    },
    imageUploadContainer: {
        flex: 0.5,
        marginTop: 15,
        justifyContent: 'center',
        alignItems: 'flex-start',

    },
    preOldIDProcessContainer: {
        height: 60,
        width: '70%',
        justifyContent: "center",
        alignItems: "center",
        alignSelf: "center",
        backgroundColor: COLORS.btnF5F5F5,
    },
    oldIDProcessContainer: {
        height: 135,
        width: '100%',
        justifyContent: "center",
        alignItems: "center",
        alignSelf: "center",
        backgroundColor: COLORS.btnF5F5F5,
        marginTop: 15,
    },
    preOldImageLoading: {
        flex: 1,
        justifyContent: "center",
        marginHorizontal: 2,
        flexDirection: "row",
        marginTop: 15,
    },
    oldImageLoading: {
        flex: 1,
        justifyContent: "center",
        marginHorizontal: 2,
        flexDirection: "row",
        marginTop: 15,
    },
    preOldItemContainer: {
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: 'row',
        height: 65,

    },
    oldItemContainer: {
        flex: 0.5,
        height: 150,
        alignItems: 'center',
        justifyContent: 'center',
    },
    titleImageContainer: {
        height: '100%',
        width: '30%',
        paddingLeft: 10,
    },
    titleImage: {
        fontWeight: 'bold',
        width: 40
    },
    preOldImageManipulation: {
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: 'row',
        height: 60,
        width: '70%'
    },
    oldImageManipulation: {
        width: '100%',
        marginTop: 15,
        height: 135,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
    },
    iconManipulation: {
        justifyContent: 'space-around',
        alignItems: 'flex-end',
        height: '100%',
    },
    oldItem: {
        height: '100%',
        width: '80%',
        alignItems: 'flex-end',
    },
    oldImage: {
        width: '100%',
        height: '100%',
    },
    imageSampleContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 15,
        flex: 0.5,
    },
    imageSample: {
        width: '90%',
        height: 150,
    },
    imageType: {
        color: COLORS.txt333333,
        fontStyle: 'italic',
        marginTop: 15
    },
    require: {
        color: COLORS.txtFF0000
    }
})

