import {
    Alert,
    Image,
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import React, { useCallback } from 'react';
import { COLORS } from '../../styles';
import { hideBlock<PERSON>, MyText, showBlockUI } from '../../components';
import { useNavigation } from '@react-navigation/native';
import { getInfoPreOrder, getProgram } from './action';
import { useSelector } from 'react-redux';
import { translate } from '../../translations';
import { helper } from '../../common';

const PreInformation = () => {
    const navigation = useNavigation();
    const { storeID, languageID, moduleID } = useSelector(state => state.userReducer);

    const apiCallHandler = useCallback((apiCall, successCallback, errorCallback) => {
        showBlockUI();
        apiCall()
            .then(successCallback)
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('saleExpress.retry'),
                            style: 'cancel',
                            onPress: errorCallback
                        },
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        }
                    ]
                );
            })
            .finally(hideBlockUI);
    }, []);

    const onSign = useCallback(() => {
        navigation.navigate('CollectInformation')
    }, []);

    const onLookUp = useCallback(() => {
        const body = {
            "lstStoreId": storeID,
            "pageSize": -1,
            "pageIndex": 1,
            "loginStoreId": storeID
        };

        apiCallHandler(
            () => getProgram(body),
            (info) => navigation.navigate('LookUpInfo', { productsByParams: info }),
            onLookUp
        );
    }, [storeID, languageID, moduleID, navigation, apiCallHandler]);

    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.info_wrapper}>
                <TouchableOpacity onPress={onSign} style={styles.touchable_item}>
                    <Image
                        style={styles.image}
                        source={require('../../../assets/agreement.png')}
                        resizeMode="contain"
                    />
                    <View style={styles.title}>
                        <MyText
                            style={styles.text}
                            text={'Đăng ký nhận thông tin'.toUpperCase()}
                        />
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={onLookUp} style={styles.touchable_item}>
                    <Image
                        style={styles.image}
                        source={require('../../../assets/look-up.png')}
                        resizeMode="contain"
                    />
                    <View style={styles.title}>
                        <MyText
                            style={styles.text}
                            text={'Tra cứu phiếu nhận thông tin'.toUpperCase()}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        </SafeAreaView>
    );
};

export default PreInformation;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    info_wrapper: {
        flex: 1,
        margin: 10
    },
    touchable_item: {
        paddingVertical: 10,
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        borderColor: COLORS.bg8E8E93,
        borderWidth: StyleSheet.hairlineWidth,
        marginVertical: 10,
        borderRadius: 10,
        paddingLeft: 10
    },
    image: {
        width: 50,
        height: 80,
        flex: 2,
    },
    title: {
        flex: 6
    },
    text: {
        fontSize: 15,
        fontWeight: 'bold',
        color: COLORS.bg288AD6
    }
});
