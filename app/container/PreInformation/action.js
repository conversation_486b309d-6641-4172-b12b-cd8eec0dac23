import { API_CONST, ENUM } from '@constants';
import { METHOD, apiBase } from '@config';
import { helper } from '../../common';


const {
    API_GET_PRODUCT_PRICE,
    API_PROMOTION_PRE,
    API_GET_INFO_PRE_ORDER,
    API_INSERT_INFO_PRE_CRM,
    API_GET_PROGRAM_PRE_CRM,
    API_CHECK_CUSTOMER_LIMIT,
    API_GET_DISCOUNT_VALUE,
    API_SIGN_PRE_ORDER
} = API_CONST;



export const getProductPrice = async (body) => {
    if (!body.isGetPrice) {
        return {};
    }

    try {
        const price = await apiBase(API_GET_PRODUCT_PRICE, METHOD.POST, body);
        if (!helper.IsEmptyObject(price.object)) {
            const bodyGetDiscount = {
                loginStoreId: body.loginStoreId,
                languageID: body.languageID,
                moduleID: body.moduleID,
                saleScenarioTypeID: 1,
                imei: "",
                productID: body.keyword,
                inventoryStatusID: 1,
                price: price.object.SalePriceVAT,
                storeID: body.loginStoreId,
                appliedQuantity: 1,
                outputStoreID: body.loginStoreId,
                deliveryTypeID: 1, // default 1 for An Khang
                storeRequests: [], // default 0 for An Khang
                saleProgramID: 0,
                cartRequest: {}, // default empty for An Khang
                pointLoyalty: 0,
                outputTypeID: 3,
                packagesTypeId: 0,
                standardPriceAreaSalePrice: price.object.StandardPriceAreaSalePrice,
                specialSaleProgram: body.specialSaleProgram,

            }
            const priceDiscount = await apiBase(API_GET_DISCOUNT_VALUE, METHOD.POST, bodyGetDiscount);
            console.log('getProductPrice success', priceDiscount);
            price.object.PriceAppliedPromo = price.object.PriceAppliedPromo - priceDiscount.object.DiscountValue || 0
            return price.object;
        } else {
            throw new Error('Không tìm thấy giá sản phẩm.');
        }
    } catch (error) {
        console.log('getProductPrice error', error);
        throw error.msgError || 'An error occurred while fetching product price.';
    }
};



export const getPromotion = (body) => {
    if (!body.isGetPromotion) return new Promise((resolve, reject) => {
        resolve({});
    });

    return new Promise((resolve, reject) => {
        apiBase(API_PROMOTION_PRE, METHOD.POST, body)
            .then((response) => {
                console.log(`getPromotion success`, response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                } else {
                    const message = 'Có lỗi trong quá trình lấy khuyến mãi.';
                    reject(message);
                }
            })
            .catch((error) => {
                console.log(`getPromotion error`, error);
                reject(error.msgError);
            });
    });
};

export const getPriceAndPromotion = async ({ bodyPrice, bodyPromotion }) => {
    const promiseAll = [
        bodyPrice && getProductPrice(bodyPrice),
        bodyPromotion && getPromotion(bodyPromotion)
    ].filter(Boolean);

    return new Promise((resolve, reject) => {
        Promise.all(promiseAll)
            .then((values) => {
                console.log('getPriceAndPromotion', values);
                const [responsePrice, responsePromotion] = values;
                resolve({ responsePrice, responsePromotion });
            })
            .catch((msgError) => {
                console.log('getPriceAndPromotion ', msgError);
                reject(msgError);
            });
    });
};

export const getInfoPreOrder = async (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_INFO_PRE_ORDER, METHOD.POST, body)
            .then((response) => {
                console.log(`getInfoPreOrder success`, response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                } else {
                    const message = 'Có lỗi trong quá trình lấy thông tin pre.';
                    reject(message);
                }
            })
            .catch((error) => {
                console.log(`getInfoPreOrder error`, error);
                reject(error.msgError);
            });
    });
};

export const insertInfo = async (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_INSERT_INFO_PRE_CRM, METHOD.POST, body)
            .then((response) => {
                console.log(`insertInfo success`, response);
                resolve(true)
            })
            .catch((error) => {
                console.log(`insertInfo error`, error);
                reject(error.msgError);
            });
    });
};

export const getProgram = async (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_PROGRAM_PRE_CRM, METHOD.POST, body)
            .then((response) => {
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object)
                }
                else {
                    reject("Không có danh sách phiếu")
                }

            })
            .catch((error) => {
                console.log(`getProgram error`, error);
                reject(error.msgError);
            });
    });
};

export const checkAllowUser = async (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_CUSTOMER_LIMIT, METHOD.POST, body)
            .then((response) => {
                console.log("checkAllowUser", response)
                const { object: { isAvailable } } = response;
                resolve(isAvailable)
            })
            .catch((error) => {
                console.log(`getProgram error`, error);
                resolve(false)
            });
    });
};


export const signInfoPreOrder = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_SIGN_PRE_ORDER, METHOD.POST, body)
            .then((response) => {
                resolve(true);
            })
            .catch((error) => {
                console.log('🚀 ~ updatePreOrder ~ error:', error);
                resolve(error);
            });
    });
};
