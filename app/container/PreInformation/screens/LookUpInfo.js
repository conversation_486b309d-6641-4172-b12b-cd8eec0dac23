import {
    Alert,
    Keyboard,
    Linking,
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Icon, MyText } from '../../../components';
import { COLORS } from '../../../styles';
import { ScrollView } from 'react-native-gesture-handler';
import { helper } from '../../../common';
import SearchInput from '../components/SearchInput';
import moment from 'moment';
import { constants } from '../../../constants';
import ModalCalendar from '../../PaymentTransactions/component/ModalCalendar';
import CardItem from '../components/CardItem';
import Title from '../components/Title';

const LookUpInfo = ({ route }) => {
    // params
    const { productsByParams } = route?.params ?? { productsByParams: [] }
    // states
    const [products, setProducts] = useState([])
    const [keyword, setKeyword] = useState("")
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [isShowCalendar, setIsShowCalendar] = useState(false);

    // useEffect
    useEffect(() => {
        if (!helper.IsNonEmptyArray(productsByParams)) return;
        handleOnSubmit();
    }, [productsByParams, fromDate, toDate]);




    const connectXfone = useCallback((phone) => {
        if (helper.IsEmptyString(phone)) return Alert.alert("", "Phiếu không có số điện thoại khách hàng.");
        const url = `xfone://${phone}`;
        Linking.openURL(url).catch((error) => {
            Alert.alert("", "Vui lòng cài app Xfone trước khi gọi.")
        })
    }, [])

    const handleOnSubmit = useCallback(() => {

        Keyboard.dismiss();

        if (!helper.IsNonEmptyArray(productsByParams)) return;

        const fromDateObj = fromDate.toISOString().split('T')[0];
        const toDateObj = toDate.toISOString().split('T')[0];
        const keywordNormalized = helper.removeAccent(keyword).toLowerCase();
        const newDataSearch = productsByParams.filter((item) => {
            const customerMobileNormalized = helper.removeAccent(item.customerMobile).toLowerCase();
            const matchesKeyword = item.customerName.includes(keyword) || customerMobileNormalized.includes(keywordNormalized);
            if (!item.createdDate || !matchesKeyword) return false;
            const date = new Date(item.createdDate).toISOString().split('T')[0];
            return date >= fromDateObj && date <= toDateObj;
        });

        setProducts(newDataSearch);
    }, [keyword, productsByParams, fromDate, toDate])



    return (

        <SafeAreaView style={styles.container_wrapper}>
            <View style={styles.container}>
                <View style={styles.search_wrapper}>
                    <View style={{ paddingBottom: 10 }}>
                        <TouchableOpacity
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 5,
                                paddingHorizontal: 30,
                                borderWidth: 1,
                                borderColor: COLORS.bdDDDDDD,
                                height: 44,
                                alignSelf: 'center',
                                width: constants.width - 20
                            }}
                            onPress={() => setIsShowCalendar(true)}>
                            <MyText
                                style={{
                                    paddingHorizontal: 5,

                                }}
                                text={`${moment(fromDate).format(
                                    'DD/MM/YYYY'
                                )} - ${moment(toDate).format('DD/MM/YYYY')} `}
                            />
                            <Icon
                                iconSet="Feather"
                                name="calendar"
                                style={{
                                    fontSize: 30,
                                    color: COLORS.ic2C8BD7
                                }}
                            />
                        </TouchableOpacity>
                    </View>
                    <SearchInput
                        onSubmit={handleOnSubmit}
                        inputText={keyword}
                        onChangeText={(text) => {
                            setKeyword(text);
                        }}
                        onClearText={() => {
                            setKeyword('');
                        }}
                        placeholder={"Nhập tên, số điện thoại khách hàng"}
                        showSearchIcon
                        showBarcodeIcon
                        onShowBarcode={() => {
                        }}
                    />
                </View>

                <Title addSize={0} title={'DANH SÁCH PHIẾU NHẬN THÔNG TIN TẠI SIÊU THỊ'} />
                <ScrollView
                    contentContainerStyle={{ flexGrow: 1, }}
                    contentInsetAdjustmentBehavior="automatic"
                    behavior="padding"
                    showsVerticalScrollIndicator={false}>
                    {
                        helper.IsNonEmptyArray(products) ? products.map((_item, index) => {
                            return (
                                <CardItem
                                    key={_item.id + index}
                                    product={_item}
                                    connectXfone={connectXfone}
                                    index={index}
                                />
                            )

                        }) : <View style={{ flex: 1, margin: 10, justifyContent: "center", alignItems: "center" }}>
                            <MyText addSize={1} style={{ color: COLORS.txtF50537 }} text={"Không tìm thấy phiếu."} />
                        </View>
                    }

                </ScrollView>
                {isShowCalendar && <ModalCalendar
                    isVisible={isShowCalendar}
                    hideModal={() => {
                        setIsShowCalendar(false);
                    }}
                    startDate={fromDate}
                    endDate={toDate}
                    setDate={(day) => {
                        setFromDate(new Date(day.startDate));
                        setToDate(new Date(day.endDate));
                    }}
                    firstDay={1}
                    hideDayNames={false}
                    showWeekNumbers={false}
                    enableSwipeMonths={false}
                />}
            </View>
        </SafeAreaView>
    );
};

export default LookUpInfo;







const styles = StyleSheet.create({

    container_wrapper: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    container: {
        flex: 1,
        // margin: 10
    },
    title: {
        alignItems: 'center',
        justifyContent: 'center',
        borderBottomWidth: 0.5,
        borderBottomColor: COLORS.bgD1D3D8,
        paddingBottom: 10,
        paddingTop: 5
    },

    search_wrapper: {
        margin: 10,
    }
});
