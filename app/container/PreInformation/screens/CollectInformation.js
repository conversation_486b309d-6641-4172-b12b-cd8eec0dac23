import React, { useEffect, useState, useMemo, useRef } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    Alert,
    Platform,
    Keyboard
} from 'react-native';
import {
    generateOptions,
    OPTIONS_TITLES,
    OPTIONS_TYPE
} from '../../PreIphone/constants';
import { Options, CustomerInfo, ProductCard } from '../../PreIphone/components';
import { helper } from '@common';
import { useSelector } from 'react-redux';
import { hideBlockUI, showBlockUI } from '@components';
import { PRODUCTS } from '../../PreIphone/Product';
import { translate } from '@translate';
import { getPromotionsForProduct } from '../../PreIphone/helper';
import { useNavigation } from '@react-navigation/native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { signInfoPreOrder } from '../action';

const SignInfoPreIphone = () => {
    const [products, setProducts] = useState([]);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [customerInfo, setCustomerInfo] = useState({
        name: '',
        phone: '',
        gender: null,
        isAgreePolicy: 0
    });

    const [promotions, setPromotions] = useState([]);

    const isCheckedSlotByPhone = useRef(null);
    const isFocusPhone = useRef(false);
    const inputPhoneNumberRef = useRef();
    const inputCustomerNameRef = useRef();
    const scrollViewRef = useRef(null);

    const navigation = useNavigation();
    const userInfo = useSelector((state) => state.userReducer);
    const { storeID, userName, brandID, companyID } = userInfo;

    const options = useMemo(
        () =>
            generateOptions(
                products,
                selectedProduct?.MODEL,
                selectedProduct?.STORAGE
            ),
        [products, selectedProduct]
    );

    const handleSelect = async (type, value) => {
        let newModel = selectedProduct?.MODEL;
        let newStorage = selectedProduct?.STORAGE;
        let newColor = selectedProduct?.COLOR;

        if (type === OPTIONS_TYPE.MODEL) {
            newModel = value;
            const modelProducts = products.filter(
                (product) => product.MODEL === newModel
            );
            if (modelProducts.length > 0) {
                const fallback = modelProducts[0];
                newStorage = fallback.STORAGE;
                newColor = fallback.COLOR;
            }
        }
        if (type === OPTIONS_TYPE.STORAGE) {
            newStorage = value;
        }
        if (type === OPTIONS_TYPE.COLOR) {
            newColor = value;
        }
        const matched = products.find(
            (product) =>
                product.MODEL === newModel &&
                product.STORAGE === newStorage &&
                product.COLOR === newColor
        );

        if (matched?.PRODUCTID == selectedProduct?.PRODUCTID) return;

        if (matched) {
            const isAllow = true;
            setSelectedProduct((prev) =>
                isAllow ? { ...matched } : { ...matched, availableSlot: 0 }
            );
        } else {
            Alert.alert('', 'Sản phẩm hiện không còn hàng!');
        }
    };

    const handleResetData = () => {
        setCustomerInfo({
            name: '',
            phone: '',
            gender: null,
            isAgreePolicy: 0
        });
        handleGetDefaultData();
        setPromotions([]);
    };

    const handleCompleted = async () => {
        if (!handleValidateInfo()) return;

        showBlockUI();

        try {
            await handleSignPreOrder();
            Alert.alert(
                translate('common.notification_uppercase'),
                'Bạn đã ghi nhận thông tin thành công!',
                [
                    {
                        text: 'OK',
                        style: 'cancel',
                        onPress: () => {
                            hideBlockUI();
                            handleResetData();
                        }
                    }
                ]
            );
        } catch (err) {
            Alert.alert(
                translate('common.notification_uppercase'),
                err?.msgError || 'Có lỗi xảy ra',
                [
                    {
                        text: translate('saleExpress.retry'),
                        style: 'cancel',
                        onPress: handleCompleted
                    },
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: () => {
                            navigation.goBack();
                        }
                    }
                ]
            );
        }
    };

    const handleSignPreOrder = () => {
        const body = {
            objCustomer: {
                FULLNAME: customerInfo.name,
                GENDER: customerInfo.gender,
                MAINMOBILE: customerInfo.phone
            },
            intSourceID: 33,
            intProgramID: 2419402,
            intGencompanyId: companyID,
            strProductID: selectedProduct.PRODUCTID,
            intStoreID: storeID,
            strCreatedUser: userName
        };

        return signInfoPreOrder(body);
    };

    const handleValidateInfo = () => {
        if (!selectedProduct) {
            Alert.alert('', 'Vui lòng chọn sản phẩm!');
            return false;
        }
        if (customerInfo.gender == null) {
            Alert.alert('', 'Vui lòng chọn giới tính khách hàng!');
            return false;
        }
        if (!customerInfo.phone) {
            Alert.alert('', 'Vui lòng nhập số điện thoại khách hàng!');
            inputPhoneNumberRef.current?.focus();
            return false;
        }
        if (!customerInfo.name) {
            Alert.alert('', 'Vui lòng nhập họ tên khách hàng!');
            inputCustomerNameRef.current?.focus();
            return false;
        }
        if (
            customerInfo.phone.length !== 10 ||
            !customerInfo.phone.startsWith('0')
        ) {
            Alert.alert(
                '',
                'Số điện thoại phải bắt đầu bằng 0 và có đúng 10 số!'
            );
            return false;
        }
        if (!customerInfo.isAgreePolicy) {
            Alert.alert(
                '',
                'Vui lòng đồng ý chính sách xử lý dữ liệu cá nhân!'
            );
            return false;
        }
        return true;
    };

    const handleGetDefaultData = async () => {
        setProducts(PRODUCTS);
        const defaultProduct = PRODUCTS.find(
            (product) => product.MODEL && product.STORAGE && product.COLOR
        );
        if (defaultProduct) {
            const isAllow = true;
            setSelectedProduct((prev) =>
                isAllow
                    ? { ...defaultProduct }
                    : { ...defaultProduct, availableSlot: 0 }
            );
        }
    };

    const handleGetPromotion = () => {
        if (!helper.IsEmptyObject(selectedProduct)) {
            const promosForProduct = getPromotionsForProduct(
                selectedProduct,
                brandID
            );
            setPromotions(promosForProduct);
        }
    };

    const handleBlurPhone = () => {
        isFocusPhone.current = false;
    };

    const handleFocusPhone = () => {
        isFocusPhone.current = true;
    };

    const hasCheckedSlotByPhone = () => {
        isCheckedSlotByPhone.current = null;
    };

    useEffect(() => {
        const run = async () => {
            handleGetDefaultData();
        };
        run();
    }, []);
    useEffect(hasCheckedSlotByPhone, [customerInfo.phone]);
    useEffect(handleGetPromotion, [selectedProduct]);

    return (
        <KeyboardAwareScrollView
            extraScrollHeight={60}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 110 : 0}
            style={[styles.container]}>
            <View style={styles.fixedProductCard}>
                <Text style={styles.sectionTitle}>Chọn sản phẩm </Text>
                <ProductCard
                    selectedProduct={selectedProduct}
                    promotions={promotions}
                />
            </View>

            <ScrollView
                ref={scrollViewRef}
                style={styles.scrollView}
                contentContainerStyle={styles.scrollViewContent}>
                <View style={styles.section}>
                    <Options
                        options={options.models}
                        label={OPTIONS_TITLES.MODEL}
                        selected={selectedProduct?.MODEL}
                        onSelect={(value) =>
                            handleSelect(OPTIONS_TYPE.MODEL, value)
                        }
                    />
                    <Options
                        options={options.storages}
                        label={OPTIONS_TITLES.STORAGE}
                        selected={selectedProduct?.STORAGE}
                        onSelect={(value) =>
                            handleSelect(OPTIONS_TYPE.STORAGE, value)
                        }
                    />
                    <Options
                        options={options.colors}
                        label={OPTIONS_TITLES.COLOR}
                        selected={selectedProduct?.COLOR}
                        onSelect={(value) =>
                            handleSelect(OPTIONS_TYPE.COLOR, value)
                        }
                    />
                </View>
                <CustomerInfo
                    customerInfo={customerInfo}
                    setCustomerInfo={setCustomerInfo}
                    handleCheckSlotByPhone={() => { }}
                    onBlur={handleBlurPhone}
                    onFocus={handleFocusPhone}
                    inputPhoneNumberRef={inputPhoneNumberRef}
                    inputCustomerNameRef={inputCustomerNameRef}
                />

                <View style={{ height: 80 }} />
            </ScrollView>
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    disabled={selectedProduct?.availableSlot == 0}
                    style={[
                        styles.continueButton,
                        {
                            backgroundColor:
                                selectedProduct?.availableSlot == 0
                                    ? '#e8e5e5ff'
                                    : '#00aa77'
                        }
                    ]}
                    onPress={handleCompleted}>
                    <Text style={styles.continueButtonText}>Hoàn tất</Text>
                </TouchableOpacity>
            </View>
        </KeyboardAwareScrollView>
    );
};

export default SignInfoPreIphone;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    },
    fixedProductCard: {
        paddingTop: 10,
        paddingHorizontal: 10,
        borderBottomWidth: 2,
        borderColor: '#eee',
        backgroundColor: '#fff',
        zIndex: 1
    },
    productCard: {
        flexDirection: 'row'
    },
    productImage: {
        width: 80,
        height: 90,
        borderRadius: 8,
        backgroundColor: '#f5f5f5'
    },
    productInfo: {
        flex: 1,
        paddingLeft: 12,
        justifyContent: 'center'
    },
    PRODUCTNAME: {
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 4
    },
    productPrice: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#d60000',
        marginBottom: 6
    },
    promoTitle: {
        color: 'green',
        fontWeight: '600'
    },
    promoText: {
        color: 'green',
        fontSize: 13
    },
    scrollView: {
        flex: 1,
        marginTop: 15
    },
    scrollViewContent: {
        paddingBottom: 20
    },
    section: {
        marginBottom: 5,
        paddingHorizontal: 10,
        borderBottomWidth: StyleSheet.hairlineWidth
    },
    sectionTitle: {
        fontSize: 15,
        fontWeight: 'bold',
        color: '#00aa77',
        paddingBottom: 10
    },
    buttonContainer: {
        paddingVertical: 16,
        alignItems: 'center'
    },
    continueButton: {
        backgroundColor: '#00aa77',
        padding: 12,
        borderRadius: 8,
        alignItems: 'center',
        width: '80%'
    },
    continueButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16
    },
    confirmButton: {
        backgroundColor: '#d60000',
        padding: 12,
        borderRadius: 8,
        alignItems: 'center',
        marginTop: 12
    },
    confirmButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16
    },
    errorText: {
        padding: 16,
        color: 'red',
        textAlign: 'center'
    },
    buttonContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        padding: 25,
        backgroundColor: '#fff',
        borderTopWidth: 1,
        borderTopColor: '#eee'
    },
    continueButton: {
        backgroundColor: '#00aa77',
        paddingVertical: 14,
        borderRadius: 8,
        alignItems: 'center'
    },
    continueButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600'
    }
});
