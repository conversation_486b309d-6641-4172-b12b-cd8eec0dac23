import React, { Component } from 'react';
import {
    ImageBackground,
    Image,
    Linking,
    Alert,
    View
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import CodePush from 'react-native-code-push';
import { MyText, showPopup, CodePushUpdate } from '@components';
import { helper, storageHelper } from '@common';
import { STORAGE_CONST, DEVICE, constants } from '@constants';
import { COLORS } from '@styles';
import { dbHelper } from "@commonDB";
import * as authActionCreator from './action';
import * as appSwitchActionCreator from "../AppSwitch/action";
import { translate } from "@translate";
import { getQueryString, getParamsURL } from "@config";
import SSOLogin from "./SSOLogin";

class SplashScreen extends Component {

    constructor(props) {
        super(props);
        this.state = {
            isVisibleLogin: false,
            isAuthenticatingSSO: false
        };
        this.opaque = '';
        this.expiredTime = 0;
        this.intervalId = null;
        this.isExpired = false;
        this.linkingEvent = null;
    }

    checkExpired = () => {
        const timestamp = Date.now();
        if (this.expiredTime < timestamp) {
            this.removeInterval();
            this.isExpired = true;
            this.setState({ isAuthenticatingSSO: false });
            Alert.alert(
                '',
                'Phiên xác thực đã hết hiệu lực. Vui lòng thử lại',
                [{ onPress: this.onLinkingSSO }]
            );
        }
    };

    addInterval = () => {
        this.isExpired = false;
        this.expiredTime = Date.now() + 60 * 1000;
        this.intervalId = setInterval(this.checkExpired, 5 * 1000);
    };

    removeInterval = () => {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
    };

    componentDidMount() {
        this.addLinkingListener();
    }

    componentDidUpdate(preProps, preState) {
        const { isFetching } = this.props;
        if (preProps.isFetching !== isFetching) {
            if (!isFetching) {
                this.handleResultAuthen(this.props);
            }
        }
    }

    componentWillUnmount() {
        this.removeInterval();
        this.removeLinkingListener();
    }

    handleResultAuthen = async (props) => {
        const {
            description,
            isError,
            isExpired,
            appSwitchAction
        } = props;
        if (isError) {
            showPopup(translate('common.notification'), description, [{
                text: translate('common.btn_notify_try_again'),
                onPress: this.checkAutoLogin
            }], "warning");
        }
        else if (isExpired) {
            showPopup(translate('common.notification'), description, [{
                text: translate("splash.btn_notify_log_out"),
                onPress: this.onLogout
            }], "warning");
        }
        else {
            const requireOTP = await storageHelper.getItem(STORAGE_CONST.REQUIRE_OTP);
            if (requireOTP == "true") {
                appSwitchAction.switchToOtpScreen();
            } else {
                appSwitchAction.switchToMainScreen();
            }
        }
    };

    checkAutoLogin = () => {
        const { authAction, appSwitchAction } = this.props;
        storageHelper.getItem(STORAGE_CONST.ACCESS_TOKEN).then(async (token) => {
            console.log("storageHelper.getItem", token);
            const isInstalled = await helper.isAppInstalled('xmanager');
            // if (!isInstalled) {
            //     Alert.alert(
            //         '',
            //         'Vui lòng cài đặt app MWG SSO để đăng nhập ứng dụng.',
            //         [
            //             {
            //                 onPress: () => {
            //                     this.addInterval();
            //                     helper.openURL('https://appsso.tgdd.vn/');
            //                 }
            //             }
            //         ]
            //     );
            // }
            // else
             if (helper.IsNonEmptyString(token)) {
                const isOffline = false; //await appSwitchAction.checkActiveOffline();
                appSwitchAction.changeSettingMode(isOffline);
                if (isOffline) {
                    authAction.callApiRequestTokenFW();
                }
                else {
                    authAction.callApiRequestToken();
                }
            }
            else {
                this.setState({ isVisibleLogin: true });
            }
        });
    };

    onLinkingSSO = () => {
        this.setState({ isAuthenticatingSSO: true });
        this.opaque = helper.uuidv4();
        const params = {
            action: 'login',
            bundleID: DEVICE.bundleId,
            device: DEVICE.uniqueId,
            opaque: this.opaque
        };
        const query = getQueryString(params);
        const url = `xmanager://xmanager?${query}`;
        console.log('Deeplink MWG SSO', url);
        this.addInterval();
        helper.openURL(url);
    };

    onLogin = (params) => {
        const { authAction } = this.props;
        if (!this.isExpired) {
            this.removeInterval();
            console.log('addEventListenerURL', params);
            if (this.opaque == params.opaque) {
                authAction.requestOauthToken(params);
            } else {
                Alert.alert(
                    '',
                    'Phiên xác thực không hợp lệ. Vui lòng thử lại',
                    [{ onPress: this.checkAutoLogin }]
                );
            }
        }
    };

    handleUrlDeeplink = ({ url }) => {
        if (helper.IsNonEmptyString(url)) {
            const params = getParamsURL(url);
            console.log('handleUrlDeeplink', params);
            switch (params.action) {
                case 'login':
                    this.onLogin(params);
                    break;
                default:
                    console.log();
                    break;
            }
        }
    };

    addLinkingListener = () => {
        this.linkingEvent = Linking.addEventListener('url', this.handleUrlDeeplink);
    };

    removeLinkingListener = () => {
        if (this.linkingEvent) {
            this.linkingEvent.remove();
        }
    };

    onLogout = async () => {
        try {
            await storageHelper.removeKeysByIgnore([STORAGE_CONST.CURRENT_LANGUAGE]);
            await dbHelper.deleteDB();
        } catch (error) {
            console.log("onLogout error", error);
        }
        this.checkAutoLogin();
    };

    render() {
        const { isVisibleLogin, isAuthenticatingSSO } = this.state;
        return (
            <ImageBackground style={{
                flex: 1,
            }}
                source={{ uri: "background" }}
            // source={require('../../../assets/splash.gif')}
            >
                <View
                    style={{
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <Image
                        style={{
                            width: 240,
                            height: 240
                        }}
                        source={{ uri: "snake_logo" }}
/>
                </View>
                <CodePushUpdate
                    onUpdateInstalled={CodePush.restartApp}
                    onUpdateIgnored={this.checkAutoLogin}
                    isAuthenticatingSSO={isAuthenticatingSSO}
                />
                <View style={{
                    width: constants.width,
                    alignItems: 'center',
                    paddingBottom: 24
                }}>
                    <MyText
                        text={`MWG POS `}
                        style={{
                            color: '#1f69ff',
                            fontWeight: 'bold',
                            fontSize: 20
                        }}
                    >
                        <MyText
                            text={`v${DEVICE.version}`}
                            style={{
                                fontStyle: 'italic',
                                fontWeight: 'bold',
                                color: COLORS.txt000000
                            }}
                        />
                    </MyText>
                </View>
                {
                    isVisibleLogin &&
                    <SSOLogin
                        visible={isVisibleLogin}
                        onLogin={() => {
                            this.setState({
                                isVisibleLogin: false
                            }, this.onLinkingSSO);
                        }} />
                }
            </ImageBackground>
        );
    }
}

const mapStateToProps = (state) => ({
    isFetching: state.authenReducer.isFetching,
    description: state.authenReducer.description,
    isError: state.authenReducer.isError,
    isExpired: state.authenReducer.isExpired
});

const mapDispatchToProps = (dispatch) => ({
    authAction: bindActionCreators(authActionCreator, dispatch),
    appSwitchAction: bindActionCreators(appSwitchActionCreator, dispatch),
});

const codePushOptions = {
    checkFrequency: CodePush.CheckFrequency.MANUAL
};

const SplashCodePush = CodePush(codePushOptions)(SplashScreen);

export default connect(mapStateToProps, mapDispatchToProps)(SplashCodePush);