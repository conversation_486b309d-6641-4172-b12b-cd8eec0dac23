import React, { useEffect, useRef, useState } from 'react';
import SafeAreaView from 'react-native-safe-area-view';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { SearchBar, AddProductForm, SearchItem, ListRequestProduct, CustomerInfo, ButtonGroup, AddButton, ModalBuyerInfo } from './components';
import { BottomSheet } from '../../AnKhangPharmacy/components';
import { BarcodeCamera, BaseLoading, CaptureCamera, Icon, hideBlockUI, showBlockUI } from '@components';
import { API_CONST, constants, ENUM } from '@constants';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { helper } from '@common';
import { Alert, FlatList, Keyboard, Modal, TouchableOpacity, View } from 'react-native';
import * as actionRestockCreator from '../action';
import { COLORS } from '@styles';
import { dateHelper } from '@common';
import { launchImageLibrary } from 'react-native-image-picker';
import { translate } from '@translate';
import { BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import ImageViewer from 'react-native-image-zoom-viewer';
import { HTTPS } from '@commonDB';
import { getImageCDN } from '../../ShoppingCart/action';

const { API_GET_IMAGE_CDN, API_GET_IMAGE_CDN_NEW } = API_CONST;
const { FILE_PATH: { CREATE_RESTOCK_REQUEST } } = ENUM;

function CreateRestockRequest({ navigation, restockAction, listRequestProduct, userName, route }) {

    const addProductSheetRef = useRef(null);
    const timeoutSearch = useRef(-1);
    const timeOutHideButton = useRef(null);
    const [keyword, setKeyword] = useState("");
    const [newProductName, setNewProductName] = useState("");
    const [productLink, setProductLink] = useState("");
    const [quantity, setQuantity] = useState(1);
    const [category, setCategory] = useState({});
    const [imageUrl, setImageUrl] = useState({});
    const [isVisibleCamera, setIsVisibleCamera] = useState(false);
    const [dataSearch, setDataSearch] = useState({
        listProduct: [],
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    });
    const [isVisibleScanCamera, setIsVisibleScanCamera] = useState(false);
    const [isShowListSearchProduct, setIsShowListSearchProduct] = useState(false);
    const [customerName, setCustomerName] = useState('');
    const [customerPhone, setCustomerPhone] = useState('');
    const [isShowAddButton, setIsShowAddButton] = useState(false);
    const [isVisibleBuyerInfo, setIsVisibleBuyerInfo] = useState(false);
    const [buyerInfo, setBuyerInfo] = useState([]);
    const [isViewImage, setIsViewImage] = useState(false);

    const isCreateButtonDisabled = listRequestProduct.findIndex(item => item.requiredQuantity == 0) > -1

    useEffect(() => {
        restockAction.updateRestockRequestProduct([]);
    }, [])

    const clearFormData = () => {
        setNewProductName('');
        setProductLink('');
        setQuantity(1);
        setCategory({});
        setImageUrl({});
    }

    const updateQuantity = (index, quantity) => {
        let newListProduct = [...listRequestProduct];
        newListProduct[index].requiredQuantity = quantity;
        restockAction.updateRestockRequestProduct(newListProduct);
    }

    const deleteProduct = (removeIndex) => {
        const newListProduct = listRequestProduct.filter((item, index) => index != removeIndex);
        restockAction.updateRestockRequestProduct(newListProduct);
    }

    const cancel = () => {
        restockAction.updateRestockRequestProduct([]);
        navigation.goBack();
    }

    const viewBuyerInfo = (product) => {
        setBuyerInfo([]);
        showBlockUI();
        const { productId, subgroupId } = product;
        restockAction.getBuyerInfo(productId, subgroupId).then(buyerInfo => {
            hideBlockUI();
            setBuyerInfo(buyerInfo);
            setIsVisibleBuyerInfo(true);
        }).catch(msgError => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: "OK",
                        style: 'cancel',
                        onPress: hideBlockUI
                    }
                ]
            );
        })
    }

    const createRequest = () => {
        const listProduct = listRequestProduct.map(item => ({
            "Status": 1,
            "ProductID": item.productId,
            "ProductName": item.productName,
            "DESCRIPTION": item.description,
            "Quantity": item.requiredQuantity,
            "Category": item.categoryId,
            "ISINVOICE": 1,
            "FilePath": item.imageUrl,
            "ActiveDate": dateHelper.formatDateYYYYMMDD(),
            "Price": 1,
            "CreatedUser": userName,
            "CreatedDate": dateHelper.formatDateYYYYMMDD(),
            "Reason": "",
            "CurrentInstock": item.stockQuantity
        }))
        const data = { listProduct, customerName, customerPhone }
        showBlockUI();
        restockAction.createRestockRequest(data).then(() => {
            Alert.alert(
                "",
                "Đã tạo yêu cầu thành công!",
                [
                    {
                        text: "OK",
                        style: 'cancel',
                        onPress: () => {
                            const { searchData } = route.params;
                            hideBlockUI();
                            restockAction.updateRestockRequestProduct([]);
                            searchData();
                            navigation.goBack();
                        }
                    },
                ]
            );
        }).catch(msgError => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: createRequest
                    }
                ]
            );
        })
    }

    const onChangeKeyword = (text) => {
        if (helper.IsNonEmptyString(text)) {
            if (helper.isValidateCharVN(text)) {
                setKeyword(text);
                const isSearch = (text.length > 2);
                if (isSearch) {
                    if (timeoutSearch.current) {
                        clearTimeout(timeoutSearch.current);
                    }
                    timeoutSearch.current = setTimeout(() => {
                        searchKeyword(text)
                    }, 1000);
                }
                else {
                    clearTimeout(timeoutSearch.current);
                    setDataSearch({
                        listProduct: [],
                        isFetching: false,
                        isEmpty: false,
                        description: "",
                        isError: false,
                    })
                }
            }
        } else {
            setKeyword('');
            setIsShowAddButton(false);
            setIsShowListSearchProduct(false);
            setDataSearch({
                listProduct: [],
                isFetching: false,
                isEmpty: false,
                description: '',
                isError: false
            });
            if (helper.IsNonEmptyArray(listRequestProduct)) {
                Keyboard.dismiss();
                setIsShowListSearchProduct(false);
            }
        }

    }

    const searchKeyword = (searchKeyword = keyword) => {
        setIsShowAddButton(false);
        setIsShowListSearchProduct(true);
        setDataSearch({
            listProduct: [],
            isFetching: true,
            isEmpty: false,
            description: "",
            isError: false,
        })
        restockAction.searchProduct(searchKeyword).then(({
            isEmpty,
            description,
            data
        }) => {
            setDataSearch({
                listProduct: data,
                isFetching: false,
                isEmpty: isEmpty,
                description: description,
                isError: false,
            })
            if (isEmpty) {
                setIsShowAddButton(true);
                clearFormData();
                if (helper.IsNonEmptyArray(listRequestProduct)) {
                    timeOutHideButton.current = setTimeout(() => {
                        setIsShowListSearchProduct(false);
                    }, 5000);
                }
            }
        }).catch(msgError => {
            setDataSearch({
                listProduct: [],
                description: msgError,
                isFetching: false,
                isEmpty: false,
                isError: true,
            })
            setIsShowAddButton(true);
            clearFormData();
            if (helper.IsNonEmptyArray(listRequestProduct)) {
                timeOutHideButton.current = setTimeout(() => {
                    setIsShowListSearchProduct(false);
                }, 5000);
            }
        });
    }

    const openBottomSheet = () => {
        Keyboard.dismiss();
        setNewProductName(keyword);
        if (timeOutHideButton.current) {
            clearTimeout(timeOutHideButton.current);
        }
        addProductSheetRef.current?.snapToIndex(1);
    }

    const closeBottomSheet = () => {
        addProductSheetRef.current?.close();
    }

    const onFocusSearchInput = () => {
        closeBottomSheet();
        setKeyword('');
        setDataSearch({
            listProduct: [],
            isFetching: false,
            isEmpty: false,
            description: '',
            isError: false
        });
    }

    const onChangeQuantity = (number) => {
        setQuantity(number);
    }

    const onSelectCategory = (cate) => {
        setCategory(cate);
        openBottomSheet();
    }

    const onOpenCategoryPicker = () => {
        closeBottomSheet();
    }

    const openCamera = () => {
        closeBottomSheet();
        setIsVisibleCamera(true)
    }

    const closeCamera = () => {
        setIsVisibleCamera(false);
        openBottomSheet();
    }

    const openScanCamera = () => {
        setIsVisibleScanCamera(true)
    }

    const closeScanCamera = () => {
        setIsVisibleScanCamera(false);
    }

    const onScanResult = (barcode) => {
        closeScanCamera();
        onChangeKeyword(barcode);
    }

    const closeModal = () => {
        setIsVisibleBuyerInfo(false);
    }

    const viewImage = () => {
        setIsViewImage(true)
    }

    const takePicture = (photo) => {
        setIsVisibleCamera(false);
        openBottomSheet();
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper.resizeImage(photo).then(({ path, uri, size, name }) => {
                const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: CREATE_RESTOCK_REQUEST });
                getImageCDN(body)
                    .then((response) => {
                        const remoteURI = API_GET_IMAGE_CDN_NEW + response[0];
                        setImageUrl({
                            realProductStatusAttachId: 0,
                            imageURL: remoteURI,
                            isDeleted: 0,
                            fileName: response[0]
                        })
                        hideBlockUI();
                    }).catch((error) => {
                        hideBlockUI();
                        console.log('uploadPicture', error);
                    })

            }).catch((error) => {
                hideBlockUI();
                console.log("resizeImage", error);
            });
        } else { hideBlockUI(); }
    }

    const selectPicture = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                setIsVisibleCamera(false);
                openBottomSheet();
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper.resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: CREATE_RESTOCK_REQUEST });
                            getImageCDN(body)
                                .then((response) => {
                                    const remoteURI = API_GET_IMAGE_CDN_NEW + response[0];
                                    setImageUrl({
                                        realProductStatusAttachId: 0,
                                        imageURL: remoteURI,
                                        isDeleted: 0,
                                        fileName: response[0]
                                    })
                                    hideBlockUI();
                                }).catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                })
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('resizeImage', error);
                        });
                } else { hideBlockUI(); }
            }
        );
    };

    const deleteImage = () => {
        setImageUrl({})
    }

    const onSelectProduct = (productInfo) => {
        setKeyword('');
        Keyboard.dismiss();
        setDataSearch({
            listProduct: [],
            isFetching: false,
            isEmpty: false,
            description: "",
            isError: false,
        })
        const { ProductID, ProductName, Quantity, QuantityUnit, StatusName, SubgroupID } = productInfo;
        restockAction.addRestockRequestProduct({
            productId: ProductID,
            productName: ProductName,
            categoryId: 0,
            inventoryStatusName: StatusName,
            stockQuantity: Quantity,
            quantityUnit: QuantityUnit,
            requiredQuantity: 1,
            isOutSystem: false,
            imageUrl: productInfo.Image,
            subgroupId: SubgroupID,
            description: ''
        });
        setIsShowListSearchProduct(false);
    }

    const addProduct = () => {
        if (validateData()) {
            setIsShowAddButton(false);
            clearFormData();
            closeBottomSheet();
            const productInfo = {
                productName: newProductName,
                category: category.name,
                categoryId: category.value,
                requiredQuantity: quantity,
                isOutSystem: true,
                imageUrl: imageUrl.imageURL,
                description: productLink
            }
            restockAction.addRestockRequestProduct(productInfo);
            setIsShowListSearchProduct(false);
        }
    }

    const cancelAddNewProduct = () => {
        closeBottomSheet();
        clearFormData();
    }

    const getCustomerInfo = () => {
        if (helper.IsNonEmptyString(customerPhone) && !helper.isValidatePhone(customerPhone)) {
            Alert.alert("", "Vui lòng nhập số điện thoại hợp lệ!");
            return false;
        } else {
            showBlockUI();
            restockAction.getCustomerInfo(customerPhone).then(({ CustomerName }) => {
                hideBlockUI();
                setCustomerName(CustomerName)
            }).catch(error => {
                hideBlockUI();
            })
        }

    }

    const validateData = () => {
        if (helper.IsEmptyString(newProductName)) {
            Alert.alert("", "Vui lòng nhập đầy đủ tên sản phẩm mới!");
            return false;
        } else if (helper.IsEmptyString(productLink)) {
            Alert.alert("", "Vui lòng nhập đường link tham khảo của sản phẩm!");
            return false;
        } else if (!productLink.startsWith(HTTPS)) {
            Alert.alert("", "Vui lòng nhập link sản phẩm hợp lệ!");
        } else if (helper.IsEmptyObject(category)) {
            Alert.alert("", "Vui lòng chọn ngành hàng của sản phẩm!");
            return false;
        } else if (helper.IsEmptyObject(imageUrl)) {
            Alert.alert("", "Vui lòng chụp hình minh hoạ sản phẩm!");
            return false;
        } else {
            return true;
        }
    }

    const renderItem = ({ item, index }) => (
        <SearchItem productInfo={item} onPress={onSelectProduct} />
    )

    return (
        <View style={{ flex: 1 }}>
            <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
                <SearchBar
                    value={keyword}
                    onChangeText={onChangeKeyword}
                    onSearch={() => { }}
                    placeholder={'Nhập tên SP / mã SP'}
                    addProduct={openBottomSheet}
                    onFocus={onFocusSearchInput}
                    openScanCamera={openScanCamera}
                />
                {
                    isShowAddButton && <AddButton onPress={openBottomSheet} />
                }
                {isShowListSearchProduct ?
                    <BaseLoading
                        isLoading={dataSearch.isFetching}
                        isEmpty={dataSearch.isEmpty}
                        textLoadingError={dataSearch.description}
                        isError={dataSearch.isError}
                        onPressTryAgains={searchKeyword}
                        content={
                            <FlatList
                                data={dataSearch.listProduct}
                                style={{ flexGrow: 1 }}
                                renderItem={renderItem}
                                keyExtractor={(item, index) => index.toString()}
                                showsVerticalScrollIndicator={false}
                                bounces={false}
                                keyboardShouldPersistTaps="always"
                            />
                        }
                    /> : (helper.IsNonEmptyArray(listRequestProduct) && <KeyboardAwareScrollView
                        style={{
                            flex: 1,
                            backgroundColor: COLORS.bgFFFFFF,
                            paddingTop: 10
                        }}
                        contentContainerStyle={{
                            alignItems: 'center'
                        }}
                        enableResetScrollToCoords={true}
                        keyboardShouldPersistTaps="always"
                        bounces={false}
                        overScrollMode="always"
                        showsHorizontalScrollIndicator={false}
                        showsVerticalScrollIndicator={false}
                        extraScrollHeight={0}
                        nestedScrollEnabled={true}
                    >
                        <ListRequestProduct
                            listProduct={listRequestProduct}
                            updateQuantity={updateQuantity}
                            deleteProduct={deleteProduct}
                            viewBuyerInfo={viewBuyerInfo}
                        />
                        <CustomerInfo
                            customerName={customerName}
                            onChangeCustomerName={setCustomerName}
                            customerPhone={customerPhone}
                            onChangeCustomerPhone={setCustomerPhone}
                            onSubmitPhoneNumber={getCustomerInfo}
                        />
                        <ButtonGroup
                            cancel={cancel}
                            confirm={createRequest}
                            isCreateButtonDisabled={isCreateButtonDisabled}
                        />
                    </KeyboardAwareScrollView>)}

                {
                    isVisibleScanCamera &&
                    <BarcodeCamera
                        isVisible={isVisibleScanCamera}
                        closeCamera={closeScanCamera}
                        resultScanBarcode={onScanResult}
                    />
                }
            </SafeAreaView>
            <ModalBuyerInfo
                isVisible={isVisibleBuyerInfo}
                buyerInfo={buyerInfo}
                closeModal={closeModal}
            />
            <Modal visible={isViewImage} transparent>
                <ImageViewer
                    renderHeader={() => (
                        <TouchableOpacity
                            style={{
                                position: 'absolute',
                                right: 5,
                                top: constants.heightTopSafe + 5,
                                zIndex: 100
                            }}
                            onPress={() => setIsViewImage(false)}>
                            <Icon
                                iconSet="Ionicons"
                                name="close"
                                color={COLORS.bgFFFFFF}
                                size={40}
                            />
                        </TouchableOpacity>
                    )}
                    imageUrls={[{
                        url: imageUrl.imageURL
                    }]}
                    enableSwipeDown
                    onCancel={() => setIsViewImage(false)}
                />
            </Modal>
            <BottomSheet
                ref={addProductSheetRef}
                snapPoints={['95%', '95%']}
                isShowBackdrop={true}
                backdropComponent={BottomSheetBackdrop}
            >
                <AddProductForm
                    newProductName={newProductName}
                    onchangeNewProductName={setNewProductName}
                    productLink={productLink}
                    onChangeProductLink={setProductLink}
                    quantity={quantity}
                    onChangeQuantity={onChangeQuantity}
                    category={category}
                    onSelectCategory={onSelectCategory}
                    onOpenCategoryPicker={onOpenCategoryPicker}
                    cancel={cancelAddNewProduct}
                    confirm={addProduct}
                    imageUrl={imageUrl}
                    openCamera={openCamera}
                    deleteImage={deleteImage}
                    onClosePicker={openBottomSheet}
                    viewImage={viewImage}
                />
                <CaptureCamera
                    isVisibleCamera={isVisibleCamera}
                    takePicture={takePicture}
                    closeCamera={closeCamera}
                    selectPicture={selectPicture}
                />
            </BottomSheet>
        </View>
    )
}

const mapStateToProps = function (state) {
    return {
        listRequestProduct: state.restockReducer.listRequestProduct,
        userName: state.userReducer.userName
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        restockAction: bindActionCreators(actionRestockCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(CreateRestockRequest)