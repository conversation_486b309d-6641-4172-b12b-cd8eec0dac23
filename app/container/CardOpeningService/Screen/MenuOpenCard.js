import { FlatList, Image, Linking, StyleSheet, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react';
import { COLORS } from '@styles';
import { MyText } from '@components';
import { constants } from '../../../constants';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionCardOpeningServiceCreator from "../action";
import BonusMessage from '../../ConsumerLoanAirtimeService/component/BonusMessage';

const MenuOpenCard = ({
  navigation,
  actionCardOpeningService,
  updateHeaderAirtime
}) => {
  const { InfoReward, AirTimeTransactionTypeID } = updateHeaderAirtime ?? {};
  const safeInfoReward = Array.isArray(InfoReward) ? InfoReward : [];

  const getScreenByTransactionType = (AirTimeTransactionTypeID) => {
    switch (AirTimeTransactionTypeID) {
      case 2012:
        return {
          create: {
            name: "Tạo yêu cầu mở thẻ",
            screen: "CustomerInfor",
          },
          manage: {
            name: "<PERSON><PERSON><PERSON><PERSON> lý hồ sơ mở thẻ",
            screen: "PullConsumerOpenCardPackage",
          },
        };
      case 2032:
      case 2034:
        return {
          create: {
            name: "Tạo yêu cầu đăng ký tài khoản",
            screen: "NoteInformationOpenCard",
          },
          manage: {
            name: "Quản lý yêu cầu đăng ký tài khoản",
            screen: "PullConsumerOpenCardPackage",
          },
        };
      default:
        return {
          create: {
            name: "Tạo yêu cầu đăng ký tài khoản",
            screen: "NoteInformationOpenCard",
          },
          manage: {
            name: "Quản lý yêu cầu đăng ký tài khoản",
            screen: "PullConsumerOpenCardPackage",
          },
        };
    }
  };

  const screenInfo = getScreenByTransactionType(AirTimeTransactionTypeID);

  const data = [
    {
      id: 1,
      name: screenInfo.create.name,
      screeen: screenInfo.create.screen,
      source: require('../../../../assets/open_card.png'),
    },
    {
      id: 2,
      name: screenInfo.manage.name,
      screeen: screenInfo.manage.screen,
      source: require('../../../../assets/open_card_manager.png'),
    }
  ];

  const renderItem = ({ item }) => {
    const handlePress = (item) => {
      if (typeof item.screeen === 'function') {
        item.screeen();
      } else {
        navigation.navigate(item.screeen);
      }
    };
    return (
      <TouchableOpacity
        onPress={() => {
          handlePress(item);
          actionCardOpeningService.clear_data_customer();
        }}
        style={{
          margin: 3,
          justifyContent: 'center',
          alignItems: 'center',
          padding: 10,
          width: constants.width / 2 - constants.getSize(10),
          height: 100,
          backgroundColor: COLORS.bgF2F2F2,
          borderRadius: 10,
          padding: 35,
          shadowColor: COLORS.bg000000,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 4,
          elevation: 5,
          flexDirection: 'row'
        }}
        activeOpacity={1}
      >
        <Image
          style={{
            width: 60,
            height: 60,
          }}
          source={item.source}
        />
        <View style={{
          width: 110,
          height: 60,
          justifyContent: 'center'
        }}>
          <MyText
            text={item.name}
            style={{
              color: COLORS.bg7F7F7F,
              fontWeight: 'bold',
              marginLeft: 5,
              fontSize: 15,
            }}
          />
        </View>
      </TouchableOpacity>
    )
  }
  return (
    <View style={{
      marginTop: 5,
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <FlatList
        data={data}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderItem}
        numColumns={2}
        bounces={false}
        scrollEventThrottle={16}
        nestedScrollEnabled={true}
        columnWrapperStyle={{
          flex: 1
        }}
      />
      <BonusMessage message={safeInfoReward} />
    </View>
  )
}

const mapStateToProps = function (state) {
  return {
    updateHeaderAirtime:
      state.cardOpeningServiceReducer.updateHeaderAirtime,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionCardOpeningService: bindActionCreators(
      actionCardOpeningServiceCreator,
      dispatch
    ),
  };
};
export default connect(mapStateToProps, mapDispatchToProps)(MenuOpenCard);

const styles = StyleSheet.create({})
