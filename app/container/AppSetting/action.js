
import { helper } from '@common';
import { apiBase, METHOD } from '@config';
import { API_CONST } from '@constants';
import { appSettingState } from './state';

const { API_GET_APP_SETTING } = API_CONST;

const UPDATE_APP_SETTING = "UPDATE_APP_SETTING";
export const appSettingAction = {
    UPDATE_APP_SETTING
}

export const getAppSetingConfig = () => {
    return async (dispatch, getState) => {
        const body = {
            "cluster": "ERP",
            "map": "SYSTEM_APPLICATIONCONFIG_CACHE",
            "key": "MASTERDATA.SYSTEM_APPLICATIONCONFIG"
        };

        try {
            global.config = appSettingState;
            const response = await apiBase(API_GET_APP_SETTING, METHOD.POST, body);
            console.log("getAppSettingConfig success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                const config = getKeyValueConfig(object);
                global.config = { ...config, isLoaded: true };
                dispatch(update_app_setting({ ...config, isLoaded: true }));
            }
        } catch (error) {
            console.log("getAppSettingConfig error", error);
        }
    };
};

const getKeyValueConfig = (data) => {
    const defaultValue = { ...appSettingState };
    data.forEach(ele => {
        const { CONFIGID, CONFIGVALUE } = ele;
        if (helper.hasProperty(defaultValue, CONFIGID)) {
            defaultValue[CONFIGID] = CONFIGVALUE;
        }
    })
    console.log(defaultValue);
    return defaultValue;
}

const update_app_setting = (config) => {
    return {
        type: UPDATE_APP_SETTING,
        config
    };
}
