import { appSettingState } from './state';
import { appSettingAction } from './action';

const appSettingReducer = (state = appSettingState, action) => {
    switch (action.type) {
        case appSettingAction.UPDATE_APP_SETTING:
            return {
                ...state,
                ...action.config,
                isLoaded: true
            };
        default:
            return state;
    }
}

export { appSettingReducer };