import { Alert } from "react-native";
import {
    API_CONST,
    DEVICE,
    STORAGE_CONST,
    constants
} from '@constants';
import {
    helper,
    storageHelper,
    getPatchVersion,
    getTokenNotification,
} from '@common';
import { apiBase, METHOD, getQueryString } from '@config';
import { translate } from "@translate";
import { getAppSetingConfig } from '../AppSetting/action';
const { URL_DOWNLOAD_APP } = constants;
const {
    API_REQUEST_OAUTH_TOKEN,
    API_REQUEST_TOKEN
} = API_CONST;

const START_REQUEST_OAUTH_TOKEN = "START_REQUEST_OAUTH_TOKEN";
const STOP_REQUEST_OAUTH_TOKEN = "STOP_REQUEST_OAUTH_TOKEN";

export const loginAction = {
    START_REQUEST_OAUTH_TOKEN,
    STOP_REQUEST_OAUTH_TOKEN,
}

export const requestOauthToken = (data) => {
    return (dispatch, getState) => {
        let body = {
            "grant_type": "password",
            "username": data.username,
            "password": data.password,
            // "device_id": DEVICE.uniqueId,
            // "device_name": DEVICE.model,
            // "device_app_version": DEVICE.version
        }
        dispatch(start_request_oauth_token());
        apiBase(API_REQUEST_OAUTH_TOKEN, METHOD.POST, body, {
            "isOauthenToken": true, "isCustomToken": true
        }).then(async (response) => {
            response.change_device = false;
            console.log("requestOauthToken success: ", response);
            const { access_token, change_device } = response;
            global.isChangeDevice = change_device;
            if (helper.IsNonEmptyString(access_token)) {
                const requireOTP = change_device ? "true" : "false";
                await storageHelper.multiSet([
                    [STORAGE_CONST.ACCESS_TOKEN, access_token],
                    [STORAGE_CONST.REQUIRE_OTP, requireOTP]
                ]);
                const authBody = await getBodyAuthInfo();
                dispatch(callApiRequestToken(authBody));
                dispatch(getAppSetingConfig());
            }
            else {
                dispatch(stop_request_oauth_token({
                    description: translate("login.cannot_authen"),
                    isError: true,
                }));
            }
        }).catch(error => {
            console.log("requestOauthToken error: ", error);
            dispatch(stop_request_oauth_token({
                description: error.msgError,
                isError: true,
            }));
        })
    }
}

export const getBodyAuthInfo = async () => {
    let body = {
        "ClientID": DEVICE.appID,
        "Secret": DEVICE.secret,
        "Scope": DEVICE.appID,
        "device": DEVICE.deviceName,
        "deviceId": DEVICE.uniqueId,
        "deviceVersion": DEVICE.version,
        "deviceName": DEVICE.model,
        "keyName": DEVICE.keyName,
        "patchVersion": "",
        "deviceLanguage": "",
        "notificationToken": "",
        "beta": true,
    }
    try {
        const notificationToken = await getTokenNotification();
        body.notificationToken = notificationToken;
        const patchVersion = await getPatchVersion();
        body.patchVersion = patchVersion;
        const deviceLanguage = await storageHelper.getItem(STORAGE_CONST.CURRENT_LANGUAGE);
        body.deviceLanguage = deviceLanguage;
    } catch (error) {
        console.log("getBodyAuthInfo error", error);
    }
    return body;
}

export const callApiRequestToken = function (body) {
    return function (dispatch, getState) {
        apiBase(API_REQUEST_TOKEN, METHOD.POST, body).then((response) => {
            console.log("callApiRequestToken success: ", response);
            const { object } = response;
            dispatch(checkAuthenInfo(object));
        }).catch(error => {
            console.log("callApiRequestToken error: ", error);
            dispatch(stop_request_oauth_token({
                description: error.msgError,
                isError: true,
            }));
        })
    }
}

const checkAuthenInfo = (data) => {
    return function (dispatch, getState) {
        const {
            couchSecret,
            couchURI,
            couchUser,
            locationInfo,
            setting,
            userInfo
        } = data;
        const isValidSecretDB = helper.IsNonEmptyString(couchSecret);
        const isValidUriDB = helper.IsNonEmptyString(couchURI);
        const isValidUserDB = helper.IsNonEmptyString(couchUser);
        const isValidCouchDB = (isValidSecretDB && isValidUriDB && isValidUserDB);
        const isValidLocalInfo = !helper.IsEmptyObject(locationInfo);
        const isValidSetting = !helper.IsEmptyObject(setting);
        const isValidUser = !helper.IsEmptyObject(userInfo);
        if (!isValidCouchDB) {
            dispatch(stop_request_oauth_token({
                description: translate("login.info_user_problem"),
                isError: true,
            }));
            return;
        }
        if (!isValidUser) {
            dispatch(stop_request_oauth_token({
                description: translate("login.cannot_information_work"),
                isError: true,
            }));
            return;
        }
        // Check store permission
        if (!isValidLocalInfo) {
            dispatch(stop_request_oauth_token({
                description: translate("login.cannot_information_warehouse"),
                isError: true,
            }));
            return;
        }
        else {
            const {
                hasRight,
                isCanOutOrder,
                isDefaultStore,
                storeID
            } = locationInfo;
            const nonDefault = (storeID == 0);
            const nonStoreInfo = !isDefaultStore;
            const nonSale = (!hasRight || !isCanOutOrder);
            if (nonDefault) {
                dispatch(stop_request_oauth_token({
                    description: translate("login.declare_default_store"),
                    isError: true,
                }));
                return;
            }
            if (nonStoreInfo) {
                dispatch(stop_request_oauth_token({
                    description: translate("login.dont_get_info_default"),
                    isError: true,
                }));
                return;
            }
            if (nonSale) {
                dispatch(stop_request_oauth_token({
                    description: translate("login.buy_store_default"),
                    isError: true,
                }));
                return;
            }
        }
        // Check Match version
        if (!isValidSetting) {
            console.log(`${translate("login.version")} “${DEVICE.version}” ${translate("login.please_update_new_version")}`);
            // return;
        }
        else {
            const { settingValue } = setting;
            const isOldVersion = (settingValue > DEVICE.version);
            if (isOldVersion) {
                const content = `${translate("login.version")} “${DEVICE.version}” ${translate("login.version_old_cus")} “${settingValue}” ${translate("login.use_app")}`;
                dispatch(stop_request_oauth_token({
                    description: content,
                    isError: true,
                }));
                Alert.alert("", content, [{
                    text: "OK",
                    onPress: () => {
                        const params = {
                            action: 'update',
                            bundleID: DEVICE.bundleId,
                            device: DEVICE.uniqueId,
                            opaque: helper.uuidv4()

                        };
                        const query = getQueryString(params);
                        const url = `xmanager://xmanager?${query}`;
                        helper.openURL(url)
                    }
                }]);
                return;
            }
        }
        // const isCTV = userInfo?.bcnbDepartmentID == 1581;
        // if (isCTV) {
        //     const content = `[CTV] Phiên bản của app hiện tại đã cũ. Vui lòng cập nhật phiên bản mới nhất`;
        //     dispatch(stop_request_oauth_token({
        //         description: content,
        //         isError: true,
        //     }));
        //     Alert.alert("", content, [{
        //         text: "OK",
        //         onPress: () => {
        //             const params = {
        //                 action: 'update',
        //                 bundleID: DEVICE.bundleId,
        //                 device: DEVICE.uniqueId,
        //                 opaque: helper.uuidv4()

        //             };
        //             const query = getQueryString(params);
        //             const url = `xmanager://xmanager?${query}`;
        //             helper.openURL(url)
        //         }
        //     }]);
        //     return;
        // }
        dispatch(setDataAuthen(data));
    }
}

const setDataAuthen = ({
    couchSecret,
    couchURI,
    couchUser,
    locationInfo,
    userInfo,
    deviceToken
}) => {
    return function (dispatch, getState) {
        const data = {
            brandID: locationInfo.brandID,
            companyID: locationInfo.companyID,
            provinceID: locationInfo.provinceID,
            storeAddress: locationInfo.storeAddress,
            storeName: locationInfo.storeName,
            storeShortName: locationInfo.storeShortName,
            fullName: userInfo.fullName,
            positionName: userInfo.positionName,
            mobi: userInfo.mobi,
            departmentName: userInfo.departmentName,
            areaID: locationInfo.areaID,
            storeID: locationInfo.storeID,
            userName: userInfo.userName,
            permissions: locationInfo.permissions,
            currency: locationInfo.currencyUnitSymbol,
            bcnbAreaID: locationInfo.bcnbAreaId,
            languageID: global.languageID,
            deviceTokenOffline: DEVICE.uniqueId,
            isDefaultStore: locationInfo.isDefaultStore,
            storeGroupID: locationInfo.storeGroupID,
            moduleID: 1,
            isShowWeb: userInfo.isShowWeb,
            positionID: userInfo.positionID
        }
        global.currency = locationInfo.currencyUnitSymbol || "đ";
        global.isVN = (locationInfo.companyID != 6);
        global.defaultStoreID = locationInfo.storeID;
        global.companyID = locationInfo.companyID
        const arrKeyValue = [
            [STORAGE_CONST.COUCH_SECRET, couchSecret],
            [STORAGE_CONST.COUCH_URI, couchURI],
            [STORAGE_CONST.COUCH_USER, couchUser],
            [STORAGE_CONST.USER_NAME, userInfo.userName],
            [STORAGE_CONST.DEFAULT_USER_INFO, JSON.stringify(data)],
        ];
        storageHelper.multiSet(arrKeyValue).then(result => {
            console.log("storageHelper.multiSet", result);
            dispatch(set_user_info(data));
            dispatch(stop_request_oauth_token({
                description: "",
                isError: false,
            }));
        })
    }
}

const start_request_oauth_token = () => {
    return ({
        type: START_REQUEST_OAUTH_TOKEN
    });
}

const stop_request_oauth_token = ({
    description,
    isError,
}) => {
    return {
        type: STOP_REQUEST_OAUTH_TOKEN,
        description: description,
        isError: isError,
    };
}

const set_user_info = (data) => {
    return ({
        type: "SET_USER_INFO",
        data
    });
}