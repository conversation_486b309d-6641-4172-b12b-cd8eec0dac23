import React, {
    useMemo,
    useRef,
    forwardRef,
    useImperativeHandle,
    useCallback
} from 'react';
import { StyleSheet, View, Animated } from 'react-native';
import BottomSheet, {
    BottomSheetScrollView,
    BottomSheetFooter,
    useBottomSheet
} from '@gorhom/bottom-sheet';
import { COLORS } from '@styles';
import { RectButton } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
    Extrapolate,
    interpolate,
    useAnimatedStyle
} from 'react-native-reanimated';
import { Icon } from '@components';
import { AN_KHANG_COLORS } from '../constants';

const AnimatedRectButton = Animated.createAnimatedComponent(RectButton);

const BtmSheet = forwardRef(({ children, onGoNext, ...props }, ref) => {
    // ref
    const bottomSheetRef = useRef(null);

    // variables
    const snapPoints = useMemo(() => ['40%', '80%'], []);

    useImperativeHandle(ref, () => ({
        snapToIndex: (index) => {
            bottomSheetRef.current.snapToIndex(index);
        },
        close: () => {
            bottomSheetRef.current.close();
        }
    }));

    const GoNext = useCallback(
        ({ animatedFooterPosition }) => {
            // #region hooks
            // we need the bottom safe insets to avoid bottom notches.
            const { bottom: bottomSafeArea } = useSafeAreaInsets();
            // extract animated index and other functionalities
            const { animatedIndex } = useBottomSheet();
            // #endregion

            // #region styles
            // create the content animated style reacting to the
            // sheet index.
            const containerAnimatedStyle = useAnimatedStyle(
                () => ({
                    opacity: interpolate(
                        animatedIndex.value,
                        [-0.85, 0],
                        [0, 1],
                        Extrapolate.CLAMP
                    )
                }),
                [animatedIndex]
            );
            const containerStyle = useMemo(
                () => [containerAnimatedStyle, styles.container],
                [containerAnimatedStyle]
            );
            // #endregion

            return (
                <BottomSheetFooter
                    // we pass the bottom safe inset
                    bottomInset={bottomSafeArea}
                    // we pass the provided `animatedFooterPosition`
                    animatedFooterPosition={animatedFooterPosition}>
                    <AnimatedRectButton
                        style={containerStyle}
                        onPress={onGoNext}>
                        <Icon
                            iconSet="Ionicons"
                            name="md-arrow-forward"
                            size={25}
                            color={COLORS.icFFFFFF}
                        />
                    </AnimatedRectButton>
                </BottomSheetFooter>
            );
        },
        [onGoNext]
    );

    // renders
    return (
        <BottomSheet
            ref={bottomSheetRef}
            index={-1}
            snapPoints={snapPoints}
            enablePanDownToClose
            enableOverDrag
            style={styles.bottomSheet}
            footerComponent={onGoNext ? GoNext : null}
            {...props}>
            <View style={styles.contentContainer}>
                <BottomSheetScrollView keyboardShouldPersistTaps={'always'}>{children}</BottomSheetScrollView>
            </View>
        </BottomSheet>
    );
});

const styles = StyleSheet.create({
    bottomSheet: {
        elevation: 5,
        shadowColor: COLORS.bg000000,
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84
    },
    container: {
        alignItems: 'center',
        alignSelf: 'flex-end',
        backgroundColor: AN_KHANG_COLORS.CART,
        borderRadius: 25,
        elevation: 8,
        height: 50,
        justifyContent: 'center',
        marginBottom: 12,
        marginHorizontal: 24,
        shadowOffset: {
            width: 0,
            height: 12
        },
        shadowOpacity: 0.25,
        shadowRadius: 8.0,

        width: 50
    },
    contentContainer: {
        backgroundColor: COLORS.bgF5F5F5,
        flex: 1
    }
});

export default BtmSheet;
