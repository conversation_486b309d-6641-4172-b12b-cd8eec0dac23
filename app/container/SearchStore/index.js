import React, { Component } from 'react';
import {
    View,
    FlatList,
    TouchableOpacity,
    Keyboard,
    StyleSheet,
    SafeAreaView
} from 'react-native';

import {
    SearchInput,
    BaseContainer,
    MyText,
    Icon,
    showPopup
} from "@components";

import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { constants, ENUM } from "@constants";
import { translate } from "@translate";
import { helper } from "@common";
import * as userActionCreator from "../UserInfo/action";
import * as storeActionCreator from './action';
import * as actionShoppingCartCreator from "../ShoppingCart/action";
import * as actionPouchCreator from "../PouchRedux/action";
import { COLORS } from "@styles";

class SearchStore extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataStores: [],
            isError: false,
            isLoading: true,
            message: '',
            valueSearch: '',
            dataItems: [],
            typingTimeout: 0,
            itemSelected: {}
        }
        this.preTimeSearching = -1;
        this.goBack = this.goBack.bind(this);
        this.getStore = this.getStore.bind(this);
        this.searchItem = this.searchItem.bind(this);
        this.onSubmitEdtingSearching = this.onSubmitEdtingSearching.bind(this);
        this.selectStore = this.selectStore.bind(this);
        this.refSearchInput = React.createRef();
    }

    componentDidMount() {
        this.getStore();
    }

    componentDidUpdate(preProps, preState) {
        if (preProps.dataItems !== this.props.dataItems) {
            this.setState({ dataItems: this.props.dataItems });
        }
    }

    render() {
        return (
            <SafeAreaView
                style={{
                    flex: 1
                }}
            >
                <View
                    style={{
                        flex: 1
                    }}
                >
                    <View
                        style={{
                            paddingTop: constants.getSize(10),
                            paddingBottom: constants.getSize(10),
                            alignSelf: 'center'
                        }}
                    >
                        <SearchInput
                            refValue={this.refSearchInput}
                            width={constants.width - constants.getSize(20)}
                            height={40}
                            placeholder={translate("searchStore.input_store")}
                            rightComponent={[
                                {
                                    source: { uri: "ic_search" },
                                }
                            ]}
                            onChangeText={(text) => {
                                if (helper.isValidateCharVN(text)) {
                                    this.searchItem(text);
                                }
                            }}
                            onSubmitEditing={this.onSubmitEdtingSearching(this.state.valueSearch)}
                            value={this.state.valueSearch}
                            returnKeyType="search"
                            autoFocus={true}
                        />
                    </View>
                    <BaseContainer
                        isLoading={this.state.isLoading}
                        isError={this.state.isError}
                        textLoadingError={this.state.message}
                        onPressTryAgains={() => {
                            this.setState({
                                isLoading: true,
                                isError: false
                            }, this.getStore);
                        }}
                        content={
                            <FlatList
                                data={this.state.dataStores}
                                renderItem={({ item, index }) => this.renderItem(item, index)}
                                keyExtractor={(item, index) => item.storeID.toString()}
                                showsVerticalScrollIndicator={false}
                                keyboardShouldPersistTaps="always"
                                keyboardDismissMode="on-drag"
                            />
                        }
                    />
                </View>
            </SafeAreaView>
        );
    }

    renderItem(item, index) {
        return (
            <TouchableOpacity
                style={{
                    flex: 1,
                    flexDirection: "row",
                    paddingRight: 10,
                    paddingLeft: 10,
                    paddingTop: 10,
                    paddingBottom: 10
                }}
                onPress={this.selectStore(item)}
            >
                <View
                    style={{
                        width: constants.getSize(24)
                    }}
                >
                    <Icon
                        iconSet={"FontAwesome"}
                        name={"store-alt"}
                        color={COLORS.ic2FB47C}
                        style={{
                            height: "100%",
                            alignItems: "center",
                            alignContent: "center",
                            justifyContent: "center",
                            width: constants.getSize(24),
                            paddingLeft: constants.getSize(4),
                        }}
                        size={constants.getSize(14)}
                    />

                </View>

                <View
                    style={{
                        flex: 1,
                        paddingLeft: constants.getSize(10)
                    }}
                >
                    <MyText
                        text={item.storeID}
                        style={{
                            flex: 1,
                            color: COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                        children={
                            <MyText
                                text={" - " + item.storeName}
                                style={{
                                    flex: 1,
                                    color: COLORS.txt333333
                                }}
                            />
                        }
                    />
                    {
                        helper.hasProperty(item, "hasRight") && helper.hasProperty(item, "isCanOutOrder") && (!item.hasRight || !item.isCanOutOrder) &&
                        <MyText
                            text={translate('searchStore.no_authority')}
                            style={{
                                flex: 1,
                                color: COLORS.txt333333,
                                fontWeight: 'bold'
                            }}
                        />
                    }

                </View>


                <View
                    style={{
                        position: "absolute",
                        bottom: 0,
                        left: 10,
                        right: 10,
                        width: constants.width - 20,
                        backgroundColor: COLORS.bg7F7F7F,
                        height: StyleSheet.hairlineWidth
                    }}
                />





            </TouchableOpacity>
        );
    }

    onSubmitEdtingSearching = text => () => {
        Keyboard.dismiss();
    }

    searchItem = text => {
        this.setState({
            valueSearch: text
        }, () => {

        });

        if (text.length > 0) {
            if (this.preTimeSearching == -1) this.preTimeSearching = new Date().getTime();
            let currentTimeSearching = new Date().getTime();
            let timeTyping = currentTimeSearching - this.preTimeSearching;
            this.preTimeSearching = currentTimeSearching;
            let timeoutTyping = timeTyping > 500 ? 350 : 500;
            if (this.typingTimeout) {
                clearTimeout(this.typingTimeout);
            }
            this.typingTimeout = setTimeout(() => {
                this.setState({
                    isLoading: true,
                    dataStores: []
                }, () => {
                    const { storeAction } = this.props;
                    storeAction.getStoreList(this.state.valueSearch).then(lstStore => {
                        if (helper.IsEmptyArray(lstStore)) {
                            this.setState({
                                isError: true,
                                isLoading: false,
                                message: translate('searchStore.no_store_found')
                            });
                        } else {
                            this.setState({
                                isLoading: false,
                                isError: false,
                                dataStores: lstStore
                            });
                        }
                    }).catch(err => {
                        this.setState({
                            isError: true,
                            isLoading: false,
                            message: err.msgError
                        })
                    });
                })
            }, 800);
        }

    }

    getStore() {
        const { valueSearch } = this.state;
        const keyword = helper.IsNonEmptyString(valueSearch) ? valueSearch : "";
        const { storeAction } = this.props;
        storeAction.getStoreList(keyword).then(lstStore => {
            if (helper.IsEmptyArray(lstStore)) {
                this.setState({
                    isError: true,
                    isLoading: false,
                    message: translate('searchStore.no_store_found')
                });
            } else {
                this.setState({
                    isLoading: false,
                    isError: false,
                    dataStores: lstStore
                });
            }
        }).catch(err => {
            this.setState({
                isError: true,
                isLoading: false,
                message: err.msgError
            })
        });
    }

    selectStore = (item) => () => {
        if (item.storeID != this.props.userInfo.storeID) {
            if (helper.hasProperty(item, "hasRight") && helper.hasProperty(item, "isCanOutOrder") && item.hasRight && item.isCanOutOrder) {
                this.setState({
                    itemSelected: item
                }, () => {
                    showPopup(translate('common.notification'), translate('searchStore.confirm_change_store'), [
                        {
                            text: "Cancel",
                            style: "cancel",
                            onPress: () => {
                                this.setState({ itemSelected: {} });
                            }
                        },
                        {
                            text: "OK",
                            style: "default",
                            onPress: this.updateUserInfo
                        },
                    ])
                });
            } else {
                showPopup(translate('common.notification'), translate('searchStore.no_store_authority'), [{
                    text: translate('searchStore.understood'),
                }])
            }
        }
        else {
            this.setState({ itemSelected: item }, () => { this.goBack() })
        }
    }

    getRouteName = (brandID, storeID) => {
        const isAva = ENUM.BRAND_ID.AVA.some((brand) => brandID == brand)
        switch (true) {
            case parseInt(brandID) == ENUM.BRAND_ID.AN_KHANG || (isAva && ENUM.ALLOW_STORE_ID.AVA.some((storeId) => storeId == -1 || storeID == storeId)):
                return ENUM.SCREENS.PHARMACY;
            default:
                return ENUM.SCREENS.SALE;
        }
    };
    goBack = () => {
        const { navigation } = this.props;
        const { itemSelected } = this.state;
        const routeName = this.getRouteName(itemSelected.brandID, itemSelected.storeID);
        navigation.reset({
            index: 0,
            routes: [{ name: routeName }]
        });
    };

    updateUserInfo = () => {
        const { itemSelected } = this.state;
        const { userInfo, userAction, storeAction, actionShoppingCart, actionPouch, navigation } = this.props;
        const data = {
            ...userInfo,
            "storeID": itemSelected.storeID,
            "storeName": itemSelected.storeName,
            "storeShortName": itemSelected.storeShortName,
            "storeAddress": itemSelected.storeAddress,
            "phoneRegexValidation": itemSelected.phoneRegexValidation,
            "brandID": itemSelected.brandID,
            "provinceID": itemSelected.provinceID,
            "currency": itemSelected.currencyUnitSymbol,
            "companyID": itemSelected.companyID,
            "isDefaultStore": itemSelected.isDefaultStore,
            "storeGroupID": itemSelected.storeGroupID,
            "areaID": itemSelected.areaID,
            "isShowWeb": itemSelected.isShowWeb
        };
        global.currency = itemSelected.currencyUnitSymbol || "đ";
        global.isVN = (itemSelected.companyID != 6);
        global.companyID = itemSelected.companyID
        userAction.setUserInfo(data)
        storeAction.resetChangeStore();
        actionShoppingCart.deleteShoppingCart();
        actionPouch.setDataCartApply();
        if (itemSelected.companyID == 2) {
            navigation.reset({
                index: 0,
                routes: [{ name: 'Home' }],
            });
        } else {
            this.goBack();
        }
    }
}

const mapStateToProps = function (state) {
    return {
        userInfo: state.userReducer,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        userAction: bindActionCreators(userActionCreator, dispatch),
        storeAction: bindActionCreators(storeActionCreator, dispatch),
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(SearchStore);