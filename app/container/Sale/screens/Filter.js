import React, { useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    Image,
    FlatList,
    TouchableOpacity,
    StyleSheet,
    Animated,
    Platform
} from 'react-native';
import { Icon } from '@components';
import { COLORS } from '../../../styles';
import { useSelector } from 'react-redux';
import { constants } from "@constants";
const { MENUID } = constants


const CategorySidebar = ({ dataMenu, selectedMenu, onSelect }) => {
    const blinkingItemID = 4789;
    const scaleAnim = useRef({}).current;
    const blinkAnim = useRef(new Animated.Value(1)).current;

    useEffect(() => {
        dataMenu.forEach((item) => {
            scaleAnim[item.menuID] = new Animated.Value(1);
        });

        if (blinkingItemID) {
            Animated.loop(
                Animated.sequence([
                    Animated.timing(blinkAnim, {
                        toValue: 0.3,
                        duration: 500,
                        useNativeDriver: true
                    }),
                    Animated.timing(blinkAnim, {
                        toValue: 1,
                        duration: 500,
                        useNativeDriver: true
                    })
                ])
            ).start();
        }
    }, [dataMenu]);

    const handleSelect = (menuID) => {
        Object.keys(scaleAnim).forEach((id) => {
            Animated.timing(scaleAnim[id], {
                toValue: id == menuID ? 1.1 : 1,
                duration: 200,
                useNativeDriver: true
            }).start();
        });
        onSelect(menuID);
    };

    return (
        <FlatList
            data={dataMenu}
            keyExtractor={(item) => item.menuID.toString()}
            showsVerticalScrollIndicator={false}
            style={styles.sidebarContainer}
            renderItem={({ item }) => {
                const isBlinking = item.menuID === blinkingItemID;
                if (isBlinking) return null;

                const isSelected = selectedMenu === item.menuID;

                return (
                    <Animated.View style={{ transform: [{ scale: scaleAnim[item.menuID] || 1 }] }}>
                        <TouchableOpacity
                            style={[
                                styles.sidebarItem,
                                isSelected && styles.selectedSidebarItem
                            ]}
                            onPress={() => handleSelect(item.menuID)}
                            activeOpacity={0.7}
                        >
                            <Text style={[
                                styles.sidebarText,
                                isSelected && styles.selectedSidebarText
                            ]}>
                                {item.menuName}
                            </Text>

                            {isBlinking && (
                                <Animated.View style={[styles.blinkingIcon, { opacity: blinkAnim }]}>
                                    <Icon
                                        iconSet="MaterialIcons"
                                        name="star"
                                        size={22}
                                        color={COLORS.txtF50537}
                                    />
                                </Animated.View>
                            )}
                        </TouchableOpacity>
                    </Animated.View>
                );
            }}
        />
    );
};

const SubcategoryItem = ({ item, getFilterPropByCategory, parentMenu }) => {
    const image = item?.icon?.includes('https://')
        ? { uri: item.icon }
        : require('../../../../assets/smartphone.png');

    return (
        <TouchableOpacity
            onPress={() => getFilterPropByCategory({ ...item, categoryID: item.categoryID || parentMenu.categoryID })}
            activeOpacity={0.7}
            style={styles.card}
        >
            <View style={styles.container}>
                <Image
                    source={image}
                    style={styles.image}
                    resizeMode="contain"
                />
                <Text numberOfLines={3} ellipsizeMode="tail" style={styles.text}>
                    {item.menuName}
                </Text>
            </View>
        </TouchableOpacity>
    );
};

const SubcategoryVertical = ({ data, getFilterPropByCategory }) => {
    return (
        <FlatList
            data={data?.ChildMenus || []}
            keyExtractor={(item, index) => index.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.subcategoryContainer}
            renderItem={({ item: parentMenu }) => {
                return (
                    <View style={styles.categorySection}>
                        <Text style={styles.categoryTitle}>{parentMenu.menuName}</Text>
                        <FlatList
                            data={parentMenu?.ChildMenus}
                            keyExtractor={(item, index) => index.toString()}
                            numColumns={2}
                            showsHorizontalScrollIndicator={false}
                            contentContainerStyle={styles.subcategoryList}
                            renderItem={({ item }) => (
                                <SubcategoryItem
                                    item={item}
                                    getFilterPropByCategory={getFilterPropByCategory}
                                    parentMenu={parentMenu}
                                />
                            )}
                        />
                    </View>
                )
            }}
        />
    );
};

const Filter = ({ listMenu, getFilterPropByCategory }) => {
    const [selectedMenu, setSelectedMenu] = useState(null);
    const [dataMenu, setDataMenu] = useState([]);
    const { brandID } = useSelector(
        (state) => state.userReducer
    );
    useEffect(() => {
        // Hàm xây dựng cây menu từ danh sách menu phẳng
        const buildMenuTree = (menus) => {
            // Bước 1: Lọc bỏ các menu có menuID trùng với parentID (tránh lặp vô hạn)
            const filteredMenus = menus.filter(item => item.menuID !== item.parentID);

            // Bước 2: Lấy ra các menu gốc (có parentID =  MENUID[brandID])) và sắp xếp theo thứ tự hiển thị
            const rootMenus = filteredMenus.filter(e => e.parentID === MENUID[brandID])
                .sort((a, b) => a.displayOrder - b.displayOrder);

            // Hàm đệ quy thêm các menu con vào menu cha
            const addChildren = (parent) => {
                // Tìm tất cả menu con của menu hiện tại
                parent.ChildMenus = filteredMenus
                    .filter(e => e.parentID === parent.menuID)
                    .sort((a, b) => a.displayOrder - b.displayOrder);

                // Với mỗi menu con:
                parent.ChildMenus.forEach(child => {
                    // Tiếp tục đệ quy để thêm menu con cho menu hiện tại
                    addChildren(child);
                });
            };

            // Áp dụng đệ quy cho tất cả menu gốc
            rootMenus.forEach(addChildren);
            const excludedMenuIDs = new Set([4198, 4815, 4996, 4991, 4961, 4979, 5242, 5233, 5196]);
            return rootMenus?.filter((item) => !excludedMenuIDs.has(item.menuID));
        };

        // Xây dựng cây menu từ listMenu đầu vào
        const menuData = buildMenuTree([...listMenu]);

        setDataMenu(menuData);

        // Nếu có menuData và chưa chọn menu nào thì chọn menu đầu tiên làm mặc định
        if (menuData.length > 0 && !selectedMenu) {
            setSelectedMenu(menuData[0]?.menuID);
        }
    }, [listMenu]);


    const selectedData = dataMenu.find(cat => cat?.menuID === selectedMenu);

    return (
        <View style={styles.filterContainer}>
            <View style={styles.sidebarWrapper}>
                <CategorySidebar
                    dataMenu={dataMenu}
                    selectedMenu={selectedMenu}
                    onSelect={setSelectedMenu}
                />
            </View>
            <View style={styles.contentWrapper}>
                {selectedData && (
                    <SubcategoryVertical
                        data={selectedData}
                        getFilterPropByCategory={getFilterPropByCategory}
                    />
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    filterContainer: {
        flex: 1,
        flexDirection: 'row',
        backgroundColor: COLORS.bgFFFFFF
    },
    sidebarWrapper: {
        flex: 0.4
    },
    contentWrapper: {
        flex: 1.2
    },
    sidebarContainer: {
        backgroundColor: COLORS.bgFFFFFF,
        marginBottom: 30,
        borderRightWidth: StyleSheet.hairlineWidth,
        borderRightColor: COLORS.bgC4C4C4
    },
    sidebarItem: {
        padding: 16,
        backgroundColor: 'transparent',
        alignItems: 'center',
        borderRadius: 8,
        position: 'relative',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: Platform.OS === 'android' ? 0 : 3,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4
    },
    selectedSidebarItem: {
        backgroundColor: '#FFFFFF'
    },
    sidebarText: {
        color: COLORS.txt333333,
        fontSize: 12.5,
        textAlign: 'center'
    },
    selectedSidebarText: {
        color: '#1E88E5',
        fontWeight: 'bold'
    },
    blinkingIcon: {
        position: 'absolute',
        top: -5,
        right: 5
    },
    card: {
        margin: 10,
        flex: 1,
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        maxWidth: '45%',
        alignItems: 'center',
        justifyContent: 'center'
    },
    container: {
        alignItems: 'center',
        justifyContent: "center"
    },
    image: {
        width: 75,
        height: 75,
        borderRadius: 12
    },
    text: {
        fontSize: 12,
        flexShrink: 1,
        fontWeight: '500',
        paddingTop: 5
    },
    subcategoryContainer: {
        padding: 10
    },
    categorySection: {
        margin: 10
    },
    categoryTitle: {
        fontSize: 13,
        fontWeight: 'bold',
        marginBottom: 10
    },
    subcategoryList: {
        padding: 10,
        backgroundColor: COLORS.bgFFFFFF
    }
});

export default Filter;
