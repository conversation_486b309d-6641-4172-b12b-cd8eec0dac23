import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
    View,
    Text,
    FlatList,
    TouchableOpacity,
    StyleSheet,
    Animated,
    Image
} from 'react-native';
import { COLORS } from '../../../styles';
import { helper } from '@common';

const FilterProduct = ({ data, toggleSelection }) => {
    const [modifyData, setMofifyData] = useState([]);
    const [expandedGroups, setExpandedGroups] = useState({});

    useEffect(() => {
        if (data?.length > 0) {
            setMofifyData(data);
        }
    }, [data]);

    useEffect(() => {
        if (modifyData?.length > 0) {
            toggleSelection(modifyData);
        }
    }, [modifyData]);





    const toggleExpand = (groupIndex) => {
        setExpandedGroups((prev) => ({
            ...prev,
            [groupIndex]: !prev[groupIndex]
        }));
    };

    const handleItemPress = (groupIndex, valueIndex) => {
        const newData = helper.deepCopy(modifyData);

        // Nếu là price range, chỉ cho phép chọn 1 khoảng giá
        // if (newData[groupIndex].isPriceRange) {
        //     newData[groupIndex].ProductPropValueBOLst.forEach((item, idx) => {
        //         item.selected = idx === valueIndex ? !item.selected : false;
        //     });
        // } else {
        // Các group khác cho phép chọn nhiều
        newData[groupIndex].ProductPropValueBOLst[valueIndex].selected =
            !newData[groupIndex].ProductPropValueBOLst[valueIndex].selected;
        // }

        setMofifyData(newData);
    };

    return (
        <View style={styles.container}>
            <FlatList
                showsVerticalScrollIndicator={false}
                data={modifyData}
                keyExtractor={(item) => item.PropertyID.toString()}
                renderItem={({ item, index: groupIndex }) => {
                    // Skip empty groups
                    if (item.ProductPropValueBOLst?.length <= 0) return null;

                    const isExpanded = expandedGroups[groupIndex] || false;
                    const displayedItems = isExpanded
                        ? item.ProductPropValueBOLst
                        : item.ProductPropValueBOLst?.slice(0, 4);

                    return (
                        <View style={styles.groupContainer}>
                            <Animated.View style={styles.animatedContainer}>
                                <Text style={styles.groupTitle}>
                                    {item.PropertyName}
                                </Text>

                                <FlatList
                                    data={displayedItems}
                                    keyExtractor={(value) => value.ValueID.toString()}
                                    numColumns={2}
                                    renderItem={({ item: value, index: valueIndex }) => (
                                        <TouchableOpacity
                                            onPress={() => handleItemPress(groupIndex, valueIndex)}
                                            style={[
                                                styles.filterItem,
                                                value.selected && styles.selectedFilterItem,
                                                item.isPriceRange && styles.priceRangeItem
                                            ]}
                                        >
                                            {item.IsManu ? (
                                                <Image
                                                    source={{ uri: value.SmallLogo }}
                                                    style={styles.manufacturerImage}
                                                    resizeMode="contain"
                                                />
                                            ) : (
                                                <Text
                                                    numberOfLines={2}
                                                    ellipsizeMode="tail"
                                                    style={[
                                                        styles.filterText,
                                                        value.selected && styles.selectedFilterText
                                                    ]}
                                                >
                                                    {value.Value}
                                                </Text>
                                            )}
                                        </TouchableOpacity>
                                    )}
                                />

                                {item.ProductPropValueBOLst.length > 4 && (
                                    <TouchableOpacity
                                        onPress={() => toggleExpand(groupIndex)}
                                        style={styles.expandButton}
                                    >
                                        <Text style={styles.expandButtonText}>
                                            {isExpanded ? 'Thu gọn' : 'Xem thêm'}
                                        </Text>
                                    </TouchableOpacity>
                                )}
                            </Animated.View>
                        </View>
                    );
                }}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF,
        padding: 10
    },
    groupContainer: {
        margin: 10,
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3
    },
    animatedContainer: {
        paddingVertical: 5,
        paddingHorizontal: 10,
    },
    groupTitle: {
        fontSize: 15,
        fontWeight: 'bold',
        marginBottom: 10
    },
    filterItem: {
        flex: 1,
        margin: 5,
        padding: 10,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: COLORS.bgA7A7A7,
        minHeight: 40,
        backgroundColor: COLORS.bgFFFFFF,
        maxWidth: '50%'
    },
    selectedFilterItem: {
        borderColor: '#A5D6A7',
        backgroundColor: '#E8F5E9',
    },
    priceRangeItem: {
        maxWidth: '100%',
    },
    filterText: {
        textAlign: 'center',
        width: '100%',
        fontSize: 13,
        color: COLORS.bg000000
    },
    selectedFilterText: {
        fontWeight: 'bold',
        color: '#2E7D32'
    },
    manufacturerImage: {
        width: 100,
        height: 30
    },
    expandButton: {
        marginTop: 10,
        alignItems: 'center',
        padding: 8,
        borderRadius: 5,
        alignSelf: 'flex-end'
    },
    expandButtonText: {
        color: COLORS.bg1E88E5,
        textDecorationLine: 'underline',
        fontSize: 13
    }
});

export default FilterProduct;