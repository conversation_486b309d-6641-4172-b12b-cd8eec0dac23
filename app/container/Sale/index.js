import React, { Component, useRef, useEffect } from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    TextInput,
    Keyboard,
    TouchableWithoutFeedback,
    Alert,
    SafeAreaView,
    FlatList,
    Text,
    StyleSheet,
} from 'react-native';
import { helper, storageHelper } from "@common";
import { constants, DEVICE } from "@constants";
import {
    BaseLoading,
    Icon,
    MyText,
    BarcodeCamera,
    ImageURI,
    showBlockUI,
    hideBlockUI,
} from "@components";
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { FloatingAction } from "react-native-floating-action";
import { COLORS } from "@styles";
import { translate } from '@translate';
import LottieView from 'lottie-react-native';
import Footer from "./component/Footer";
import HistoryList from "./component/HistoryList";
import FavoriteList from "./component/FavoriteList";
import ProductList from "./component/ProductList";
import * as actionSaleCreator from "./action";
import * as actionPouchCreator from "../PouchRedux/action";
import * as actionDetailCreator from "../Detail/action";
import * as actionCartCreator from "../ShoppingCart/action";
import * as actionSaleOrderCreator from "../SaleOrderCart/action";
import { sale_christmas } from '@animation';
import MenuProductSheet from './sheets/MenuProductSheet'
import FilterSheet from './sheets/FilterSheet'
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
const { MENUID } = constants


const configUser = "165059,11498,27325,16901"

class Sale extends Component {
    constructor() {
        super();
        this.state = {
            keyword: "",
            dataSearch: [],
            isFetching: false,
            isEmpty: false,
            description: "",
            isError: false,
            isFocus: false,
            isVisibleScan: false,
            dataHistory: [],
            listMenu: [],
            listFilter: [],
            categorySelected: {},
            stateProduct: {
                isFetching: false,
                isError: false,
                description: "",
                data: [],
            },
            categoryName: "",
            isShowFilter: false,
            selectedIndex: 0
        };
        this.timeoutSearch = null;
        this.isBarcode = false;
        this.FLOAT_ACTION = [
            {
                text: translate('sale.go_to_cart'),
                icon: { uri: "ic_cartshopping" },
                name: "1",
                tintColor: 'transparent'
            }
        ];
        this.menuProductSheetRef = React.createRef(null)
        this.filterSheet = React.createRef(null)
        this.flatListRef = React.createRef();


    }

    componentDidMount() {
        this.getDataHistory();
        const { userInfo, navigation, stateFilter } = this.props;
        if (userInfo.companyID == 2) {
            navigation.reset({
                index: 0,
                routes: [{ name: 'Home' }],
            });
        }
        setTimeout(() => {
            if (true) {
                this.setState({ isShowFilter: true })
            }
        }, 300);
        if (!helper.IsEmptyObject(stateFilter)) {
            this.setState(prevState => ({
                ...prevState,
                stateProduct: {
                    ...prevState.stateProduct,
                    isFetching: true,
                    isError: false,
                    description: "",
                    data: stateFilter.products,
                },
                categorySelected: stateFilter.category,
                listFilter: stateFilter.filter,

            }));
        }
    }

    componentWillUnmount() {
        if (this.timeoutSearch) {
            clearTimeout(this.timeoutSearch);
        }
    }

    componentDidUpdate(preProps, preState) {
        if (preState.keyword !== this.state.keyword) {
            this.onChangeKeyword(this.state.keyword);
        }
        if (preState.listFilter !== this.state.listFilter) {
            this.handleSearchFilterProduct()
        }

    }

    handleSearchFilterProduct = async () => {
        const { userInfo: { brandID, storeID } } = this.props

        this.setState(prevState => ({
            ...prevState,
            stateProduct: {
                ...prevState.stateProduct,
                isFetching: true,
                isError: false,
                description: "",
                data: [],
            }
        }));

        const { manuList, propList, minPrice, maxPrice } = this.state.listFilter.reduce(
            (acc, { IsManu, isPriceRange, ProductPropValueBOLst }) => {
                ProductPropValueBOLst.forEach(({ selected, ValueID, min, max }) => {
                    if (!selected) return;
                    if (isPriceRange) {
                        if (acc.minPrice == 0 || min < acc.minPrice) {
                            acc.minPrice = min;
                        }
                        if (acc.maxPrice == 0 || max > acc.maxPrice) {
                            acc.maxPrice = max;
                        }
                    } else if (IsManu) {
                        acc.manuList.push(ValueID);
                    } else {
                        acc.propList.push(ValueID);
                    }
                });
                return acc;
            },
            { manuList: [], propList: [], minPrice: 0, maxPrice: 0 }
        );



        const body = {
            "outputStoreId": storeID,
            "inventoryStatusId": 1,
            "siteId": 2,
            "categoryId": this.state.categorySelected.categoryID,
            "pageSize": 10,
            "manufacturerIds": manuList.length > 0 ? manuList : [],
            "propValueIds": propList.length > 0 ? propList : [],
            "price": [minPrice, maxPrice || 100000000]
        }

        actionSaleCreator.getProductsByFilter(body).then((data) => {
            this.setState(prevState => ({
                ...prevState,
                stateProduct: {
                    ...prevState.stateProduct,
                    isFetching: false,
                    isError: false,
                    description: "",
                    data: data,
                }
            }));
        }).catch((msgError) => {
            this.setState(prevState => ({
                ...prevState,
                stateProduct: {
                    ...prevState.stateProduct,
                    isFetching: false,
                    isError: true,
                    description: msgError,
                    data: [],
                }
            }));
        })
    }

    getDataHistory = async () => {
        const { actionDetail, actionSaleOrder } = this.props;
        actionSaleOrder.setInitScreen('Sale');
        actionDetail.resetDetailState();
        const dataHistory = await storageHelper.getDataHistory();
        this.setState({ dataHistory: dataHistory });
    }

    rederProductSearch = (item, index) => {
        const {
            productName,
            imageUrl
        } = item;
        return (
            <TouchableOpacity style={{
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: COLORS.btnFFFFFF,
                width: (constants.width) / 2,
                backgroundColor: COLORS.btnFFFFFF,
                borderWidth: 2,
                borderColor: COLORS.bdF5F5F5,
                paddingVertical: 4
            }}
                onPress={() => this.onSelectProduct(item)}
            >
                {
                    imageUrl
                        ? <ImageURI
                            uri={imageUrl}
                            style={{
                                width: 130,
                                height: 150
                            }}
                            resizeMode={"contain"}
                        />
                        : <Icon
                            iconSet={"MaterialIcons"}
                            name={"sentiment-satisfied-alt"}
                            color={COLORS.icEA4C89}
                            size={120}
                        />
                }
                <MyText
                    text={productName}
                    style={{
                        fontWeight: 'bold',
                        color: COLORS.txt333333,
                        textAlign: "center",
                    }}
                />
            </TouchableOpacity>
        );
    }

    getMenu = () => {
        Keyboard.dismiss()
        this.setState({ keyword: "" })
        showBlockUI()
        const { userInfo: { brandID } } = this.props
        const body = {
            "siteId": 2,
            "menuId": MENUID[brandID]
        }
        actionSaleCreator.getMenu(body).then((menu) => {
            this.setState({ listMenu: menu }, () => {
                this.menuProductSheetRef.current?.present()
            })
            hideBlockUI()
        }).catch((msgError) => {
            Alert.alert("Thông báo", msgError, [
                {
                    text: "Bỏ Qua",
                    onPress: hideBlockUI
                },
                {
                    text: "Thử Lại",
                    onPress: this.getMenu
                }
            ])

        })
    }

    getFilterPropByCategory = async (item) => {
        try {
            showBlockUI();
            const { userInfo: { brandID } } = this.props;

            const body = {
                siteId: 2,
                langId: "vi-VN",
                categoryId: item.categoryID
            };

            const [filterProps, manufacturers] = await Promise.all([
                actionSaleCreator.getFilterPropByCategories(body),
                actionSaleCreator.getManuByCategoryID(body),
            ]);
            const finalData = enrichData(filterProps, manufacturers)
            this.setState({ listFilter: finalData, categorySelected: item, categoryName: item.menuName }, () => {
                // this.menuProductSheetRef.current?.dismiss();
                this.filterSheet.current?.present()

            });

        } catch (error) {
            Alert.alert("Thông báo", error || "Không tìm thấy thông tin danh mục", [
                { text: "Bỏ Qua", onPress: hideBlockUI },
                { text: "Thử Lại", onPress: () => this.getFilterPropByCategory(item) }
            ]);
        } finally {
            hideBlockUI();
        }
    };

    applyFilter = (data) => {
        this.menuProductSheetRef.current?.dismiss()
        this.filterSheet.current?.dismiss()
        this.setState({ listFilter: data })
    };

    onClearSelect = () => {
        const newData = helper.deepCopy(this.state.listFilter);
        newData.forEach(element => {
            element.ProductPropValueBOLst.forEach(element => {
                if (element.selected
                ) {
                    element.selected = false
                }
            });
        });
        this.setState({ listFilter: newData })
    }

    deleteItemFilter = (item) => {
        const keyFilter = `${item.groupID}_${item.valueID}`;
        const newData = this.state.listFilter.map(group => ({
            ...group,
            ProductPropValueBOLst: group.ProductPropValueBOLst.map(value => ({
                ...value,
                selected: value.selected && `${group.GroupID}_${value.ValueID}` === keyFilter ? !value.selected : value.selected
            }))
        }));

        this.setState({ listFilter: newData });
    }

    handleSelectManu = (item) => {
        const keyFilter = `${item.groupID}_${item.valueID}`;
        const { listFilter } = this.state;

        let foundIndex = -1;

        const newListFilter = listFilter.map(group => ({
            ...group,
            ProductPropValueBOLst: group.ProductPropValueBOLst.map((value, index) => {
                const isSelectedItem = `${group.GroupID}_${value.ValueID}` === keyFilter;
                if (isSelectedItem) foundIndex = index;

                return {
                    ...value,
                    selected: isSelectedItem ? !value.selected : value.selected,
                };
            })
        }));

        this.setState({ listFilter: newListFilter, selectedIndex: foundIndex }, () => {
            if (this.flatListRef?.current && foundIndex !== -1) {
                let viewPosition = 0.5;

                if (foundIndex < this.state.selectedIndex) {
                    viewPosition = 0;
                } else if (foundIndex > this.state.selectedIndex) {
                    viewPosition = 1;
                }

                try {
                    this.flatListRef.current.scrollToIndex({
                        index: foundIndex,
                        animated: true,
                        viewPosition,
                    });
                } catch (e) {
                    console.warn("scrollToIndex failed", e);
                }
            }
        });
    };





    onClearFilter = () => {
        this.setState(prevState => ({
            ...prevState,
            stateProduct: {
                ...prevState.stateProduct,
                isFetching: false,
                isError: false,
                description: "",
                data: [],
            },
            categorySelected: {},
            listFilter: [],

        }));
        this.props.actionSale.setStateFilter({})
    }


    render() {
        const {
            keyword,
            dataSearch,
            isFetching,
            isEmpty,
            description,
            isError,
            isFocus,
            isVisibleScan,
            dataHistory,
        } = this.state;
        const { dataFavorite, cartRequest, dataCartApply } = this.props;
        const isNonEmptyKeyword = helper.IsNonEmptyString(keyword);
        const isNonEmptyFavorite = helper.IsNonEmptyArray(dataFavorite);
        const isNonEmptyHistory = helper.IsNonEmptyArray(dataHistory);
        const isHistory = (isFocus || !isNonEmptyFavorite) && isNonEmptyHistory;
        const isFavorite = (!isFocus && isNonEmptyFavorite);
        const isClose = (isFocus && isNonEmptyKeyword);
        const isCartEmpty = helper.IsEmptyObject(cartRequest);
        const isCartApplyEmpty = helper.IsEmptyObject(dataCartApply);
        const getSelectedItems = () => {
            return this.state.listFilter.flatMap(group =>
                group.ProductPropValueBOLst
                    .filter(item => item.selected)
                    .map(item => ({
                        groupID: group.GroupID,
                        valueID: item.ValueID,
                        valueName: item.Value
                    }))
            );
        };


        const getManuItems = () =>
            this.state.listFilter
                .filter(({ IsManu }) => IsManu)
                .flatMap(({ GroupID, ProductPropValueBOLst }) =>
                    ProductPropValueBOLst.map(({ ValueID, SmallLogo, selected }) => ({
                        groupID: GroupID,
                        valueID: ValueID,
                        image: SmallLogo,
                        selected: selected
                    }))
                );
        const countSelectedItems = () => {
            return this.state.listFilter.reduce((total, group) => {
                return total + group.ProductPropValueBOLst.filter(item => item.selected).length;
            }, 0);
        };

        return (
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <BottomSheetModalProvider>
                    <View style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF,
                    }}>

                        <SafeAreaView style={{
                            flex: 1
                        }}>
                            <View style={{
                                flexDirection: "row", justifyContent: "space-between", alignItems: "center",
                                width: constants.width - 20, alignSelf: "center"
                            }}>
                                {
                                    this.state.isShowFilter && <TouchableOpacity onPress={() => {
                                        this.getMenu()
                                    }} style={{ justifyContent: "flex-start", paddingRight: 10 }}>
                                        <Image
                                            style={{
                                                width: 30,
                                                height: 30,
                                                resizeMode: "contain",
                                            }}
                                            source={require('../../../assets/filter_sale.png')}
                                        />
                                    </TouchableOpacity>
                                }

                                <InputSearch
                                    value={keyword}
                                    onChangeText={(text) => {
                                        if (helper.isValidateCharVN(text)) {
                                            this.isBarcode = false;
                                            this.setState({ keyword: text });
                                        }
                                    }}
                                    clearText={this.clearKeyword}
                                    openBarcode={this.onOpenBarcode}
                                    isClose={isClose}
                                    onFocus={this.onFocus}
                                    onBlur={this.onBlur}
                                    storeID={this.props.userInfo.storeID}
                                />


                            </View>
                            {
                                (helper.IsNonEmptyArray(this.state.listFilter) && !isNonEmptyKeyword) ? (
                                    <>
                                        <TouchableOpacity onPress={this.onClearFilter} style={{ padding: 7, alignItems: "flex-end", paddingRight: 20 }}>
                                            <MyText style={{ color: COLORS.txtF50537, fontWeight: "500" }} text={"Xoá tất cả"} />
                                        </TouchableOpacity>
                                        <View style={{ flex: 1 }}>
                                            <View style={{ flexDirection: 'row', alignItems: 'center', paddingHorizontal: 10 }}>
                                                {/* Icon Filter - Cố định */}

                                                {/* Danh sách item - Cuộn ngang */}
                                                {
                                                    // getSelectedItems()?.length > 0 
                                                    false ? <FlatList
                                                        contentContainerStyle={{ flexGrow: 1 }}
                                                        data={getSelectedItems() || []}
                                                        keyExtractor={(item, index) => index.toString()}
                                                        horizontal
                                                        showsHorizontalScrollIndicator={false}
                                                        renderItem={({ item }) => (
                                                            <View

                                                                style={{
                                                                    paddingHorizontal: 15,
                                                                    paddingVertical: 8,
                                                                    backgroundColor: COLORS.bdFFFFFF,
                                                                    borderRadius: 15,
                                                                    marginRight: 10,
                                                                    flexDirection: "row",
                                                                    justifyContent: "space-between",
                                                                    alignItems: "center"
                                                                }}>

                                                                <Text>{item.valueName}</Text>
                                                                <TouchableOpacity
                                                                    onPress={() => this.deleteItemFilter(item)}
                                                                    style={{
                                                                        position: 'absolute',
                                                                        top: -2,
                                                                        right: -2,
                                                                    }}>
                                                                    <Icon
                                                                        iconSet={"MaterialCommunityIcons"}
                                                                        name={"close"}
                                                                        color={COLORS.ic848A8C}
                                                                        size={16}
                                                                    />
                                                                </TouchableOpacity>

                                                            </View>
                                                        )}
                                                    /> :
                                                        <FlatList
                                                            ref={this.flatListRef}
                                                            contentContainerStyle={{ flexGrow: 1 }}
                                                            data={getManuItems() || []}
                                                            keyExtractor={(item, index) => index.toString()}
                                                            horizontal
                                                            showsHorizontalScrollIndicator={false}
                                                            renderItem={({ item }) => (
                                                                <TouchableOpacity
                                                                    onPress={() => this.handleSelectManu(item)}
                                                                    style={{
                                                                        padding: 3,
                                                                        borderRadius: 15,
                                                                        marginRight: 10,
                                                                        flexDirection: "row",
                                                                        justifyContent: "space-between",
                                                                        alignItems: "center",
                                                                        minHeight: 40,
                                                                        backgroundColor: item.selected ? "#E8F5E9" : COLORS.bgFFFFFF,
                                                                        borderColor: item.selected
                                                                            ? "#A5D6A7"
                                                                            : COLORS.bgA7A7A7,
                                                                        borderWidth: item.selected ? 1 : StyleSheet.hairlineWidth,
                                                                    }}>

                                                                    <Image
                                                                        source={{ uri: item.image }}
                                                                        style={{ width: 90, height: 30 }}
                                                                        resizeMode="contain"
                                                                    />
                                                                </TouchableOpacity>
                                                            )}
                                                        />

                                                }


                                                <TouchableOpacity
                                                    style={{
                                                        paddingVertical: 12,
                                                        alignItems: 'center',
                                                        paddingHorizontal: 5,
                                                        borderRadius: 8,
                                                        position: 'relative',
                                                    }}
                                                    onPress={() => { this.filterSheet.current?.present() }}>
                                                    <Icon
                                                        iconSet={"MaterialCommunityIcons"}
                                                        name={"filter-outline"}
                                                        color={COLORS.ic848A8C}
                                                        size={35}
                                                    />
                                                    {
                                                        countSelectedItems() > 0 && <View
                                                            style={{
                                                                position: 'absolute',
                                                                top: 12,
                                                                right: 5,
                                                                backgroundColor: "red",
                                                                padding: 5,
                                                                borderRadius: 10,
                                                            }}
                                                        >

                                                        </View>
                                                    }
                                                </TouchableOpacity>
                                            </View>
                                            <BaseLoading
                                                isLoading={this.state.stateProduct.isFetching}
                                                textLoadingError={this.state.stateProduct.description}
                                                isError={this.state.stateProduct.isError}
                                                onPressTryAgains={this.handleSearchFilterProduct}
                                                content={
                                                    <View style={{}}>
                                                        <FlatList
                                                            data={this.state.stateProduct.data}
                                                            numColumns={2}
                                                            keyExtractor={(item) => item.productCode.toString()}
                                                            contentContainerStyle={{ padding: 10, backgroundColor: COLORS.bdFFFFFF }}
                                                            renderItem={({ item }) => {
                                                                const image = item?.Image?.includes("https://")
                                                                    ? { uri: item.Image }
                                                                    : require('../../../assets/smartphone.png');

                                                                return (
                                                                    <View style={{
                                                                        margin: 10,
                                                                        flex: 1,
                                                                        backgroundColor: '#fff',
                                                                        borderRadius: 12,
                                                                        padding: 10,
                                                                        shadowColor: '#000',
                                                                        shadowOffset: { width: 0, height: 2 },
                                                                        shadowOpacity: 0.1,
                                                                        shadowRadius: 4,
                                                                        elevation: 3, // For Android shadow effect,
                                                                        maxWidth: "50%"
                                                                    }}>
                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                const productSearch = {
                                                                                    imageUrl: item.Image,
                                                                                    productID: item.ProductID,
                                                                                    productIDERP: item.productCode,
                                                                                    productName: item.ProductName,
                                                                                    inventoryStatusID: 1
                                                                                };
                                                                                this.onSelectProduct(productSearch)
                                                                            }
                                                                            }
                                                                            activeOpacity={0.7}
                                                                            style={{
                                                                                paddingVertical: 5,
                                                                                paddingHorizontal: 10,

                                                                            }}>

                                                                            {/* Định dạng thành hàng ngang */}
                                                                            <View style={{ alignItems: 'center' }}>
                                                                                {/* Hình ảnh */}
                                                                                <Image
                                                                                    source={image}
                                                                                    style={{ width: 75, height: 75, borderRadius: 10, }}
                                                                                    resizeMode="contain"
                                                                                />

                                                                                {/* Tên menu */}
                                                                                <Text
                                                                                    numberOfLines={6}
                                                                                    ellipsizeMode="tail"
                                                                                    style={{ color: COLORS.bg0000007, fontSize: 12, flexShrink: 1, paddingTop: 5 }}>
                                                                                    {item.ProductName}
                                                                                </Text>

                                                                            </View>
                                                                            {
                                                                                item.InstockQuantity > 0 && <Text
                                                                                    numberOfLines={2}
                                                                                    ellipsizeMode="tail"
                                                                                    style={{ color: COLORS.bg0000007, fontSize: 12, paddingTop: 5 }}>
                                                                                    {`Tồn bán được: `}
                                                                                    <Text
                                                                                        numberOfLines={2}
                                                                                        ellipsizeMode="tail"
                                                                                        style={{ color: COLORS.bg1E88E5, fontSize: 14, fontWeight: "bold", paddingTop: 5 }}>
                                                                                        {`${item.InstockQuantity}/${item.Quantity}`}
                                                                                    </Text>
                                                                                </Text>
                                                                            }
                                                                            {
                                                                                item.SalePrice > 0 && <>
                                                                                    <Text
                                                                                        numberOfLines={2}
                                                                                        ellipsizeMode="tail"
                                                                                        style={{ color: COLORS.bg0000007, fontSize: 12, paddingTop: 5 }}>
                                                                                        {`Giá: `}
                                                                                        <Text
                                                                                            numberOfLines={2}
                                                                                            ellipsizeMode="tail"
                                                                                            style={{ color: COLORS.bg1E88E5, fontSize: 14, fontWeight: "bold", paddingTop: 5 }}>
                                                                                            {`${helper.convertNum(item.SalePrice)}`}
                                                                                        </Text>
                                                                                    </Text>
                                                                                    <Text
                                                                                        numberOfLines={2}
                                                                                        ellipsizeMode="tail"
                                                                                        style={{ textDecorationLine: "line-through", color: COLORS.bg1E88E5, fontSize: 11, fontWeight: "bold", paddingTop: 5 }}>
                                                                                        {`          ${helper.convertNum(item.SalePrice)}`}
                                                                                    </Text>
                                                                                </>
                                                                            }

                                                                        </TouchableOpacity>
                                                                    </View>

                                                                );
                                                            }}
                                                        // ListFooterComponent={
                                                        //     <TouchableOpacity
                                                        //         onPress={() => { }}
                                                        //         style={{
                                                        //             marginTop: 10,
                                                        //             alignItems: 'center',
                                                        //             padding: 8,
                                                        //             // backgroundColor: COLORS.bg1E88E5,
                                                        //             borderRadius: 5,
                                                        //         }}>
                                                        //         <Text style={{ color: COLORS.bg1E88E5, fontSize: 15 }}>
                                                        //             Xem thêm
                                                        //         </Text>
                                                        //     </TouchableOpacity>
                                                        // }

                                                        />

                                                    </View>
                                                }
                                            />

                                        </View>
                                    </>
                                    // </View>


                                ) :
                                    <>
                                        {
                                            isNonEmptyKeyword
                                                ? <View style={{
                                                    flex: 1,
                                                    alignItems: "center",
                                                }}>
                                                    {
                                                        isEmpty
                                                            ? <EmptyMessage
                                                                description={description}
                                                            />
                                                            : <BaseLoading
                                                                isLoading={isFetching}
                                                                isEmpty={isEmpty}
                                                                textLoadingError={description}
                                                                isError={isError}
                                                                onPressTryAgains={this.searchKeyword(keyword)}
                                                                content={
                                                                    !isFetching &&
                                                                    <ProductList
                                                                        data={dataSearch}
                                                                        renderItem={({ item, index }) => this.rederProductSearch(item, index)}
                                                                    />
                                                                }
                                                            />
                                                    }
                                                </View>
                                                : <>
                                                    {
                                                        isHistory && <View style={{
                                                            flex: 1,
                                                            alignItems: "center",
                                                            // height: constants.height * 0.76,
                                                            marginTop: 55,
                                                            zIndex: 999999,
                                                            position: 'absolute',
                                                        }}>
                                                            <HistoryList
                                                                data={dataHistory}
                                                                renderItem={(item, index) => this.renderItemHistory(item, index)}
                                                            />
                                                        </View>
                                                    }
                                                    {
                                                        isFavorite && <View style={{
                                                            flex: 1,
                                                            alignItems: "center",
                                                            // height: constants.height * 0.76,
                                                        }}>
                                                            <FavoriteList
                                                                data={dataFavorite}
                                                                renderItem={({ item, index }) => this.renderItemFavorite(item, index)}
                                                            />
                                                        </View>
                                                    }
                                                </>
                                        }
                                        {
                                            !isFavorite ? ((!isNonEmptyKeyword) ? <View
                                                style={{
                                                    // height: constants.height * 0.76,
                                                    flex: 1,
                                                    alignItems: 'center',
                                                }}>
                                                {/* <LottieView
                                    autoPlay
                                    source={sale_christmas}
                                /> */}
                                            </View>
                                                :
                                                null)
                                                :
                                                null
                                        }
                                    </>
                            }

                            <Footer
                                version={DEVICE.version}
                            />
                            {
                                (!isCartEmpty || !isCartApplyEmpty) &&
                                <FloatingAction
                                    actions={this.FLOAT_ACTION}
                                    color={COLORS.txt597B66}
                                    distanceToEdge={{
                                        vertical: constants.heightBottomSafe + 64,
                                        horizontal: 10
                                    }}
                                    overlayColor={COLORS.bg0000005}
                                    dismissKeyboardOnPress={true}
                                    iconWidth={18}
                                    iconHeight={18}
                                    buttonSize={42}
                                    animated={false}
                                    onPressItem={name => {
                                        this.moveToShoppingCart();
                                    }}
                                />
                            }
                        </SafeAreaView>
                        {
                            isVisibleScan &&
                            <BarcodeCamera
                                isVisible={isVisibleScan}
                                closeCamera={this.onCloseBarcode}
                                resultScanBarcode={(barcode) => {
                                    this.isBarcode = true;
                                    this.setState({
                                        keyword: barcode,
                                        isVisibleScan: false
                                    })
                                }}
                            />
                        }
                        <MenuProductSheet
                            menuProductSheetRef={this.menuProductSheetRef}
                            listMenu={this.state.listMenu}
                            onChangeStatusSheet={() => { }}
                            getFilterPropByCategory={this.getFilterPropByCategory}
                        />
                        <FilterSheet
                            filterSheetRef={this.filterSheet}
                            onChangeStatusSheet={(index) => {
                                if (index === -1) {
                                    this.setState({ categoryName: "" })
                                }
                            }}
                            listFilter={this.state.listFilter}
                            applyFilter={this.applyFilter}
                            categoryName={this.state.categoryName}
                            onClearSelect={this.onClearSelect}
                        />
                    </View>
                </BottomSheetModalProvider>
            </TouchableWithoutFeedback>
        );
    }

    searchKeyword = (keyword) => () => {
        const { actionSale } = this.props;
        this.setState({
            isFetching: true,
            isEmpty: false,
            description: "",
            isError: false,
        });
        actionSale.searchKeywordProduct(keyword).then(({
            isEmpty,
            description,
            data
        }) => {
            if (this.state.keyword == keyword) {
                const isUnique = (data.length == 1);
                this.setState({
                    dataSearch: data,
                    isEmpty: isEmpty,
                    description: description,
                    isFetching: false,
                    isError: false,
                })
                if (isUnique) {
                    const { productIDERP, imei } = data[0];
                    const isAuto = !!imei || (keyword == productIDERP);
                    if (isAuto) {
                        this.onSelectProduct(data[0]);
                    }
                }
            }
        }).catch(msgError => {
            if (this.isBarcode) {
                this.onSearchBarcode(keyword);
            }
            else {
                this.setState({
                    dataSearch: [],
                    description: msgError,
                    isFetching: false,
                    isEmpty: false,
                    isError: true,
                })
            }
        });
    }

    onChangeKeyword = (keyword) => {
        const isSearch = (keyword.length > 2);
        if (isSearch) {
            if (this.timeoutSearch) {
                clearTimeout(this.timeoutSearch);
            }
            this.timeoutSearch = setTimeout(this.searchKeyword(keyword), 1000);
        }
        else {
            clearTimeout(this.timeoutSearch);
            this.setState({
                dataSearch: [],
                isFetching: false,
                isEmpty: false,
                description: "",
                isError: false,
            })
        }
    }

    onSearchBarcode = (barcode) => {
        const product = {
            productIDERP: barcode,
            imageUrl: "",
            imei: "",
            inventoryStatusID: 0,
            inventoryStatusName: "",
            isImeiSim: false,
            price: 0,
            priceSpecified: null,
            productID: 0,
            productIdSpecified: null,
            productInstallationConsultancyUrl: "",
            productInstallmentUrl: "",
            productName: "",
            webStatus: 0,
            webStatusSpecified: null,
        }
        const {
            actionSale,
            navigation,
            userInfo: { storeID, userName }
        } = this.props;

        this.isBarcode = false;
        actionSale.setProductSearch(product);
        storageHelper.updateSarchHistory(barcode);
        this.setState({
            keyword: "",
            dataSearch: [],
            isFetching: false,
            isEmpty: false,
            description: "",
            isError: false,
        });
        navigation.navigate('Detail');
    }

    onSelectProduct = (product) => {
        const { keyword } = this.state;
        const {
            actionSale,
            navigation,
            userInfo: { storeID, userName }
        } = this.props;
        this.isBarcode = false;
        Keyboard.dismiss();

        actionSale.setProductSearch(product);
        storageHelper.updateSarchHistory(keyword);
        this.setState({
            keyword: "",
            dataSearch: [],
            isFetching: false,
            isEmpty: false,
            description: "",
            isError: false,
        });
        if (this.state.stateProduct?.data?.length > 0) {
            actionSale.setStateFilter({
                products: this.state.stateProduct?.data,
                filter: this.state.listFilter,
                category: this.state.categorySelected
            })
        }
        navigation.navigate('Detail');
    }

    onFocus = () => {
        this.setState({ isFocus: true });
    }

    onBlur = () => {
        this.setState({ isFocus: false });
    }

    clearKeyword = () => {
        this.setState({ keyword: "" });
    }

    onOpenBarcode = () => {
        this.setState({ isVisibleScan: true });
    }

    onCloseBarcode = () => {
        this.setState({ isVisibleScan: false });
    }

    renderItemFavorite = (product, index) => {
        const {
            imageUrl,
            productName,
            productID,
            colorName
        } = product;
        return (
            <TouchableOpacity style={{
                width: constants.width - 10,
                height: 80,
                flexDirection: "row",
                alignItems: "center",
                backgroundColor: COLORS.btnFFFFFF,
                paddingHorizontal: 10,
                borderWidth: 1,
                borderColor: COLORS.bdE4E4E4,
                borderRadius: 2
            }}
                activeOpacity={0.8}
                onPress={this.onPressItemFavorite(product)}
                onLongPress={() => {
                    const { productName } = product;
                    Alert.alert("",
                        `${translate('sale.delete')} "${productName}" ${translate('sale.favorites_list_question')}`,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: "cancel",
                            },
                            {
                                text: translate('common.btn_continue'),
                                style: "default",
                                onPress: this.deleteProductFavorite(product)
                            }
                        ]
                    )
                }}
            >
                <Image
                    style={{
                        width: 60,
                        height: 60
                    }}
                    source={{ uri: imageUrl }}
                    resizeMode={"center"}
                />

                <View style={{
                    flexWrap: "wrap",
                    height: 80,
                    width: constants.width - 100,
                    paddingHorizontal: 5,
                    justifyContent: "center",
                }}>
                    <MyText
                        text={productName}
                        style={{
                            color: COLORS.txt288AD6,
                            fontWeight: "bold",
                            width: "100%",
                            marginBottom: 2,
                        }}
                        numberOfLines={2}
                    />
                    <MyText
                        text={translate('common.code_product')}
                        style={{
                            color: COLORS.txt000000,
                        }}
                        numberOfLines={1}
                        children={<MyText
                            text={productID}
                            style={{
                                color: COLORS.txt505050,
                                marginBottom: 2,
                            }}
                        />}
                    />
                    <MyText
                        text={translate('sale.color')}
                        style={{
                            color: COLORS.txt000000,
                        }}
                        numberOfLines={1}
                        children={<MyText
                            text={colorName}
                            style={{
                                color: COLORS.txt505050,
                                marginBottom: 2
                            }}
                        />}
                    />
                </View>
            </TouchableOpacity>
        );
    }

    onPressItemFavorite = (product) => () => {
        const {
            actionSale,
            navigation,
            userInfo: { storeID, userName }
        } = this.props;

        const productSearch = {
            imageUrl: product.imageUrl,
            productID: product.productIDRef,
            productIDERP: product.productID,
            productName: product.productName,
            inventoryStatusID: product.inventoryStatusID
        };

        actionSale.setProductSearch(productSearch);
        this.setState({
            keyword: "",
            dataSearch: [],
            isFetching: false,
            isEmpty: false,
            description: "",
            isError: false,
        });
        navigation.navigate('Detail');
    }

    renderItemHistory = (item, index) => {
        return (
            <TouchableOpacity style={{
                marginVertical: 8,
                marginHorizontal: 5,
            }}
                key={index}
                activeOpacity={0.8}
                onPress={this.onPressItemHistory(item)}
            >
                <View style={{
                    paddingVertical: 4,
                    paddingHorizontal: 10,
                    borderColor: COLORS.bd288AD6,
                    borderWidth: 1,
                    borderRadius: 4,
                    justifyContent: "center",
                }}>
                    <MyText
                        text={item}
                        style={{
                            color: COLORS.txt288AD6
                        }}
                    />
                </View>
            </TouchableOpacity>
        );
    }

    onPressItemHistory = (keyword) => () => {
        this.isBarcode = false;
        this.setState({
            keyword: keyword,
            isFetching: true,
            isEmpty: false,
            description: "",
            isError: false
        });
        Keyboard.dismiss();
    }

    deleteProductFavorite = (product) => () => {
        const { actionPouch } = this.props;
        actionPouch.removeProductFavorite(product)
    }

    moveToShoppingCart = () => {
        const {
            cartRequest,
            dataCartApply,
            navigation,
            actionCart,
        } = this.props;
        const isCartEmpty = helper.IsEmptyObject(cartRequest);
        const isCartApplyEmpty = helper.IsEmptyObject(dataCartApply);
        if (isCartEmpty && isCartApplyEmpty) {
            Alert.alert("", translate('sale.no_product_in_cart'));
        }
        else {
            actionCart.setDataShoppingCart(dataCartApply);
            navigation.navigate("ShoppingCart");
        }
    }
}

const mapStateToProps = function (state) {
    return {
        dataFavorite: state.pouchFavorite.dataFavorite,
        userInfo: state.userReducer,
        cartRequest: state.shoppingCartReducer.dataShoppingCart,
        dataCartApply: state.pouchCartApply.dataCartApply,
        stateFilter: state.saleReducer.stateFilter
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionSale: bindActionCreators(actionSaleCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch),
        actionDetail: bindActionCreators(actionDetailCreator, dispatch),
        actionCart: bindActionCreators(actionCartCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(Sale);

const EmptyMessage = ({ description }) => {
    return (
        <View style={{
            width: constants.width,
            padding: 10
        }}>
            <MyText
                text={description}
                style={{
                    color: COLORS.txtFF0000,
                    fontStyle: 'italic'
                }}
            />
        </View>
    );
}

const InputSearch = ({
    value,
    onChangeText,
    onFocus,
    onBlur,
    clearText,
    openBarcode,
    isClose,
    storeID
}) => {
    const inputRef = useRef(null);
    let timeFocus = null;
    const onAutoFocus = () => {
        if (inputRef) {
            inputRef.current?.focus()
        }
    }
    const onClearTimer = () => {
        if (timeFocus) {
            clearTimeout(timeFocus)
        }
    }
    const didMount = () => {
        timeFocus = setTimeout(onAutoFocus, 50);
        return onClearTimer
    }

    useEffect(didMount, [])

    return (
        <View style={{
            flexDirection: "row",
            // width: storeID == 907 ? constants.width - 60 : constants.width - 20,
            marginBottom: 8,
            borderWidth: 1,
            borderColor: COLORS.bdE4E4E4,
            height: 40,
            backgroundColor: COLORS.bgFFFFFF,
            borderRadius: 20,
            justifyContent: "space-between",
            alignItems: "center",
            alignSelf: "center",
            marginTop: 10
        }}>
            {
                <TextInput
                    ref={inputRef}
                    onFocus={onFocus}
                    onBlur={onBlur}
                    autoFocus={true}
                    value={value}
                    onChangeText={onChangeText}
                    placeholder={translate('sale.place_holder_search_product')}
                    placeholderTextColor={"gray"}
                    onSubmitEditing={Keyboard.dismiss}
                    style={{
                        height: 40,
                        // width: storeID == 907 ? constants.width - 108 : constants.width - 68,
                        paddingLeft: 20,
                        flex: 1
                    }}
                    maxLength={150}
                />
            }
            {
                isClose
                    ? <TouchableOpacity style={{
                        height: 40,
                        width: 48,
                        justifyContent: "center",
                        alignItems: "flex-end",
                        paddingRight: 20,
                    }}
                        onPress={clearText}
                    >
                        <Icon
                            iconSet={"Ionicons"}
                            name={"close"}
                            color={COLORS.ic848A8C}
                            size={22}
                        />
                    </TouchableOpacity>
                    : <TouchableOpacity style={{
                        height: 40,
                        width: 48,
                        justifyContent: "center",
                        alignItems: "flex-end",
                        paddingRight: 20,
                    }}
                        onPress={openBarcode}
                    >
                        <Icon
                            iconSet={"MaterialCommunityIcons"}
                            name={"barcode-scan"}
                            color={COLORS.icFFD400}
                            size={22}
                        />
                    </TouchableOpacity>
            }
        </View>
    );
}


function enrichData(productList = [], manufacturerList = []) {
    const newProductList = productList?.filter((item) => item?.ProductPropValueBOLst?.length > 0)
    const transformedManufacturers = {
        IsManu: true,
        PropertyID: 999999,
        GroupID: 99999,
        GroupName: "Danh sách hãng",
        Description: `Hãng sản xuất:`,
        PropertyName: "Hãng sản xuất",
        PropertyType: 2,
        IsActived: true,
        CreatedUser: "system",
        UpdatedUser: "system",
        IsShowSpecs: 1,
        andFilter: false,
        ProductPropValueBOLst: manufacturerList?.filter((item) => item?.SmallLogo?.includes("https://"))?.map(manufacturer => ({
            ValueID: manufacturer.ManufacturerID,
            PropertyID: manufacturer.ManufacturerID,
            Value: manufacturer.ManufacturerName,
            DisplayOrder: manufacturer.DisplayOrder || 0,
            IsActived: true,
            CreatedDate: new Date().toISOString(),
            CreatedUser: "system",
            IconScale: 100,
            SmallLogo: manufacturer.SmallLogo || null,
            BigLogo: manufacturer.BigLogo || null
        }))
    }

    const priceRangeGroup = {
        PropertyID: 'price-range',
        PropertyName: 'Giá',
        isPriceRange: true,
        ProductPropValueBOLst: [
            { ValueID: 'price-0-2', Value: 'Dưới 2 triệu', selected: false, min: 0, max: 2000000 },
            { ValueID: 'price-2-4', Value: 'Từ 2 - 4 triệu', selected: false, min: 2000000, max: 4000000 },
            { ValueID: 'price-4-7', Value: 'Từ 4 - 7 triệu', selected: false, min: 4000000, max: 7000000 },
            { ValueID: 'price-7-13', Value: 'Từ 7 - 13 triệu', selected: false, min: 7000000, max: 13000000 },
            { ValueID: 'price-13-20', Value: 'Từ 13 - 20 triệu', selected: false, min: 13000000, max: 20000000 },
            { ValueID: 'price-20', Value: 'Trên 20 triệu', selected: false, min: 20000000, max: 100000000 }
        ]
    };
    return [priceRangeGroup, transformedManufacturers, ...newProductList];
}



