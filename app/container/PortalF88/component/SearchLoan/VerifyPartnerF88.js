import React, { Component } from 'react'
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert
} from "react-native";
import { MyText, CaptureCamera, Icon, showBlockUI, hideBlock<PERSON> } from "@components";
import { launchImageLibrary } from 'react-native-image-picker';
import { helper, dateHelper } from "@common";
import { COLORS } from "@styles";
import { API_CONST } from '@constants';
import { ButtonAction } from '../ButtonAction';
import ImageProcess from '../ImageProcess';
import SignImage from '../SignImage';
import { translate } from '@translate';
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionF88Creator from "../../action";
import { getImageCDN } from '../../../ShoppingCart/action';

export class VerifyPartnerF88 extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isVisibleCamera: false,
      isVisibleSign: false,
      currentPicture: '',
      introLetter: '',
      agentCard: '',
      frontIDCard: '',
      backIDCard: '',
      contract: [''],
      portrait: '',
      currentContractIndex: 0,
      signature: '',
      isSuccess: false
    }
  }

  takePicture = (photo) => {
    const { currentPicture, currentContractIndex } = this.state;
    this.setState({ isVisibleCamera: false });
    showBlockUI();
    if (helper.hasProperty(photo, 'uri')) {
      helper.resizeImage(photo).then(({ path, uri, size, name }) => {
        let bodyFromData = new FormData();
        bodyFromData.append('file', {
          uri: uri,
          type: 'image/jpg',
          name: name
        });
        getImageCDN(bodyFromData)
          .then((response) => {
            const remoteURI = API_CONST.API_GET_IMAGE_CDN + response[0];
            switch (currentPicture) {
              case 'introLetter':
                this.setState({ introLetter: remoteURI });
                break;
              case 'agentCard':
                this.setState({ agentCard: remoteURI });
                break;
              case 'frontIDCard':
                this.setState({ frontIDCard: remoteURI });
                break;
              case 'backIDCard':
                this.setState({ backIDCard: remoteURI });
                break;
              case 'portrait':
                this.setState({ portrait: remoteURI });
                break;
              case 'contract':
                const newContract = [...this.state.contract];
                newContract[currentContractIndex] = remoteURI;
                this.setState({ contract: newContract });
                break;
              default:
                break;
            };
            hideBlockUI();
          }).catch((error) => {
            hideBlockUI();
            console.log('uploadPicture', error);
          })

      }).catch((error) => {
        hideBlockUI();
        console.log("resizeImage", error);
      });
    } else { hideBlockUI(); }
  }

  selectPicture = () => {
    const { currentPicture, currentContractIndex } = this.state;
    launchImageLibrary(
      {
        mediaType: 'photo',
        noData: true
      },
      (response) => {
        this.setState({ isVisibleCamera: false });
        showBlockUI();
        if (helper.hasProperty(response, 'uri')) {
          helper.resizeImage(response)
            .then(({ path, uri, size, name }) => {
              let bodyFromData = new FormData();
              bodyFromData.append('file', {
                uri: uri,
                type: 'image/jpg',
                name: name
              });
              getImageCDN(bodyFromData)
                .then((response) => {
                  const remoteURI = API_CONST.API_GET_IMAGE_CDN + response[0];
                  switch (currentPicture) {
                    case 'introLetter':
                      this.setState({ introLetter: remoteURI });
                      break;
                    case 'agentCard':
                      this.setState({ agentCard: remoteURI });
                      break;
                    case 'frontIDCard':
                      this.setState({ frontIDCard: remoteURI });
                      break;
                    case 'backIDCard':
                      this.setState({ backIDCard: remoteURI });
                      break;
                    case 'portrait':
                      this.setState({ portrait: remoteURI });
                      break;
                    case 'contract':
                      const newContract = [...this.state.contract];
                      newContract[currentContractIndex] = remoteURI;
                      this.setState({ contract: newContract });
                      break;
                    default:
                      break;
                  }
                  hideBlockUI();
                }).catch((error) => {
                  hideBlockUI();
                  console.log('uploadPicture', error);
                })
            })
            .catch((error) => {
              hideBlockUI();
              console.log('resizeImage', error);
            });
        } else { hideBlockUI(); }
      }
    );
  };

  takeSignature = (signature) => {
    this.setState({ isVisibleSign: false });
    showBlockUI();
    let bodyFromData = new FormData();
    bodyFromData.append('base64', signature);
    getImageCDN(bodyFromData)
      .then((response) => {
        const remoteSignatureURI = API_CONST.API_GET_IMAGE_CDN + response[0];
        this.setState({ signature: remoteSignatureURI });
        hideBlockUI();
      }).catch((error) => {
        hideBlockUI();
        console.log('uploadPicture', error);
      })
  };

  verifyPartner = () => {
    const { handoverRequestID, isRequiredPortrait } = this.props.route.params;
    const { introLetter, agentCard, frontIDCard, backIDCard, contract, signature, portrait } = this.state;
    if (helper.IsEmptyString(introLetter) && !isRequiredPortrait) {
      Alert.alert("", translate('f88.please_capture_introduction_letter'));
    } else if (helper.IsEmptyString(agentCard) && !isRequiredPortrait) {
      Alert.alert("", translate('f88.please_capture_agent_card'));
    } else if (helper.IsEmptyString(portrait) && isRequiredPortrait) {
      Alert.alert("", translate('f88.please_capture_customer_portrait'));
    } else if (helper.IsEmptyString(frontIDCard)) {
      Alert.alert("", translate('f88.please_capture_agent_front_id_card'));
    } else if (helper.IsEmptyString(backIDCard)) {
      Alert.alert("", translate('f88.please_capture_agent_back_id_card'));
    } else if (!contract.some(el => el !== '')) {
      Alert.alert("", translate('f88.please_capture_contract_with_signature'));
    } else if (helper.IsEmptyString(signature)) {
      Alert.alert("", translate('f88.please_take_partner_signature'));
    } else {
      //please_capture_customer_portrait
      showBlockUI();
      const attachBOList = { introLetter, agentCard, frontIDCard, backIDCard, contract, signature, portrait }
      const info = isRequiredPortrait ? [
        {
          "ShortName": "CD",
          "IsDeleted": 0,
          "FilePath": portrait,
          "UrlFile": portrait
        }
      ] : [
        {
          "ShortName": "GGT",
          "IsDeleted": 0,
          "FilePath": introLetter,
          "UrlFile": introLetter
        },
        {
          "ShortName": "F88CARD",
          "IsDeleted": 0,
          "FilePath": agentCard,
          "UrlFile": agentCard
        }
      ]
      this.props.actionF88.updateLoanHandoverRequest(handoverRequestID, attachBOList, info).then((response) => {
        hideBlockUI();
        this.setState({
          isSuccess: true
        })
        Alert.alert("", translate('f88.update_handover_request_success'), [
          {
            text: "OK",
            style: "default",
            onPress: () => {
              const { initialScreen } = this.props.route.params;
              if (this.state.isSuccess) {
                this.props.navigation.reset({
                  index: 0,
                  routes: [{ name: initialScreen }],
                });
              }
            }
          }
        ]);
      }).catch((msgError) => {
        hideBlockUI();
        Alert.alert(translate('common.notification_uppercase'), msgError, [
          {
            text: "OK",
            style: "default",
            onPress: hideBlockUI
          }
        ]);
      });
    }
  }

  render() {
    const { isRequiredPortrait } = this.props.route.params;
    return (
      <SafeAreaView
        style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>

        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          {/* <Title title={"Thông tin nhân viên lấy chứng từ "} /> */}
          {
            isRequiredPortrait ? // LOANHANDOVERREQUESTTYPE == 2
              <View>
                <MyText
                  text={"Chân dung khách hàng"}
                  addSize={1}
                  style={{ fontWeight: 'bold', marginTop: 10, marginHorizontal: 10 }}
                />
                <View
                  style={{
                    borderWidth: StyleSheet.hairlineWidth,
                    margin: 15,
                    paddingBottom: 15,
                    paddingHorizontal: 10,
                    borderRadius: 5
                  }}>
                  {/* <MyText
                text={"Chân dung khách hàng"}
                style={{
                  color: COLORS.txt333333,
                  fontStyle: "italic",
                  marginTop: 15
                }}
              >
                <MyText
                  text={"*"}
                  style={{
                    color: COLORS.txtFF0000
                  }}
                />
              </MyText> */}
                  <ImageProcess
                    onCamera={() => {
                      this.setState({ isVisibleCamera: true, currentPicture: 'portrait' })
                    }}
                    urlImageLocal={this.state.portrait}
                    urlImageRemote={this.state.portrait}
                    deleteImage={() => { this.setState({ portrait: '' }) }}
                  />
                </View>
              </View> :
              <View>
                <MyText
                  text={translate('f88.capture_introduction_letter')}
                  addSize={1}
                  style={{ fontWeight: 'bold', marginTop: 10, marginHorizontal: 10 }}
                />
                <View
                  style={{
                    borderWidth: StyleSheet.hairlineWidth,
                    margin: 15,
                    paddingBottom: 15,
                    paddingHorizontal: 10,
                    borderRadius: 5
                  }}>
                  <MyText
                    text={translate('f88.introduction_letter')}
                    style={{
                      color: COLORS.txt333333,
                      fontStyle: "italic",
                      marginTop: 15
                    }}
                  >
                    <MyText
                      text={"*"}
                      style={{
                        color: COLORS.txtFF0000
                      }}
                    />
                  </MyText>
                  <ImageProcess
                    onCamera={() => {
                      this.setState({ isVisibleCamera: true, currentPicture: 'introLetter' })
                    }}
                    urlImageLocal={this.state.introLetter}
                    urlImageRemote={this.state.introLetter}
                    deleteImage={() => { this.setState({ introLetter: '' }) }}
                  />

                  <MyText
                    text={translate('f88.agent_card')}
                    style={{
                      color: COLORS.txt333333,
                      fontStyle: "italic",
                      marginTop: 15
                    }}
                  >
                    <MyText
                      text={"*"}
                      style={{
                        color: COLORS.txtFF0000
                      }}
                    />
                  </MyText>
                  <ImageProcess
                    onCamera={() => {
                      this.setState({ isVisibleCamera: true, currentPicture: 'agentCard' })
                    }}
                    urlImageLocal={this.state.agentCard}
                    urlImageRemote={this.state.agentCard}
                    deleteImage={() => { this.setState({ agentCard: '' }) }}
                  />
                </View>
              </View>
          }

          <MyText
            text={translate('f88.capture_agent_id_card')}
            addSize={1}
            style={{ fontWeight: 'bold', marginTop: 10, marginHorizontal: 10 }}
          />
          <View
            style={{
              borderWidth: StyleSheet.hairlineWidth,
              margin: 15,
              paddingBottom: 15,
              paddingHorizontal: 10,
              borderRadius: 5
            }}>
            <MyText
              text={translate('f88.agent_front_id_card')}
              style={{
                color: COLORS.txt333333,
                fontStyle: "italic",
                marginTop: 15
              }}
            >
              <MyText
                text={"*"}
                style={{
                  color: COLORS.txtFF0000
                }}
              />
            </MyText>
            <ImageProcess
              onCamera={() => {
                this.setState({ isVisibleCamera: true, currentPicture: 'frontIDCard' })
              }}
              urlImageLocal={this.state.frontIDCard}
              urlImageRemote={this.state.frontIDCard}
              deleteImage={() => { this.setState({ frontIDCard: '' }) }}
            />

            <MyText
              text={translate('f88.agent_back_id_card')}
              style={{
                color: COLORS.txt333333,
                fontStyle: "italic",
                marginTop: 15
              }}
            >
              <MyText
                text={"*"}
                style={{
                  color: COLORS.txtFF0000
                }}
              />
            </MyText>
            <ImageProcess
              onCamera={() => {
                this.setState({ isVisibleCamera: true, currentPicture: 'backIDCard' })
              }}
              urlImageLocal={this.state.backIDCard}
              urlImageRemote={this.state.backIDCard}
              deleteImage={() => { this.setState({ backIDCard: '' }) }}
            />
          </View>

          <MyText
            text={translate('f88.capture_contract_with_signature')}
            addSize={1}
            style={{ fontWeight: 'bold', marginTop: 10, marginHorizontal: 10 }}
          />
          <View
            style={{
              borderWidth: StyleSheet.hairlineWidth,
              margin: 15,
              paddingBottom: 15,
              paddingHorizontal: 10,
              borderRadius: 5
            }}>
            <MyText
              text={translate('f88.contract_with_signature')}
              style={{
                color: COLORS.txt333333,
                fontStyle: "italic",
                marginTop: 15
              }}
            >
              <MyText
                text={"*"}
                style={{
                  color: COLORS.txtFF0000
                }}
              />
            </MyText>

            {this.state.contract.map((contract, index) => (
              <ImageProcess
                onCamera={() => {
                  this.setState({
                    isVisibleCamera: true,
                    currentPicture: 'contract',
                    currentContractIndex: index
                  })
                }}
                urlImageLocal={contract}
                urlImageRemote={contract}
                deleteImage={() => {
                  this.state.contract.length > 1 ?
                    this.setState({ contract: this.state.contract.filter(ct => ct != contract) })
                    : this.setState({ contract: [''] })
                }}
                key={index.toString()}
              />
            ))}

            <TouchableOpacity style={{
              flex: 1,
              height: 150,
              width: 150,
              justifyContent: "center",
              alignItems: "center",
              alignSelf: "center",
              marginTop: 15,
              backgroundColor: COLORS.btnF5F5F5,
              marginHorizontal: 2
            }}
              onPress={() => { this.setState({ contract: [...this.state.contract, ''] }) }}
              activeOpacity={0.6}
            >
              <View style={{ justifyContent: "center", alignItems: "center" }}>
                <Icon
                  iconSet={"Ionicons"}
                  name={"add-circle-outline"}
                  color={COLORS.icFFB23F}
                  size={60}
                />
              </View>
            </TouchableOpacity>
          </View>

          <MyText
            text={translate('f88.take_partner_signature')}
            addSize={1}
            style={{ fontWeight: 'bold', marginTop: 10, marginHorizontal: 10 }}
          />
          <View
            style={{
              borderWidth: StyleSheet.hairlineWidth,
              margin: 15,
              paddingBottom: 15,
              paddingHorizontal: 10,
              borderRadius: 5
            }}>
            <MyText
              text={translate('f88.partner_signature')}
              style={{
                color: COLORS.txt333333,
                fontStyle: "italic",
                marginTop: 15
              }}
            >
              <MyText
                text={"*"}
                style={{
                  color: COLORS.txtFF0000
                }}
              />
            </MyText>
            <ImageProcess
              onCamera={() => {
                this.setState({
                  isVisibleSign: true
                });
              }}
              urlImageLocal={this.state.signature}
              urlImageRemote={this.state.signature}
              deleteImage={() => { this.setState({ signature: '' }) }}
              isSignature={true}
            />
          </View>
          <ButtonAction
            title={translate('f88.btn_done')}
            onPress={this.verifyPartner}
            disabled={false}
          />
        </ScrollView>

        <CaptureCamera
          isVisibleCamera={this.state.isVisibleCamera}
          takePicture={this.takePicture}
          closeCamera={() => {
            this.setState({ isVisibleCamera: false })
          }}
          selectPicture={this.selectPicture}
        />

        <SignImage
          isVisibleSign={this.state.isVisibleSign}
          takeSignature={this.takeSignature}
          closeSignature={() => { this.setState({ isVisibleSign: false }) }}
        />

        {/* <Popup
          isShowPopup={this.state.isShowPopup}
          message={this.state.message}
          hidePopup={() => {
            const { initialScreen } = this.props.route.params;
            if (this.state.isSuccess) {
              this.setState({ isShowPopup: false });
              this.props.navigation.reset({
                index: 0,
                routes: [{ name: initialScreen }],
              });
            }
            else this.setState({ isShowPopup: false })
          }}
        /> */}

      </SafeAreaView >
    )
  }
}

const mapStateToProps = (state) => ({
});

const mapDispatchToProps = (dispatch) => ({
  actionF88: bindActionCreators(actionF88Creator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(VerifyPartnerF88);