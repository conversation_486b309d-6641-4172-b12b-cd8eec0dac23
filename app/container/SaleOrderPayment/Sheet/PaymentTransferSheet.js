import { StyleSheet, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { BottomSheet, Icon, MyText } from '@components';
import { COLORS } from '@styles';
import PaymentTransferInner from '../component/PaymentTransferInner';
import { TouchableWithoutFeedback } from '@gorhom/bottom-sheet';
import { ImageURI } from '../../../components';
import { ScrollView } from 'react-native-gesture-handler';
import { constants } from '@constants';

const PaymentTransferSheet = React.forwardRef(({
    onChangeStatusSheet,
    handleIntervalPayment,
    paymentTransferSheetRef,
    bankList,
    bankSelected,
    saleOrderID,
    onChangeBank,
    statusTransferPaymentSuccess
}) => {
    const handleComponent = () => {
        return (
            <View style={styles.handle_wrapper}>
                <View style={{ flex: 1 }} />
                <View
                    style={{
                        flex: 6,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <MyText
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.txt000000
                        }}
                        text="Thông tin chuyển khoản"
                    />
                </View>
                <View style={{ flex: 1 }}>
                    <TouchableWithoutFeedback
                        style={{ marginLeft: 10 }}
                        onPress={() => {
                            paymentTransferSheetRef.current?.dismiss();
                        }}>
                        <Icon
                            iconSet={'MaterialIcons'}
                            name={'clear'}
                            color={COLORS.txt000000}
                            size={22}
                        />
                    </TouchableWithoutFeedback>
                </View>
            </View>
        );
    };

    const footerComponent = () => {
        if (statusTransferPaymentSuccess.type != 'INIT')
            return (
                <View style={styles.footer_wrapper}>
                    <TouchableOpacity
                        style={{
                            backgroundColor: COLORS.bg64B74F,
                            width: constants.width / 2,
                            alignItems: 'center',
                            height: 40,
                            justifyContent: 'center',
                            borderRadius: 15
                        }}
                        onPress={() =>
                            paymentTransferSheetRef.current?.dismiss()
                        }>
                        <MyText
                            style={{
                                color: COLORS.bgFFFFFF,
                                fontWeight: 'bold'
                            }}
                            text={'OK'}
                        />
                    </TouchableOpacity>
                </View>
            );
        return (
            <View style={styles.footer_wrapper}>
                <ScrollView horizontal contentContainerStyle={{}}>
                    {bankList.map((_item) => {
                        const isCheck = _item.BankId == bankSelected.BankId;
                        return (
                            <View
                                View
                                style={[
                                    styles.imageContainer,
                                    {
                                        borderColor: isCheck
                                            ? COLORS.bg64B74F
                                            : COLORS.bgC4C4C4,
                                        borderWidth: isCheck ? 1 : 0.2
                                    }
                                ]}>
                                <TouchableOpacity
                                    onPress={() => onChangeBank(_item)}>
                                    <ImageURI
                                        style={styles.image}
                                        uri={_item.IconFilePath}
                                        resizeMode="center"
                                    />
                                </TouchableOpacity>
                            </View>
                        );
                    })}
                </ScrollView>
            </View>
        );
    };
    return (
        <View>
            <BottomSheet
                snapPoints={['99.99%']}
                bs={paymentTransferSheetRef}
                handleComponent={handleComponent}
                footerComponent={footerComponent}
                enableContentPanningGesture={false}
                enableHandlePanningGesture={false}
                onChangeStatusSheet={onChangeStatusSheet}>
                <PaymentTransferInner
                    bankList={bankList}
                    bankSelected={bankSelected}
                    saleOrderID={saleOrderID}
                    handleIntervalPayment={handleIntervalPayment}
                    statusTransferPaymentSuccess={statusTransferPaymentSuccess}
                />
            </BottomSheet>
        </View>
    );
});

export default PaymentTransferSheet;

const styles = StyleSheet.create({
    handle_wrapper: {
        backgroundColor: COLORS.bgFFFFFF,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 50,
        elevation: 2,
        borderTopStartRadius: 22,
        borderTopEndRadius: 22,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4
    },
    footer_wrapper: {
        backgroundColor: COLORS.bgFFFFFF,
        // flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 100,
        elevation: 2,
        borderTopWidth: StyleSheet.hairlineWidth,
        borderTopColor: COLORS.bgC4C4C4,
        paddingHorizontal: 10
    },
    image: {
        height: 50,
        width: 150
    },
    imageContainer: {
        alignSelf: 'center',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 10,
        marginRight: 10,
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderColor: COLORS.bgC4C4C4,
        borderWidth: 0.2,
        marginBottom: 20
    }
});
