import React, { Component } from 'react';
import {
    View,
    ScrollView,
    Alert,
} from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import SafeAreaView from "react-native-safe-area-view";
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
    Button,
    MyText,
    showBlockUI,
    hideBlockUI,
    showPopup,
    ImageURI
} from "@components";
import { constants } from '@constants';
import { helper, dateHelper } from "@common";
import * as actionSaleOrderCreator from "./action";
import { translate } from '@translate';
import { COLORS } from "@styles";
import Discription from "./component/Discription";
const { PAYMENT_PARTNER_ID, PARTNER_ID } = constants



class QRPayment extends Component {

    constructor(props) {
        super(props);
        this.state = {
            description: translate('saleOrderPayment.please_scan_qr_code_to_pay'),
            PaymentStatusID: ""
        };
        this.queryJob = null;
        this.transaction = {};
    }

    cancleJob = () => {
        if (this.queryJob) {
            clearInterval(this.queryJob);
        }
    }

    componentDidMount() {
        this.getInitStatus();
        const { qrInfo } = this.props;
        const { transactionType: { PaymentTransactionTypeID } } = qrInfo;
        const intervalTime = PaymentTransactionTypeID == PAYMENT_PARTNER_ID.KREDIVO ? 30000 : 5000;
        this.queryJob = setInterval(this.onQueryStatusQR, intervalTime);
    }

    getInitStatus = () => {
        const { qrInfo } = this.props;
        const { transactionType: { PaymentTransactionTypeID } } = qrInfo
        const { status, description } = getDefaultStatus(PaymentTransactionTypeID);
        this.setState({ PaymentStatusID: status, description: description });
    }

    componentWillUnmount() {
        this.cancleJob();
    }

    onQueryStatusQR = () => {
        const { qrInfo, actionSaleOrder } = this.props;
        const { transactionType: { PaymentTransactionTypeID } } = qrInfo;
        actionSaleOrder.queryStatusQR(qrInfo).then(transaction => {
            this.transaction = transaction;
            const status = getStatusType(PaymentTransactionTypeID, transaction.PaymentStatusID);
            switch (true) {
                case status.SUCCESS:
                    this.cancleJob();
                    this.setState({
                        description: translate('saleOrderPayment.transact_successfully'),
                        PaymentStatusID: transaction.PaymentStatusID
                    });
                    showPopup(translate('saleOrderPayment.transact_successfully'), translate('saleOrderPayment.system_auto_create'),
                        [{
                            text: "OK",
                            onPress: this.onSuccess(transaction)
                        }],
                        'success');
                    break;
                case status.ERROR:
                    this.cancleJob();
                    this.setState({
                        description: translate('saleOrderPayment.transact_fail'),
                        PaymentStatusID: transaction.PaymentStatusID
                    });
                    showPopup(translate('saleOrderPayment.transact_fail'), "Vui lòng tạo lại giao dịch mới để thực hiện thanh toán",
                        [
                            {
                                text: translate('saleOrderPayment.btn_skip_uppercase'),
                                onPress: this.onError(transaction),
                                style: "cancel"
                            },
                            {
                                text: translate('saleOrderPayment.btn_create_again'),
                                onPress: this.onPressCreate(transaction)
                            }
                        ],
                        'error');
                    break;
                case status.SCANNED:
                    this.setState({
                        description: translate('saleOrderPayment.description'),
                        PaymentStatusID: transaction.PaymentStatusID
                    });
                    break;
                case status.PROCESSING:
                    this.setState({
                        description: translate('saleOrderPayment.transact_pending'),
                        PaymentStatusID: transaction.PaymentStatusID
                    });
                    break;
                default:
                    this.setState({
                        description: translate('saleOrderPayment.please_scan_qr_code_to_pay'),
                        PaymentStatusID: transaction.PaymentStatusID
                    });
                    break;
            }
        }).catch(({ msgError, errorType }) => {
            if (errorType == "1910") {
                this.cancleJob();
                Alert.alert(translate('common.notification_uppercase'), msgError, [
                    {
                        text: "Ok",
                        onPress: () => {
                            this.props.navigation.pop(2)
                        },
                    }
                ]);
            }

        })
    }

    render() {
        const { description, PaymentStatusID } = this.state;
        const {
            dataSaleOrder: { SaleOrderID, CustomerPhone },
            qrInfo,
        } = this.props;
        const {
            amount,
            qrValue,
            transactionID,
            transactionType,
            expried
        } = qrInfo
        const { PaymentTransactionTypeID } = transactionType;
        const isHasLogo = helper.hasProperty(transactionType, "ImageURL");
        const status = getStatusView(PaymentTransactionTypeID, PaymentStatusID);
        const titleValid = PaymentTransactionTypeID == PAYMENT_PARTNER_ID.KREDIVO ? "Giao dịch có hiệu lực: " : translate('saleOrderPayment.QR_code_valid_until')
        const titleButtonDelete = PaymentTransactionTypeID == PAYMENT_PARTNER_ID.KREDIVO ? "HUỶ GIAO DỊCH" : translate('saleOrderPayment.btn_cancel_QR_code_uppercase')
        return (
            <View style={{
                flex: 1
            }}>
                <Header title={`${translate('saleOrderPayment.pay_for_output_demand')}\n${SaleOrderID}`} />
                <ScrollView contentContainerStyle={{
                    flexGrow: 1,
                    paddingVertical: 10
                }}>
                    <SafeAreaView style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFAFAFA,
                        justifyContent: "center",
                        alignItems: "center"
                    }}>
                        {
                            isHasLogo &&
                            <ImageURI
                                uri={transactionType.ImageURL}
                                style={{
                                    width: 80,
                                    height: 80,
                                }}
                            />
                        }
                        {
                            (status.EXPIRED && helper.IsNonEmptyString(expried)) &&
                            <MyText
                                text={titleValid}
                                addSize={1}
                                style={{
                                    color: COLORS.txt333333,
                                    textAlign: 'center',
                                    marginTop: 8,
                                    width: constants.width - 40
                                }}>
                                <MyText
                                    text={dateHelper.formatStrDateHHMM(expried)}
                                    addSize={2}
                                    style={{
                                        color: COLORS.txt333333,
                                        textAlign: 'center',
                                        marginTop: 8,
                                        fontWeight: 'bold',
                                        fontStyle: 'normal'
                                    }} />
                            </MyText>
                        }
                        <MyText
                            text={description}
                            addSize={1}
                            style={{
                                color: COLORS.txt147EFB,
                                marginBottom: 20,
                                textAlign: 'center',
                                marginTop: 8,
                                width: constants.width - 40,
                                fontStyle: 'italic'
                            }} />
                        {PaymentTransactionTypeID == PAYMENT_PARTNER_ID.KREDIVO ?
                            <MyText addSize={1} style={{ marginHorizontal: 10 }} text={`Yêu cầu thanh toán qua ví Kredivo đã được gửi đến Kredivo:\n- Mã giao dịch: ${transactionID}\n- Tài khoản ví Kredivo: ${CustomerPhone}\n- Số tiền thanh toán: ${helper.convertNum(amount)}\nKhách hàng mở App Kredivo tìm giao dịch và thực hiện thanh toán.`} />
                            :
                            <QRCode
                                size={180}
                                value={qrValue}
                            />}
                        <Amount
                            value={helper.convertNum(amount)}
                            isPending={status.BACK}
                        />
                        <View style={{
                            width: constants.width,
                            alignItems: "center",
                            justifyContent: "center",
                            flexDirection: "row",
                            paddingVertical: 10
                        }}>
                            {
                                status.BACK &&
                                <Button
                                    text={translate('saleOrderPayment.btn_go_back_uppercase')}
                                    styleContainer={{
                                        borderRadius: 4,
                                        backgroundColor: COLORS.btn3A9691,
                                        height: 40,
                                        width: 140,
                                        marginLeft: 10,
                                        borderWidth: 1,
                                        borderColor: COLORS.bd3A9691,
                                    }}
                                    styleText={{
                                        color: COLORS.txtF4F7B9,
                                        fontSize: 12.5,
                                        fontWeight: "bold"
                                    }}
                                    onPress={this.onPending(transactionID, qrInfo, SaleOrderID)}
                                />
                            }
                            {
                                status.DELETE &&
                                <Button
                                    text={titleButtonDelete}
                                    styleContainer={{
                                        borderRadius: 4,
                                        height: 40,
                                        width: 140,
                                        borderWidth: 1,
                                        borderColor: COLORS.bd8A7967,
                                        backgroundColor: COLORS.btn8A7967,
                                        marginLeft: 10
                                    }}
                                    styleText={{
                                        color: COLORS.txtFFFFFF,
                                        fontSize: 12.5,
                                        fontWeight: "bold"
                                    }}
                                    onPress={this.onPressCancel(transactionID)}
                                />
                            }
                        </View>
                        <Discription
                            title={"Chỉ được thanh toán 1 lần bằng ví điện từ trên 1 đơn hàng"}
                        />
                        <Guide
                            appName={transactionType.CustomerDisplayName}
                            checkShowSMPGuide={transactionType.PaymentTransactionTypeID}
                            paymentTransactionTypeID={PaymentTransactionTypeID}
                        />
                    </SafeAreaView>
                </ScrollView>
            </View>
        );
    }

    modifySaleOrder = (data) => {
        const {
            route,
            navigation,
            actionSaleOrder,
        } = this.props;
        const {
            isOutput,
            isPayCash,
        } = route.params;
        showBlockUI();
        actionSaleOrder.modifySaleOrderPayment({
            saleOrder: data,
            isOutput,
            isPayCash
        }).then(success => {
            hideBlockUI();
            navigation.goBack();
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError, [
                {
                    text: translate('common.btn_skip'),
                    style: "default",
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: "default",
                    onPress: () => { this.modifySaleOrder(data) }
                }
            ]);
        });
    }

    applyTransaction = (transaction, dataTransaction) => {
        const { dataSaleOrder, actionSaleOrder } = this.props;
        const { KREDIVO, CAKE } = PARTNER_ID
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.PaymentTransactions = [transaction];
        const isNewFollowPartner = helper.isNewFollowPartner(dataModifySaleOrder?.SaleOrderSaleProgramInfo?.PartnerInstallmentID);
        if (isNewFollowPartner) {
            const partnerCustomerName = JSON.parse(transaction.ResponseMessage)?.installmentData?.userName
            dataModifySaleOrder.SaleOrderSaleProgramInfo.ContractID = dataModifySaleOrder?.SaleOrderSaleProgramInfo?.PartnerInstallmentID == PARTNER_ID.MOMO ? transaction.PartnerTransactionID : transaction.PaymentTransactionID;
            if (`${KREDIVO},${CAKE}`.includes(String(dataModifySaleOrder?.SaleOrderSaleProgramInfo?.PartnerInstallmentID))) {
                dataModifySaleOrder.SaleOrderSaleProgramInfo.TermLoan = -1
            }
            if (!!partnerCustomerName) {
                dataModifySaleOrder.CustomerInfo.CustomerName = partnerCustomerName;
                dataModifySaleOrder.CustomerName = partnerCustomerName;
            }
        }
        actionSaleOrder.updateDataTransaction(dataTransaction);
        this.modifySaleOrder(dataModifySaleOrder);
    }

    onSuccess = (transaction) => () => {
        const { dataQRTransaction: {
            dataTransaction
        } } = this.props;
        const newDataTransaction = dataTransaction.filter(ele => ele.PaymentTransactionID != transaction.PaymentTransactionID);
        this.applyTransaction(transaction, newDataTransaction);
    }

    onError = (transaction) => () => {
        const {
            dataQRTransaction: {
                dataTransaction
            },
            actionSaleOrder,
            navigation
        } = this.props;
        const newDataTransaction = dataTransaction.filter(ele => ele.PaymentTransactionID != transaction.PaymentTransactionID);
        actionSaleOrder.updateDataTransaction(newDataTransaction);
        navigation.goBack();
    }

    onPressCancel = (transactionID) => () => {
        Alert.alert("", translate('saleOrderPayment.you_want_delete_QR_code'), [
            {
                text: translate('saleOrderPayment.btn_skip_uppercase'),
            },
            {
                text: translate('saleOrderPayment.btn_continue'),
                onPress: this.onCancel(transactionID)
            }
        ]);
    }

    onCancel = (transactionID) => () => {
        const {
            dataQRTransaction: {
                dataTransaction
            },
            qrInfo,
            actionSaleOrder,
            navigation
        } = this.props;
        const { KREDIVO, CAKE } = PAYMENT_PARTNER_ID
        if (`${KREDIVO},${CAKE}`.includes(String(qrInfo.transactionType?.PaymentTransactionTypeID))) {
            this.cancleJob();
        }
        showBlockUI();
        actionSaleOrder.deleteQRTransaction({
            transactionID,
            imei: null,
            partner: 'mwg'
        }).then(success => {
            hideBlockUI();
            const newDataTransaction = dataTransaction.filter(ele => ele.PaymentTransactionID != transactionID);
            actionSaleOrder.updateDataTransaction(newDataTransaction);
            navigation.goBack();
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI
                },
            ]);
        })
    }

    onPressCreate = (transaction) => () => {
        const {
            dataQRTransaction: {
                dataTransaction,
            },
            actionSaleOrder,
            qrInfo: { transactionType },
            dataSaleOrder
        } = this.props;
        const info = {
            "amount": transaction.Amount,
            "saleOrderID": transaction.VoucherConcern,
            "transactionType": transactionType,
            "orderTypeID": dataSaleOrder.OrderTypeID
        };
        const newDataTransaction = dataTransaction.filter(ele => ele.PaymentTransactionID != transaction.PaymentTransactionID);
        actionSaleOrder.updateDataTransaction(newDataTransaction);
        this.createQR(info);
    }

    createQR = (data) => {
        showBlockUI();
        this.props.actionSaleOrder.getDataQRPayment(data).then(success => {
            this.queryJob = setInterval(this.onQueryStatusQR, 5000);
            hideBlockUI();
            this.setState({
                description: translate('saleOrderPayment.please_scan_qr_code_to_pay')
            });
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [{
                text: "OK",
                onPress: () => {
                    hideBlockUI();
                    this.props.navigation.goBack();
                }
            }]);
        })
    }

    onPending = (transactionID, qrInfo, saleOrderID) => () => {
        const {
            dataQRTransaction: {
                dataTransaction
            },
            navigation,
            actionSaleOrder,
        } = this.props;
        const newDataTransaction = dataTransaction.filter(ele => ele.PaymentTransactionID != transactionID);
        if (!helper.IsEmptyObject(this.transaction)) {
            newDataTransaction.push(this.transaction);
        }
        else {
            const transaction = {
                "VoucherConcern": saleOrderID,
                "PaymentTransactionID": transactionID,
                "Amount": qrInfo.amount,
                "transactionType": qrInfo.transactionType,
                "QRCodeData": qrInfo.qrValue,
                "QRCodeExpriedDate": qrInfo.expried,
                "CustomerDisplayName": qrInfo.transactionType.CustomerDisplayName,
                "PaymentTransactionTypeID": qrInfo.transactionType.PaymentTransactionTypeID,
                "PaymentStatusID": "00"
            }
            newDataTransaction.push(transaction);
        }
        actionSaleOrder.updateDataTransaction(newDataTransaction);
        navigation.goBack();
    }
}

const mapStateToProps = function (state) {
    return {
        dataSaleOrder: state.saleOrderPaymentReducer.dataSaleOrder,
        qrInfo: state.saleOrderPaymentReducer.qrInfo,
        dataQRTransaction: state.saleOrderPaymentReducer.dataQRTransaction,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(QRPayment);

const Guide = ({ appName, checkShowSMPGuide, paymentTransactionTypeID }) => {

    const handleGetGuide = () => {
        switch (checkShowSMPGuide) {
            case 5:
                return `${translate('saleOrderPayment.open_app')} ${appName} hoặc ${translate('saleOrderPayment.app_bank')}`;
            case 18:
                return `Mở app Evo`;
            default:
                return `${translate('saleOrderPayment.open_app')} ${appName}`;
        }
    };

    const title = paymentTransactionTypeID == PAYMENT_PARTNER_ID.KREDIVO ? "Chọn giao dịch cần thanh toán" : translate('saleOrderPayment.choose_scan_QR')
    return (
        <View style={{
            width: constants.width,
            padding: 10
        }}>
            <MyText
                text={translate('saleOrderPayment.customer_guide')}
                addSize={1}
                style={{
                    color: COLORS.txt288AD6,
                    fontWeight: 'bold'
                }} />
            <View style={{
                width: constants.width - 20,
                paddingLeft: 8
            }}>
                <MyText
                    text={translate('saleOrderPayment.step_one')}
                    style={{
                        color: COLORS.txt333333,
                        marginBottom: 4,
                        fontWeight: 'bold'
                    }}>
                    <MyText
                        text={handleGetGuide()}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'normal'
                        }} />
                </MyText>
                <MyText
                    text={translate('saleOrderPayment.step_two')}
                    style={{
                        color: COLORS.txt333333,
                        fontWeight: 'bold'
                    }}>
                    <MyText
                        text={title}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'normal'
                        }} />
                </MyText>
            </View>
        </View>
    );
}

const Amount = ({ value, isPending }) => {
    return (
        <>
            <MyText
                text={translate('saleOrderPayment.money_amount')}
                addSize={2}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: 'bold',
                    marginTop: 20
                }}>
                {" "}
                <MyText
                    text={value}
                    addSize={2}
                    style={{
                        color: COLORS.txtD0021B,
                        fontWeight: 'bold'
                    }} />
            </MyText>
            {
                isPending
                    ? <MyText
                        text={translate('saleOrderPayment.press')}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txt333333,
                            textAlign: 'center',
                            fontStyle: 'italic',
                            width: constants.width - 10
                        }}>
                        <MyText
                            text={translate('saleOrderPayment.btn_go_back_uppercase')}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt147EFB,
                                textAlign: 'center',
                                fontStyle: 'normal',
                                fontWeight: 'bold'
                            }} />
                        <MyText
                            text={translate('saleOrderPayment.continue_with_different_payment_method')}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt333333,
                                textAlign: 'center',
                                fontStyle: 'italic',
                                width: constants.width - 10
                            }} />
                    </MyText>
                    : <MyText
                        text={translate('saleOrderPayment.if_customer_deny_paying')}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txt333333,
                            textAlign: 'center',
                            fontStyle: 'italic'
                        }}>
                        <MyText
                            text={translate('saleOrderPayment.btn_cancel_QR_code_uppercase')}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt147EFB,
                                textAlign: 'center',
                                fontStyle: 'normal',
                                fontWeight: 'bold'
                            }} />
                        <MyText
                            text={' )'}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt333333,
                                textAlign: 'center',
                                fontStyle: 'italic'
                            }} />
                    </MyText>
            }
        </>
    );
}

const Header = ({ title }) => {
    return (
        <View style={{
            width: constants.width,
            height: 50,
            alignItems: "center",
            justifyContent: 'center',
            backgroundColor: COLORS.bg2FB47C,
        }}>
            <MyText
                text={title}
                addSize={2}
                style={{
                    fontWeight: 'bold',
                    color: COLORS.txtFFF6AD,
                    textAlign: 'center'
                }} />
        </View>
    );
}

const getStatusType = (type, status) => {
    switch (type) {
        case 1:
            return {
                SUCCESS: (status == '00'),
                ERROR: (status == '04'),
                SCANNED: false,
                PROCESSING: false
            };
        case 5:
            return {
                SUCCESS: (status == 'PAYED'),
                ERROR: (status == 'PAY_ERROR'),
                SCANNED: (status == 'SCANNED'),
                PROCESSING: (status == 'PROCESSING')
            };
        case 6:
            return {
                SUCCESS: (status == 'MWGPAYED'),
                ERROR: (status == 'MWGPAY_ERROR'),
                SCANNED: false,
                PROCESSING: (status == 'MWGPROCESSING')
            };
        case 7:
            return {
                SUCCESS: (status == 'MOMO_PAYED') || (status == '0'),
                ERROR: (status == 'MOMO_PAY_ERROR'),
                SCANNED: false,
                PROCESSING: (status == 'MOMO_PROCESSING') || (status == '700')
            };
        case 10:
            return {
                SUCCESS: (status == '1'),
                ERROR: (status == '2'),
                SCANNED: false,
                PROCESSING: (status == '3') || (status == '0')
            };
        case 13:
            return {
                SUCCESS: (status == 'DONE'),
                ERROR: (status == 'ADJUSTED') || (status == 'CANCELLED') || (status == 'DENIED'),
                SCANNED: false,
                PROCESSING: (status == 'SCANNED') || (status == 'IN_PROGRESS') || (status == '400') || (status == '422') || (status == '500')
            };

        case 14:
            return {
                SUCCESS: (status == 'MOMOINS_PAYED') || (status == '0'),
                ERROR: (status == 'MOMOINS_PAY_ERROR'),
                SCANNED: false,
                PROCESSING: (status == 'MOMOINS_PROCESSING') || (status == '700')
            };
        case 15:
            return {
                SUCCESS: (status == 'KREDIVO_PAYED'),
                ERROR: (status == 'KREDIVO_PAY_ERROR'),
                SCANNED: false,
                PROCESSING: (status == 'KREDIVO_PROCESSING')
            };
        case 16:
            return {
                SUCCESS: (status == 'CAKE_PAYED'),
                ERROR: (status == 'CAKE_PAY_ERROR'),
                SCANNED: false,
                PROCESSING: (status == 'CAKE_PROCESSING')
            };
        case 17:
        case 19:
            return {
                SUCCESS: (status == 'QTV_PAYED'),
                ERROR: (status == 'QTV_PAY_ERROR'),
                SCANNED: false,
                PROCESSING: (status == 'QTV_PROCESSING')
            };
        case 18:
            return {
                SUCCESS: (status == 'TPBanhEVO_PAYED'),
                ERROR: (status == 'TPBanhEVO_PAY_ERROR'),
                SCANNED: false,
                PROCESSING: (status == 'TPBanhEVO_PROCESSING')
            };
        default:
            return {
                SUCCESS: false,
                ERROR: false,
                SCANNED: false,
                PROCESSING: false
            };;
    }
}

const getStatusView = (type, status) => {
    switch (type) {
        case 1:
            return {
                BACK: (status == 'MWGOPEN'),
                DELETE: false,
                EXPIRED: false
            };
        case 5:
            return {
                BACK: (status == 'SCANNED') || (status == 'PROCESSING') || (status == 'OPEN'),
                DELETE: false,
                EXPIRED: true
            };
        case 6:
            return {
                BACK: false,
                DELETE: (status == '0') || (status == 'MWGPROCESSING'),
                EXPIRED: true
            };
        case 7:
            return {
                BACK: (status == 'MOMO_PROCESSING'),
                DELETE: false,
                EXPIRED: true
            };
        case 10:
            return {
                BACK: (status == '3') || (status == '0'),
                DELETE: false,
                EXPIRED: true
            };
        case 13:
            return {
                BACK: (status == 'CREATED') || (status == 'IN_PROGRESS') || (status == '400') || (status == '422') || (status == '500'),
                DELETE: false,
                EXPIRED: true
            };
        case 14:
            return {
                BACK: (status == 'MOMOINS_PROCESSING'),
                DELETE: false,
                EXPIRED: true
            };
        case 15:
            return {
                BACK: (status == 'KREDIVO_PROCESSING'),
                DELETE: (status == 'KREDIVO_PROCESSING'),
                EXPIRED: true
            };
        case 16:
            return {
                BACK: (status == 'CAKE_PROCESSING'),
                DELETE: (status == 'CAKE_PROCESSING'),
                EXPIRED: true
            };
        case 17:
        case 19:
            return {
                BACK: (status == 'QTV_PROCESSING'),
                DELETE: false,
                EXPIRED: true
            };
        case 18:
            return {
                BACK: (status == 'TPBanhEVO_PROCESSING'),
                DELETE: (status == 'TPBanhEVO_PROCESSING'),
                EXPIRED: true
            };
        default:
            return {
                BACK: false,
                DELETE: false,
                EXPIRED: true
            };;
    }
}

const getDefaultStatus = (type) => {
    switch (type) {
        case 1:
            return { status: "MWGOPEN", description: translate('saleOrderPayment.please_scan_qr_code_to_pay') };
        case 5:
            return { status: "OPEN", description: translate('saleOrderPayment.please_scan_qr_code_to_pay') };
        case 6:
            return { status: "MWGPROCESSING", description: translate('saleOrderPayment.transact_pending') };
        case 7:
            return { status: "MOMO_PROCESSING", description: translate('saleOrderPayment.transact_pending') };
        case 10:
            return { status: "0", description: translate('saleOrderPayment.transact_pending') };
        case 13:
            return { status: "CREATED", description: translate('saleOrderPayment.transact_pending') };
        case 14:
            return { status: "MOMOINS_PROCESSING", description: translate('saleOrderPayment.transact_pending') };
        case 15:
            return { status: "KREDIVO_PROCESSING", description: translate('saleOrderPayment.transact_pending') };
        case 16:
            return { status: "CAKE_PROCESSING", description: translate('saleOrderPayment.transact_pending') };
        case 17:
            return { status: "QTV_PROCESSING", description: translate('saleOrderPayment.transact_pending') };
        case 18:
            return { status: "TPBanhEVO_PROCESSING", description: translate('saleOrderPayment.transact_pending') };
        default:
            return "";
    }
}