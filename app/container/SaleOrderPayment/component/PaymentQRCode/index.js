import React, { useState } from 'react';
import {
    View,
    StyleSheet,
} from 'react-native';
import {
    showBlockUI,
    hideBlockUI,
    showPopup
} from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import PaymentTitle from "../PaymentTitle";
import InputCard from "./component/InputCard";
import Transaction from "./component/Transaction";
import { translate } from '@translate';
import { COLORS } from "@styles";

const PaymentQRCode = ({
    title,
    total,
    maxPayment,
    dataQRType,
    dataTransaction,
    onApplyTransaction,
    updateTransaction,
    createQR,
    onGetQR,
    actionSaleOrder,
    dataSO
}) => {
    const [isShow, setIsShow] = useState(false);
    const isVisibleTR = helper.IsNonEmptyArray(dataTransaction);

    const onQueryQR = (info) => {
        showBlockUI();
        const { transactionType: { PaymentTransactionTypeID } } = info;
        actionSaleOrder.queryStatusQR(info).then(transaction => {
            const status = getStatusType(PaymentTransactionTypeID, transaction.PaymentStatusID);
            hideBlockUI();
            switch (true) {
                case status.SUCCESS:
                    showPopup(translate('saleOrderPayment.transact_successfully'), translate('saleOrderPayment.system_auto_create'),
                        [{
                            text: "OK",
                            onPress: querySuccess(transaction)
                        }],
                        'success');
                    break;
                case status.ERROR:
                    showPopup(translate('saleOrderPayment.transact_fail'), "Vui lòng tạo lại giao dịch mới để thực hiện thanh toán",
                        [
                            {
                                text: translate('saleOrderPayment.btn_skip_uppercase'),
                                onPress: queryError(transaction),
                                style: "cancel"
                            },
                            {
                                text: translate('saleOrderPayment.btn_create_again'),
                                onPress: onCreate(transaction)
                            }
                        ],
                        'error');
                    break;
                case status.INIT:
                    onGetQR(info);
                    break;
                case status.SCANNED:
                    showPopup(translate('saleOrderPayment.unconfirmed_transaction'), translate('saleOrderPayment.please_confirm_transcation'));
                    break;
                default:
                    showPopup(translate('saleOrderPayment.transact_pending'), translate('saleOrderPayment.please_again'));
                    break;
            }
        }).catch(({ msgError }) => {
            hideBlockUI();
            showPopup("", msgError);
        })
    }

    const querySuccess = (transaction) => () => {
        const data = dataTransaction.filter(ele => ele.PaymentTransactionID != transaction.PaymentTransactionID);
        onApplyTransaction(transaction, data);
    }

    const queryError = (transaction) => () => {
        const data = dataTransaction.filter(ele => ele.PaymentTransactionID != transaction.PaymentTransactionID);
        updateTransaction(data);
    }

    const onCreate = (transaction) => () => {
        const transactionType = dataQRType.find(qrType => transaction.PaymentTransactionTypeID == qrType.PaymentTransactionTypeID);
        const data = dataTransaction.filter(ele => ele.PaymentTransactionID != transaction.PaymentTransactionID);
        const info = {
            "amount": transaction.Amount,
            "saleOrderID": transaction.VoucherConcern,
            "transactionType": transactionType,
            "orderTypeID": dataSO.OrderTypeID
        };
        updateTransaction(data);
        createQR(info);
    }

    return (
        <View style={{
            backgroundColor: COLORS.bgF5F5F5,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF,
            width: constants.width
        }}>
            <PaymentTitle
                title={title}
                total={total}
                onPress={() => {
                    setIsShow(!isShow)
                }}
                isShow={isShow}
            />
            {
                isShow &&
                <View style={{
                    width: constants.width,
                }}>
                    <InputCard
                        dataSO={dataSO}
                        maxPayment={maxPayment}
                        data={dataQRType}
                        createQR={createQR}
                    />
                    {
                        isVisibleTR &&
                        <Transaction
                            data={dataTransaction}
                            dataQRType={dataQRType}
                            onGetQR={onGetQR}
                            onQueryQR={onQueryQR}
                        />
                    }
                </View>
            }
        </View>
    );
}

export default PaymentQRCode;

const getStatusType = (type, status) => {
    switch (type) {
        case 1:
            return {
                SUCCESS: (status == '00'),
                ERROR: (status == '04'),
                SCANNED: false,
                INIT: (status == 'MWGOPEN')
            };
        case 5:
            return {
                SUCCESS: (status == 'PAYED'),
                ERROR: (status == 'PAY_ERROR'),
                SCANNED: (status == 'SCANNED'),
                INIT: (status == 'OPEN')
            };
        case 6:
            return {
                SUCCESS: (status == 'MWGPAYED'),
                ERROR: (status == 'MWGPAY_ERROR'),
                SCANNED: false,
                INIT: (status == '0') || (status == 'MWGPROCESSING')
            };
        case 7:
            return {
                SUCCESS: (status == 'MOMO_PAYED'),
                ERROR: (status == 'MOMO_PAY_ERROR'),
                SCANNED: false,
                INIT: (status == '0') || (status == 'MOMO_PROCESSING')
            };
        case 10:
            return {
                SUCCESS: (status == '1'),
                ERROR: (status == '2'),
                SCANNED: false,
                INIT: (status == '0') || (status == '3')
            };
        case 13:
            return {
                SUCCESS: (status == 'DONE'),
                ERROR: (status == 'ADJUSTED') || (status == 'CANCELLED') || (status == 'DENIED'),
                SCANNED: false,
                INIT: (status == 'CREATED') || (status == 'IN_PROGRESS') || (status == '400') || (status == '422') || (status == '500')
            };
        case 14:
            return {
                SUCCESS: (status == 'MOMOINS_PAYED'),
                ERROR: (status == 'MOMOINS_PAY_ERROR'),
                SCANNED: false,
                INIT: (status == '0') || (status == 'MOMOINS_PROCESSING')
            };
        case 15:
            return {
                SUCCESS: (status == 'KREDIVO_PAYED'),
                ERROR: (status == 'KREDIVO_PAY_ERROR'),
                SCANNED: false,
                INIT: (status == '0') || (status == 'KREDIVO_PROCESSING')
            };
        case 16:
            return {
                SUCCESS: (status == 'CAKE_PAYED'),
                ERROR: (status == 'CAKE_PAY_ERROR'),
                SCANNED: false,
                INIT: (status == '0') || (status == 'CAKE_PROCESSING')
            };
        case 17:
        case 19:
            return {
                SUCCESS: (status == 'QTV_PAYED'),
                ERROR: (status == 'QTV_PAY_ERROR'),
                SCANNED: false,
                INIT: (status == '0') || (status == 'QTV_PROCESSING')
            };
        case 18:
            return {
                SUCCESS: (status == 'TPBanhEVO_PAYED'),
                ERROR: (status == 'TPBanhEVO_PAY_ERROR'),
                SCANNED: false,
                INIT: (status == '0') || (status == 'TPBanhEVO_PROCESSING')
            };
        default:
            return {
                SUCCESS: false,
                ERROR: false,
                SCANNED: false,
                INIT: false
            };;
    }
}

