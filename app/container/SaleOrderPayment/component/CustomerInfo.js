import React, { useEffect, useRef, useState } from 'react';
import { Alert, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Icon, MyText, hideBlockUI, showBlockUI } from '@components';
import { constants } from '@constants';
import { translate } from '@translate';
import { COLORS } from '@styles';
import { useDispatch } from 'react-redux';
import { helper } from '@common';
import { logSeenCustomerInfo } from '../../EditSaleOrder/action';
import { TYPE_PROFILE } from '../../../constants/constants';

const FieldText = ({ name, value }) => {
    return (
        <View
            style={{
                justifyContent: 'space-between',
                flexDirection: 'row',
                width: constants.width - 20
            }}>
            <MyText
                text={name}
                addSize={-1}
                style={{
                    color: COLORS.txt333333,
                    marginBottom: 4,
                    fontWeight: 'bold',
                    width: 110
                }}
            />
            <MyText
                text={value}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: 'normal',
                    width: constants.width - 130
                }}
            />
        </View>
    );
};

const CustomerInfo = ({ info, profileInfo }) => {
    const {
        StaffUser,
        StaffFullName,
        OutputStoreID,
        OutputStoreName,
        SaleOrderID,
    } = info;
    const { CustomerName, CustomerPhone } = getCustomerInfo(info, profileInfo)
    const isSeenLog = useRef(false);
    const dispatch = useDispatch();
    const [isShowNumberPhone, setIsShowNumberPhone] = useState(false);
    const [newCustomerPhone, setNewCustomerPhone] = useState('');

    useEffect(() => {
        if (!helper.IsEmptyString(CustomerPhone) && CustomerPhone != null) {
            if (!isShowNumberPhone) {
                const customerPhoneRegex = encryptNum(CustomerPhone);
                setNewCustomerPhone(customerPhoneRegex);
            } else {
                setNewCustomerPhone(CustomerPhone);
            }
        }
    }, [isShowNumberPhone]);

    const onShowCustomerInfo = () => {
        if (!isSeenLog.current) {
            showBlockUI();
            dispatch(logSeenCustomerInfo(SaleOrderID))
                .then(() => {
                    isSeenLog.current = true;
                    setIsShowNumberPhone(true);
                    setNewCustomerPhone(CustomerPhone);
                    hideBlockUI();
                })
                .catch((error) => {
                    Alert.alert('Không thể xem thông tin', error.msgError, [
                        {
                            text: 'OK',
                            onPress: hideBlockUI
                        }
                    ]);
                });
        } else {
            setIsShowNumberPhone(!isShowNumberPhone);
        }
    };

    return (
        <View
            style={{
                width: constants.width,
                paddingHorizontal: 10,
                paddingVertical: 4,
                backgroundColor: COLORS.bgFFFFFF,
                borderBottomWidth: StyleSheet.hairlineWidth,
                borderBottomColor: COLORS.bdE4E4E4
            }}>
            <FieldText
                name={translate('saleOrderPayment.output_store')}
                value={`${OutputStoreID} - ${OutputStoreName}`}
            />
            <FieldText
                name={translate('common.customer')}
                value={CustomerName}
            />
            <FieldTextCustom
                name={translate('saleOrderPayment.customer_phone')}
                value={newCustomerPhone}
                isShow={isShowNumberPhone}
                onPress={onShowCustomerInfo}
            />
            <FieldText
                name={translate('saleOrderPayment.sale_employee')}
                value={`${StaffUser} - ${StaffFullName}`}
            />
        </View>
    );
};

export default CustomerInfo;

const FieldTextCustom = ({ name, value, isShow, onPress }) => {
    return (
        <View
            style={{
                flexDirection: 'row',
                width: constants.width - 20
            }}>
            <MyText
                text={name}
                addSize={-1}
                style={{
                    color: COLORS.txt333333,
                    marginBottom: 4,
                    fontWeight: 'bold',
                    width: 110
                }}
            />
            <TouchableOpacity
                style={{ flexDirection: 'row' }}
                onPress={onPress}>
                <MyText
                    text={value}
                    style={{
                        color: COLORS.txt333333,
                        fontWeight: 'normal',
                        marginRight: 10
                    }}
                />

                <Icon
                    iconSet="Ionicons"
                    name={isShow ? 'eye-outline' : 'eye-off-outline'}
                    color={COLORS.ic147EFB}
                    size={16}
                />
            </TouchableOpacity>
        </View>
    );
};

const encryptNum = (number) => {
    number = number.replace(/[-,\s]/g, '');
    return `${number.substring(0, 3)}-${number.substring(3, 4)}XX-XXXX`;
};

const getCustomerInfo = (info, profileInfo) => {
    const { CustomerName, CustomerPhone, TaxID } = info
    if (helper.IsEmptyObject(profileInfo)) return {
        CustomerName,
        CustomerPhone
    }
    if (!helper.IsEmptyObject(profileInfo[TYPE_PROFILE.COMPANY]) && !!TaxID) {
        return {
            CustomerName: profileInfo[TYPE_PROFILE.COMPANY].companyName,
            CustomerPhone: profileInfo[TYPE_PROFILE.COMPANY].companyPhone
        }
    }
    if (!helper.IsEmptyObject(profileInfo[TYPE_PROFILE.CUSTOMER])) {
        return {
            CustomerName: profileInfo[TYPE_PROFILE.CUSTOMER].customerName,
            CustomerPhone: profileInfo[TYPE_PROFILE.CUSTOMER].phoneNumber
        }
    }
    return {
        CustomerName,
        CustomerPhone
    }
}