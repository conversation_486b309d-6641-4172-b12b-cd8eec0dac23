import {
    <PERSON><PERSON>,
    <PERSON>List,
    Keyboard,
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '../../AnKhangNew/components';
import { dateHelper, debounce, helper } from '../../../common';
import ProductInfo from '../Components/ProductInfo';
import {
    BarcodeCamera,
    BaseLoading,
    Icon,
    MyText,
    hideBlockUI,
    showBlockUI
} from '../../../components';
import { COLORS } from '../../../styles';
import { constants } from '../../../constants';
import moment from 'moment';
import ModalCalendar from '../../PaymentTransactions/component/ModalCalendar';
import { searchProduct, stopProduct } from '../action';
import { useSelector } from 'react-redux';
import { translate } from '../../../translations';
import { useNavigation } from '@react-navigation/native';
import SearchBar from '../Components/SearchBar';

const TIMEOUT_SEARCH = 700;

const DeclaredPriceList = () => {
    const navigation = useNavigation();

    const [keyword, setKeyword] = useState('');
    const [isShowCamera, setIsShowCamera] = useState(false);
    const [stateSearchProducts, setStateSearchProducts] = useState({
        isFetching: false,
        isError: false,
        description: '',
        isEmpty: false,
        data: []
    });
    const [isShowCalendar, setIsShowCalendar] = useState(false);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [isStopped, setIsStopped] = useState(false);

    const { storeID, languageID, moduleID, userName } = useSelector(
        (state) => state.userReducer
    );

    const debounceSearchProducts = useCallback(
        debounce((data) => handleOnSearchProduct(data), TIMEOUT_SEARCH),
        []
    );

    const baseBody = {
        loginStoreId: storeID,
        languageID: languageID,
        moduleID: moduleID
    };

    const handleOnSearchProduct = (data) => {
        const body = {
            ...baseBody,
            loginuser: userName,
            fromDate: dateHelper.formatDateDDMMYYYY(data.fromDate),
            toDate: dateHelper.formatDateDDMMYYYY(data.toDate),
            isStopped: data.isStopped ? 1 : 0,
            productIdList: data.keyword,
            storeId: storeID
        };
        setStateSearchProducts({
            isFetching: true,
            isError: false,
            description: '',
            isEmpty: false,
            data: []
        });
        searchProduct(body)
            .then((response) => {
                if (helper.IsNonEmptyArray(response)) {
                    setStateSearchProducts({
                        isFetching: false,
                        isError: false,
                        description: '',
                        isEmpty: false,
                        data: response
                    });
                } else {
                    setStateSearchProducts({
                        isFetching: false,
                        isError: false,
                        description: 'Không tìm thấy sản phẩm làm giá.',
                        isEmpty: true,
                        data: []
                    });
                }
            })
            .catch((msgError) => {
                setStateSearchProducts({
                    isFetching: false,
                    isError: true,
                    description: msgError,
                    isEmpty: false,
                    data: []
                });
            });
    };

    const onStopProduct = (item) => () => {
        Alert.alert(
            translate('common.notification_uppercase'),
            `Bạn có chắc chắn muốn ngưng làm giá sản phẩm ${item?.ProductName}?`,
            [
                {
                    text: 'Bỏ Qua',
                    style: 'cancel'
                },
                {
                    text: 'Đồng Ý',
                    style: 'cancel',
                    onPress: () => handleStop(item)
                }
            ]
        );
    };

    const handleStop = (item) => {
        showBlockUI();
        const body = {
            ...baseBody,
            loginUser: userName,
            priceOfStore: {
                priceOfStoreId: item.PriceOfStoreID,
                stoppedReason: null,
                updatedUser: userName
            }
        };
        stopProduct(body)
            .then(() => {
                hideBlockUI();
                const newProducts = stateSearchProducts.data.filter(
                    (_item) => _item.PriceOfStoreID != item.PriceOfStoreID
                );
                setStateSearchProducts({
                    ...stateSearchProducts,
                    data: newProducts
                });
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: 'Thử lại',
                            style: 'cancel',
                            onPress: () => handleStop(item)
                        },
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: () => hideBlockUI()
                        }
                    ]
                );
            });
    };

    useEffect(() => {
        // if (keyword === null || keyword.length === 0) return;
        debounceSearchProducts({
            keyword,
            fromDate,
            toDate,
            isStopped
        });
    }, [keyword, isStopped, fromDate, toDate]);

    return (
        <SafeAreaView
            style={{
                backgroundColor: COLORS.bgFFFFFF,
                flex: 1,
                position: 'relative'
            }}>
            <View
                style={{
                    marginHorizontal: 10,
                    marginVertical: 10
                }}>
                <MyText
                    addSize={1.5}
                    style={{
                        paddingHorizontal: 10,
                        paddingVertical: 5,
                        fontWeight: 'bold'
                    }}
                    text={'Quản lý siêu thị điều chỉnh giá bán'}
                />
                <TouchableOpacity
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        backgroundColor: COLORS.bgFFFFFF,
                        borderRadius: 5,
                        paddingHorizontal: 5,
                        borderWidth: 1,
                        borderColor: COLORS.bdDDDDDD,
                        height: 44,
                        alignSelf: 'center'
                    }}
                    onPress={() => setIsShowCalendar(true)}>
                    <MyText
                        style={{
                            width: '87%',
                            paddingHorizontal: 5
                        }}
                        text={`${moment(fromDate).format(
                            'DD/MM/YYYY'
                        )} - ${moment(toDate).format('DD/MM/YYYY')} `}
                    />
                    <Icon
                        iconSet="Feather"
                        name="calendar"
                        style={{
                            fontSize: 30,
                            color: COLORS.ic2C8BD7
                        }}
                    />
                </TouchableOpacity>
            </View>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginHorizontal: 10
                }}>
                <TouchableOpacity
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingHorizontal: 5,
                        alignSelf: 'center',
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}
                    onPress={() => setIsStopped((pre) => !pre)}>
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        name={
                            !isStopped
                                ? 'checkbox-blank-outline'
                                : 'checkbox-marked'
                        }
                        style={{
                            fontSize: 22,
                            color: COLORS.ic2C8BD7
                        }}
                    />
                    <MyText
                        style={{
                            paddingHorizontal: 5
                        }}
                        text={`Xem các sản phẩm đã ngưng áp dụng `}
                    />
                </TouchableOpacity>
            </View>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginHorizontal: 18,
                    marginVertical: 10
                }}
                onLayout={({ nativeEvent }) => {
                    // searchHeight.current = nativeEvent.layout.height;
                }}>
                <SearchBar
                    onFocus={() => { }}
                    setShowListSearch={() => { }}
                    value={keyword}
                    placeholder={'-- Mã hoặc tên sản phẩm --'}
                    onChangeText={(text) => {
                        setKeyword(text);
                    }}
                    onPressBarcode={() => setIsShowCamera((prev) => !prev)}
                    onSearch={() => {
                        debounceSearchProducts.stop();
                        handleOnSearchProduct({
                            keyword,
                            fromDate,
                            toDate,
                            isStopped
                        });
                    }}
                />
            </View>

            <View style={{ flex: 1 }}>
                <BaseLoading
                    isLoading={stateSearchProducts.isFetching}
                    isEmpty={stateSearchProducts.isEmpty}
                    textLoadingError={stateSearchProducts.description}
                    isError={stateSearchProducts.isError}
                    onPressTryAgains={() => {
                        handleOnSearchProduct({
                            keyword,
                            fromDate,
                            toDate,
                            isStopped
                        });
                    }}
                    content={
                        <FlatList
                            data={stateSearchProducts.data}
                            renderItem={({ item, index }) => (
                                <ProductInfo
                                    onPress={onStopProduct(item)}
                                    item={item}
                                    index={index}
                                />
                            )}
                            keyboardShouldPersistTaps="never"
                            keyboardDismissMode="on-drag"
                            keyExtractor={(item) => item?.ProductID}
                        />
                    }
                />
            </View>
            <View
                style={[
                    styles.bottomContainer,
                    {
                        marginBottom: constants.heightBottomSafe > 0 ? 0 : 8
                    }
                ]}>
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'center'
                    }}>
                    <Button
                        style={{
                            ...styles.btnNext,
                            opacity: 1,
                            backgroundColor: COLORS.bdFFFFFF
                        }}
                        color={COLORS.bg40B93C}
                        onPress={() => {
                            navigation.navigate('PriceAdjust', {
                                onSearch: () => {
                                    handleOnSearchProduct({
                                        keyword: '',
                                        fromDate: new Date(),
                                        toDate: new Date(),
                                        isStopped: 0
                                    });
                                }
                            });
                        }}
                        text={'Thêm mới'}
                        outline
                    />
                </View>
            </View>
            <ModalCalendar
                isVisible={isShowCalendar}
                hideModal={() => {
                    setIsShowCalendar(false);
                }}
                startDate={fromDate}
                endDate={toDate}
                setDate={(day) => {
                    setFromDate(new Date(day.startDate));
                    setToDate(new Date(day.endDate));
                }}
                // minDate={minDate.current}
                // maxDate={maxDate}
                hideExtraDays
                disableMonthChange
                firstDay={1}
                hideDayNames={false}
                showWeekNumbers={false}
                enableSwipeMonths={false}
                onSelectDate={(date) => { }}
            />
            {isShowCamera && (
                <BarcodeCamera
                    isVisible={isShowCamera}
                    closeCamera={() => {
                        setIsShowCamera(false);
                    }}
                    resultScanBarcode={(barcode) => {
                        setIsShowCamera(false);
                        setKeyword(barcode);
                        setIsShowCamera(false);

                    }}
                />
            )}
        </SafeAreaView>
    );
};

export default DeclaredPriceList;

const styles = StyleSheet.create({
    bottomContainer: {
        backgroundColor: COLORS.bgFFFFFF,
        paddingHorizontal: 18
    },
    btnNext: {
        alignItems: 'center',
        borderRadius: 5,
        paddingHorizontal: 15,
        paddingVertical: 7,
        width: constants.width - 20
    }
});
