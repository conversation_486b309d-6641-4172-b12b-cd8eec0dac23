import { <PERSON><PERSON>, <PERSON><PERSON>ist, Keyboard, SafeAreaView, StyleSheet, View } from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '../../AnKhangNew/components';
import { debounce, helper } from '../../../common';
import ProductPriceAdjustment from '../Components/ProductPriceAdjustment';
import {
    BarcodeCamera,
    BaseLoading,
    MyText,
    hideBlockUI,
    showBlockUI
} from '../../../components';
import { COLORS } from '../../../styles';
import { constants } from '../../../constants';
import { getProduct, updateProductPrice } from '../action';
import { translate } from '../../../translations';
import { useDispatch, useSelector } from 'react-redux';
import { searchKeywordProduct } from '../../Sale/action';
import ProductSearchItem from '../Components/ProductSearchItem';
import { useNavigation } from '@react-navigation/native';
import SearchBar from '../Components/SearchBar';

const TIMEOUT_SEARCH = 700;

const PriceAdjust = ({ route }) => {
    const { onSearch } = route.params ?? { onSearch: () => { } };
    const dispatch = useDispatch();
    const navigation = useNavigation();

    const [keyword, setKeyword] = useState('');
    const [isShowCamera, setIsShowCamera] = useState(false);
    const [stateSearchProducts, setStateSearchProducts] = useState({
        isFetching: false,
        isError: false,
        description: '',
        isEmpty: false,
        data: []
    });
    const [showListSearch, setShowListSearch] = useState(false);
    const [product, setProduct] = useState({});
    const [dataAdjust, setDataAdjust] = useState({});
    const [isFocus, setIsFocus] = useState(false);

    const isScan = useRef(false);

    const { storeID, languageID, moduleID, userName } = useSelector(
        (state) => state.userReducer
    );

    const baseBody = {
        loginStoreId: storeID,
        languageID: languageID,
        moduleID: moduleID
    };

    const debounceSearchProducts = useCallback(
        debounce((text) => handleOnSearchProduct(text), TIMEOUT_SEARCH),
        []
    );

    const handleOnSearchProduct = (keyword) => {
        setShowListSearch(true);
        setStateSearchProducts({
            isFetching: true,
            isError: false,
            description: '',
            isEmpty: false,
            data: []
        });
        dispatch(searchKeywordProduct(keyword))
            .then(({ isEmpty, description, data }) => {
                setStateSearchProducts({
                    isFetching: false,
                    isError: false,
                    description: description,
                    isEmpty: isEmpty,
                    data: data
                });
                if (isScan.current && data?.length == 1) {
                    handleOnClickProduct({ productID: data[0].productIDERP });
                    isScan.current = false;
                }
            })
            .catch((msgError) => {
                setStateSearchProducts({
                    isFetching: false,
                    isError: true,
                    description: msgError,
                    isEmpty: false,
                    data: []
                });
            });
    };

    const handleOnClickProduct = ({ productID }) => {
        const body = {
            ...baseBody,
            loginUser: userName,
            productId: productID,
            storeId: storeID
        };
        showBlockUI();
        setKeyword('');
        getProduct(body)
            .then((response) => {
                setShowListSearch(false);
                response.StandardPrice = Number(
                    response.StandardPrice.toFixed(0)
                );
                setProduct(response);
                hideBlockUI();
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: 'Thử lại',
                            style: 'cancel',
                            onPress: () => handleOnClickProduct({ productID })
                        },
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: () => hideBlockUI()
                        }
                    ]
                );
            });
    };

    const onUpdatePrice = () => {
        const { fromDate, toDate, priceAtStore, minValue, maxValue } = dataAdjust;
        if (priceAtStore <= 0) {
            return Alert.alert("Thông báo",
                `Vui lòng nhập số tiền khác 0.`,
                [{ text: "OK", onPress: () => { } }],
                { cancelable: false },
            );
        }
        if (priceAtStore > maxValue) {
            return Alert.alert("Thông báo",
                `Số tiền [${helper.convertNum(priceAtStore)}] quá khung cho phép điều chỉnh giá`,
                [{ text: "OK", onPress: () => { } }],
                { cancelable: false },
            );

        }
        if (priceAtStore < minValue) {
            return Alert.alert("Thông báo",
                `Số tiền giảm vượt quá % được khai báo!`,
                [{ text: "OK", onPress: () => { } }],
                { cancelable: false },
            );

        }
        if (priceAtStore == product.StandardPriceVAT) {
            return Alert.alert("Thông báo",
                `Giá khai báo phải khác giá tại siêu thị!`,
                [{ text: "OK", onPress: () => { } }],
                { cancelable: false },
            );

        }
        showBlockUI();

        const body = {
            ...baseBody,
            loginUser: userName,
            priceOfStore: {
                productID: product.ProductID,
                storeID: storeID,
                FromDate: new Date((new Date(fromDate)).setUTCHours(0, 0, 0, 0)),
                ToDate: new Date((new Date(toDate)).setUTCHours(23, 59, 59, 59)),
                SalePriceVAT: priceAtStore,
                SalePrice: product.SalePrice,
                StandardPrice: product.StandardPrice,
                StandardPriceVAT: product.StandardPriceVAT,
                maxSalePrice: product.MaxSalePrice,
                MinSalePrice: product.MinSalePrice,
                ProductName: product.ProductName,
                VAT: product.VAT,
                VATPercent: product.VATPercent,
                createdUser: userName,

            }
        };
        updateProductPrice(body)
            .then(() => {
                hideBlockUI();
                Alert.alert(
                    'Thông báo',
                    'Bạn đã khai báo giá sản phẩm thành công !',
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                navigation.goBack();
                                onSearch();
                            }
                        }
                    ],
                    { cancelable: false }
                );
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: 'Thử lại',
                            style: 'cancel',
                            onPress: onUpdatePrice
                        },
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        }
                    ]
                );
            });
    };

    useEffect(() => {
        if (keyword === null || keyword.length === 0) return;
        debounceSearchProducts(keyword);
    }, [keyword]);

    return (
        <SafeAreaView
            style={{
                backgroundColor: COLORS.bgFFFFFF,
                flex: 1,
                position: 'relative'
            }}>
            <View
                style={{
                    marginHorizontal: 10,
                    marginVertical: 10
                }}>
                <MyText
                    addSize={1.5}
                    style={{
                        paddingHorizontal: 10,
                        fontWeight: 'bold'
                    }}
                    text={'Giá bán sản phẩm'}
                />
            </View>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginHorizontal: 18
                }}
                onLayout={({ nativeEvent }) => {
                    // searchHeight.current = nativeEvent.layout.height;
                }}>
                <SearchBar
                    onFocus={() => { }}
                    setShowListSearch={setShowListSearch}
                    value={keyword}
                    placeholder={'-- Mã hoặc tên sản phẩm --'}
                    onChangeText={(text) => {
                        setKeyword(text);
                    }}
                    onPressBarcode={() => setIsShowCamera((prev) => !prev)}
                    onSearch={() => {
                        debounceSearchProducts.stop();
                        handleOnSearchProduct(keyword);
                    }}
                />
            </View>

            {showListSearch ? (
                <View
                    style={{
                        backgroundColor: COLORS.bgFFFFFF,
                        height: '100%',
                        left: 0,
                        paddingBottom: 50,
                        // position: 'absolute',
                        // top: 58,
                        width: '100%',
                        zIndex: 100
                    }}>
                    <BaseLoading
                        isLoading={stateSearchProducts.isFetching}
                        isEmpty={stateSearchProducts.isEmpty}
                        textLoadingError={stateSearchProducts.description}
                        isError={stateSearchProducts.isError}
                        onPressTryAgains={() => {
                            handleOnSearchProduct(keyword);
                        }}
                        content={
                            <FlatList
                                data={stateSearchProducts.data}
                                renderItem={({ item }) => (
                                    <ProductSearchItem
                                        data={item}
                                        onPress={handleOnClickProduct}
                                    />
                                )}
                                keyboardShouldPersistTaps="never"
                                keyboardDismissMode="on-drag"
                                keyExtractor={(item) => item?.ProductID}
                            />
                        }
                    />
                </View>
            ) : (
                <>
                    <View style={{ flex: 1 }}>
                        {!helper.IsEmptyObject(product) && (
                            <ProductPriceAdjustment
                                productInfo={product}
                                setDataAdjust={setDataAdjust}
                                setIsFocus={setIsFocus}
                            />
                        )}
                    </View>
                    <View
                        style={[
                            styles.bottomContainer,
                            {
                                marginBottom:
                                    constants.heightBottomSafe > 0 ? 0 : 8
                            }
                        ]}>
                        {!helper.IsEmptyObject(product) && (
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between'
                                }}>
                                <Button
                                    style={{
                                        ...styles.btnNext,
                                        opacity: 1,
                                        backgroundColor: COLORS.bgFFFFFF
                                    }}
                                    color={COLORS.bgEA1D5D}
                                    onPress={() => {
                                        navigation.goBack();
                                    }}
                                    text={'Bỏ qua'}
                                    colorProp={COLORS.bgEA1D5D}
                                />
                                <Button
                                    style={{
                                        ...styles.btnNext,
                                        opacity: 1,
                                        backgroundColor: COLORS.bg40B93C
                                    }}
                                    color={COLORS.bg40B93C}
                                    onPress={onUpdatePrice}
                                    text={'Cập nhật'}
                                    outline={false}
                                    disabled={isFocus}
                                />
                            </View>
                        )}
                    </View>
                </>
            )}
            {isShowCamera && (
                <BarcodeCamera
                    isVisible={isShowCamera}
                    closeCamera={() => {
                        setIsShowCamera(false);
                    }}
                    resultScanBarcode={(barcode) => {
                        setIsShowCamera(false);
                        setKeyword(barcode);
                        isScan.current = true;
                        setIsShowCamera(false);

                    }}
                />
            )}
        </SafeAreaView>
    );
};

export default PriceAdjust;

const styles = StyleSheet.create({
    bottomContainer: {
        backgroundColor: COLORS.bgFFFFFF,
        paddingHorizontal: 18
    },
    btnNext: {
        alignItems: 'center',
        borderRadius: 5,
        paddingHorizontal: 15,
        paddingVertical: 7,
        width: constants.width / 2 - 40
    }
});
