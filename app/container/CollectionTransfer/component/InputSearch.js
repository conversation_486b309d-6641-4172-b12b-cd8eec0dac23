import React from 'react';
import {
  View,
  TouchableOpacity,
  TextInput,
  Keyboard,
} from 'react-native';
import { constants } from "@constants";
import { Icon, } from "@components";
import { COLORS } from "@styles";

const InputSearch = ({
  value,
  onChangeText,
  onFocus,
  onBlur,
  clearText,
  isShowButtonClose,
  isCollection
}) => {
  return (
    <View style={{
      flexDirection: "row",
      width: isCollection ?  constants.width - 60 : constants.width - 20,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: COLORS.bdE4E4E4,
      backgroundColor: COLORS.bgFFFFFF,
      borderRadius: 20,
      justifyContent: "space-between",
      alignItems: "center",
      alignSelf: "center",
      marginTop: 10
    }}>
      {
        <TextInput
          onFocus={onFocus}
          onBlur={onBlur}
          autoFocus={true}
          value={value}
          onChangeText={onChangeText}
          placeholder="Nhập tên chức năng, VD: Nạp vào số tài khoản"
          placeholderTextColor={"gray"}
          onSubmitEditing={Keyboard.dismiss}
          style={{
            height: 40,
            width: constants.width - 68,
            paddingLeft: 20
          }}
          maxLength={150}
        />
      }
      {
        isShowButtonClose && <TouchableOpacity style={{
          height: 40,
          width: 48,
          justifyContent: "center",
          alignItems: "flex-end",
          paddingRight: 20,
        }}
          onPress={clearText}
        >
          <Icon
            iconSet={"Ionicons"}
            name={"close"}
            color={COLORS.ic848A8C}
            size={22}
          />
        </TouchableOpacity>
      }
    </View>
  );
}

export default InputSearch;
