import React from 'react';
import { COLORS } from '@styles';
import { Image, TouchableOpacity } from 'react-native';
import { Icon, Text } from '@components';
import { constants } from '@constants';
import { helper } from '@common';
import { CatalogCollectionAssets } from '@components';

const MenuItem = ({ item, onPress, isPinned, onPin, onUnPin }) => {

    const { source, screen, AirtimeServiceGroupName, AirtimeServiceGroupID } = item;

    return (
        <TouchableOpacity
            onPress={onPress}
            style={{
                alignItems: 'center',
                width: (constants.width - 20) / 4,
                marginTop: 15
            }}
            activeOpacity={0.6}
            onLongPress={isPinned ? onUnPin : onPin}
        >
            <Image
                style={{
                    width: 40,
                    height: 40,
                }}
                source={CatalogCollectionAssets[`${AirtimeServiceGroupID}`]}
            />
            {
                helper.IsNonEmptyString(screen) ?
                    <Text style={{
                        marginTop: 10,
                        textAlign: 'center',
                        width: 60,
                        color: helper.IsNonEmptyString(source) ? COLORS.bgE0E0E0 : COLORS.bg000000

                    }}>
                        {AirtimeServiceGroupName}
                    </Text>
                    :
                    <Text style={{
                        marginTop: 10,
                        textAlign: 'center',
                        width: 60,
                        color: COLORS.bgE0E0E0
                    }}>
                        {AirtimeServiceGroupName} (Bảo trì)
                    </Text>
            }
            {isPinned &&
                <Icon
                    iconSet={'Entypo'}
                    name={'pin'}
                    color={COLORS.icD0021B}
                    size={22}
                    style={{
                        position: 'absolute',
                        top: 0,
                        right: 3
                    }}
                />
            }
        </TouchableOpacity>
    );
};

export default MenuItem;
