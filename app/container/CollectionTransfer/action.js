import {
    apiBase,
    EMP<PERSON>,
    ERROR,
    <PERSON>TH<PERSON>,
    SUCCE<PERSON>
} from "@config";
import { API_CONST } from '@constants';
import { helper } from '@common';
const {
    API_GET_BANK_ACCOUNT_LIST,
    API_QUERY_CUSTOMER_BANK_ACCOUNT,
    API_GET_FEE_CASHIN,
    API_INSERT_AND_CREATE_TICKET,
    API_CREATE_TICKET,
    API_CHECK_STATUS_TICKET,
    API_ADD_TO_SHOPPING_CART_05,
    API_ADD_TO_SALE_ORDER_CART_05,
    API_GET_DATA_PAY_BILL,
    API_GET_CATALOG_LIST,
    API_GET_SERVICE_GROUP_LIST_CATALOG,
    API_CREATE_TICKET_SERVICE,
    API_CHECK_STATUS_TICKET_SERVICE,
} = API_CONST;

const START_QUERY_CUSTOMER_BANK_ACCOUNT = 'START_QUERY_CUSTOMER_BANK_ACCOUNT';
const STOP_QUERY_CUSTOMER_BANK_ACCOUNT = 'STOP_QUERY_CUSTOMER_BANK_ACCOUNT';
const START_GET_FEE_CASHIN = 'START_GET_FEE_CASHIN';
const STOP_GET_FEE_CASHIN = 'STOP_GET_FEE_CASHIN';
const START_INSERT_AND_CREATE_TICKET = 'START_INSERT_AND_CREATE_TICKET';
const STOP_INSERT_AND_CREATE_TICKET = 'STOP_INSERT_AND_CREATE_TICKET';
const STOP_MODIFY_CART = 'STOP_MODIFY_CART';
const START_ADD_TO_SALE_ORDER_CART = 'START_ADD_TO_SALE_ORDER_CART';
const STOP_ADD_TO_SALE_ORDER_CART = 'STOP_ADD_TO_SALE_ORDER_CART';
const START_GET_DATA_PAY_BILL = 'START_GET_DATA_PAY_BILL';
const STOP_GET_DATA_PAY_BILL = 'STOP_GET_DATA_PAY_BILL';
const CLEAR_DATA_STICKET = 'CLEAR_DATA_STICKET';
const START_GET_CATALOG_LIST_COLLECTION = 'START_GET_CATALOG_LIST_COLLECTION';
const STOP_GET_CATALOG_LIST_COLLECTION = 'STOP_GET_CATALOG_LIST_COLLECTION';
const START_GET_SERVICE_GROUP_LIST_CATALOG = 'START_GET_SERVICE_GROUP_LIST_CATALOG';
const STOP_GET_SERVICE_GROUP_LIST_CATALOG = 'STOP_GET_SERVICE_GROUP_LIST_CATALOG';
const UPDATE_ITEM_CATALOG = 'UPDATE_ITEM_CATALOG';

export const actionCollection = {
    START_QUERY_CUSTOMER_BANK_ACCOUNT,
    STOP_QUERY_CUSTOMER_BANK_ACCOUNT,
    START_GET_FEE_CASHIN,
    STOP_GET_FEE_CASHIN,
    START_INSERT_AND_CREATE_TICKET,
    STOP_INSERT_AND_CREATE_TICKET,
    STOP_MODIFY_CART,
    START_ADD_TO_SALE_ORDER_CART,
    STOP_ADD_TO_SALE_ORDER_CART,
    START_GET_DATA_PAY_BILL,
    STOP_GET_DATA_PAY_BILL,
    CLEAR_DATA_STICKET,
    START_GET_CATALOG_LIST_COLLECTION,
    STOP_GET_CATALOG_LIST_COLLECTION,
    START_GET_SERVICE_GROUP_LIST_CATALOG,
    STOP_GET_SERVICE_GROUP_LIST_CATALOG,
    UPDATE_ITEM_CATALOG
};

export const getBankAccountList = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                bankAccountType: 1
            };
            apiBase(API_GET_BANK_ACCOUNT_LIST, METHOD.POST, body).then((response) => {
                console.log("getBankAccountList success", response);
                if (helper.IsNonEmptyArray(response.object)) {
                    resolve(response.object);
                }
            }).catch(error => {
                console.log("getBankAccountList error", error);
                reject(error.msgError)
            })
        });
    }
}

export const queryCustomerBankAcc = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            Customer: data.Customer,
            BenAccNumber: data.BenAccNumber,
            IsCashInForm: data.IsCashInForm,
            BenBankCode: data.BenBankCode
        }
        dispatch(start_query_customer_banks_account())
        apiBase(API_QUERY_CUSTOMER_BANK_ACCOUNT, METHOD.POST, body)
            .then((response) => {
                console.log("queryCustomerBankAcc success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_query_customer_banks_account(response.object, false, '', false));
                } else {
                    dispatch(stop_query_customer_banks_account({}, true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_query_customer_banks_account({}, false, error.msgError, true))
                console.log("queryCustomerBankAcc error", error);
            })
    }
}

export const getFeeCashinBanks = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                AirTimeTransactionTypeID: data.AirTimeTransactionTypeID,
                BankCode: data.BankCode,
                Amount: data.Amount,
                OriginalOrderID: data.OriginalOrderID,
                OriginalTransID: data.OriginalTransID,
                Sender: data.Sender,
                Receiver: data.Receiver

            }
            dispatch(start_get_fee_cashin())
            apiBase(API_GET_FEE_CASHIN, METHOD.POST, body)
                .then((response) => {
                    console.log("getFeeCashinBanks success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_get_fee_cashin(response.object, false, '', false));
                        if (response?.object?.CustFee == null) {
                            reject(response.object.ResponseMessage);
                        }
                        else {
                            resolve(true);
                        }
                    }
                    else {
                        reject("Không lấy được dữ liệu");
                    }
                }).catch(error => {
                    reject(error.msgError);
                    console.log("getFeeCashinBanks error", error);
                })
        })
    }
}


export const inserAndCreateticket = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                AirTimeTransactionBO: data.AirTimeTransactionBO,
                Cus_AirTimeTransactionAttachBOList: data.Cus_AirTimeTransactionAttachBOList,
                Cus_AirtimeTransactionStateBOList: data.Cus_AirtimeTransactionStateBOList,
            };
            apiBase(API_INSERT_AND_CREATE_TICKET, METHOD.POST, body).then((response) => {
                console.log("inserAndCreateticket success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_insert_and_createticket(response.object, false, '', false));
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("inserAndCreateticket error", error);
                reject(error.msgError)
            })
        });
    }
}

export const createTiclet = function (AirtimeTransactionID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                AirtimeTransactionID: AirtimeTransactionID
            };
            apiBase(API_CREATE_TICKET, METHOD.POST, body).then((response) => {
                console.log("createTiclet success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_insert_and_createticket(response.object, false, '', false));
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("createTiclet error", error);
                reject(error.msgError)
            })
        });
    }
}

export const createTicletServiceRequest = function (AirtimeTransactionID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: AirtimeTransactionID,
                ticketType: 1
            };
            apiBase(API_CREATE_TICKET_SERVICE, METHOD.POST, body).then((response) => {
                console.log("createTiclet success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_insert_and_createticket(response.object, false, '', false));
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("createTiclet error", error);
                reject(error.msgError)
            })
        });
    }
}

export const checksSatusTicket = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                TicketID: data.TICKETID,
                AirtimeTransactionID: data.AIRTIMETRANSACTIONID,
                TicketType: data.TicketType
            };
            apiBase(API_CHECK_STATUS_TICKET, METHOD.POST, body).then((response) => {
                console.log("checksSatusTicket success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("checksSatusTicket error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getDataPayBill = function (AirtimeTransactionID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                AirtimeTransactionID: AirtimeTransactionID
            };
            dispatch(start_get_data_pay_bill())
            apiBase(API_GET_DATA_PAY_BILL, METHOD.POST, body).then((response) => {
                console.log("getDataPayBill success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_get_data_pay_bill(response.object, false, '', false));
                    resolve(response.object);
                }
            }).catch(error => {
                console.log("getDataPayBill error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getAddCartRequest = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "saleScenarioTypeID": 6,
                "mainProduct": data.mainProduct,
                "storeRequests": data.storeRequests,
                "delivery": data.delivery,
                "promotionGroups": data.promotionGroups,
                "promotionRequest": data.promotionRequest,
                "cartRequest": data.cartRequest,
            }
            apiBase(API_ADD_TO_SHOPPING_CART_05, METHOD.POST, body, { setTimeOut: 10000 })
                .then((response) => {
                    console.log("getAddCartRequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_modify_cart_request(response.object));
                        resolve(true);
                    } else {
                        reject("Lỗi thêm sản phẩm vào giỏ hàng");
                    }
                }).catch(error => {
                    reject(error.msgError);
                    console.log("getAddCartRequest error", error);
                })
        })
    }
}

export const addToSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": 0,
                "customerInfo": data.customerInfo,
                "relationShip": {
                    "relationShipType": -1
                },
                "cartRequest": data.cartRequest,
                "requestIDLoyalty": null,
                "customerIDLoyalty": null
            };
            dispatch(start_add_to_sale_order_cart());

            apiBase(API_ADD_TO_SALE_ORDER_CART_05, METHOD.POST, body).then((response) => {
                console.log("addToSaleOrderCart success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    dispatch(stop_add_to_sale_order_cart(object));
                    resolve(response);
                }
                else {
                    dispatch(stop_add_to_sale_order_cart({}));
                    reject({ msgError: translate("saleOrder.error_create_order") });
                }
            }).catch(error => {
                dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                const { msgError } = error;
                console.log("addToSaleOrderCart error", error);
                reject(msgError);
            });
        });
    };
}

export const getCatalogListCollection = function () {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID
            };
            dispatch(start_get_catalog_list_collection())
            apiBase(API_GET_CATALOG_LIST, METHOD.POST, body).then((response) => {
                console.log("getCatalogListCollection success", response);
                if (
                    helper.hasProperty(response, 'object') &&
                    helper.isArray(response.object) &&
                    response.object.length > 0
                ) {
                    dispatch(stop_get_catalog_list_collection(response.object));
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                    dispatch(stop_get_catalog_list_collection([], false, 'Không tìm thấy danh mục Catalog', true));
                }
            }).catch(error => {
                console.log("getCatalogListCollection error", error);
                dispatch(stop_get_catalog_list_collection([], !EMPTY, error.msgError, ERROR));
                reject(error.msgError)
            })
        });
    }
}

export const getServiceGroupListCatalog = function (catalogID, isLoadSearchBy) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": catalogID,
                "isLoadSearchBy": isLoadSearchBy
            };
            dispatch(start_get_service_group_list_catalog())
            apiBase(API_GET_SERVICE_GROUP_LIST_CATALOG, METHOD.POST, body).then((response) => {
                console.log("getServiceGroupListCatalog success", response);
                if (
                    helper.hasProperty(response, 'object') &&
                    helper.isArray(response.object) &&
                    response.object.length > 0
                ) {
                    dispatch(stop_get_get_service_group_list_catalog(response.object));
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                    dispatch(stop_get_get_service_group_list_catalog([], false, 'Không tìm thấy chi tiết danh mục Catalog', true));
                }
            }).catch(error => {
                console.log("getServiceGroupListCatalog error", error);
                dispatch(stop_get_get_service_group_list_catalog([], !EMPTY, error.msgError, ERROR));
                reject(error.msgError)
            })
        });
    }
}

export const checksSatusTicketService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                ticketID: data.TICKETID,
                airtimeTransactionID: data.AIRTIMETRANSACTIONID
            };
            apiBase(API_CHECK_STATUS_TICKET_SERVICE, METHOD.POST, body).then((response) => {
                console.log("checksSatusTicketService success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("checksSatusTicketService error", error);
                reject(error.msgError)
            })
        });
    }
}


export const createTicletEditImeiServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                ticketType: 2,
                imei: data.imei

            };
            apiBase(API_CREATE_TICKET_SERVICE, METHOD.POST, body).then((response) => {
                console.log("createTiclet success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_insert_and_createticket(response.object, false, '', false));
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("createTiclet error", error);
                reject(error.msgError)
            })
        });
    }
}



export function clearCustomerBankAccountInfo() {
    return (dispatch, getState) => {
        dispatch(stop_query_customer_banks_account({}));
    }
}

export const cleardDataTicket = () => ({
    type: CLEAR_DATA_STICKET
});

const start_query_customer_banks_account = () => ({
    type: START_QUERY_CUSTOMER_BANK_ACCOUNT
});
const stop_query_customer_banks_account = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_QUERY_CUSTOMER_BANK_ACCOUNT,
    data,
    isEmpty,
    description,
    isError
});

const start_get_fee_cashin = () => ({
    type: START_INSERT_AND_CREATE_TICKET
});
const stop_get_fee_cashin = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_FEE_CASHIN,
    data,
    isEmpty,
    description,
    isError
});

const start_insert_and_createticket = () => ({
    type: START_INSERT_AND_CREATE_TICKET
});
const stop_insert_and_createticket = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_INSERT_AND_CREATE_TICKET,
    data,
    isEmpty,
    description,
    isError
});

const stop_modify_cart_request = (data) => ({
    type: STOP_MODIFY_CART,
    data
})

export const start_add_to_sale_order_cart = () => {
    return {
        type: START_ADD_TO_SALE_ORDER_CART,
    };
};

export const stop_add_to_sale_order_cart = (dataSaleOrderCart = {}) => {
    return {
        type: STOP_ADD_TO_SALE_ORDER_CART,
        dataSaleOrderCart
    };
};

const start_get_data_pay_bill = () => ({
    type: START_GET_DATA_PAY_BILL
});
const stop_get_data_pay_bill = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_DATA_PAY_BILL,
    data,
    isEmpty,
    description,
    isError
});

const start_get_catalog_list_collection = () => ({
    type: START_GET_CATALOG_LIST_COLLECTION
});
const stop_get_catalog_list_collection = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_CATALOG_LIST_COLLECTION,
    data,
    isEmpty,
    description,
    isError
});

  start_get_service_group_list_catalog = () => ({
    type: START_GET_SERVICE_GROUP_LIST_CATALOG
});
const stop_get_get_service_group_list_catalog = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SERVICE_GROUP_LIST_CATALOG,
    data,
    isEmpty,
    description,
    isError
});


const update_item_catalog = (
    data
) => ({
    type: UPDATE_ITEM_CATALOG,
    data
});

export const updateItemCatalog = (data) => {
    return function (dispatch, getState) {
        dispatch(update_item_catalog(data));
    }
}

