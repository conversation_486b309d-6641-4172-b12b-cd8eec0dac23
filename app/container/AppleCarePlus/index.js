import {
    <PERSON><PERSON>,
    FlatList,
    Keyboard,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { constants } from '../../constants';
import PackageInfo from './components/PackageInfo';
import ModalCalendar from '../PaymentTransactions/component/ModalCalendar';
import {
    BarcodeCamera,
    BaseLoading,
    Icon,
    MyText,
    hideBlockUI,
    showBlockUI
} from '../../components';
import { COLORS } from '../../styles';
import moment from 'moment';
import SearchInput from './components/SearchInput';
import {
    checkImeiPackage,
    searchImeiPackage,
    searchWaterFilterPackage
} from './action';
import { connect, useSelector } from 'react-redux';
import { translate } from '../../translations';
import { dateHelper, helper } from '../../common';
import { bindActionCreators } from 'redux';
import * as actionShoppingCartCreator from '../ShoppingCart/action';
import * as actionPouchCreator from '../PouchRedux/action';
import * as actionDetailCreator from '../Detail/action';
import { TYPE_OF_CARE_PACKAGE } from '../Detail/constants';
import PackageAppleCareItem from './components/PackageAppleCareItem';
import PackageWaterFilterItem from './components/PackageWaterFilterItem';
import DropdownSearch from './components/DropDownSearch';
import { Button } from '../AnKhangNew/components';

const { APPLE_CARE, WATER_FILTER } = TYPE_OF_CARE_PACKAGE;

const DATA_TYPES = [
    {
        value: 'saleorder',
        label: 'Mã đơn hàng',

    },
    {
        value: 'IMEI',
        label: 'Mã IMEI',

    },
    {
        value: 'phone_number',
        label: 'Số điện thoại',

    },
    {
        value: 'product',
        label: 'Sản phẩm',

    }
];

const AppleCarePlus = ({
    actionShoppingCart,
    route,
    navigation,
    actionDetail,
    actionPouch,
    searchInfo,
    productSearch
}) => {
    const [keyword, setKeyword] = useState('');
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [isShowCalendar, setIsShowCalendar] = useState(false);
    const [isVisibleBarcode, setIsVisibleBarcode] = useState(false);

    const [statePackage, setStatePackage] = useState({
        isFetching: false,
        isError: false,
        description: '',
        isEmpty: false,
        data: []
    });

    const [typeSearch, setTypeSearch] = useState('phone_number');
    const [isShowGetMore, setShowGetMore] = useState(false);
    const [placeholder, setPlaceHolder] = useState("")

    const originData = useRef([]);

    const { storeID, languageID, moduleID } = useSelector(
        (state) => state.userReducer
    );

    const { saleScenarioTypeID } = useSelector(
        (state) => state.specialSaleProgramReducer
    );

    const baseRequest = {
        moduleID,
        languageID,
        loginStoreId: storeID,
        saleScenarioTypeID
    };

    const { typePackage } = route.params ?? { typePackage: '' };

    const placeholderSearch = getPlaceholder(typePackage, placeholder);

    const handleAPISearchImeiPackage = (code, isScan) => {
        Keyboard.dismiss(code, isScan);
        const contentSearch = isScan ? code : keyword;
        if (contentSearch.length > 0) {
            const body = {
                ...baseRequest,
                keyword: contentSearch,
                fromDate: fromDate,
                toDate: toDate,
                productId: productSearch.productID
            };
            setStatePackage({
                isFetching: true,
                isError: false,
                description: '',
                isEmpty: false,
                data: []
            });
            searchImeiPackage(body)
                .then((response) => {
                    if (response.length > 0) {
                        setStatePackage({
                            isFetching: false,
                            isError: false,
                            description: '',
                            isEmpty: false,
                            data: response
                        });
                    } else {
                        setStatePackage({
                            isFetching: false,
                            isError: false,
                            description: 'Không tìm thấy thông tin sản phẩm.',
                            isEmpty: true,
                            data: []
                        });
                    }
                })
                .catch((msgError) => {
                    setStatePackage({
                        isFetching: false,
                        isError: true,
                        description: msgError,
                        isEmpty: false,
                        data: []
                    });
                });
        }
    };

    const handleAPISearchWaterFilterPackage = ({
        code = "",
        isScan = false,
        isGetMore = false
    }) => {
        const contentSearch = isScan ? code : keyword;
        if (originData.current.length > 0) {
            const newDataSearch = originData.current.filter(
                (item) =>
                    item.PRODUCTID.includes(`${contentSearch}`) ||
                    helper
                        .removeAccent(item.PRODUCTNAME)
                        .toLowerCase()
                        .includes(
                            helper.removeAccent(contentSearch).toLowerCase()
                        )
            );
            if (helper.IsNonEmptyArray(newDataSearch)) {
                setStatePackage({
                    ...statePackage,
                    data: newDataSearch,
                    isEmpty: false,
                    isError: false
                });
            } else {
                setStatePackage({
                    ...statePackage,
                    data: newDataSearch,
                    isError: false,
                    isEmpty: true,
                    description:
                        'Không tìm thấy thông tin gói dịch vụ map với sản phẩm ở khai báo gợi ý mua thêm. Vui lòng kiểm tra lại.'
                });
            }
        } else {
            if (typeSearch != "product" && helper.IsEmptyString(keyword)) return Alert.alert('', 'Vui lòng nhập từ khoá tìm kiếm!');
            const body = {
                ...baseRequest,
                productId: productSearch.productID,
                keytype: typeSearch,
                keyword: keyword,
                isGetMore: isGetMore,

            };
            setStatePackage({
                isFetching: true,
                isError: false,
                description: '',
                isEmpty: false,
                data: []
            });
            searchWaterFilterPackage(body)
                .then(({ DATA, SHOWGETMORE }) => {
                    setShowGetMore(SHOWGETMORE)
                    if (helper.IsNonEmptyArray(DATA)) {
                        setStatePackage({
                            isFetching: false,
                            isError: false,
                            description: '',
                            isEmpty: false,
                            data: DATA
                        });
                        if (typeSearch == "product") {
                            originData.current = DATA;
                        }
                    }
                    else {
                        const content = SHOWGETMORE ? `Không tìm thấy thông tin ${placeholder} trong vòng 14 ngày. Vui lòng chọn XEM THÊM để xem thêm thông tin.` : "Không tìm thấy thông tin sản phẩm"
                        setStatePackage({
                            isFetching: false,
                            isError: false,
                            isEmpty: true,
                            description: content,
                            data: []
                        });
                    }
                })
                .catch((msgError) => {
                    setStatePackage({
                        isFetching: false,
                        isError: true,
                        description: msgError,
                        isEmpty: false,
                        data: []
                    });
                });
        }
    };

    const handleAPICheckAppleCarePackage = (item) => async () => {
        const { dataCart } = route.params ?? { dataCart: {} };
        const newProductServicePackageBO =
            dataCart.mainProduct?.extensionProperty?.ProductServicePackageBO;
        if (helper.IsEmptyObject(newProductServicePackageBO))
            return Alert.alert('', 'Không có thông tin gói!');
        const { ServicePackID } = newProductServicePackageBO;
        const { SALEORDERID, IMEI, SALEORDERDETAILID } = item;
        const body = {
            ...baseRequest,
            servicePackId: ServicePackID,
            getAddInReq: {
                imei: IMEI,
                saleOrderId: SALEORDERID,
                saleOrderDetailId: SALEORDERDETAILID
            }
        };
        try {
            showBlockUI();
            const packageDetail = await checkImeiPackage(body);
            const message = packageDetail?.resultExtraData?.message;
            if (!!message) {
                Alert.alert(translate('common.notification_uppercase'), message, [

                    {
                        text: "Ok",
                        style: 'default',
                        onPress: () => {
                            dataCart.mainProduct.extensionProperty.ProductServicePackageBO.ExtensionAddInPackage =
                                packageDetail;
                            handleAPIAddToCart(dataCart);
                        }
                    }
                ]);
            }
            else {
                dataCart.mainProduct.extensionProperty.ProductServicePackageBO.ExtensionAddInPackage =
                    packageDetail;
                handleAPIAddToCart(dataCart);
            }
        } catch (msgError) {
            const content = msgError || 'Đã có lỗi xảy ra!';
            Alert.alert(translate('common.notification_uppercase'), content, [
                {
                    text: translate('common.btn_skip'),
                    style: 'cancel',
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: 'default',
                    onPress: handleAPICheckAppleCarePackage(item)
                }
            ]);
        }
    };

    const handleAPICheckWaterFilterPackage = (item) => async () => {
        const { dataCart } = route.params ?? { dataCart: {} };
        const newProductServicePackageBO =
            dataCart.mainProduct?.extensionProperty?.ProductServicePackageBO;
        if (helper.IsEmptyObject(newProductServicePackageBO))
            return Alert.alert('', 'Không có thông tin gói!');
        const { ServicePackID } = newProductServicePackageBO;
        const { PRODUCTID, IMEI, SALEORDERDETAILID, SALEORDERID } = item;
        const body = {
            ...baseRequest,
            servicePackId: ServicePackID,
            getAddInReq: {
                productId: PRODUCTID,
                saleOrderDetailId: SALEORDERDETAILID,
                saleOrderId: SALEORDERID,
                imei: IMEI
            }
        };
        try {
            showBlockUI();
            const packageDetail = await checkImeiPackage(body);
            dataCart.mainProduct.extensionProperty.ProductServicePackageBO.ExtensionAddInPackage =
                packageDetail;
            handleAPIAddToCart(dataCart);
        } catch (msgError) {
            const content = msgError || 'Đã có lỗi xảy ra!';
            Alert.alert(translate('common.notification_uppercase'), content, [
                {
                    text: translate('common.btn_skip'),
                    style: 'cancel',
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: 'default',
                    onPress: handleAPICheckWaterFilterPackage(item)
                }
            ]);
        }
    };

    const handleAPIAddToCart = (data) => {
        showBlockUI();
        actionShoppingCart
            .addToShoppingCart(data)
            .then((response) => {
                hideBlockUI();
                if (!!data.delivery.customerPhone) {
                    actionDetail.set_phone_number_create_at_home(
                        data.delivery.customerPhone
                    );
                }
                actionDetail.reset_package_services();
                actionDetail.reset_map_content_promotion_input();
                // Push để mount ShoppingCart
                navigation.push('ShoppingCart');
                actionPouch.setDataCartApply(response);
            })
            .catch((error) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => handleAPIAddToCart(data)
                        }
                    ]
                );
            });
    };

    const handleTryAgains = () => {
        switch (typePackage) {
            case APPLE_CARE:
                return handleAPISearchImeiPackage();
            case WATER_FILTER:
                return handleAPISearchWaterFilterPackage({ isGetMore: isShowGetMore });
            default:
                return null;
        }
    };

    const handleOnSubmit = () => {
        switch (typePackage) {
            case APPLE_CARE:
                return handleAPISearchImeiPackage();
            case WATER_FILTER:
                return handleAPISearchWaterFilterPackage({});
            default:
                return null;
        }
    };

    const handleScanBarcode = (code) => {
        switch (typePackage) {
            case APPLE_CARE:
                return handleAPISearchImeiPackage(code, isScan = true);
            case WATER_FILTER:
                return handleAPISearchWaterFilterPackage({
                    code,
                    isScan: true,
                });
            default:
                return null;
        }
    };

    useEffect(() => {
        setKeyword("")
        originData.current = []
        setStatePackage({
            isFetching: false,
            isError: false,
            description: '',
            isEmpty: false,
            data: []
        });
        const placeholderTypeSearch = DATA_TYPES.find((_item) => _item.value == typeSearch).lable || ""
        setPlaceHolder(placeholderTypeSearch)
        if (typePackage == WATER_FILTER && typeSearch == "product") {
            handleAPISearchWaterFilterPackage({});
        }
    }, [typeSearch]);



    const renderItems = ({ item, index }) => {
        switch (typePackage) {
            case APPLE_CARE:
                return (
                    <PackageAppleCareItem
                        onPress={handleAPICheckAppleCarePackage(item)}
                        item={item}
                        index={index}
                    />
                );
            case WATER_FILTER:
                return (
                    <PackageWaterFilterItem
                        onPress={handleAPICheckWaterFilterPackage(item)}
                        item={item}
                        index={index}
                    />
                );
            default:
                return null;
        }
    };

    return (
        <View
            style={{
                flex: 1,
                width: constants.width - 20,
                alignSelf: 'center'
            }}>
            <PackageInfo
                info={{
                    imageUrl: searchInfo.imageUrl,
                    productName: productSearch.productName
                }}
            />
            <View style={{ paddingVertical: 10 }}>
                <MyText
                    addSize={1}
                    style={{ fontWeight: 'bold' }}
                    text={'Xác định thiết bị áp dụng gói dịch vụ'}
                />
            </View>
            {typePackage !== WATER_FILTER && (
                <View style={{ paddingBottom: 10 }}>

                    <TouchableOpacity
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            backgroundColor: COLORS.bgFFFFFF,
                            borderRadius: 5,
                            width: constants.width - 20,
                            paddingHorizontal: 5,
                            borderWidth: 1,
                            borderColor: COLORS.bdDDDDDD,
                            height: 44,
                            alignSelf: 'center'
                        }}
                        onPress={() => setIsShowCalendar(true)}>
                        <MyText
                            style={{
                                width: '87%',
                                paddingHorizontal: 5
                            }}
                            text={`${moment(fromDate).format(
                                'DD/MM/YYYY'
                            )} - ${moment(toDate).format('DD/MM/YYYY')} `}
                        />
                        <Icon
                            iconSet="Feather"
                            name="calendar"
                            style={{
                                fontSize: 30,
                                color: COLORS.ic2C8BD7
                            }}
                        />
                    </TouchableOpacity>
                </View>
            )}
            {
                typePackage !== APPLE_CARE && <DropdownSearch
                    typeSearch={typeSearch}
                    dataTypes={DATA_TYPES}
                    onChange={({ value }) => {
                        setTypeSearch(value)
                    }}
                />
            }

            <SearchInput
                onSubmit={handleOnSubmit}
                inputText={keyword}
                onChangeText={(text) => {
                    setKeyword(text);
                }}
                onClearText={() => {
                    setKeyword('');
                }}
                placeholder={placeholderSearch}
                showSearchIcon
                showBarcodeIcon
                onShowBarcode={() => {
                    setIsVisibleBarcode(true);
                }}
            />
            <View
                style={{
                    flex: 1
                }}>
                <BaseLoading
                    isLoading={statePackage.isFetching}
                    isEmpty={statePackage.isEmpty}
                    textLoadingError={statePackage.description}
                    isError={statePackage.isError}
                    onPressTryAgains={handleTryAgains}
                    content={
                        <FlatList
                            showsHorizontalScrollIndicator={false}
                            showsVerticalScrollIndicator={false}
                            data={statePackage.data}
                            keyExtractor={(item, index) => `${index}`}
                            renderItem={renderItems}
                            ListFooterComponent={
                                <View style={{ height: 20 }}></View>
                            }
                        />
                    }
                />
                {
                    isShowGetMore && typeSearch != "product" && <View
                        style={[

                            {
                                marginBottom: constants.heightBottomSafe > 0 ? 30 : 48,
                                paddingHorizontal: 18,
                                alignItems: 'center',
                                flexDirection: 'row',
                                paddingTop: 8,
                                justifyContent: "center"
                            }
                        ]}>
                        <Button
                            style={{
                                alignItems: 'center',
                                borderRadius: 5,
                                opacity: 1,
                                backgroundColor: COLORS.bg1E88E5,
                                width: constants.width / 2,
                                height: 50
                            }}
                            color={COLORS.bgFFFFFF}
                            disabled={statePackage.isFetching}
                            onPress={() => {
                                handleAPISearchWaterFilterPackage({
                                    code: "",
                                    isScan: true,
                                    isGetMore: true
                                })
                            }
                            }
                            text={"Xem thêm"}
                            isLoading={statePackage.isFetching}
                        />
                    </View>
                }

            </View>


            <ModalCalendar
                isVisible={isShowCalendar}
                hideModal={() => {
                    setIsShowCalendar(false);
                }}
                startDate={fromDate}
                endDate={toDate}
                setDate={(day) => {
                    setFromDate(new Date(day.startDate));
                    setToDate(new Date(day.endDate));
                }}
                minDate={dateHelper.formatDateYYYYMMDD(
                    new Date(new Date().setDate(new Date().getDate() - 7))
                )}
                maxDate={dateHelper.formatDateYYYYMMDD()}
                hideExtraDays
                disableMonthChange
                firstDay={1}
                hideDayNames={false}
                showWeekNumbers={false}
                enableSwipeMonths={false}
            />
            {isVisibleBarcode && (
                <BarcodeCamera
                    isVisible={isVisibleBarcode}
                    closeCamera={() => {
                        setIsVisibleBarcode(false);
                    }}
                    resultScanBarcode={(code) => {
                        setKeyword(code);
                        handleScanBarcode(code);
                        setIsVisibleBarcode(false);
                    }}
                />
            )}
        </View>
    );
};

const mapStateToProps = function (state) {
    return {
        searchInfo: state.saleReducer.productSearch,
        productSearch: state.detailReducer.productSearch
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionShoppingCart: bindActionCreators(
            actionShoppingCartCreator,
            dispatch
        ),
        actionDetail: bindActionCreators(actionDetailCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(AppleCarePlus);

// const DATA = [
//     {
//         "SALEORDERID": "00907SO24060519513",
//         "ENTITYID": "1245",
//         "IMEI": "C04JMAAG0F0Q",
//         "MODELID": "DOROGYNEF",
//         "MODELNAME": "DOROGYNE F",
//         "OUTPUTSTOREID": "907",
//         "OUTPUTSTORENAME": "Tên kho - Dữ liệu gáng cứng để test",
//         "OUTPRODUCTDATE": "",
//         "PRODUCTID": "0131491001175",
//         "PRODUCTNAME": "Samsung Galaxy Note 9 N960 Brown",
//         "INVENTORYSTATUSID": "1",
//         "INVENTORYSTATUSNAME": "Mới",
//         "SALEORDERDETAILID": "1B0E69E62365577AE0638C0C010A4402",
//         "SALEPRICEVAT": "10000000",
//         "SALEPRICE": "9090909.0909",
//         "VAT": "3",
//         "VATPERCENT": "100"
//     },
//     {
//         "SALEORDERID": "00907SO24060519513",
//         "ENTITYID": "1245",
//         "IMEI": "C04JMAAG0F0Q",
//         "MODELID": "DOROGYNEF",
//         "MODELNAME": "DOROGYNE F",
//         "OUTPUTSTOREID": "907",
//         "OUTPUTSTORENAME": "Tên kho - Dữ liệu gáng cứng để test",
//         "OUTPRODUCTDATE": "",
//         "PRODUCTID": "0131491001175",
//         "PRODUCTNAME": "Samsung Galaxy Note 9 N960 Brown",
//         "INVENTORYSTATUSID": "1",
//         "INVENTORYSTATUSNAME": "Mới",
//         "SALEORDERDETAILID": "1B0E69E62365577AE0638C0C010A4402",
//         "SALEPRICEVAT": "10000000",
//         "SALEPRICE": "9090909.0909",
//         "VAT": "3",
//         "VATPERCENT": "100"
//     },
//     {
//         "SALEORDERID": "00907SO24060519513",
//         "ENTITYID": "1245",
//         "IMEI": "C04JMAAG0F0Q",
//         "MODELID": "DOROGYNEF",
//         "MODELNAME": "DOROGYNE F",
//         "OUTPUTSTOREID": "907",
//         "OUTPUTSTORENAME": "Tên kho - Dữ liệu gáng cứng để test",
//         "OUTPRODUCTDATE": "",
//         "PRODUCTID": "0131491001175",
//         "PRODUCTNAME": "Samsung Galaxy Note 9 N960 Brown",
//         "INVENTORYSTATUSID": "1",
//         "INVENTORYSTATUSNAME": "Mới",
//         "SALEORDERDETAILID": "1B0E69E62365577AE0638C0C010A4402",
//         "SALEPRICEVAT": "10000000",
//         "SALEPRICE": "9090909.0909",
//         "VAT": "3",
//         "VATPERCENT": "100"
//     },
//     {
//         "SALEORDERID": "00907SO24060519513",
//         "ENTITYID": "1245",
//         "IMEI": "C04JMAAG0F0Q",
//         "MODELID": "DOROGYNEF",
//         "MODELNAME": "DOROGYNE F",
//         "OUTPUTSTOREID": "907",
//         "OUTPUTSTORENAME": "Tên kho - Dữ liệu gáng cứng để test",
//         "OUTPRODUCTDATE": "",
//         "PRODUCTID": "0131491001175",
//         "PRODUCTNAME": "Samsung Galaxy Note 9 N960 Brown",
//         "INVENTORYSTATUSID": "1",
//         "INVENTORYSTATUSNAME": "Mới",
//         "SALEORDERDETAILID": "1B0E69E62365577AE0638C0C010A4402",
//         "SALEPRICEVAT": "10000000",
//         "SALEPRICE": "9090909.0909",
//         "VAT": "3",
//         "VATPERCENT": "100"
//     },
//     {
//         "SALEORDERID": "00907SO24060519513",
//         "ENTITYID": "1245",
//         "IMEI": "C04JMAAG0F0Q",
//         "MODELID": "DOROGYNEF",
//         "MODELNAME": "DOROGYNE F",
//         "OUTPUTSTOREID": "907",
//         "OUTPUTSTORENAME": "Tên kho - Dữ liệu gáng cứng để test",
//         "OUTPRODUCTDATE": "",
//         "PRODUCTID": "0131491001175",
//         "PRODUCTNAME": "Samsung Galaxy Note 9 N960 Brown",
//         "INVENTORYSTATUSID": "1",
//         "INVENTORYSTATUSNAME": "Mới",
//         "SALEORDERDETAILID": "1B0E69E62365577AE0638C0C010A4402",
//         "SALEPRICEVAT": "10000000",
//         "SALEPRICE": "9090909.0909",
//         "VAT": "3",
//         "VATPERCENT": "100"
//     }
// ]

const getPlaceholder = (type, placeholder) => {
    switch (type) {
        case APPLE_CARE:
            return 'Nhập IMEI, SĐT, Mã Đơn hàng';
        case WATER_FILTER:
            return `Nhập ${placeholder}`;
        default:
            return '';
    }
};
