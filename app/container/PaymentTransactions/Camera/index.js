import React, { useEffect, useRef, useState } from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { COLORS } from '@styles';
import {
    Alert,
    Keyboard,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
    Modal
} from 'react-native';
import {
    BaseLoading,
    Button,
    hideBlockUI,
    MyText,
    showBlockUI,
    Picker,
    Icon,
    TitleInput,
    CaptureCamera
} from '@components';
import { constants, API_CONST, ENUM } from '@constants';
import { translate } from '@translate';
import { helper } from '@common';

import { launchImageLibrary } from 'react-native-image-picker';
import ImageProcess from '../component/ImageProcess';
import { getImageCDN } from '../../ShoppingCart/action';
const { FILE_PATH: { PAYMENT_TRANSACTIONS } } = ENUM;

const Camera = ({ navigation, route, handleGetImage, lstImage }) => {
    const [strImage, setStrImage] = useState([
        {
            AttachmentID: 0,
            FileTypeID: 4,
            UrlFile: ''
        }
    ]);
    const [currentPicture, setCurrentPicture] = useState('');
    const [currentStrImageIndex, setCurrentStrImageIndex] = useState(0);
    const [isVisibleCamera, setIsVisibleCamera] = useState(false);
    useEffect(() => {
        if (!helper.IsNonEmptyString(strImage[0])) {
            handleGetImage(strImage);
        }
    }, [strImage]);
    useEffect(() => {
        if (!helper.IsNonEmptyArray(lstImage)) {
            setStrImage([
                {
                    AttachmentID: 0,
                    FileTypeID: 4,
                    UrlFile: ''
                }
            ]);
        }
    }, [lstImage]);
    const takePicture = (photo) => {
        setIsVisibleCamera(false);
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper
                .resizeImage(photo)
                .then(({ path, uri, size, name }) => {
                    const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: PAYMENT_TRANSACTIONS });
                    getImageCDN(body)
                        .then((response) => {
                            const remoteURI =
                                API_CONST.API_GET_IMAGE_CDN_NEW +
                                response[0];
                            switch (currentPicture) {
                                case 'strImage':
                                    const newContract = [...strImage];
                                    newContract[
                                        currentStrImageIndex
                                    ].UrlFile = remoteURI;
                                    setStrImage(newContract);
                                    break;
                                default:
                                    break;
                            }
                            hideBlockUI();
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('uploadPicture', error);
                            Alert.alert("", error.msgError, [
                                {
                                    text: "OK",
                                    onPress: () => {
                                        hideBlockUI();
                                    },
                                },
                            ]);
                        });
                })
                .catch((error) => {
                    hideBlockUI();
                    console.log('resizeImage', error);
                });
        } else {
            hideBlockUI();
        }
    };
    const selectPicture = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                setIsVisibleCamera(false);
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper
                        .resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: PAYMENT_TRANSACTIONS });
                            getImageCDN(body)
                                .then((response) => {
                                    const remoteURI =
                                        API_CONST.API_GET_IMAGE_CDN_NEW +
                                        response[0];
                                    switch (currentPicture) {
                                        case 'strImage':
                                            const newContract = [...strImage];
                                            newContract[
                                                currentStrImageIndex
                                            ].UrlFile = remoteURI;
                                            setStrImage(newContract);
                                            break;
                                        default:
                                            break;
                                    }
                                    hideBlockUI();
                                })
                                .catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                    Alert.alert("", error.msgError, [
                                        {
                                            text: "OK",
                                            onPress: () => {
                                                hideBlockUI();
                                            },
                                        },
                                    ]);
                                });
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('resizeImage', error);
                        });
                } else {
                    hideBlockUI();
                }
            }
        );
    };

    return (
        <SafeAreaView
            style={{
                flex: 1,
                width: constants.width,
                alignItems: 'center'
            }}>
            <View>
                <View
                    // eslint-disable-next-line react-native/no-color-literals
                    style={{
                        width: constants.width - 30,
                        height: 175
                    }}>
                    {strImage.map((item, index) => (
                        <ImageProcess
                            onCamera={() => {
                                setIsVisibleCamera(true);
                                setCurrentPicture('strImage');
                                setCurrentStrImageIndex(index);
                            }}
                            urlImageLocal={item.UrlFile}
                            urlImageRemote={item.UrlFile}
                            deleteImage={() => {
                                strImage.length > 1
                                    ? setStrImage(
                                        strImage.filter(
                                            (ct) => ct.UrlFile != item.UrlFile
                                        )
                                    )
                                    : setStrImage([
                                        {
                                            AttachmentID: 0,
                                            FileTypeID: 4,
                                            UrlFile: ''
                                        }
                                    ]);
                            }}
                            key={index.toString()}
                        />
                    ))}
                </View>
            </View>

            <CaptureCamera
                isVisibleCamera={isVisibleCamera}
                takePicture={takePicture}
                closeCamera={() => {
                    setIsVisibleCamera(false);
                }}
                selectPicture={selectPicture}
            />
        </SafeAreaView>
    );
};
export default Camera;
