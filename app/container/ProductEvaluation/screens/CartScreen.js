import {
    View,
    Keyboard,
    TouchableOpacity,
    SafeAreaView,
    Alert,
    Modal,
    Image,
    BackHandler
} from 'react-native';
import React, { useState, useEffect } from 'react';
import { constants, STORAGE_CONST, API_CONST, ENUM } from '@constants';
import {
    Button,
    CaptureCamera,
    hideBlockUI,
    Icon,
    ImageCDN,
    MyText,
    ScanIdentity,
    showBlockUI,
    TitleInput
} from '@components';
import { translate } from '@translate';
import { COLORS } from '@styles';
import { helper, storageHelper, convertHtml2Pdf, dateHelper } from '@common';
// eslint-disable-next-line import/no-unresolved
import { launchImageLibrary } from 'react-native-image-picker';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import <PERSON>Viewer from 'react-native-image-zoom-viewer';
import { useSelector } from 'react-redux';
import RadioGender from '../../ShoppingCart/component/Radio/RadioGender';
import {
    getUser,
    insertExchangeReceipt,
    printExchangeReceipt
} from '../actions';
import { Section, Title, ReturnProductsSection, RadioBox } from '../components';
import { COLOR, ID_CARD_SIDE, ID_CARD_TYPE } from '../constants';
import { IconField } from '../components/ExchangeReceiptItem';
import { getInfoByImage } from '../../InstallmentManager/action';
import { hasValidPartnerEco } from '../helper';
const { FILE_PATH: { PRODUCT_EVALUATION } } = ENUM;

const ButtonConfirm = ({ onPress }) => {
    return (
        <View
            style={{
                width: constants.width,
                alignItems: 'center',
                justifyContent: 'center',
                paddingVertical: 8
            }}>
            <Button
                text="TẠO PHIẾU"
                styleContainer={{
                    paddingHorizontal: 40,
                    borderRadius: 4,
                    backgroundColor: COLORS.btn1E88E5,
                    height: 44
                }}
                styleText={{
                    color: COLORS.txtFFFFFF,
                    fontSize: 14,
                    fontWeight: 'bold'
                }}
                onPress={onPress}
            />
        </View>
    );
};

const CartScreen = ({ route, navigation }) => {
    const { exchangeReceiptBO, exchangeProduct, shouldGoBack } = route.params;
    const {
        storeID: loginStoreId,
        moduleID,
        languageID
    } = useSelector((state) => state.userReducer);
    const { dataFromPartner } = useSelector(
        (state) => state.productEvaluationReducer
    );
    const {
        ExchangeProgramName,
        MaxQuantityOfApplyProduct,
        cus_ExchangeReceiptDetailBOList,
        ExchangeProgramType
    } = exchangeReceiptBO;
    console.log(
        'cus_ExchangeReceiptDetailBOList',
        cus_ExchangeReceiptDetailBOList
    );
    console.log('exchangeProduct', exchangeProduct);
    const {
        ExchangeProductID,
        ExchangeProductName,
        ExchangeProductImage,
        TotalDiscountValue
    } = exchangeProduct;
    const [gender, setGender] = useState(true);
    const [customerInfo, setCustomerInfo] = useState({
        customerPhone: '',
        customerName: '',
        customerIDCard: '',
        customerAddress: '',
        ConfirmedUser: ''
    });
    const [isVisibleOpenCamera, setIsVisibleOpenCamera] = useState(false);
    const [uriImages, setUriImages] = useState([]);
    const [isVisibleScan, setIsVisibleScan] = useState(false);
    const [userConfirm, setUserConfirm] = useState(null);
    const [indexSelected, setIndexSelected] = useState(0);
    const [token, setToken] = useState('');

    const [isError, setIsError] = useState(false);
    const [idCardType, setIdCardType] = useState(1);
    const userInfo = useSelector((state) => state.userReducer);

    const imgSource =
        isError || !ExchangeProductImage
            ? require('../../../../assets/smartphone.png')
            : { uri: ExchangeProductImage };

    console.log('uriImages', uriImages);

    const getToken = () => {
        storageHelper
            .getItem(STORAGE_CONST.ACCESS_TOKEN)
            .then((accessToken) => {
                setToken(accessToken);
            })
            .catch((error) => {
                console.log('authen error', error);
            });
    };
    useEffect(getToken, []);

    useEffect(() => {
        const backHandler = BackHandler.addEventListener(
            'hardwareBackPress',
            () => {
                if (shouldGoBack) {
                    navigation.goBack();
                } else {
                    navigation.navigate('ReturnProductScreen');
                }
                return true;
            }
        );

        return () => backHandler.remove();
    }, []);

    const propsImage = {
        width: constants.width,
        height: constants.height,
        props: {
            source: {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            },
            style: {
                resizeMode: 'contain'
            }
        }
    };

    const [isShowImageViewer, setIsShowImageViewer] = useState(false);
    const [imgIndex, setImgIndex] = useState(0);

    const { customerPhone, customerName, customerIDCard, customerAddress } =
        customerInfo;

    const getInfoCustomerByImage = (uriImage) => {
        return new Promise((resolve, reject) => {
            const typeImageDetect = helper.getTypeImage(idCardType, indexSelected);
            if (typeImageDetect != 0) {
                const bodyFromData = new FormData();
                bodyFromData.append('file', {
                    uri: uriImage,
                    type: 'image/jpg',
                    name: `getInfoCustomerByImage${dateHelper.getTimestamp()}`
                });
                bodyFromData.append('client_id', `MWGPOS_${userInfo.userName}`);
                bodyFromData.append('chosen_side', typeImageDetect);
                installmentBCAction.ocrCCCD(bodyFromData)
                    .then((res) => {
                        // resolve(res)
                        switch (typeImageDetect) {
                            case 1:
                                resolve({
                                    customerName: res.full_name,
                                    customerAddress: res.place_of_permanent,
                                    customerIDCard: res.id_no,
                                    idCardExpiriedDate: res.expiration_date
                                });
                                break;
                            case 2:
                                resolve({
                                    customerName: res.full_name,
                                    customerAddress: res.place_of_permanent,
                                    customerIDCard: res.id_no,
                                    idCardExpiriedDate: res.expiration_date
                                });
                                break;
                            case 3:
                                resolve({
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    idCardIssuePlace: res.erp_place_of_issue_id
                                });
                                break;
                            case 4:
                                resolve({
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    idCardIssuePlace: res.erp_place_of_issue_id
                                });
                                break;
                            default:
                                console.log(res);
                                break;
                        }
                    })
                    .catch((err) => {
                        console.log('err getInfoCustomerByImage', err);
                        reject(err);
                    });
            }
        });
    };

    const takePicture = (photo) => {
        setIsVisibleOpenCamera(false);
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper
                .resizeImage(photo)
                .then(({ uri, name }) => {
                    const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: PRODUCT_EVALUATION });
                    getImageCDN(body)
                        .then((res) => {
                            const remoteURI =
                                API_CONST.API_GET_IMAGE_CDN_NEW + res[0];
                            const newUriImages = [...uriImages];
                            newUriImages[indexSelected] = {
                                url: uri,
                                remoteURI,
                                ...propsImage
                            };
                            setUriImages(newUriImages);
                            console.log('newUriImages', newUriImages);
                            hideBlockUI();
                            if (indexSelected == 0) {
                                getInfoCustomerByImage(uri)
                                    .then((result) => {
                                        console.log(
                                            'getInfoCustomerByImage success',
                                            result
                                        );
                                        setCustomerInfo({
                                            ...customerInfo,
                                            customerAddress:
                                                result.customerAddress,
                                            customerIDCard:
                                                result.customerIDCard,
                                            customerName:
                                                result.customerName
                                        });
                                        setGender(null);
                                    })
                                    .catch((err) => {
                                        console.log(
                                            'getInfoCustomerByImage err',
                                            err
                                        );
                                    });
                            }
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('uploadPicture', error);
                        });
                })
                .catch((error) => {
                    hideBlockUI();
                    console.log('resizeImage', error);
                });
        }
    };

    const onPickerPhoto = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                if (helper.hasProperty(response, 'uri')) {
                    helper
                        .resizeImage(response)
                        .then(({ uri, name }) => {
                            const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: PRODUCT_EVALUATION });
                            getImageCDN(body)
                                .then((res) => {
                                    const remoteURI =
                                        API_CONST.API_GET_IMAGE_CDN_NEW + res[0];
                                    const newUriImages = [...uriImages];
                                    newUriImages[indexSelected] = {
                                        url: uri,
                                        remoteURI,
                                        ...propsImage
                                    };
                                    setUriImages(newUriImages);
                                    console.log('newUriImages', newUriImages);
                                    hideBlockUI();
                                    setIsVisibleOpenCamera(false);
                                    if (indexSelected === 0) {
                                        getInfoCustomerByImage(uri)
                                            .then((result) => {
                                                console.log(
                                                    'getInfoCustomerByImage success',
                                                    result
                                                );
                                                setCustomerInfo({
                                                    ...customerInfo,
                                                    customerAddress:
                                                        result.customerAddress,
                                                    customerIDCard:
                                                        result.customerIDCard,
                                                    customerName:
                                                        result.customerName
                                                });
                                                setGender(null);
                                            })
                                            .catch((err) => {
                                                console.log(
                                                    'getInfoCustomerByImage err',
                                                    err
                                                );
                                            });
                                    }
                                })
                                .catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                });
                        })
                        .catch((error) => {
                            console.log('resizeImage', error);
                        });
                }
            }
        );
    };

    const validateInfo = () => {
        // if (!helper.IsNonEmptyString(uriImages[0]?.url)) {
        //     Alert.alert('', 'Vui lòng chụp hình CMND/CCCD mặt trước');
        //     return false;
        // }
        // if (!helper.IsNonEmptyString(uriImages[1]?.url)) {
        //     Alert.alert('', 'Vui lòng chụp hình CMND/CCCD mặt sau');
        //     return false;
        // }
        if (gender == null) {
            Alert.alert('', translate('shoppingCart.validation_gender'));
            return false;
        }
        if (helper.IsEmptyString(customerPhone)) {
            Alert.alert('', translate('detail.please_enter_phone_number'));
            return false;
        }
        if (!helper.isValidatePhone(customerPhone)) {
            Alert.alert('', 'Vui lòng nhập số điện thoại hợp lệ');
            return false;
        }
        if (helper.IsEmptyString(customerName)) {
            Alert.alert('', translate('activeSimManager.message_3'));
            return false;
        }
        if (helper.IsEmptyString(customerIDCard)) {
            Alert.alert('', translate('activeSimManager.message_16'));
            return false;
        }
        if (userConfirm === null) {
            Alert.alert(
                '',
                'Vui lòng quét mã danh tính của nhân viên duyệt phiếu này.'
            );
            return false;
        }
        const regExpIDCard9 = new RegExp(/^\d{9}$/);
        const regExpIDCard12 = new RegExp(/^\d{12}$/);
        const isValidateIDCard9 = regExpIDCard9.test(customerIDCard);
        const isValidateIDCard12 = regExpIDCard12.test(customerIDCard);
        if (!isValidateIDCard9 && !isValidateIDCard12) {
            Alert.alert('', 'CMND/CCCD không hợp lệ');
            return false;
        }
        return true;
    };

    const getInfoUser = (userName) => {
        if (!helper.IsNonEmptyString(userName)) {
            Alert.alert(
                translate('common.notification'),
                translate('instalmentManager.cannot_read_id_code'),
                [
                    {
                        text: 'OK'
                    }
                ]
            );
            return;
        }
        showBlockUI();
        getUser({
            "keyword": userName,
            "username": userName,
        })
            .then((res) => {
                setUserConfirm(res[0]);
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel'
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => getInfoUser(userName)
                        }
                    ]
                );
            })
            .finally(hideBlockUI);
    };

    const openCamera = (index) => {
        setIsVisibleOpenCamera(true);
        setIndexSelected(index);
    };

    const deleteImage = (index) => {
        const newUriImages = [...uriImages];
        newUriImages[index] = {};
        setUriImages(newUriImages);
    };

    const getBase64Reprint = (exchangeReceiptID) => {
        const body = {
            loginStoreId,
            languageID,
            moduleID,
            exchangeReceiptID
        };
        printExchangeReceipt(body)
            .then(async (html) => {
                const base64PDF = await convertHtml2Pdf(html);
                hideBlockUI();
                navigation.reset({
                    index: 1,
                    routes: [
                        { name: 'ProductEvaluation' },
                        { name: 'PrintScreen', params: { base64: base64PDF } }
                    ]
                });
            })
            .catch((msgError) => {
                Alert.alert('Thông báo', msgError, [
                    {
                        text: 'Bỏ qua',
                        onPress: () => {
                            hideBlockUI();
                            navigation.reset({
                                index: 0,
                                routes: [{ name: 'ProductEvaluation' }]
                            });
                        }
                    },
                    {
                        text: 'Thử lại',
                        onPress: () => getBase64Reprint(exchangeReceiptID)
                    }
                ]);
            });
    };
    const handleSave = () => {
        if (validateInfo()) {
            showBlockUI();
            exchangeReceiptBO.CustomerName = customerName;
            exchangeReceiptBO.Gender = gender ? 1 : 0;
            exchangeReceiptBO.CustomerPhone = customerPhone;
            exchangeReceiptBO.CustomerAddress = customerAddress;
            exchangeReceiptBO.CustomerIDCard = customerIDCard;
            exchangeReceiptBO.ReviewedUser = userConfirm?.username;
            exchangeReceiptBO.cus_ExchangeReceiptVCBO = {
                "VoucherconcernID": dataFromPartner?.data?.[0]?.ESVNID || null
            };
            const urlFileBOList = uriImages.map(({ remoteURI }, index) => ({
                Description:
                    index === 0 ? 'CMND/CCCD mặt trước' : 'CMND/CCCD mặt sau',
                UrlFile: remoteURI
            }));
            console.log('ExchangeBO', exchangeReceiptBO);
            console.log('urlFileBOList', urlFileBOList);
            insertExchangeReceipt({
                loginStoreId,
                languageID,
                moduleID,
                urlFileBOList,
                exchangeReceiptBO
            })
                .then(({ ExchangeReceiptID }) => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        `Tạo phiếu thẩm định thành công!\nMã phiếu ${ExchangeReceiptID}`,
                        [
                            {
                                text: 'BỎ QUA',
                                style: 'default',
                                onPress: () => {
                                    hideBlockUI();
                                    navigation.reset({
                                        index: 0,
                                        routes: [{ name: 'ProductEvaluation' }]
                                    });
                                }
                            },
                            {
                                text: 'IN PHIẾU',
                                style: 'default',
                                onPress: () =>
                                    getBase64Reprint(ExchangeReceiptID)
                            }
                        ]
                    );
                })
                .catch((error) => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        error,
                        [
                            {
                                text: 'OK',
                                style: 'default',
                                onPress: hideBlockUI
                            }
                        ]
                    );
                });
        }
    };

    return (
        <View style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
            <KeyboardAwareScrollView
                style={{
                    flex: 1
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}>
                <SafeAreaView
                    style={{
                        flex: 1
                    }}>
                    <Title name={ExchangeProgramName} />
                    {
                        (hasValidPartnerEco(dataFromPartner) && ExchangeProgramType === 3) ? null : <Section title="Sản phẩm đổi sang">
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center'
                                }}>
                                <Image
                                    style={{
                                        height: 60,
                                        width: 60
                                    }}
                                    resizeMode="contain"
                                    source={imgSource}
                                    onError={() => setIsError(true)}
                                />
                                <MyText
                                    style={{
                                        fontWeight: 'bold',
                                        flex: 1,
                                        marginLeft: 5
                                    }}
                                    addSize={1}
                                    text={`${ExchangeProductName} - ${ExchangeProductID}`}
                                />
                            </View>
                        </Section>
                    }

                    <Section title={`Danh sách sản phẩm thu cũ (${cus_ExchangeReceiptDetailBOList.length}/${MaxQuantityOfApplyProduct} máy)`}>
                        <ReturnProductsSection
                            data={cus_ExchangeReceiptDetailBOList}
                            totalMoney={TotalDiscountValue}
                        />
                    </Section>

                    {/* <Section title="Chụp hình CMND/CCCD">
                        <View style={{ justifyContent: 'space-between' }}>
                            <RadioBox
                                idCardType={idCardType}
                                onSwitch={(type) => {
                                    setIdCardType(type);
                                }}
                            />
                        </View>
                        <ImageAdjust
                            onViewImage={() => {
                                setImgIndex(0);
                                setIsShowImageViewer(true);
                            }}
                            title={translate(
                                'editSaleOrder.take_front_ID_picture'
                            )}
                            onCamera={() => {
                                openCamera(0);
                            }}
                            onDelete={() => {
                                deleteImage(0);
                            }}
                            uri={uriImages[0]?.url}
                        />
                        <ImageAdjust
                            onViewImage={() => {
                                setImgIndex(1);
                                setIsShowImageViewer(true);
                            }}
                            title="Chụp hình CMND/CCCD mặt sau:"
                            onCamera={() => {
                                openCamera(1);
                            }}
                            onDelete={() => {
                                deleteImage(1);
                            }}
                            uri={uriImages[1]?.url}
                        />
                    </Section> */}
                    <Section title="Thông tin khách hàng">
                        <View
                            style={{
                                width: constants.width - 20,
                                flexDirection: 'row',
                                marginBottom: 4,
                                justifyContent: 'space-between'
                            }}>
                            <RadioGender
                                gender={gender}
                                onSwitchGender={(value) => {
                                    setGender(value);
                                }}
                            />
                        </View>

                        <TitleInput
                            title={translate('shoppingCart.text_input_phone')}
                            isRequired
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                                backgroundColor: COLORS.bgFFFFFF
                            }}
                            placeholder={translate(
                                'shoppingCart.placeholder_phone'
                            )}
                            value={customerPhone}
                            onChangeText={(text) => {
                                const regExpPhone = new RegExp(/^[0]\d{0,10}$/);
                                const isValidate =
                                    regExpPhone.test(text) || text === '';
                                if (isValidate) {
                                    setCustomerInfo({
                                        ...customerInfo,
                                        customerPhone: text
                                    });
                                }
                            }}
                            keyboardType="numeric"
                            returnKeyType="done"
                            blurOnSubmit
                            width={constants.width - 20}
                            height={40}
                            maxLength={10}
                            clearText={() => {
                                setCustomerInfo({
                                    ...customerInfo,
                                    customerPhone: ''
                                });
                            }}
                            key="customerPhone"
                        />

                        <TitleInput
                            title={translate('shoppingCart.text_input_name')}
                            isRequired
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                                backgroundColor: COLORS.bgFFFFFF
                            }}
                            placeholder={translate(
                                'shoppingCart.placeholder_name'
                            )}
                            value={customerName}
                            onChangeText={(text) => {
                                if (helper.isValidateCharVN(text)) {
                                    setCustomerInfo({
                                        ...customerInfo,
                                        customerName: text
                                    });
                                }
                            }}
                            keyboardType="default"
                            returnKeyType="done"
                            blurOnSubmit
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setCustomerInfo({
                                    ...customerInfo,
                                    customerName: ''
                                });
                            }}
                            key="customerName"
                            maxLength={50}
                        />
                        {/* Nhập căn cước công dân */}
                        <TitleInput
                            title={translate('shoppingCart.text_input_cccd')}
                            isRequired
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                                backgroundColor: COLORS.bgFFFFFF
                            }}
                            placeholder={translate(
                                'shoppingCart.placeholder_cccd'
                            )}
                            value={customerIDCard}
                            onChangeText={(text) => {
                                if (helper.isValidateCharVN(text)) {
                                    setCustomerInfo({
                                        ...customerInfo,
                                        customerIDCard: text
                                    });
                                }
                            }}
                            keyboardType="default"
                            returnKeyType="done"
                            blurOnSubmit
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setCustomerInfo({
                                    ...customerInfo,
                                    customerIDCard: ''
                                });
                            }}
                            key="customerIDCard"
                            maxLength={12}
                        />

                        <TitleInput
                            title={translate('shoppingCart.text_input_address')}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                                backgroundColor: COLORS.bgFFFFFF
                            }}
                            textAlignVertical="center"
                            underlineColorAndroid="transparent"
                            placeholder={translate(
                                'shoppingCart.placeholder_address'
                            )}
                            value={customerAddress}
                            onChangeText={(text) => {
                                if (helper.isValidateCharVN(text)) {
                                    setCustomerInfo({
                                        ...customerInfo,
                                        customerAddress: text
                                    });
                                }
                            }}
                            keyboardType="default"
                            returnKeyType="done"
                            blurOnSubmit
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            multiline
                            height={40}
                            clearText={() => {
                                setCustomerInfo({
                                    ...customerInfo,
                                    customerAddress: ''
                                });
                            }}
                            key="customerAddress"
                            maxLength={300}
                        />
                    </Section>
                    <Section title="Nhân viên duyệt">
                        {userConfirm == null ? (
                            <View>
                                <ProductSearch
                                    ids={['35425']}
                                    callAction={({ code }) => {
                                        setCustomerInfo({
                                            ...customerInfo,
                                            ConfirmedUser: code ?? ''
                                        });
                                        getInfoUser(code);
                                    }}
                                />
                                {/* <View
                                    style={{
                                        alignItems: 'center',
                                        marginVertical: 10
                                    }}>
                                    <TouchableOpacity
                                        onPress={() => {
                                            setIsVisibleScan(true);
                                        }}>
                                        <Icon
                                            iconSet="MaterialCommunityIcons"
                                            name="qrcode-scan"
                                            color={COLORS.icFFD400}
                                            size={80}
                                        />
                                    </TouchableOpacity>
                                </View> */}
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        backgroundColor: COLOR.BLUE_3,
                                        padding: 5,
                                        borderRadius: 5
                                    }}>
                                    <Icon
                                        iconSet="MaterialCommunityIcons"
                                        name="information"
                                        color={COLOR.BLUE_1}
                                        size={24}
                                    />
                                    <MyText style={{ flex: 1, marginLeft: 5 }}>
                                        Vui lòng yêu cầu nhân viên quản lý hoặc
                                        nhân viên có quyền xác nhận tạo yêu cầu
                                        thẩm định máy cũ mở app{' '}
                                        <MyText
                                            style={{
                                                fontWeight: 'bold'
                                            }}
                                            text="MWG"
                                        />
                                        , bấm vào logo{' '}
                                        <MyText
                                            style={{
                                                fontWeight: 'bold'
                                            }}
                                            text="Thế Giới Di Động"
                                        />{' '}
                                        ở góc trái màn hình, chọn vào{' '}
                                        <MyText
                                            style={{
                                                fontWeight: 'bold'
                                            }}
                                            text="Mã danh tính"
                                        />
                                    </MyText>
                                </View>
                            </View>
                        ) : (
                            <UserInfo
                                data={userConfirm}
                                onDelete={() => setUserConfirm(null)}
                            />
                        )}
                    </Section>
                    <View styles={{ flex: 1, alignItems: 'center' }}>
                        <ButtonConfirm onPress={handleSave} />
                    </View>
                </SafeAreaView>
            </KeyboardAwareScrollView>
            <CaptureCamera
                isVisibleCamera={isVisibleOpenCamera}
                takePicture={takePicture}
                closeCamera={() => {
                    setIsVisibleOpenCamera(false);
                }}
                selectPicture={onPickerPhoto}
            />
            {isVisibleScan && (
                <ScanIdentity
                    isVisible={isVisibleScan}
                    closeCamera={() => {
                        setIsVisibleScan(false);
                    }}
                    resultScanBarcode={(barcode) => {
                        setCustomerInfo({
                            ...customerInfo,
                            ConfirmedUser: barcode ?? ''
                        });
                        setIsVisibleScan(false);
                        getInfoUser(barcode);
                    }}
                />
            )}
            <Modal visible={isShowImageViewer} transparent>
                <ImageViewer
                    renderHeader={() => (
                        <TouchableOpacity
                            style={{
                                position: 'absolute',
                                right: 5,
                                top: constants.heightTopSafe + 5,
                                zIndex: 100
                            }}
                            onPress={() => setIsShowImageViewer(false)}>
                            <Icon
                                iconSet="Ionicons"
                                name="close"
                                color={COLORS.bgFFFFFF}
                                size={40}
                            />
                        </TouchableOpacity>
                    )}
                    imageUrls={uriImages}
                    index={imgIndex}
                    enableSwipeDown
                    onCancel={() => setIsShowImageViewer(false)}
                />
            </Modal>
        </View>
    );
};

export default CartScreen;

const ImageAdjust = ({ onCamera, onDelete, uri, title, onViewImage }) => {
    const isImage = helper.IsNonEmptyString(uri);
    const imageWidth = constants.width / 2;
    return (
        <View
            style={{
                width: constants.width,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 10
            }}>
            <MyText
                style={{
                    color: COLORS.txt333333,
                    fontWeight: 'bold',
                    fontStyle: 'italic',
                    width: constants.width - 20,
                    marginBottom: 6
                }}
                addSize={-1.5}
                text={title}>
                <MyText
                    style={{
                        color: COLORS.txtFF0000
                    }}
                    addSize={-1.5}
                    text="*"
                />
            </MyText>
            {isImage ? (
                <TouchableOpacity
                    onPress={onViewImage}
                    style={{
                        width: imageWidth,
                        height: imageWidth,
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        marginHorizontal: 1,
                        backgroundColor: COLORS.bgF5F5F5
                    }}>
                    <View
                        style={{
                            width: imageWidth,
                            height: imageWidth,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                        <ImageCDN
                            style={{
                                width: imageWidth - 10,
                                height: imageWidth - 10
                            }}
                            uri={uri}
                            resizeMode="contain"
                        />
                    </View>
                    <TouchableOpacity
                        style={{
                            padding: 5,
                            justifyContent: 'center',
                            alignItems: 'center',
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            backgroundColor: COLORS.btnFFFFFF,
                            borderWidth: 1,
                            borderColor: COLORS.bdF5F5F5
                        }}
                        onPress={onDelete}>
                        <MyText
                            style={{
                                color: COLORS.txtD0021B
                            }}
                            text={translate('editSaleOrder.btn_delete')}
                        />
                    </TouchableOpacity>
                </TouchableOpacity>
            ) : (
                <TouchableOpacity
                    style={{
                        width: imageWidth,
                        height: imageWidth,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: COLORS.btnF5F5F5,
                        marginHorizontal: 1
                    }}
                    onPress={onCamera}>
                    <Icon
                        iconSet="Ionicons"
                        name="ios-camera"
                        color={COLORS.icFFB23F}
                        size={60}
                    />
                </TouchableOpacity>
            )}
        </View>
    );
};

const ProductSearch = ({ ids, callAction }) => (
    <View>
        {ids.map((id) => (
            <Button
                key={id}
                onPress={() =>
                    callAction({
                        code: id,
                        productRequest: null,
                        isScan: true
                    })
                }
                outline
                text="Mã danh tính test"
                styleContainer={{
                    padding: 10,
                    borderColor: COLORS.bg000000,
                    borderWidth: 1,
                    borderRadius: 5
                }}
            />
        ))}
    </View>
);

const UserInfo = ({ data, onDelete }) => {
    const { username, fullname, positionname, mobi } = data;
    return (
        <View>
            <TouchableOpacity
                style={{
                    position: 'absolute',
                    top: -5,
                    right: -10,
                    paddingRight: 10,
                    backgroundColor: COLORS.btnDCE1E1,
                    paddingBottom: 3,
                    paddingLeft: 5,
                    borderBottomLeftRadius: 5,
                    zIndex: 100
                }}
                onPress={onDelete}>
                <IconField
                    title="Huỷ"
                    iconSet="Ionicons"
                    name="trash"
                    color={COLORS.icFF0000}
                />
            </TouchableOpacity>
            <MyText text="Mã NV: ">
                <MyText
                    style={{ fontWeight: 'bold' }}
                    text={`${username} - ${fullname}`}
                />
            </MyText>
            <MyText text="Chức vụ: ">
                <MyText style={{ fontWeight: 'bold' }} text={positionname} />
            </MyText>
            <MyText text="Số điện thoại: ">
                <MyText style={{ fontWeight: 'bold' }} text={mobi} />
            </MyText>
        </View>
    );
};
