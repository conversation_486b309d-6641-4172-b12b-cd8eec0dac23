import {
    View,
    Keyboard,
    SafeAreaView,
    TouchableWithoutFeedback,
    FlatList,
    Alert,
    TouchableOpacity
} from 'react-native';
import React, { useEffect, useState, useRef } from 'react';
import { COLORS } from '@styles';
import { helper } from '@common';
import {
    BarcodeCamera,
    BaseLoading,
    Icon,
    ImageURI,
    MyText,
    showBlockUI,
    hideBlockUI
} from '@components';
import { constants } from '@constants';
import { useDispatch, useSelector } from 'react-redux';
import { translate } from '@translate';
import { InputSearch, ProductStateModal } from '../components';
import { addReturnProduct, getReturnProductStates, searchProduct } from '../actions';
import { COLOR } from '../constants';
import { useNavigation } from '@react-navigation/native';
import { hasValidPartnerEco } from '../helper';

const SearchReturnProductScreen = ({ route }) => {
    const {
        storeID: loginStoreId,
        moduleID,
        languageID
    } = useSelector((state) => state.userReducer);
    const navigation = useNavigation();

    const [stateSearch, setStateSearch] = useState({
        keyword: '',
        dataSearch: [],
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false,
        isFocus: false,
        isVisibleScan: false
    });

    const [isVisibleModal, setIsVisibleModal] = useState(false);
    const [productInfo, setProductInfo] = useState({});
    const [productStates, setProductStates] = useState([]);
    const editableInput = useRef(true);

    const { IMEI, ExchangeProgramID, ExchangeProgramType } = route.params;
    const dispatch = useDispatch();
    const timeoutSearchRef = useRef(null);
    const { saleScenarioTypeID } = useSelector(
        (state) => state.specialSaleProgramReducer
    );
    const { dataFromPartner } = useSelector((state) => state.productEvaluationReducer)

    const isNonEmptyKeyword = helper.IsNonEmptyString(stateSearch.keyword);
    const isClose = stateSearch.isFocus && isNonEmptyKeyword;

    const onSelectProduct = async ({ ReturningProductID, ReturningProductName, ReturningProductImage }) => {
        try {
            showBlockUI()
            const ReturnProductStates = await getReturnProductStates({
                loginStoreId,
                languageID,
                moduleID,
                exchangeProgramID: ExchangeProgramID,
                exchangeProgramType: ExchangeProgramType,
                returnProductID: ReturningProductID,
                imeiReturnProduct: IMEI
            })

            if (hasValidPartnerEco(dataFromPartner)) {
                const partnerData = dataFromPartner.data;
                const selectedItem = partnerData.find(item => item.IsSelected);
                const stateGroupID = selectedItem?.PartnerReceiptBO?.StateGroupID
                dispatch(
                    addReturnProduct({
                        IMEI,
                        productID: ReturningProductID,
                        productImage: ReturningProductImage,
                        productName: ReturningProductName,
                        saleOrderID: "",
                        saleOrderDetailID: "",
                        customerName: "",
                        customerPhone: "",
                        states: [],
                        StateGroupID: stateGroupID
                    })
                );
                navigation.goBack()
            }
            else {
                setProductStates(ReturnProductStates);
                setProductInfo({
                    productName: ReturningProductName,
                    productID: ReturningProductID,
                    productImage: ReturningProductImage
                });
                setIsVisibleModal(true);
                hideBlockUI();

            }
        } catch (error) {
            hideBlockUI();
            Alert.alert(translate('common.notification_uppercase'), error, [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: () => {
                        if (hasValidPartnerEco(dataFromPartner)) {
                            navigation.goBack()
                        }
                    }
                }
            ]);
        } finally {
            hideBlockUI()
            // test
        }

    };



    const searchKeyword = (keyword) => {
        setStateSearch({
            ...stateSearch,
            isFetching: true,
            isEmpty: false,
            description: '',
            isError: false
        });
        dispatch(searchProduct(keyword, saleScenarioTypeID, ExchangeProgramID))
            .then(({ isEmpty, description, data }) => {
                setStateSearch({
                    ...stateSearch,
                    dataSearch: data,
                    isEmpty,
                    description,
                    isFetching: false,
                    isError: false,

                });
            })
            .catch((msgError) => {
                setStateSearch({
                    ...stateSearch,
                    dataSearch: [],
                    description: msgError,
                    isFetching: false,
                    isEmpty: false,
                    isError: true
                });
            });
    };

    useEffect(() => {
        if (stateSearch.keyword.length > 2) {
            timeoutSearchRef.current = setTimeout(() => {
                searchKeyword(stateSearch.keyword);
            }, 1000);
            return () => clearTimeout(timeoutSearchRef.current);
        }
    }, [stateSearch.keyword]);
    useEffect(() => {
        if (timeoutSearchRef.current && stateSearch.isFetching) {
            clearTimeout(timeoutSearchRef.current);
        }
    }, [stateSearch.isFetching]);

    useEffect(() => {
        if (hasValidPartnerEco(dataFromPartner)) {
            const partnerData = dataFromPartner.data;
            const selectedItem = partnerData.find(item => item.IsSelected);
            const ModelName = selectedItem?.PartnerReceiptBO?.ModelName
            if (selectedItem.IsRequirePartnerReceipt) {
                editableInput.current = false
            }
            if (ModelName) {
                setStateSearch({
                    ...stateSearch,
                    keyword: ModelName.trim()
                });
            }

        }
    }, [dataFromPartner]);

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bgF5F5F5
                }}>
                <SafeAreaView
                    style={{
                        flex: 1
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            backgroundColor: COLOR.BLUE_3,
                            padding: 5,
                            borderRadius: 5,
                            margin: 5
                        }}>
                        <Icon
                            iconSet="MaterialCommunityIcons"
                            name="information"
                            color={COLOR.BLUE_1}
                            size={24}
                        />
                        <MyText style={{ flex: 1, marginLeft: 5 }}>
                            IMEI/SN{' '}
                            <MyText
                                text={IMEI.trim()}
                                style={{ fontWeight: 'bold' }}
                            />{' '}
                            của khách không phải là máy được bán ở hệ thống.
                            Chương trình này cho phép nhập máy ngoài hệ thống.
                            Bạn vui lòng chọn sản phẩm của IMEI/SN này.
                        </MyText>
                    </View>
                    <InputSearch
                        editable={editableInput.current}
                        value={stateSearch.keyword}
                        onChangeText={(text) => {
                            if (helper.isValidateCharVN(text)) {
                                setStateSearch({
                                    ...stateSearch,
                                    keyword: text
                                });
                            }
                        }}
                        clearText={() => {
                            setStateSearch({
                                ...stateSearch,
                                keyword: ''
                            });
                        }}
                        openBarcode={() => {
                            setStateSearch({
                                ...stateSearch,
                                isVisibleScan: true
                            });
                        }}
                        isClose={isClose}
                        onFocus={() => {
                            setStateSearch({
                                ...stateSearch,
                                isFocus: true
                            });
                        }}
                        onBlur={() => {
                            setStateSearch({
                                ...stateSearch,
                                isFocus: false
                            });
                        }}
                    />
                    <View
                        style={{
                            flex: 1,
                            alignItems: 'center'
                        }}>
                        <BaseLoading
                            isLoading={stateSearch.isFetching}
                            isEmpty={stateSearch.isEmpty}
                            textLoadingError={stateSearch.description}
                            isError={stateSearch.isError}
                            onPressTryAgains={() =>
                                searchKeyword(stateSearch.keyword)
                            }
                            content={
                                <FlatList
                                    data={stateSearch.dataSearch}
                                    renderItem={({ index, item }) => (
                                        <RenderProductSearch
                                            index={index}
                                            item={item}
                                            onSelectProduct={(product) => {
                                                onSelectProduct(product);
                                            }}
                                        />
                                    )}
                                    keyExtractor={(item, index) =>
                                        index.toString()
                                    }
                                    numColumns={2}
                                    showsVerticalScrollIndicator={false}
                                    showsHorizontalScrollIndicator={false}
                                    removeClippedSubviews
                                    directionalLockEnabled
                                    keyboardShouldPersistTaps="always"
                                />
                            }
                        />
                    </View>
                </SafeAreaView>
                {stateSearch.isVisibleScan && (
                    <BarcodeCamera
                        isVisible={stateSearch.isVisibleScan}
                        closeCamera={() => {
                            setStateSearch({
                                ...stateSearch,
                                isVisibleScan: false
                            });
                        }}
                        resultScanBarcode={(barcode) => {
                            setStateSearch({
                                ...stateSearch,
                                keyword: barcode,
                                isVisibleScan: false
                            });
                        }}
                    />
                )}
                {isVisibleModal && (
                    <ProductStateModal
                        isShowModal={isVisibleModal}
                        handleOnClose={() => {
                            setIsVisibleModal(false);
                        }}
                        productStates={productStates}
                        productInfo={productInfo}
                        IMEI={IMEI}
                        shouldGoBack
                    />
                )}
            </View>
        </TouchableWithoutFeedback>
    );
};

export default SearchReturnProductScreen;

const RenderProductSearch = ({ item, onSelectProduct }) => {
    const { ReturningProductName, ReturningProductImage } = item;

    return (
        <TouchableOpacity
            style={{
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: COLORS.btnFFFFFF,
                width: constants.width / 2,
                borderWidth: 2,
                borderColor: COLORS.bdF5F5F5,
                paddingVertical: 4
            }}
            onPress={() => onSelectProduct(item)}>
            {ReturningProductImage ? (
                <ImageURI
                    uri={ReturningProductImage}
                    style={{
                        width: 130,
                        height: 150
                    }}
                    resizeMode="contain"
                />
            ) : (
                <Icon
                    iconSet="MaterialIcons"
                    name="sentiment-satisfied-alt"
                    color={COLORS.icEA4C89}
                    size={120}
                />
            )}
            <MyText
                text={ReturningProductName}
                style={{
                    fontWeight: 'bold',
                    color: COLORS.txt333333,
                    textAlign: 'center'
                }}
            />
        </TouchableOpacity>
    );
};
