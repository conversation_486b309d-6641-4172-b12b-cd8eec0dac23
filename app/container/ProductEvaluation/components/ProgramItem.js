import { TouchableOpacity, View, Text, Alert } from 'react-native';
import React, { useMemo } from 'react';
import { COLORS } from '@styles';
import { MyText } from '@components';
import { useNavigation } from '@react-navigation/core';
import { useDispatch, useSelector } from 'react-redux';
import { setDataFromPartner, setExchangeProgram } from '../actions';
import { hasValidPartnerEco } from '../helper';

const getRandomColor = () => {
    // eslint-disable-next-line no-bitwise
    return `#${(((1 << 24) * Math.random()) | 0)

        .toString(16)
        .padStart(6, '0')}`;
};

const getExchangeProductTypeName = (type) => {
    switch (type) {
        case 0:
            return 'Hỗ trợ thu: Sản phẩm trong và ngoài hệ thống';
        case 1:
            return 'Hỗ trợ thu: Sản phẩm trong hệ thống';
        case 2:
            return 'Hỗ trợ thu: Sản phẩm ngoài hệ thống';
        default:
            return null;
    }
};

const PromotionItem = ({ data }) => {
    const navigation = useNavigation();
    const dispatch = useDispatch();
    const { dataFromPartner } = useSelector(
        (state) => state.productEvaluationReducer
    );

    const { ExchangeProgramID, ExchangeProgramName, ExchangeProductType, IsRequirePartnerReceipt } =
        data;

    const color = useMemo(getRandomColor, []);

    const productTypeName = useMemo(
        () => getExchangeProductTypeName(ExchangeProductType),
        [ExchangeProductType]
    );

    const handleOnPress = async () => {
        if (hasValidPartnerEco(dataFromPartner)) {
            const partnerData = dataFromPartner.data
            await dispatch(setDataFromPartner({
                data: partnerData.map(item => ({
                    ...item,
                    IsSelected: item.ExchangeProgramID === data.ExchangeProgramID
                }))
            }));
        } else if (IsRequirePartnerReceipt) {
            return Alert.alert(
                "",
                "Chương trình này có yêu cầu thông tin từ đối tác. Vui lòng chọn chương trình khác."
            );
        }

        dispatch(setExchangeProgram(data));
        navigation.navigate('ReturnProductScreen');
    };


    return (
        <TouchableOpacity
            onPress={handleOnPress}
            style={{
                backgroundColor: COLORS.bgFFFFFF,
                padding: 10,
                marginHorizontal: 10,
                marginVertical: 5,
                borderRadius: 10,
                elevation: 5,
                shadowColor: COLORS.bg000000,
                shadowOffset: {
                    width: 0,
                    height: 2
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
                flexDirection: 'row',
                alignItems: 'center'
            }}>
            <View
                style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: color,
                    width: 60,
                    height: 60,
                    borderRadius: 30
                }}>
                <Text
                    style={{
                        color: COLORS.bgFFFFFF,
                        fontWeight: 'bold',
                        fontSize: 18
                    }}
                    adjustsFontSizeToFit
                    numberOfLines={1}>
                    {ExchangeProgramID}
                </Text>
            </View>
            <View style={{ flex: 1, marginLeft: 15 }}>
                <MyText
                    style={{ fontWeight: 'bold', marginBottom: 5 }}
                    addSize={2}
                    text={ExchangeProgramName}
                />
                <MyText text={productTypeName} addSize={-1} />
            </View>
        </TouchableOpacity>
    );
};

export default PromotionItem;
