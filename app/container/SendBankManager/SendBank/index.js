import React, { useCallback, useEffect, useState } from 'react';
import {
    FlatList,
    KeyboardAvoidingView,
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View,
    Alert,
    Pressable,
    Keyboard
} from 'react-native';
import { connect } from 'react-redux';
import { constants } from '@constants';
import {
    MyText,
    Picker,
    BaseContainer,
    Icon,
    TitleInput,
    showBlockUI,
    hideBlockUI,
    ImageCDN
} from '@components';
import { COLORS } from '@styles';
import { helper, dateHelper } from '@common';
import { translate } from '@translate';
import { bindActionCreators } from 'redux';
import * as sendBankActionCreator from '../action';
import TextField from './components/TextField';
import ModalImage from './components/ModalImage';
import ModalApprove from './components/ModalApprove';
import DropdownMenu from './components/DropdownMenu';
import ModalApproveReason from './components/ModalApproveReason';
import ModalBrowse from './components/ModalBrowse';
import SearchInputFilter from './components/SearchInputFilter';
import ModalFilterSearch from './components/ModalFilterSearch';

const SendBank = ({
    navigation,
    sendBankAction,
    stateSearchList,
    dataCancelReason,
    dataApprovalStatus,
    dataBankList,
    dataSearchList,
    userName
}) => {
    const [dataList, setDataList] = useState([]);
    const [userText, setUserText] = useState('');
    const [userTextVoucher, setTextVoucher] = useState('');
    const [toDate, setToDate] = useState(new Date());
    const [fromDate, setFromDate] = useState(new Date());
    const [statusCode, setStatusCode] = useState('');
    const [dataStatus, setDataStatus] = useState();
    const [dataImage, setDataImage] = useState([]);
    const [isVisible, setIsVisible] = useState(false);
    const [isShowApprove, setIsShowApprove] = useState(false);
    const [totalId, setTotalId] = useState('');
    const [isShowReason, setIsShowReason] = useState(false);
    const [isShowBrowse, setIsShowBrowse] = useState(false);
    const [approveRequestId, setApproveRequestId] = useState('');
    const [dataReason, setDataReason] = useState([]);
    const [listBank, setListBank] = useState([]);
    const [reasonName, setReasonName] = useState('');
    const [isShowFilter, setIsShowFilter] = useState(false);
    const [pageIndex, setPageIndex] = useState(1);

    useEffect(() => {
        didMount();
    }, []);

    useEffect(() => {
        if (dataApprovalStatus) {
            setDataStatus(dataApprovalStatus);
        }
        if (dataCancelReason) {
            setDataReason(dataCancelReason);
        }
        if (dataBankList) {
            setListBank(dataBankList);
        }
        if (dataSearchList) {
            setDataList(dataSearchList);
        }
    }, [dataApprovalStatus, dataCancelReason, dataBankList, dataSearchList]);

    const didMount = async () => {
        sendBankAction.getApprovalStatus();
        sendBankAction.getCancelReason();
        sendBankAction.getBankList();
        const data = await sendBankAction.getListSendBank();
        setDataList(data);
    };

    const handleSearchList = async () => {
        Keyboard.dismiss();
        let body = {
            fromDate: fromDate,
            toDate: toDate,
            statusId: statusCode,
            userText: userText,
            userTextVoucher: userTextVoucher,
            pageIndex: 1
        };
        const data = await sendBankAction.getListSendBank(body);
        setDataList(data);
    };

    const handleSearchFilter = async (data) => {
        Keyboard.dismiss();
        setTextVoucher(data.userTextVoucher);
        setFromDate(data.fromDate);
        setToDate(data.toDate);
        setStatusCode(data.statusCode);
        setIsShowFilter(false);
        const datalist = await sendBankAction.getListSendBank(data);
        setDataList(datalist);
    };

    const loadMoreData = async () => {
        const nextPage = pageIndex + 1;
        let data = {
            fromDate: fromDate,
            toDate: toDate,
            statusId: statusCode,
            userText: userText,
            userTextVoucher: userTextVoucher,
            pageIndex: nextPage
        };
        const moreData = await sendBankAction.getListSendBank(data);
        if (moreData.length > 0) {
            setPageIndex(nextPage);
            setDataList((prevData) => [...prevData, ...moreData]);
        } else {
            setPageIndex(pageIndex);
        }
    };

    const handledAttach = async (approveRequestId) => {
        showBlockUI();
        await sendBankAction
            .getFileAttach(approveRequestId)
            .then((response) => {
                setDataImage(response);
                setIsVisible(true);
                hideBlockUI();
            })
            .catch((error) => {
                hideBlockUI();
                Alert.alert(
                    'Thông báo',
                    translate('sendbank.error_employee_file_attach', error.msgError)
                );
            });
    };

    const handledActionMenu = async (
        title,
        isCode,
        totalId,
        approveRequestID,
        status,
        approveRQDate,
        approveUserId,
        createdUser
    ) => {
        const time = new Date(approveRQDate);
        const dataTime = time.getTime() + 24 * 60 * 60 * 1000;
        showBlockUI();
        setTotalId(totalId);
        setApproveRequestId(approveRequestID);
        if (isCode == 1) {
            setTimeout(() => {
                setIsShowApprove(true);
            }, 200);
            hideBlockUI();
            return;
        } else if (isCode == 2) {
            if (dataTime < new Date().getTime()) {
                setTimeout(() => {
                    Alert.alert(
                        'Thông báo',
                        translate(
                            'sendbank.error_only_allowed_to_approve_payment_with_day'
                        ),
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    hideBlockUI();
                                }
                            }
                        ]
                    );
                }, 200);
            } else if (userName.toString() !== approveUserId.toString()) {
                return setTimeout(() => {
                    Alert.alert('Thông báo', 'Bạn không có quyền duyệt phiếu', [
                        {
                            text: 'OK',
                            onPress: () => {
                                hideBlockUI();
                            }
                        }
                    ]);
                }, 200);
            } else {
                setTimeout(() => {
                    setIsShowBrowse(true);
                }, 200);
                hideBlockUI();
            }
            return;
        } else if (isCode == 3) {
            if (
                userName.toString() === createdUser.toString() &&
                status === 'NEEDHANDLE'
            ) {
                setTimeout(() => {
                    setIsShowApprove(false);
                }, 200);
                handleCancelApprove(status, approveRequestID);
                hideBlockUI();
            } else {
                setTimeout(() => {
                    Alert.alert(
                        'Thông báo',
                        'Bạn không có quyền huỷ yêu cầu phiếu chi',
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    hideBlockUI();
                                }
                            }
                        ]
                    );
                }, 200);
            }
            return;
        }
    };

    const handleCancelApprove = async (status, approveRequestID) => {
        showBlockUI();
        const dataChangeApprove = {
            status: status,
            approveRequestId: approveRequestID
        };
        Alert.alert(
            'Thông báo',
            'Bạn xác nhận hủy phiếu yêu cầu duyệt chi tiền nộp ngân hàng này?',
            [
                {
                    text: translate('common.btn_skip'),
                    onPress: () => hideBlockUI(),
                    style: 'destructive'
                },
                {
                    text: translate('common.btn_accept'),
                    onPress: async () => {
                        await sendBankAction
                            .getCancelApprove(dataChangeApprove)
                            .then(() => {
                                Alert.alert(
                                    'Thông báo',
                                    'Cập nhật thành công',
                                    [
                                        {
                                            text: 'OK',
                                            onPress: () => {
                                                sendBankAction.getListSendBank();
                                                hideBlockUI();
                                            }
                                        }
                                    ]
                                );
                            })
                            .catch((error) => {
                                Alert.alert('Thông báo', error.msgError);
                                hideBlockUI();
                            });
                    },
                    style: 'submit'
                }
            ]
        );
    };

    const handleApprove = (reasonNote) => {
        const data = {
            reasonNote: reasonNote,
            approveRequestId: approveRequestId
        };
        showBlockUI();
        setIsShowApprove(false);
        if (reasonNote == 'WRONGBANK') {
            setTimeout(() => {
                setIsShowReason(true);
            }, 200);
            setReasonName(reasonNote);
            return;
        } else {
            sendBankAction
                .getOtherWrongbank(reasonNote, approveRequestId)
                .then((res) => {
                    if (res.error === false) {
                        Alert.alert('Thông báo', 'Cập nhật thành công', [
                            {
                                text: 'OK',
                                onPress: () => {
                                    sendBankAction.getListSendBank();
                                    hideBlockUI();
                                }
                            }
                        ]);
                    }
                })
                .catch((err) => {
                    Alert.alert('Thông báo', err.msgError);
                    hideBlockUI();
                });
        }
    };

    const handleReason = (bankId, bankAccount) => {
        setIsShowReason(false);
        sendBankAction
            .getWrongbank(reasonName, approveRequestId, bankId, bankAccount)
            .then((res) => {
                if (res.error === false) {
                    Alert.alert('Thông báo', 'Cập nhật thành công', [
                        {
                            text: 'OK',
                            onPress: () => {
                                sendBankAction.getListSendBank();
                                hideBlockUI();
                            }
                        }
                    ]);
                }
            })
            .catch((err) => {
                Alert.alert('Thông báo', err.msgError, [
                    {
                        text: 'OK',
                        onPress: () => {
                            hideBlockUI();
                        }
                    }
                ]);
            });
    };

    const handleBrowse = async () => {
        setIsShowBrowse(false);
        showBlockUI();
        await sendBankAction
            .getApproveRequests(approveRequestId)
            .then((response) => {
                Alert.alert('Thông báo', 'Cập nhật thành công', [
                    {
                        text: 'OK',
                        onPress: () => {
                            handleSearchList();
                            hideBlockUI();
                        }
                    }
                ]);
            })
            .catch((error) => {
                Alert.alert('Thông báo', error.msgError, [
                    {
                        text: 'OK',
                        onPress: () => hideBlockUI(),
                        style: 'cancel'
                    }
                ]);
            });
    };

    const Item = ({ item }) => {
        const statusMapping = {
            NEW: 'Khởi tạo',
            NEEDHANDLE: 'Chờ xử lý',
            APPROVED: 'Đã duyệt',
            CANCEL: 'Đã huỷ'
        };
        const statusColors = {
            NEW: '#6600FF',
            NEEDHANDLE: '#FF9900',
            APPROVED: '#00CC00',
            CANCEL: '#FF0000'
        };
        const noteMapping = {
            WRONGRECEIVECOMPANYNAME: 'Sai tên Tài khoản/ Số Tài Khoản nộp tiền',
            WRONGATTACHMENTFILE: 'Hình ảnh giấy nộp tiền đính kèm không đúng',
            WRONGTOTALMONEY:
                'Số tiền nộp của yêu cầu duyệt và giấy nộp tiền không khớp',
            WRONGBANK: 'Sai tên ngân hàng nộp tiền'
        };
        let menuItemNew = [
            {
                isCode: 1,
                title: 'Yêu cầu điều chỉnh',
                iconName: 'print',
                iconSet: 'FontAwesome6',
                color: COLORS.bg57a7ff,
                disabled: false
            },
            {
                isCode: 2,
                title: 'Duyệt yêu cầu',
                iconName: 'account-check',
                iconSet: 'MaterialCommunityIcons',
                color: COLORS.ic00C300,
                disabled: false
            }
        ];

        let menuItemNeedHandle = [
            {
                isCode: 3,
                title: 'Huỷ yêu cầu duyệt',
                iconName: 'trash-outline',
                iconSet: 'Ionicons',
                color: COLORS.bgFF0000,
                disabled: false
            }
        ];
        return (
            <View style={styles.vwItem}>
                <View style={styles.vwButtonAction}>
                    <View style={{ flex: 0.5, justifyContent: 'center' }}>
                        <MyText
                            style={{
                                fontWeight: '500'
                            }}
                            text={
                                translate('sendbank.requires_approval_code') +
                                ':'
                            }
                        />
                    </View>
                    <View style={styles.txButtonAction}>
                        <MyText
                            style={{
                                flex: 0.8,
                                fontSize: 15
                            }}
                            text={item.approveRequestId}
                        />
                        {item.status === 'NEW' &&
                        userName.toString() ==
                            item.approveUserId?.toString() ? (
                            <View
                                style={{
                                    flex: 0.2,
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}>
                                <DropdownMenu
                                    menu={menuItemNew}
                                    onPressItem={(title, isCode) =>
                                        handledActionMenu(
                                            title,
                                            isCode,
                                            item.totalMoney,
                                            item.approveRequestId,
                                            item.status,
                                            item.approveRQDate,
                                            item.approveUserId,
                                            item.createdUser
                                        )
                                    }
                                />
                            </View>
                        ) : null}
                        {item.status === 'NEEDHANDLE' &&
                        userName.toString() == item.createdUser?.toString() ? (
                            <View
                                style={{
                                    flex: 0.2,
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}>
                                <DropdownMenu
                                    menu={menuItemNeedHandle}
                                    onPressItem={(title, isCode) =>
                                        handledActionMenu(
                                            title,
                                            isCode,
                                            item.totalMoney,
                                            item.approveRequestId,
                                            item.status,
                                            item.approveRQDate,
                                            item.approveUserId,
                                            item.createdUser
                                        )
                                    }
                                />
                            </View>
                        ) : null}
                    </View>
                </View>
                <TextField
                    label={translate('sendbank.bank_pays_money')}
                    value={item.bankName}
                />
                <TextField
                    label={translate('sendbank.total_amount')}
                    value={helper.convertNum(item.totalMoney)}
                />
                <TextField
                    label={translate('sendbank.supermarket_requires_approval')}
                    value={item.storeName}
                />
                <TextField
                    label={translate('sendbank.staff_created')}
                    value={item.createdUserName}
                />
                <View style={styles.vwButtonAction}>
                    <View style={{ flex: 0.5, justifyContent: 'center' }}>
                        <MyText
                            style={{
                                fontWeight: '500'
                            }}
                            text={translate('sendbank.staff_approved') + ':'}
                        />
                    </View>
                    <TouchableOpacity
                        disabled={
                            item.status === 'CANCEL' ||
                            item.status === 'APPROVED' ||
                            item.changeApproveUserName
                                ? true
                                : false
                        }
                        style={styles.txButtonAction}
                        onPress={() => {
                            navigation.navigate('EditSendBank', item);
                        }}>
                        <MyText
                            style={{
                                flex: 0.8,
                                fontSize: 15,
                                fontWeight: '600',
                                color: item.changeApproveUserName
                                    ? COLORS.txt555555
                                    : COLORS.txt008848
                            }}
                            text={item.approveUserName}
                        />
                        <View
                            style={{
                                flex: 0.2,
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                            <Icon
                                iconSet="Feather"
                                name={'edit'}
                                size={18}
                                color={
                                    item.changeApproveUserName
                                        ? COLORS.txt555555
                                        : COLORS.txt008848
                                }
                            />
                        </View>
                    </TouchableOpacity>
                </View>
                <TextField
                    label={translate('sendbank.replacement_review_staff')}
                    value={
                        item.changeApproveUserName != null
                            ? item.changeApproveUserName
                            : ''
                    }
                    styleValue={{ color: COLORS.txt008848 }}
                />
                <View
                    style={{
                        width: 'auto',
                        height: 1,
                        backgroundColor: COLORS.bgDDDDDD
                    }}
                />
                <TextField
                    styleValue={{
                        color: statusColors[item.status]
                    }}
                    label={translate('sendbank.processing_status')}
                    value={statusMapping[item.status]}
                />
                <TextField
                    label={translate('sendbank.processing_times')}
                    value={dateHelper.formatTimeFULL(
                        new Date(item.approveRQDate)
                    )}
                />
                <TextField
                    label={translate('sendbank.note')}
                    value={noteMapping[item.cancelReason]}
                />
                <View
                    style={{
                        width: 'auto',
                        height: 1,
                        backgroundColor: COLORS.bgDDDDDD
                    }}
                />
                <View style={styles.vwButtonAction}>
                    <View style={{ flex: 0.5, justifyContent: 'center' }}>
                        <MyText
                            style={{
                                fontWeight: '500'
                            }}
                            text={
                                translate('sendbank.receipt_code_information') +
                                ':'
                            }
                        />
                    </View>
                    <TouchableOpacity
                        style={styles.txButtonAction}
                        onPress={() => {
                            sendBankAction.getPaymentDetail(
                                item.approveRequestId
                            );
                            navigation.navigate('DetailSendBank', item);
                        }}>
                        <MyText style={styles.txSeeMore} text={'Xem thêm'} />
                        <Icon
                            iconSet="Feather"
                            name={'eye'}
                            size={14}
                            color={COLORS.bg57a7ff}
                        />
                    </TouchableOpacity>
                </View>
                <View style={styles.vwButtonAction}>
                    <View style={{ flex: 0.5, justifyContent: 'center' }}>
                        <MyText
                            style={{
                                fontWeight: '500'
                            }}
                            text={translate('sendbank.list_attachments') + ':'}
                        />
                    </View>
                    <TouchableOpacity
                        style={styles.txButtonAction}
                        onPress={() => {
                            handledAttach(item.approveRequestId);
                        }}>
                        <MyText style={styles.txSeeMore} text={'Xem thêm'} />
                        <Icon
                            iconSet="Feather"
                            size={14}
                            color={COLORS.bg57a7ff}
                            name={'eye'}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };

    return (
        <SafeAreaView style={styles.container}>
            <SearchInputFilter
                inputText={userText}
                onChangeText={setUserText}
                onClearText={() => {
                    setUserText('');
                }}
                placeholder={'Vui lòng nhập mã số nhân viên tạo hoặc duyệt'}
                onPressFilter={() => setIsShowFilter(true)}
                onSubmit={() => handleSearchList()}
            />
            <TouchableOpacity
                style={styles.btSearch}
                onPress={() => {
                    handleSearchList();
                }}>
                <MyText style={styles.txButtonSearch} text={'Tìm kiếm'} />
            </TouchableOpacity>
            <BaseContainer
                isLoading={stateSearchList.isFetching}
                isError={stateSearchList.isError}
                isEmpty={stateSearchList.isEmpty}
                textLoadingError={stateSearchList.description}
                onPressTryAgains={() => handleSearchList()}
                content={
                    <View style={styles.vwListItem}>
                        <FlatList
                            data={dataList}
                            renderItem={({ item }) => <Item item={item} />}
                            keyExtractor={(index) => index}
                            keyboardShouldPersistTaps={'handled'}
                            showsVerticalScrollIndicator={false}
                            removeClippedSubviews={true}
                            onEndReached={() => {
                                console.log('onEndReached triggered');
                                if (dataList.length >= 10) {
                                    loadMoreData();
                                }
                            }}
                            onEndReachedThreshold={0.1}
                            ListEmptyComponent={
                                dataList.length === 0 ? (
                                    <View style={styles.vwListEmpty}>
                                        <MyText
                                            style={{
                                                fontSize: 16,
                                                fontWeight: '500',
                                                color: COLORS.txt333333
                                            }}
                                            text={translate(
                                                'sendbank.see_more'
                                            )}
                                        />
                                    </View>
                                ) : null
                            }
                        />
                    </View>
                }
            />
            {dataImage.map((item) => {
                return (
                    <ModalImage
                        isVisible={isVisible}
                        hideModal={() => setIsVisible(false)}
                        listImage={dataImage}
                    />
                );
            })}
            {!!isShowApprove && (
                <ModalApprove
                    title={'Yêu cầu điều chỉnh'}
                    listReason={dataReason}
                    isVisible={isShowApprove}
                    hideModal={() => setIsShowApprove(false)}
                    onSubmit={(reasonNote) => handleApprove(reasonNote)}
                    onCancel={() => setIsShowApprove(false)}
                />
            )}
            {!!isShowReason && (
                <ModalApproveReason
                    title={'Yêu cầu điều chỉnh'}
                    listBank={listBank}
                    isVisible={isShowReason}
                    hideModal={() => setIsShowReason(false)}
                    onSubmit={(bankId, bankAccount, totalMoney) =>
                        handleReason(bankId, bankAccount, totalMoney)
                    }
                    onCancel={() => setIsShowReason(false)}
                    totalId={totalId}
                />
            )}
            {!!isShowBrowse && (
                <ModalBrowse
                    title={'Xác nhận số tiền nộp'}
                    isVisible={isShowBrowse}
                    hideModal={() => setIsShowBrowse(false)}
                    onSubmit={(total) => handleBrowse(total)}
                    onCancel={() => setIsShowBrowse(false)}
                    totalId={totalId}
                />
            )}
            {!!isShowFilter && (
                <ModalFilterSearch
                    title={''}
                    isVisible={isShowFilter}
                    hideModal={() => {
                        setIsShowFilter(false);
                    }}
                    onPressSearch={(data) => handleSearchFilter(data)}
                />
            )}
        </SafeAreaView>
    );
};

const mapStateToProps = function (state) {
    return {
        dataSearchList: state.sendBankReducer.dataSearchList,
        stateSearchList: state.sendBankReducer.stateSearchList,
        stateCancelReason: state.sendBankReducer.stateCancelReason,
        dataApprovalStatus: state.sendBankReducer.dataApprovalStatus,
        dataCancelReason: state.sendBankReducer.dataCancelReason,
        dataBankList: state.sendBankReducer.dataBankList,
        userName: state.userReducer.userName
    };
};

const mapDispatchToProps = function (dispacth) {
    return {
        sendBankAction: bindActionCreators(sendBankActionCreator, dispacth)
    };
};

const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    vwSearch: {
        width: 'auto',
        height: 40,
        marginVertical: 8,
        marginHorizontal: 10
    },
    btModalCalendar: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 5,
        paddingHorizontal: 5,
        height: 44
    },
    vwPickerSearch: {
        flexDirection: 'row',
        height: 38,
        width: constants.width - 20,
        backgroundColor: COLORS.btnFFFFFF,
        paddingHorizontal: 10,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center'
    },
    vwInput: {
        marginTop: 10,
        height: 'auto',
        alignItems: 'center'
    },
    txInput: {
        borderRadius: 8,
        borderColor: COLORS.bdCCCCCC,
        marginBottom: 5,
        paddingHorizontal: 10,
        paddingVertical: 8,
        backgroundColor: COLORS.bgFFFFFF
    },
    vwListItem: {
        flex: 1
    },
    vwItem: {
        backgroundColor: COLORS.bgFFFFFF,
        padding: 8,
        borderRadius: 8,
        marginVertical: 8,
        marginHorizontal: 10
    },
    btSearch: {
        width: constants.width / 3,
        height: 36,
        borderRadius: 18,
        backgroundColor: COLORS.bg288AD6,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        marginVertical: 10
    },
    txButtonSearch: { color: COLORS.txtFFFFFF, fontWeight: '800' },
    txText: {
        flex: 0.5,
        fontWeight: '500'
    },
    btUserBrowser: {
        flex: 0.5,
        backgroundColor: COLORS.ic288AD6,
        borderRadius: 10,
        padding: 8
    },
    vwButtonAction: {
        flexDirection: 'row',
        backgroundColor: COLORS.bgFFFFFF,
        width: 'auto',
        borderRadius: 8,
        marginVertical: 6
    },
    txButtonAction: {
        flex: 0.5,
        borderRadius: 10,
        alignItems: 'center',
        flexDirection: 'row'
    },
    txSeeMore: {
        fontSize: 13,
        fontWeight: '600',
        color: COLORS.txt0088F2,
        marginRight: 8
    },
    btnActionFile: {
        borderRadius: 10,
        alignItems: 'center',
        flexDirection: 'row'
    },
    txFile: {
        fontSize: 12,
        fontWeight: '600',
        color: COLORS.txt0088F2,
        marginLeft: 3
    }
});

export default connect(mapStateToProps, mapDispatchToProps)(SendBank);
