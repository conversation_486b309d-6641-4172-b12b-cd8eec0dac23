import { API_CONST } from '@constants';
import { helper, dateHelper } from '@common';
import { METHOD, apiBase, EMPTY, ERROR } from '@config';
import { translate } from '@translate';

const {
    API_GET_STORE_INFO,
    API_SEARCH_LIST_SENDBANK,
    API_CHANGE_APPROVE_USER_SENDBANK,
    API_GET_USER,
    API_GET_APPROVAL_STATUS,
    API_GET_CANCEL_REASON,
    API_GET_PAYMENT_DETAILS,
    API_GET_FILE_ATTACH,
    API_APPROVE_REQ_MPOS,
    API_CANCEL_APPROVE_REQ,
    API_GET_LIST_BANK,
    // deposipt
    API_GET_BANK_CREARTE_FORM,
    API_GET_BANK_SEARCH_FORM,
    API_SEARCH_SENDBANK,
    API_GET_DAILYFUND,
    API_CREATE_SENDBANK,
    API_UPDATE_SENDBANK,
    API_DELETE_SENDBANK,
    API_CREATE_SENDBANK_APPROVE_REQ,
    API_GET_DETAIL_SENDBANK,
    API_VALIDATE_POPUP_SENDBANK_REQ,
    API_UPLOAD_IMAGE_CDN_NEW,
    API_PRINT_SALE_ORDER,
    API_CHECK_PERMISSION_SENDBANK,
    API_GET_PRINTER,
    API_PRINT_SENDBANK,
    API_CHECK_PRINT_SEND_BANK
} = API_CONST;

const START_SEARCH_LIST_SENDBANK = 'START_SEARCH_LIST_SENDBANK';
const STOP_SEARCH_LIST_SENDBANK = 'STOP_SEARCH_LIST_SENDBANK';
const RESET_SEARCH_LIST_SENDBANK = 'RESET_SEARCH_LIST_SENDBANK';
const START_CHANGE_STORE = 'START_CHANGE_STORE';
const STOP_CHANGE_STORE = 'STOP_CHANGE_STORE';
const RESET_CHANGE_STORE = 'RESET_CHANGE_STORE';
const START_SEARCH_USER = 'START_SEARCH_USER';
const STOP_SEARCH_USER = 'STOP_SEARCH_USER';
const RESET_SEARCH_USER = 'RESET_SEARCH_USER';
const START_CHANGE_APPROVE_USER = 'STARTCHANGE_APPROVE_USER';
const STOP_CHANGE_APPROVE_USER = 'STOP_CHANGE_APPROVE_USER';
const START_GET_CANCEL_REASON = 'START_GET_CANCEL_REASON';
const STOP_GET_CANCEL_REASON = 'STOP_GET_CANCEL_REASON';
const START_GET_APPROVAL_STARTS = 'START_GET_APPROVAL_STARTS';
const STOP_GET_APPROVAL_STARTS = 'STOP_GET_APPROVAL_STARTS';
const START_GET_PAYMENT_DETAILS = 'START_GET_PAYMENT_DETAILS';
const STOP_GET_PAYMENT_DETAILS = 'STOP_GET_PAYMENT_DETAILS';
const START_GET_BANKS = 'START_GET_BANKS';
const STOP_GET_BANKS = 'STOP_GET_BANKS';

// deposipt for
const START_SEARCH_LIST_DEPOSITRECEIPT = 'START_SEARCH_LIST_DEPOSITRECEIPT';
const STOP_SEARCH_LIST_DEPOSITRECEIPT = 'STOP_SEARCH_LIST_DEPOSITRECEIPT';
const START_GET_BANK_CREARTE_FORM = 'START_GET_BANK_CREARTE_FORM';
const STOP_GET_BANK_CREARTE_FORM = 'STOP_GET_BANK_CREARTE_FORM';
const START_GET_BANK_SEARCH_FORM = 'START_GET_BANK_SEARCH_FORM';
const STOP_GET_BANK_SEARCH_FORM = 'STOP_GET_BANK_SEARCH_FORM';
const START_DAILY_FUND = 'START_DAILY_FUND';
const STOP_DAILY_FUND = 'STOP_DAILY_FUND';
const START_CREATE_SEND_BANK = 'START_CREATE_SEND_BANK';
const STOP_CREATE_SEND_BANK = 'STOP_CREATE_SEND_BANK';
const START_UPDATE_SEND_BANK = 'START_UPDATE_SEND_BANK';
const STOP_UPDATE_SEND_BANK = 'STOP_UPDATE_SEND_BANK';
const START_CREATE_SEND_BANK_APPROVE = 'START_CREATE_SEND_BANK_APPROVE';
const STOP_CREATE_SEND_BANK_APPROVE = 'STOP_CREATE_SEND_BANK_APPROVE';
const START_DELETE_SEND_BANK = 'START_DELETE_SEND_BANK';
const STOP_DELETE_SEND_BANK = 'STOP_DELETE_SEND_BANK';
const START_GET_DETAIL_SEND_BANK = 'START_GET_DETAIL_SEND_BANK';
const STOP_GET_DETAIL_SEND_BANK = 'STOP_GET_DETAIL_SEND_BANK';
const START_VALIDATE_POPUP_SEND_BANK_REQ = 'START_VALIDATE_POPUP_SEND_BANK_REQ';
const STOP_VALIDATE_POPUP_SEND_BANK_REQ = 'STOP_VALIDATE_POPUP_SEND_BANK_REQ';
const START_PRINT_DEPOSIT_RECEIPT = 'START_PRINT_DEPOSIT_RECEIPT';
const STOP_PRINTE_DEPOSIT_RECEIPT = 'STOP_PRINTE_DEPOSIT_RECEIPT';
const START_GET_REPORT_PRINTER = 'START_GET_REPORT_PRINTER';
const STOP_GET_REPORT_PRINTER = 'STOP_GET_REPORT_PRINTER';

export const sendBankAction = {
    START_SEARCH_LIST_SENDBANK,
    STOP_SEARCH_LIST_SENDBANK,
    RESET_SEARCH_LIST_SENDBANK,
    START_CHANGE_STORE,
    STOP_CHANGE_STORE,
    RESET_CHANGE_STORE,
    START_SEARCH_USER,
    STOP_SEARCH_USER,
    RESET_SEARCH_USER,
    START_CHANGE_APPROVE_USER,
    STOP_CHANGE_APPROVE_USER,
    START_GET_APPROVAL_STARTS,
    STOP_GET_APPROVAL_STARTS,
    START_GET_PAYMENT_DETAILS,
    STOP_GET_PAYMENT_DETAILS,
    START_GET_CANCEL_REASON,
    STOP_GET_CANCEL_REASON,
    START_GET_BANKS,
    STOP_GET_BANKS,
    // deposipt
    START_SEARCH_LIST_DEPOSITRECEIPT,
    STOP_SEARCH_LIST_DEPOSITRECEIPT,
    START_GET_BANK_CREARTE_FORM,
    STOP_GET_BANK_CREARTE_FORM,
    START_GET_BANK_SEARCH_FORM,
    STOP_GET_BANK_SEARCH_FORM,
    START_DAILY_FUND,
    STOP_DAILY_FUND,
    START_CREATE_SEND_BANK,
    STOP_CREATE_SEND_BANK,
    START_UPDATE_SEND_BANK,
    STOP_UPDATE_SEND_BANK,
    START_CREATE_SEND_BANK_APPROVE,
    STOP_CREATE_SEND_BANK_APPROVE,
    START_DELETE_SEND_BANK,
    STOP_DELETE_SEND_BANK,
    START_GET_DETAIL_SEND_BANK,
    STOP_GET_DETAIL_SEND_BANK,
    START_VALIDATE_POPUP_SEND_BANK_REQ,
    STOP_VALIDATE_POPUP_SEND_BANK_REQ,
    START_PRINT_DEPOSIT_RECEIPT,
    STOP_PRINTE_DEPOSIT_RECEIPT,
    START_GET_REPORT_PRINTER,
    STOP_GET_REPORT_PRINTER
};

export const getListSendBank = function (data = {}) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                fromDate: data.fromDate || new Date(),
                toDate: data.toDate || new Date(),
                keyWordType: '',
                keyWordCharacter: '',
                companyId: 1,
                lstStoreId: [getState().userReducer.storeID],
                status: data.statusCode,
                pageIndex: data.pageIndex || 1,
                pageSize: 10,
                approveRequestId: data.userTextVoucher,
                createdUser: data.userText
            };
            if (body.pageIndex < 2) {
                dispatch(start_list_search_send_bank());
            }
            apiBase(API_SEARCH_LIST_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (
                        helper.IsNonEmptyArray(
                            object.lstTotalPaymentInformation
                        )
                    ) {
                        dispatch(
                            stop_list_search_send_bank(
                                object.lstTotalPaymentInformation
                            )
                        );
                        resolve(object.lstTotalPaymentInformation);
                    } else {
                        dispatch(
                            stop_list_search_send_bank(
                                [],
                                !EMPTY,
                                'Không tìm thấy giao dịch nào',
                                true
                            )
                        );
                        resolve([]);
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_list_search_send_bank(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error.msgError);
                });
        });
    };
};

export const getStoreList = function (keyword = '') {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                keyword: keyword,
                storeIDList: ''
            };
            dispatch(start_get_store_list());
            apiBase(API_GET_STORE_INFO, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        dispatch(stop_get_store_list(object));
                        resolve(object);
                    } else {
                        dispatch(
                            stop_get_store_list(
                                [],
                                !EMPTY,
                                'Không lấy được dữ liệu'
                            )
                        );
                        resolve([]);
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_get_service_receipt(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error);
                });
        });
    };
};

export const searchUser = function (keyword) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                userName: keyword,
                storeIdList: [getState().userReducer.storeID]
            };
            dispatch(start_search_user());
            apiBase(API_GET_USER, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        dispatch(stop_search_user(object));
                        resolve(object);
                    } else {
                        dispatch(
                            stop_search_user(
                                [],
                                !EMPTY,
                                'Không lấy được dữ liệu',
                                !ERROR
                            )
                        );
                        resolve([], EMPTY, 'Không lấy được dữ liệu');
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_search_user([], !EMPTY, error.msgError, ERROR)
                    );
                    reject(error);
                });
        });
    };
};

export const checkPremissionSendBank = function (keyPermisson) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                saleScenarioTypeID: 0,
                userName: getState().userReducer.userName,
                permissionKey: keyPermisson || ''
            };
            apiBase(API_CHECK_PERMISSION_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    if (response.error === false) {
                        resolve(response);
                    }
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

export const getChangeApproveUserSendbank = function (
    item,
    userId,
    userFullName
) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                lstChangeApprove: [
                    {
                        totalRow: item.totalRow,
                        realSendDate: item.realSendDate,
                        approveRequestId: item.approveRequestId,
                        updatedUserFullName: item.updatedUserFullName,
                        totalMoney: item.totalMoney,
                        bankName: item.bankName,
                        updatedDate: item.updatedDate,
                        storeId: item.storeId,
                        approveUserId: userId,
                        approveUserName: userFullName,
                        createdDate: item.createdDate,
                        createdUserName: item.createdUserName,
                        approveRQDate: item.approveRQDate,
                        voucherId: item.voucherId,
                        attached: item.attached,
                        approveRequestUserVS: item.approveRequestUserVS,
                        storeName: item.storeName,
                        approveRequestUserId: item.approveRequestUserId,
                        cancelReason: item.cancelReason,
                        status: item.status
                    }
                ]
            };
            dispatch(start_change_approve_user());
            apiBase(API_CHANGE_APPROVE_USER_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    if (!response.error) {
                        dispatch(stop_change_approve_user(response));
                        resolve('Cập nhật thành công');
                    } else {
                        dispatch(
                            stop_change_approve_user(
                                [],
                                !EMPTY,
                                'Không lấy được dữ liệu'
                            )
                        );
                        resolve('Không lấy được dữ liệu');
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_change_approve_user(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error);
                });
        });
    };
};

// API Lấy trạng thái xử lý
export const getApprovalStatus = () => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            dispatch(start_get_approval_status());
            apiBase(API_GET_APPROVAL_STATUS, METHOD.POST, {})
                .then((res) => {
                    const object = res.object;
                    if (helper.IsNonEmptyArray(object.lstApproveReqStatus)) {
                        dispatch(
                            stop_get_approval_status(object.lstApproveReqStatus)
                        );
                        resolve(object);
                    } else {
                        dispatch(
                            stop_get_approval_status(
                                [],
                                !EMPTY,
                                'Không lấy được dữ liệu',
                                !ERROR
                            )
                        );
                        resolve('Không lấy được dữ liệu');
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_get_approval_status(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error);
                });
        });
    };
};

// API lấy lý do yêu cầu điều chỉnh
export const getCancelReason = () => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0
            };
            dispatch(start_get_cancel_reason());
            apiBase(API_GET_CANCEL_REASON, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object.lstCancelReason)) {
                        dispatch(
                            stop_get_cancel_reason(object.lstCancelReason)
                        );
                        resolve(object);
                    } else {
                        dispatch(
                            stop_get_cancel_reason(
                                [],
                                !EMPTY,
                                'Không tìm thấy dữ liệu',
                                !ERROR
                            )
                        );
                        resolve('Không lấy được dữ liệu');
                    }
                })
                .catch((error) => {
                    console.log(
                        '🤜 ******* returnnewPromise ******* error:',
                        error
                    );
                    dispatch(
                        stop_get_cancel_reason(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error);
                });
        });
    };
};

// API Lấy thông tin mã phiếu nộp tiền/chi
export const getPaymentDetail = (approveRequestId) => {
    return (dispatch, getState) => {
        let body = {
            loginStoreId: getState().userReducer.storeID,
            moduleID: getState().userReducer.moduleID,
            languageID: getState().userReducer.languageID,
            saleScenarioTypeID: 0,
            approveRequestId: approveRequestId
        };
        dispatch(start_get_payment_details());
        apiBase(API_GET_PAYMENT_DETAILS, METHOD.POST, body)
            .then((response) => {
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    dispatch(stop_get_payment_details(object));
                } else {
                    dispatch(
                        stop_get_payment_details(
                            [],
                            !EMPTY,
                            'Không tìm thấy dữ liệu'
                        )
                    );
                }
            })
            .catch((error) => {
                dispatch(
                    stop_get_payment_details([], !EMPTY, error.msgError, ERROR)
                );
            });
    };
};

// API lấy danh sách file đính kèm
export const getFileAttach = (approveRequestId) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                approveRequestId: approveRequestId
            };
            apiBase(API_GET_FILE_ATTACH, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        resolve(object);
                    } else {
                        reject({ msgError: 'Không có dữ liệu file đính kèm' });
                    }
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

// API duyệt yêu cầu
export const getApproveRequests = (approveRequestId) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                userName: getState().userReducer.userName,
                approveRequestId: approveRequestId
            };
            apiBase(API_APPROVE_REQ_MPOS, METHOD.POST, body)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

// API Huỷ yêu cầu
export const getCancelApprove = (dataChangeApprove) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                saleScenarioTypeID: 0,
                lstChangeApprove: [dataChangeApprove]
            };
            apiBase(API_CANCEL_APPROVE_REQ, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

// Lấy danh sách ngân hàng
export const getBankList = () => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                storeId: getState().userReducer.storeID
            };
            dispatch(start_get_bank_list());
            apiBase(API_GET_LIST_BANK, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    console.log('🤜 ******* .then ******* object:', object);

                    if (helper.IsNonEmptyArray(object.lstBank)) {
                        console.log('🤜 ******* .then ******* object:', object);

                        dispatch(stop_get_bank_list(object.lstBank));
                        resolve(object);
                    } else {
                        dispatch(stop_get_bank_list());
                        reject({ msgError: 'Không có dữ liệu ngân hàng' });
                    }
                })
                .catch((error) => {
                    dispatch(stop_get_bank_list(object));
                    reject(error);
                });
        });
    };
};

// API Cập nhật yêu cầu điều chỉnh (Khác WRONGBANK)
export const getOtherWrongbank = (cancelReason, approveRequestId) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                cancelReason: cancelReason,
                approveRequestId: approveRequestId,
                userName: getState().userReducer.userName
            };
            apiBase(API_APPROVE_REQ_MPOS, METHOD.POST, body)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

// API Cập nhật yêu cầu điều chỉnh (WRONGBANK)
export const getWrongbank = (
    cancelReason,
    approveRequestId,
    bankId,
    receiveBankAccount
) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                cancelReason: cancelReason,
                approveRequestId: approveRequestId,
                userName: getState().userReducer.userName,
                bankId: bankId,
                receiveBankAccount: receiveBankAccount
            };
            apiBase(API_APPROVE_REQ_MPOS, METHOD.POST, body)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

export const resetListSendBank = () => {
    return (dispatch, getState) => {
        dispatch(reset_search_list_send_bank());
    };
};
export const resetChangeStore = () => {
    return (dispatch, getState) => {
        dispatch(reset_change_store());
    };
};

export const resetSearchUser = () => {
    return (dispatch, getState) => {
        dispatch(reset_search_user());
    };
};

const start_list_search_send_bank = () => {
    return {
        type: START_SEARCH_LIST_SENDBANK
    };
};

const stop_list_search_send_bank = (
    dataSearchList,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_SEARCH_LIST_SENDBANK,
        dataSearchList,
        isEmpty,
        description,
        isError
    };
};

const start_get_store_list = () => {
    return {
        type: START_CHANGE_STORE
    };
};

const stop_get_store_list = (
    dataSearch,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_CHANGE_STORE,
        dataSearch,
        isEmpty,
        description,
        isError
    };
};

const reset_search_list_send_bank = () => {
    return {
        type: RESET_SEARCH_LIST_SENDBANK
    };
};

const reset_change_store = () => {
    return {
        type: RESET_CHANGE_STORE
    };
};

const start_search_user = () => {
    return {
        type: START_SEARCH_USER
    };
};

const stop_search_user = (
    dataSearchUser,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_SEARCH_USER,
        dataSearchUser,
        isEmpty,
        description,
        isError
    };
};

const reset_search_user = () => {
    return {
        type: RESET_SEARCH_USER
    };
};

const start_change_approve_user = () => {
    return {
        type: START_CHANGE_APPROVE_USER
    };
};

const stop_change_approve_user = (
    dataApproveUser,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_CHANGE_APPROVE_USER,
        dataApproveUser,
        isEmpty,
        description,
        isError
    };
};

const start_get_cancel_reason = () => {
    return {
        type: START_GET_CANCEL_REASON
    };
};

const stop_get_cancel_reason = (
    dataCancelReason,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_CANCEL_REASON,
        dataCancelReason,
        isEmpty,
        description,
        isError
    };
};

const start_get_approval_status = () => {
    return {
        type: START_GET_APPROVAL_STARTS
    };
};

const stop_get_approval_status = (
    dataApprovalStatus,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_APPROVAL_STARTS,
        dataApprovalStatus,
        isEmpty,
        description,
        isError
    };
};

const start_get_payment_details = () => {
    return {
        type: START_GET_PAYMENT_DETAILS
    };
};

const stop_get_payment_details = (
    dataPaymentDetails,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_PAYMENT_DETAILS,
        dataPaymentDetails,
        isEmpty,
        description,
        isError
    };
};

const start_get_bank_list = () => {
    return {
        type: START_GET_BANKS
    };
};

const stop_get_bank_list = (
    dataBankList,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_BANKS,
        dataBankList,
        isEmpty,
        description,
        isError
    };
};

/// ============= Deposipt =============================

export const getListDepositReceipt = function (data = {}) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                fromDate:
                    dateHelper.formatDateYYYYMMDD(data.fromDate) +
                        'T00:00:00' || new Date() + 'T00:00:00',
                toDate:
                    dateHelper.formatDateYYYYMMDD(data.toDate) + 'T23:59:59' ||
                    new Date() + 'T23:59:59',
                sendUser: data.sendUser || '',
                bankId: data.bankId > 0 ? data.bankId : null,
                isInOutVoucher: data.isVoucher,
                sendBankType: data.sendBankType || -1,
                isLatest: data.isLatest || -1
            };
            dispatch({
                type: START_SEARCH_LIST_DEPOSITRECEIPT
            });
            apiBase(API_SEARCH_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object.lstSendBank)) {
                        dispatch(
                            stop_search_list_deposit_receipt(object.lstSendBank)
                        );
                        resolve(object);
                    } else {
                        dispatch(
                            stop_search_list_deposit_receipt(
                                [],
                                !EMPTY,
                                'Không tìm thấy dữ liệu',
                                ERROR
                            )
                        );
                        reject([]);
                    }
                })
                .catch((err) => {
                    console.log('Error: ', err);
                    dispatch(
                        stop_search_list_deposit_receipt(
                            [],
                            !EMPTY,
                            err.msgError,
                            ERROR
                        )
                    );
                    reject(err.msgError);
                });
        });
    };
};

export const getImageCDN = function (bodyFromData) {
    return new Promise((resolve, reject) => {
        apiBase(API_UPLOAD_IMAGE_CDN_NEW, METHOD.POST, bodyFromData, {
            isCustomToken: true,
            isUpload: true
        })
            .then((response) => {
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                } else {
                    reject(translate('shoppingCart.upload_image_error'));
                }
            })
            .catch((error) => {
                console.log('getImageCDN', error);
                reject(error.msgError);
            });
    });
};

export const getBankCrearteForm = () => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                storeId: getState().userReducer.storeID,
                sendBankType: 1
            };
            dispatch({ type: START_GET_BANK_CREARTE_FORM });
            apiBase(API_GET_BANK_CREARTE_FORM, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object.lstBank)) {
                        dispatch(stop_bank_create_from(object));
                        resolve(object);
                    } else {
                        dispatch(
                            stop_bank_create_from([], ERROR, 'Không có dữ liệu')
                        );
                        reject('Không có dữ liệu');
                    }
                })
                .catch((err) => {
                    dispatch(stop_bank_create_from([], ERROR, err.msgError));
                    reject(err.msgError);
                });
        });
    };
};

export const getBankSearchForm = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: data.saleScenarioTypeID || 0
            };
            dispatch({ type: START_GET_BANK_SEARCH_FORM });
            apiBase(API_GET_BANK_SEARCH_FORM, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    dispatch({
                        type: STOP_GET_BANK_SEARCH_FORM,
                        payload: object.lstBank
                    });
                    resolve(object);
                })
                .catch((err) => {
                    console.log('Error: ', err);
                    reject(err.msgError);
                });
        });
    };
};

export const getDailyFund = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                storeId: getState().userReducer.storeID,
                userName: getState().userReducer.userName,
                saleScenarioTypeID: data.saleScenarioTypeID || 0,
                currencyUnitId: data.sendType || 1
            };
            dispatch({ type: START_DAILY_FUND });
            apiBase(API_GET_DAILYFUND, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object.lstDailyFund)) {
                        dispatch(stop_get_daily_fund(object));
                        resolve();
                    } else {
                        dispatch(
                            stop_get_daily_fund(
                                [],
                                !EMPTY,
                                'Không tìm thấy dữ liệu',
                                ERROR
                            )
                        );
                        reject('Không tìm thấy dữ liệu');
                    }
                })
                .catch((err) => {
                    console.log('Error: ', err);
                    reject(err.msgError);
                });
        });
    };
};

export const createSendBank = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                storeId: getState().userReducer.storeID,
                userLogin: getState().userReducer.userName,
                userName: data.userName || '',
                sendBankType: data.sendBankType || 1,
                bankId: data.bankId || 0,
                bankAccount: data.bankAccount || 0,
                sendType: data.sendType || 0,
                currencyUnitId: data.currencyUnitId || 1,
                totalMoney: data.totalMoney || 0,
                lstDailyFund: data.lstDailyFund || []
            };
            dispatch({ type: START_CREATE_SEND_BANK });
            apiBase(API_CREATE_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    resolve(object);
                })
                .catch((err) => {
                    reject(err.msgError);
                });
        });
    };
};

export const updateSendBank = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                storeId: getState().userReducer.storeID,
                userName: getState().userReducer.userName,
                saleScenarioTypeID: data.saleScenarioTypeID || 0,
                totalMoney: data.totalMoney || 0,
                sendBankType: data.sendBankType || -1,
                lstSendBankDen: data.lstSendBankDen || []
            };
            dispatch({ type: START_UPDATE_SEND_BANK });
            apiBase(API_UPDATE_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    if (response.Object == null) {
                        resolve(response);
                    }
                })
                .catch((err) => {
                    reject(err.msgError);
                });
        });
    };
};

export const onDeleteSendBank = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                storeId: getState().userReducer.storeID,
                userName: getState().userReducer.userName,
                saleScenarioTypeID: data.saleScenarioTypeID || 0,
                lstSendBank: data.lstSendBank || []
            };
            dispatch({ type: START_DELETE_SEND_BANK });
            apiBase(API_DELETE_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    resolve(object);
                })
                .catch((err) => {
                    console.log('Error: ', err);
                    reject(err.msgError);
                });
        });
    };
};

export const createSendBankApprove = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                storeId: getState().userReducer.storeID,
                userName: getState().userReducer.userName,
                saleScenarioTypeID: data.saleScenarioTypeID || 0,
                realSendDate: data.realSendDate || '',
                approveUser: data.approveUser || '',
                totalMoney: data.totalMoney || 0,
                lstSendBank: data.lstSendBank || [],
                lstFile: data.lstFile || []
            };
            apiBase(API_CREATE_SENDBANK_APPROVE_REQ, METHOD.POST, body)
                .then((response) => {
                    resolve(response);
                })
                .catch((err) => {
                    console.log('Error: ', err);
                    reject(err.msgError);
                });
        });
    };
};

export const getDetailSendBank = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                storeId: getState().userReducer.storeID,
                userName: getState().userReducer.userName,
                createdUser: data.createdUser || '',
                sendUser: data.sendUser || '',
                createdDate: data.createdDate || '',
                inOutVoucherId: data.inOutVoucherId || null,
                approveRequestId: data.approveRequestId || '',
                sendBankId: data.sendBankId || '',
                currencyUnitId: data.currencyUnitId || 1
            };
            dispatch({ type: START_GET_DETAIL_SEND_BANK });
            apiBase(API_GET_DETAIL_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    dispatch(stop_get_detail_send_bank(object));
                    resolve(object);
                })
                .catch((err) => {
                    console.log('Error: ', err);
                    reject(err.msgError);
                });
        });
    };
};

export const validatePopupSendbank = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                storeId: getState().userReducer.storeID,
                userName: data.userName || getState().userReducer.userName,
                saleScenarioTypeID: data.saleScenarioTypeID || 0,
                lstSendBank: data.lstSendBank || []
            };
            dispatch({ type: START_VALIDATE_POPUP_SEND_BANK_REQ });
            apiBase(API_VALIDATE_POPUP_SENDBANK_REQ, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    // dispatch(stop_validate_popup_send_bank_req(object));
                    resolve(object);
                })
                .catch((err) => {
                    console.log('Error: ', err);
                    reject(err.msgError);
                });
        });
    };
};

export const onPrintsaleorder = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                storeId: getState().userReducer.storeID,
                userName: getState().userReducer.userName,
                saleOrderID: data.saleOrderID || '',
                reportContents: data.reportContents || [],
                isGetContentHTML: data.isGetContentHTML || true,
                isFitContent: data.isFitContent || true,
                isPublishEBill: data.isPublishEBill || true,
                deviceInfo: data.deviceInfo || '',
                getNewTemplate: data.getNewTemplate || true
            };
            dispatch({ type: START_PRINT_DEPOSIT_RECEIPT });
            apiBase(API_PRINT_SALE_ORDER, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    dispatch(stop_print_sale_order(object));
                    resolve(object);
                })
                .catch((err) => {
                    console.log('Error: ', err);
                    reject(err.msgError);
                });
        });
    };
};

export const getReportPrinter = function (orderTypeID = '') {
    return function (dispatch, getState) {
        let body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            orderTypeID: orderTypeID
        };
        dispatch(start_get_report_printer());
        apiBase(API_GET_PRINTER, METHOD.POST, body)
            .then((response) => {
                console.log('getReportPrinter success', response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    const dataReports = classifyReport(object);
                    dispatch(stop_get_report_printer(dataReports));
                } else {
                    dispatch(
                        stop_get_report_printer(
                            {},
                            EMPTY,
                            translate('saleOrderPayment.no_information_printer')
                        )
                    );
                }
            })
            .catch((error) => {
                console.log('getReportPrinter error', error);
                dispatch(
                    stop_get_report_printer({}, !EMPTY, error.msgError, ERROR)
                );
            });
    };
};

export const checkPrinterSendbank = (data = {}) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                lstSendBank: data.lstSendBank || []
            };
            dispatch(start_get_report_printer());
            apiBase(API_CHECK_PRINT_SEND_BANK, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (object) {
                        resolve(object);
                    } else {
                        resolve(3);
                    }
                })
                .catch((error) => {
                    reject(error.msgError);
                });
        });
    };
};

export const printSendbank = (data = {}) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                userLogin: getState().userReducer.userName,
                showInfoUser: data.showInfoUser || false,
                sendBankTypeId: data.sendBankTypeId,
                lstSendBank: data.lstSendBank || []
            };
            dispatch({ type: START_PRINT_DEPOSIT_RECEIPT });
            apiBase(API_PRINT_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    resolve(response);
                })
                .catch((err) => {
                    console.log('Error: ', err);
                    reject(err.msgError);
                });
        });
    };
};
const stop_search_list_deposit_receipt = (
    dataSearchListDeposit,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_SEARCH_LIST_DEPOSITRECEIPT,
        dataSearchListDeposit,
        isEmpty,
        description,
        isError
    };
};

const stop_get_daily_fund = (
    dataDailyFund,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_DAILY_FUND,
        dataDailyFund,
        isEmpty,
        description,
        isError
    };
};

const stop_bank_create_from = (
    dataBankCrearteForm,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_BANK_CREARTE_FORM,
        dataBankCrearteForm,
        isEmpty,
        description,
        isError
    };
};

const stop_update_send_bank = (
    dataSendBank,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_UPDATE_SEND_BANK,
        dataSendBank,
        isEmpty,
        description,
        isError
    };
};

const stop_create_send_bank_approve = (
    dataSendBankApprove,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_CREATE_SEND_BANK_APPROVE,
        dataSendBankApprove,
        isEmpty,
        description,
        isError
    };
};

const stop_get_detail_send_bank = (
    dataDetailSendBank,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_DETAIL_SEND_BANK,
        dataDetailSendBank,
        isEmpty,
        description,
        isError
    };
};

// const stop_validate_popup_send_bank_req = (
//     dataValidatePopupSendBankReq,
//     isEmpty = false,
//     description = '',
//     isError = false
// ) => {
//     return {
//         type: STOP_VALIDATE_POPUP_SEND_BANK_REQ,
//         dataValidatePopupSendBankReq,
//         isEmpty,
//         description,
//         isError
//     };
// };

const stop_print_sale_order = (
    dataPrint,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_PRINTE_DEPOSIT_RECEIPT,
        dataPrint,
        isEmpty,
        description,
        isError
    };
};

const start_get_report_printer = () => {
    return {
        type: START_GET_REPORT_PRINTER
    };
};

const stop_get_report_printer = (
    { printerRetail = [], printerVAT = [], printerCommon = [] },
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_REPORT_PRINTER,
        printerRetail,
        printerVAT,
        printerCommon,
        isEmpty,
        description,
        isError
    };
};

const classifyReport = (reports) => {
    const printerRetail = reports.filter(
        (e) => e.PRINTERTYPEID == 'InvoiceRetailPrinter'
    );
    const printerVAT = reports.filter(
        (e) => e.PRINTERTYPEID == 'VATInvoicePrinter'
    );
    const printerCommon = reports.filter(
        (e) => e.PRINTERTYPEID == 'CommonPrinter'
    );
    return { printerRetail, printerVAT, printerCommon };
};
