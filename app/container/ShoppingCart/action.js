import { Alert } from "react-native";
import { batch } from 'react-redux';
import { API_CONST, STORAGE_CONST, constants, ENUM } from '@constants';
import { helper, storageHelper, base64 } from "@common";
import { apiBase, METHOD } from '@config';
import { translate } from '@translate';
import { setShouldCallModifyAdjust, setShouldCallMultiSale } from '../AnKhangPharmacy/action';
import { reset_suggest_products } from "../Detail/action";
import { TYPE_PROFILE } from "../../constants/constants";
import { store } from "../../store"
const { AN_KHANG_PHARMACY, SALE } = ENUM.SALE_SCENARIO_TYPE;

const { CUSTOMER, COMPANY } = TYPE_PROFILE;

const {
    API_ADD_TO_SHOPPING_CART,
    SALE_ORDER_CART_PROMOTION,
    API_GET_PERMISSION_ADJUST_PRICE,
    API_UPLOAD_IMAGE_CDN,
    API_GET_DATA_CREATE_PRICE,
    API_GET_CUSTOMER_INFO,
    API_GET_COMPANY_INFO,
    API_CHECK_CREDENTIAL,
    API_CREATE_OTP,
    API_VERIFY_OTP,
    API_VERIFY_IDENTIFY,
    API_GET_MEMBER_POINT,
    API_REQUIRE_OTP,
    API_CHECK_PHONE_GIFT_VOUCHER,
    API_DETAILS_PRODUCT,
    API_GET_SIM_PACKAGES_NEW,
    API_CHECK_PACKAGE_SIM_PRICE,
    API_GET_INFO_CREATE_PRICE,
    API_GET_PAYMENTMONTHLY,
    API_GET_INFO_CREATE_PRICE_SO,
    API_GET_PROMOTION_PROFIT,
    API_CHECK_DOCUMENT_INFORMATION,
    API_GET_CUSTOMER_INFO_BARCODE,
    API_LOAD_INFO_BATCH_NO_BY_CART,
    SALE_ORDER_CART_PROMOTION_NEW,
    API_APPLY_COUPON,
    API_CHECK_PROMOTION,
    API_SEARCH_BARCODE_CART_PROMOTION,
    API_SEARCH_BARCODE_CART_PROMOTION_NEW,
    API_GET_CUSTOMER_INFO_NEW,
    API_GET_CUSTOMER_INFO_BARCODE_NEW,
    API_GET_SUGGUEST_PROMOTION,
    API_LOCK_INSTOCK_CART,
    API_GET_CUSTOMER_PROFILE,
    API_MODIFY_CUSTOMER_PROFILE,
    API_UPDATE_CUSTOMER_RECEIVE,
    API_INSERT_CUSTOMER_RECEIVE,
    API_GET_PROFILE_SO,
    API_GET_QUESTIONS,
    API_CHECK_IMEI_PROMOTION,
    API_CHECK_STUDENT_DISCOUNT,
    API_UPLOAD_IMAGE_CDN_NEW,
    API_CHECK_NUMBER_PHONE_APPLY_VOUCHER,
    API_CHECK_NUMBER_PHONE_APPLY_MEDICINE,
    API_CHECK_STATUS_APPLY_GIFT_AMOUNT,
    API_CHECK_SEND_OTP_PARTNER,
    API_SAVE_INFO_CUSTOMER,
    API_CHECK_COUPON_EXPIRED_BY_PHONE,
    API_GET_VOUCHER_CUSTOMER,
    API_GET_PRICE_NEAR_STORE
} = API_CONST;

const { PROMOTION_CONFIG } = constants;

const START_ADD_TO_SHOPPING_CART = "START_ADD_TO_SHOPPING_CART";
const STOP_ADD_TO_SHOPPING_CART = "STOP_ADD_TO_SHOPPING_CART";
const START_MODIFY_SHOPPING_CART = "START_MODIFY_SHOPPING_CART";
const STOP_MODIFY_SHOPPING_CART = "STOP_MODIFY_SHOPPING_CART";
const START_GET_CART_PROMOTION = "START_GET_CART_PROMOTION";
const STOP_GET_CART_PROMOTION = "STOP_GET_CART_PROMOTION";

const SET_DATA_CART_ADJUST = "SET_DATA_CART_ADJUST";
const START_GET_CART_ADJUST = "START_GET_CART_ADJUST";
const STOP_GET_CART_ADJUST = "STOP_GET_CART_ADJUST";
const RESET_SHOPPING_CART = "RESET_SHOPPING_CART";
const SET_DATA_LOYALTY = "SET_DATA_LOYALTY";
const RESET_CART_PROMOTION = "RESET_CART_PROMOTION";
const SET_DATA_MEMBER = "SET_DATA_MEMBER";
const SET_DATA_CART_INSTALLMENT = "SET_DATA_CART_INSTALLMENT";
const SET_DATA_VOUCHER_PROMOTION = "SET_DATA_VOUCHER_PROMOTION";
const SET_DEFAULT_CUSTOMER = "SET_DEFAULT_CUSTOMER";
const SET_SUPPLEMENT_IMAGE_CART = "SET_SUPPLEMENT_IMAGE_CART";
const SET_BATCH_NO_BY_CART = 'SET_BATCH_NO_BY_CART';
const SET_SHOULD_CALL_MODIFY_ADJUST = "SET_SHOULD_CALL_MODIFY_ADJUST";
const SET_MAP_CUSTOMER_CONFIRM_POLICY = "SET_MAP_CUSTOMER_CONFIRM_POLICY";
const RESET_MAP_CUSTOMER_CONFIRM_POLICY = "RESET_MAP_CUSTOMER_CONFIRM_POLICY";



export const shoppingCartAction = {
    START_ADD_TO_SHOPPING_CART,
    STOP_ADD_TO_SHOPPING_CART,
    START_MODIFY_SHOPPING_CART,
    STOP_MODIFY_SHOPPING_CART,
    START_GET_CART_PROMOTION,
    STOP_GET_CART_PROMOTION,

    SET_DATA_CART_ADJUST,
    START_GET_CART_ADJUST,
    STOP_GET_CART_ADJUST,
    RESET_SHOPPING_CART,
    SET_DATA_LOYALTY,
    RESET_CART_PROMOTION,
    SET_DATA_MEMBER,
    SET_DATA_CART_INSTALLMENT,
    SET_DATA_VOUCHER_PROMOTION,
    SET_DEFAULT_CUSTOMER,
    SET_SUPPLEMENT_IMAGE_CART,
    SET_BATCH_NO_BY_CART,
    SET_SHOULD_CALL_MODIFY_ADJUST,
    SET_MAP_CUSTOMER_CONFIRM_POLICY,
    RESET_MAP_CUSTOMER_CONFIRM_POLICY
};

const ERROR = true;
const EMPTY = true;

export const lockInstockCart = function (dataCart) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const saleOrderDetails = dataCart?.SaleOrderDetails ?? [];
            const requestObjectArr = [];
            helper.getObjectArrLock(requestObjectArr, saleOrderDetails);
            saleOrderDetails.forEach(product => {
                const { saleSaleOrders, saleDeliverySaleOrders, Quantity } = product;
                helper.getObjectArrLock(requestObjectArr, saleSaleOrders, Quantity);
                helper.getObjectArrLock(requestObjectArr, saleDeliverySaleOrders, Quantity);
            });
            if (helper.IsNonEmptyArray(requestObjectArr)) {
                let body = {
                    "loginStoreId": getState().userReducer.storeID,
                    "languageID": getState().userReducer.languageID,
                    "moduleID": getState().userReducer.moduleID,
                    "requestType": "gen_guiIdlock",
                    "requestObjectArr": requestObjectArr
                };
                apiBase(API_LOCK_INSTOCK_CART, METHOD.POST, body, { setTimeOut: 5000 }).then((response) => {
                    console.log("lockInstockCart success", response);
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        helper.setObjectArrInstock(object, dataCart.SaleOrderDetails);
                        dataCart.SaleOrderDetails.forEach(product => {
                            const { saleSaleOrders, saleDeliverySaleOrders } = product;
                            helper.setObjectArrInstock(object, saleSaleOrders);
                            helper.setObjectArrInstock(object, saleDeliverySaleOrders);
                        });
                    }
                    resolve(dataCart);
                }).catch(error => {
                    console.log("lockInstockCart error", error);
                    resolve(dataCart);
                });
            }
            else {
                resolve(dataCart);
            }
        });
    };
};

export const unlockInstockCart = function (requestObjectArr) {
    return (dispatch, getState) => {
        if (helper.IsNonEmptyArray(requestObjectArr)) {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "requestType": "remove_guiIdlock",
                "requestObjectArr": requestObjectArr
            };
            apiBase(API_LOCK_INSTOCK_CART, METHOD.POST, body, { setTimeOut: 5000 }).then((response) => {
                console.log("unlockInstockCart success", response);
            }).catch(error => {
                console.log("unlockInstockCart error", error);
            });
        }
    };
};

export const addToShoppingCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "mainProduct": data.mainProduct,
                "storeRequests": data.storeRequests,
                "delivery": data.delivery,
                "promotionGroups": data.promotionGroups,
                "cartRequest": data.cartRequest,
                "discountCode": null,
                "giftCode": null,
                "profile": data.profile
            };
            helper.LoggerDebug({ "{+ApplySelectedPromotion+} REQUEST": body });
            dispatch(start_add_to_shopping_cart());
            apiBase(API_ADD_TO_SHOPPING_CART, METHOD.POST, body).then(async (response) => {
                helper.LoggerInfo({ "{+ApplySelectedPromotion+} RESPONSE": response });
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    const lockcart = await dispatch(lockInstockCart(object));
                    const { totalreward, imei } = data.mainProduct;
                    const checkTotalReward = data.mainProduct.totalreward;
                    if (helper.isNumber(checkTotalReward)) {
                        const index = object.SaleOrderDetails.findIndex(so => so.IMEI === imei);
                        if (index !== -1) {
                            object.SaleOrderDetails[index].cus_TotalReward = totalreward;
                        }
                    }
                    dispatch(stop_add_to_shopping_cart(lockcart));
                    // Bật tính hiệu gọi KM tổng đơn cho An Khang
                    dispatch(setShouldCallMultiSale(true));
                    dispatch(setShouldCallModifyAdjust(true));

                    resolve(lockcart);
                }
                else {
                    dispatch(stop_add_to_shopping_cart());
                    resolve({});
                }
            }).catch(error => {
                console.log("addToShoppingCart error", error);
                reject(error);
            });
        });
    };
};

export const modifyShoppingCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "discountCode": data.discountCode,
                "giftCode": data.giftCode,
                "cartRequest": data.cartRequest,
                "promotionGroups": data.promotionGroups,
                "mainProduct": null,
                "storeRequests": null,
                "delivery": null,
            };
            helper.LoggerDebug({ "{+ModifyShoppingCart+} REQUEST": body });
            dispatch(start_modify_shopping_cart());
            apiBase(API_ADD_TO_SHOPPING_CART, METHOD.POST, body).then((response) => {
                helper.LoggerInfo({ "{+ModifyShoppingCart+} RESPONSE": response });
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    dispatch(stop_modify_shopping_cart(object));
                    resolve(object);
                    dispatch(setShouldCallModifyAdjust(false));
                }
                else {
                    dispatch(stop_modify_shopping_cart({}));
                    resolve({});
                }
            }).catch(error => {
                console.log("modifyShoppingCart error", error);
                reject(error);
            });
        });
    };
};

export const giftVoucher = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "discountCode": data.discountCode,
                "giftCode": data.giftCode,
                "cartRequest": data.cartRequest,
                "promotionGroups": data.promotionGroups,
                "mainProduct": null,
                "storeRequests": null,
                "delivery": null,
            };
            dispatch(start_modify_shopping_cart());
            apiBase(API_ADD_TO_SHOPPING_CART, METHOD.POST, body).then((response) => {
                const { object } = response;
                console.log("giftVoucher success", object);
                if (!helper.IsEmptyObject(object)) {
                    dispatch(stop_modify_shopping_cart(object));
                    resolve(object);
                }
                else {
                    resolve({});
                }
            }).catch(error => {
                console.log("giftVoucher error", error);
                reject(error);
            });
        });
    };
};

export const applyCouponExcludePromotion = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "discountCode": data.discountCode,
                "giftCode": data.giftCode,
                "cartRequest": data.cartRequest,
                "promotionGroups": data.promotionGroups,
                "mainProduct": null,
                "storeRequests": null,
                "delivery": null,
            };
            apiBase(API_APPLY_COUPON, METHOD.POST, body).then((response) => {
                const { object } = response;
                console.log("applyCouponExcludePromotion sucess", object);
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    resolve({});
                }
            }).catch(error => {
                console.log("applyCouponExcludePromotion error", error);
                reject(error);
            });
        });
    };
};

export const checkValidPromotion = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                promotionIDList: data.promotionIDList,
                outputTypeIDList: data.outputTypeIDList
            };
            apiBase(API_CHECK_PROMOTION, METHOD.POST, body).then((response) => {
                console.log("checkValidPromotion sucess", response);
                const { object } = response;
                resolve(object);
            }).catch(error => {
                console.log("checkValidPromotionn error", error);
                reject(error);
            });
        });
    };
};

export const newAddToShoppingCartForCoupon = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "mainProduct": data.mainProduct,
                "storeRequests": data.storeRequests,
                "delivery": data.delivery,
                "promotionGroups": data.promotionGroups,
                "cartRequest": data.cartRequest,
                "discountCode": null,
                "giftCode": null,
            };
            apiBase(API_ADD_TO_SHOPPING_CART, METHOD.POST, body).then((response) => {
                const { object } = response;
                console.log("newAddToShoppingCartForCoupon success", object);
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    resolve({});
                }
            }).catch(error => {
                console.log("newAddToShoppingCartForCoupon error", error);
                reject(error);
            });
        });
    };
};

export const deleteShoppingCart = function () {
    return (dispatch, getState) => {
        dispatch(reset_shopping_cart());
        dispatch(reset_suggest_products());
    };
};

export const setDataShoppingCart = (data) => {
    return (dispatch, getState) => {
        dispatch(stop_add_to_shopping_cart(data));
    };
};

export const getCartPromotion = function (cartRequest) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const { userName, storeID, languageID, moduleID, brandID, provinceID } =
                getState().userReducer;
            const { saleScenarioTypeID } = getState().specialSaleProgramReducer;
            const body = {
                loginStoreId: storeID,
                languageID,
                moduleID,
                saleScenarioTypeID:
                    getState().specialSaleProgramReducer.saleScenarioTypeID,
                promotionWithSaleOrderRequest: {
                    saleOrderTypeID: 1,
                    isGetPromotionForSaleProduct: false,
                    appliedPromotion: null
                },
                cartRequest
            };
            dispatch(start_get_cart_promotion());
            dispatch(set_data_voucher_promotion([]));
            const isPromotionEngine = (saleScenarioTypeID == SALE) || (saleScenarioTypeID == AN_KHANG_PHARMACY);
            const hasConfig = helper.checkConfigStoreEGPromotion(storeID) ||
                (helper.checkConfigBrandEGPromotion(brandID) && helper.checkConfigProvinceEGPromotion(provinceID));
            let url = SALE_ORDER_CART_PROMOTION;
            // if (isPromotionEngine && hasConfig) {
            //     url = SALE_ORDER_CART_PROMOTION_NEW;
            // }
            apiBase(url, METHOD.POST, body)
                .then((response) => {
                    console.log('getCartPromotion success', response);
                    const {
                        object: {
                            giftPromotion,
                            salePromotion,
                            appliedSODToPromotionShoppingCart
                        }
                    } = response;
                    const dataPromotion =
                        helper.handelGiftPromotion(giftPromotion);
                    const ProductCSListGroupsOld =
                        salePromotion?.subCategory?.[0]
                            ?.promotionGroups ?? [];
                    const dataSalePromotion = ProductCSListGroupsOld.map(
                        (item) => {
                            return {
                                ...item,
                                promotionProducts: [],
                            };
                        }
                    );
                    const applyDetailIDs = new Set(
                        appliedSODToPromotionShoppingCart
                    );
                    dataPromotion.forEach((groupPromotion) => {
                        groupPromotion.excludePromotionIDs = [];
                    });
                    resolve({ dataPromotion, dataSalePromotion });
                    batch(() => {
                        dispatch(
                            stop_get_cart_promotion(
                                dataPromotion,
                                applyDetailIDs,
                                dataSalePromotion
                            )
                        );
                        // Tắt tính hiệu gọi KM tổng đơn cho An Khang
                        dispatch(setShouldCallMultiSale(false));
                    });
                })
                .catch((error) => {
                    console.log('getCartPromotion error', error);
                    reject(error.msgError);
                    dispatch(
                        stop_get_cart_promotion(
                            [],
                            new Set(),
                            [],
                            error.msgError,
                            ERROR
                        )
                    );
                });
        });
    };
};

const modifyPromotionProfit = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_ADD_TO_SHOPPING_CART, METHOD.POST, body).then((response) => {
            console.log("modifyPromotionProfit success", response);
            const { object } = response;
            if (!helper.IsEmptyObject(object)) {
                resolve(object);
            }
            else {
                reject({ msgError: "modifyPromotionProfit error" });
            }
        }).catch(error => {
            console.log("modifyPromotionProfit error", error);
            reject(error);
        });
    });
};

export const getMultiSalePromotion = function (data, isWarning) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            if (data.cartRequest.IsSOAnKhang) {
                dispatch(getCartPromotion(data.cartRequest));
                resolve(true);
            }
            else {
                let body = {
                    "loginStoreId": getState().userReducer.storeID,
                    "languageID": getState().userReducer.languageID,
                    "moduleID": getState().userReducer.moduleID,
                    "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                    "promotionWithSaleOrderRequest": {
                        saleOrderTypeID: 1,
                        isGetPromotionForSaleProduct: false,
                        appliedPromotion: null,
                    },
                    "discountCode": data.discountCode,
                    "giftCode": data.giftCode,
                    "cartRequest": data.cartRequest,
                    "promotionGroups": data.promotionGroups,
                };
                const cartRequest = helper.deepCopy(data.cartRequest);
                apiBase(API_GET_PROMOTION_PROFIT, METHOD.POST, body)
                    .then(async (response) => {
                        console.log("getMultiSalePromotion success", response);
                        const { object } = response;
                        if (!helper.IsEmptyObject(object)) {
                            body.cartRequest = object;
                            const shoppingCart = await modifyPromotionProfit(body);
                            dispatch(stop_modify_shopping_cart(shoppingCart));
                            dispatch(getCartPromotion(shoppingCart));
                            resolve(true);
                        }
                        else {
                            dispatch(getCartPromotion(cartRequest));
                            resolve(true);
                        }
                    }).catch(error => {
                        console.log("getMultiSalePromotion error", error);
                        if (isWarning) {
                            Alert.alert("", error.msgError, [{
                                text: "OK",
                                onPress: () => {
                                    dispatch(getCartPromotion(cartRequest));
                                    resolve(true);
                                }
                            }]);
                        }
                        else {
                            dispatch(getCartPromotion(cartRequest));
                            resolve(true);
                        }
                    });
            }

        });
    };
};

export const getAdjustUserInfo = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "userName": data.userName,
                "adjustPriceTypeID": data.adjustPriceTypeID
            };
            apiBase(API_GET_PERMISSION_ADJUST_PRICE,
                METHOD.POST,
                body).then((response) => {
                    console.log("getAdjustUserInfo success", response);
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        resolve(object[0]);
                    }
                    else {
                        reject(translate('shoppingCart.cannot_information_user'));
                    }
                }).catch(error => {
                    console.log("getAdjustUserInfo error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getImageCDN = function (bodyFromData) {
    return new Promise((resolve, reject) => {
        apiBase(API_UPLOAD_IMAGE_CDN_NEW,
            METHOD.POST,
            bodyFromData,
            { "isCustomToken": true, "isUpload": true }).then((response) => {
                console.log("getImageCDN", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                }
                else {
                    reject(translate('shoppingCart.upload_image_error'));
                }
            }).catch(error => {
                console.log("getImageCDN", error);
                reject(error.msgError);
            });
    });
};

export const getDataAdjustPrice = function (cartAdjustPrice) {
    return (dispatch, getState) => {
        dispatch(set_data_adjust_price(cartAdjustPrice));
        return Promise.resolve();
    };
};

export const getDataCreatePrice = function (cartRequest) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "cartRequest": cartRequest,
                "discountCode": "",
                "giftCode": "",
                "promotionGroups": []
            };
            apiBase(API_GET_DATA_CREATE_PRICE, METHOD.POST, body).then((response) => {
                console.log("getDataCreatePrice success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(true);
                    dispatch(set_data_adjust_price(object));
                }
                else {
                    reject(translate('shoppingCart.cannot_data_price'));
                }
            }).catch(error => {
                console.log("getDataCreatePrice error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getCustomerByPhone = function (phoneNumber) {
    return new Promise((resolve, reject) => {
        let body = {
            "phoneNumber": phoneNumber
        };
        apiBase(API_GET_CUSTOMER_INFO, METHOD.POST, body).then((response) => {
            console.log("getCustomerByPhone success", response);
            const { object } = response;
            if (!helper.IsEmptyObject(object) && helper.hasProperty(object, "customerName")) {
                resolve(object);
            }
        }).catch(error => {
            console.log("getCustomerByPhone error", error);
        });
    });
};

export const getCompanyByTax = function (taxID) {
    return new Promise((resolve, reject) => {
        let body = {
            "taxID": taxID,
        };
        apiBase(API_GET_COMPANY_INFO, METHOD.POST, body).then((response) => {
            console.log("getCompanyByTax success", response);
            const { object } = response;
            if (!helper.IsEmptyObject(object) && helper.hasProperty(object, "customerName")) {
                resolve(object);
            }
        }).catch(error => {
            console.log("getCompanyByTax error", error);
        });
    });
};

//check CMND
export const checkDocumentInformation = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "CustomerIDCard": data.customerIDCard,
                "BrandID": data.BrandID,
                "Quantity": data.Quantity
            };
            apiBase(API_CHECK_DOCUMENT_INFORMATION, METHOD.POST, body).then((response) => {
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    resolve({});
                }
            }).catch(error => {
                console.log("checkDocumentInformation error", error);
                reject(error);
            });
        });
    };
};

export const getCustomerByPhoneNew = function (phoneNumber) {
    return new Promise((resolve, reject) => {
        let body = {
            "phoneNumber": phoneNumber
        };
        apiBase(API_GET_CUSTOMER_INFO_NEW, METHOD.POST, body).then((response) => {
            const { object } = response;
            console.log("getCustomerByPhoneNew success", object);
            if (helper.IsNonEmptyArray(object) && helper.hasProperty(object[0], "CustomerName")) {
                resolve(object);
                console.log("getCustomerByPhoneNew object", object);
            }
        }).catch(error => {
            console.log("getCustomerByPhoneNew error", error);
        });
    });
};

export const checkCredentialExist = function (principal, dataCartLoyalty) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            dispatch(set_data_loyalty({
                ...dataCartLoyalty,
                "isCredentialExist": true
            }));
            resolve(true);
            // let body = {
            //     "principal": principal
            // };
            // apiBase(API_CHECK_CREDENTIAL, METHOD.POST, body).then((response) => {
            //     console.log("checkCredentialExist success", response);
            //     const { object } = response;
            //     dispatch(set_data_loyalty({
            //         ...dataCartLoyalty,
            //         "isCredentialExist": object
            //     }));
            //     resolve(true);
            // }).catch(error => {
            //     console.log("checkCredentialExist error", error);
            //     // reject(error);
            //     dispatch(set_data_loyalty({
            //         ...dataCartLoyalty,
            //         "isCredentialExist": false
            //     }));
            //     resolve(true);
            // })
        });
    };
};

export const createOTP = function (data) {
    return new Promise((resolve, reject) => {
        let body = {
            "type": data.type,
            "phoneNumber": data.phoneNumber,
            "typeContent": data.typeContent,
            "data": {
                "OTP_LEN": data.lenOtp,
                ...(data.price > 0 && { AMOUNT: helper.convertMaskString(data.price) }) // Conditionally add AMOUNT if price > 0
            },
            "company": data.brandID,
            "onlySms": (data.onlySms == undefined) ? true : data.onlySms
        };
        apiBase(API_CREATE_OTP, METHOD.POST, body).then((response) => {
            console.log("createOTP success", response);
            let isSMS = true;
            const data = response?.object?.notifyData;
            if (data) {
                isSMS = data?.[0]?.channelId == 266;
            }
            resolve(isSMS);
        }).catch(error => {
            console.log("createOTP error", error);
            const { msgError } = error;
            if (helper.hasProperty(OTP_CODE, msgError)) {
                reject(OTP_CODE[msgError]);
            }
            else {
                reject(msgError);
            }
        });
    });
};

export const verifyOTP = function (otp, phoneNumber) {
    return new Promise((resolve, reject) => {
        let body = {
            "otp": otp,
            "phoneNumber": phoneNumber,
            // "opts": {
            //     IDENTITY_QUERY_ATTR: "CUSTOMERID"
            // }
        };
        apiBase(API_VERIFY_OTP, METHOD.POST, body).then((response) => {
            console.log("verifyOTP success", response);
            const { object } = response;
            if (
                // helper.hasProperty(object, 'CUSTOMERID') &&
                helper.hasProperty(object, 'ID')
            ) {
                resolve({
                    customerId: object.CUSTOMERID,
                    requestId: object.ID,
                });
            }
            else {
                reject(translate('shoppingCart.no_verified_information'));
            }
        }).catch(error => {
            console.log("verifyOTP error", error);
            const { msgError } = error;
            if (helper.hasProperty(OTP_CODE, msgError)) {
                reject(OTP_CODE[msgError]);
            }
            else {
                reject(msgError);
            }
        });
    });
};

export const verifyOTPBeta = function (otp, phoneNumber) {
    return new Promise((resolve) => {
        resolve()
    });
};

export const verifyIdentify = function (identifier, identityData, customerPhone, brandID = null) {
    return new Promise((resolve, reject) => {
        let body = {
            "principal": customerPhone,
            "identifier": identifier,
            "identityData": identityData,
            "opts": {
                IDENTITY_QUERY_ATTR: "CUSTOMERID"
            },
            "createNotFound": true,
            "branchId": brandID
        };
        apiBase(API_VERIFY_IDENTIFY, METHOD.POST, body).then((response) => {
            console.log("verifyIdentify success", response);
            const { object } = response;
            if (
                helper.hasProperty(object, 'principal') &&
                helper.hasProperty(object, 'datas') &&
                helper.hasProperty(object, 'id') &&
                helper.hasProperty(object.datas, 'CUSTOMERID')
            ) {
                if (customerPhone != object.principal) {
                    reject(translate('shoppingCart.code_verified_incorrect'));
                }
                else {
                    resolve({
                        customerId: object.datas.CUSTOMERID,
                        requestId: object.id
                    });
                }
            }
            else {
                reject(translate('shoppingCart.no_verified_information'));
            }
        }).catch(error => {
            console.log("verifyIdentify error", error);
            const { msgError } = error;
            if (helper.hasProperty(IDENTIFY_CODE, msgError)) {
                reject(IDENTIFY_CODE[msgError]);
            }
            else {
                reject(msgError);
            }
        });
    });
};

export const resetCartPromotion = function () {
    return (dispatch, getState) => {
        dispatch(reset_cart_promotion());
    };
};

export const getMemberPoint = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "customerInfo": data.customerInfo,
                "relationShip": data.relationShip,
                "cartRequest": data.cartRequest
            };
            apiBase(API_GET_MEMBER_POINT, METHOD.POST, body).then((response) => {
                console.log("getMemberPoint success", response);
                const { object } = response;
                if (helper.hasProperty(object, "decUsePoint")) {
                    resolve(object);
                    dispatch(set_data_member({
                        ...data,
                        "memberPoint": object
                    }));
                }
                else {
                    resolve({ "decUsePoint": 0 });
                }
            }).catch(error => {
                console.log("getMemberPoint error", error);
                resolve({ "decUsePoint": 0 });
            });
        });
    };
};

export const getOTPcode = function (data, typeOTP, brandID) {
    return new Promise((resolve, reject) => {
        let body = {
            "customerInfo": data.customerInfo,
            "relationShip": data.relationShip,
            "cartRequest": data.cartRequest,
            "typeOTP": typeOTP,
            "company": brandID
        };
        apiBase(API_REQUIRE_OTP, METHOD.POST, body).then((response) => {
            console.log("getOTPcode success", response);
            resolve(true);
        }).catch(error => {
            console.log("getOTPcode error", error);
            const { msgError } = error;
            if (helper.hasProperty(OTP_CODE, msgError)) {
                reject(OTP_CODE[msgError]);
            }
            else {
                reject(msgError);
            }
        });
    });
};

export const checkGiftVoucher = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "customerPhone": data.customerPhone,
                "promotionGroups": data.promotionGroups,
                "saleOrderID": data.saleOrderID
            };
            apiBase(API_CHECK_PHONE_GIFT_VOUCHER, METHOD.POST, body).then((response) => {
                console.log("checkGiftVoucher success: ", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    dispatch(set_data_voucher_promotion(object));
                    resolve(true);
                }
                else {
                    dispatch(set_data_voucher_promotion([]));
                    resolve(false);
                }
            }).catch(error => {
                console.log("checkGiftVoucher error: ", error);
                dispatch(set_data_voucher_promotion([]));
                resolve(false);
            });
        });
    };
};

export const searchSimPromotion = function (productInfo) {
    return new Promise((resolve, reject) => {
        let body = {
            "imei": productInfo.imei,
            "productID": productInfo.productID,
            "storeID": productInfo.storeID,
            "productIDRef": "",
            "inventoryStatusID": 1,
            "saleProgramID": 0,
            "isInstalment": false,
            "isImeiSim": true,
        };
        apiBase(API_DETAILS_PRODUCT, METHOD.POST, body).then((response) => {
            console.log("searchSimProduct success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                resolve({
                    isEmpty: !EMPTY,
                    description: "",
                    product: object[0]
                });
            }
            else {
                resolve({
                    isEmpty: EMPTY,
                    description: translate('shoppingCart.cannot_information_IMEI_SIM'),
                    product: {}
                });
            }
        }).catch(error => {
            console.log("searchSimProduct error", error);
            reject(error.msgError);
        });
    });
};

export const getPackageSimPromotion = function ({ productID, promotionListId, promotionID, extensionProperty }) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "productID": productID,
                "promotionListId": promotionListId,
                "promotionID": promotionID, // dentifier
                "extensionProperty": extensionProperty
            };
            apiBase(API_GET_SIM_PACKAGES_NEW, METHOD.POST, body).then((response) => {
                console.log("getPackageSimPromotion success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                }
                else {
                    reject(translate('shoppingCart.no_package_information'));
                }
            }).catch(error => {
                console.log("getPackageSimPromotion error", error);
                reject(error.msgError);
            });
        });
    };
};

export const checkPackageSimPrice = (simInfo) =>
    new Promise((resolve, reject) => {
        const body = {
            ...simInfo // Sim Package Object
        };
        apiBase(API_CHECK_PACKAGE_SIM_PRICE, METHOD.POST, body)
            .then((response) => {
                console.log('[API] checkPackageSimPrice success', response);
                resolve();
            })
            .catch((error) => {
                console.log('[API] checkPackageSimPrice error', error);
                reject(error.msgError);
            });
    });

export const getInfoCreatePrice = function (cartRequest) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "cartRequest": cartRequest,
                "discountCode": "",
                "giftCode": "",
                "promotionGroups": [],
            };
            apiBase(API_GET_INFO_CREATE_PRICE, METHOD.POST, body).then((response) => {
                console.log("getInfoCreatePrice success", response);
                const { errorReason } = response;
                if (helper.IsNonEmptyString(errorReason)) {
                    resolve(errorReason);
                }
                else {
                    resolve("");
                }
            }).catch(error => {
                console.log("getInfoCreatePrice error", error);
                reject(error.msgError);
            });
        });
    };
};

export const loadInforBatchNoByCart = (data) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "cartRequest": data.cartRequest,
                "saleOrderDetailID": data.saleOrderDetailID
            };
            apiBase(API_LOAD_INFO_BATCH_NO_BY_CART, METHOD.POST, body).then((response) => {
                console.log("loadInforBatchNoByCart success", response);
                resolve(response.object);
            }).catch(error => {
                console.log("loadInforBatchNoByCart error", error);
                reject(error.msgError);
            });
        });
    };
};

export const setBatchNoByCart = ({ batchNo, id }) => ({
    type: SET_BATCH_NO_BY_CART,
    batchNo,
    id
});

export const getInfoCreatePrice4SO = function (cartRequest) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "cartRequest": cartRequest,
                "discountCode": "",
                "giftCode": "",
                "promotionGroups": [],
            };
            apiBase(API_GET_INFO_CREATE_PRICE_SO, METHOD.POST, body).then((response) => {
                console.log("getInfoCreatePrice4SO success", response);
                const { errorReason } = response;
                if (helper.IsNonEmptyString(errorReason)) {
                    resolve(errorReason);
                }
                else {
                    resolve("");
                }
            }).catch(error => {
                console.log("getInfoCreatePrice4SO error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getDataInstallment = function (cartInstallment, typeOTP = "INSTALLMENT") {
    return (dispatch, getState) => {
        dispatch(set_data_cart_installment(cartInstallment, typeOTP));
        return Promise.resolve();
    };
};

export const getPaymentMonthly = function (cartRequest) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "shoppingCartRequest": cartRequest,
            };
            apiBase(API_GET_PAYMENTMONTHLY, METHOD.POST, body).then((response) => {
                console.log("getPaymentMonthly success", response);
                const { object } = response;
                if (helper.hasProperty(object, "PAYMENTAMOUNTMONTHLY")) {
                    const { PAYMENTAMOUNTMONTHLY } = object;
                    resolve(PAYMENTAMOUNTMONTHLY);
                }
                else {
                    reject(translate('shoppingCart.no_information_payment_month'));
                }
            }).catch(error => {
                console.log("getPaymentMonthly error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getCustomerProfile = function (data) {
    return new Promise((resolve, reject) => {
        const { phoneNumber, typeProfile, loginStoreId, languageID, moduleID } = data;
        let body = {
            "keyword": phoneNumber,
            "type": typeProfile,
            loginStoreId,
            languageID,
            moduleID
        };
        apiBase(API_GET_CUSTOMER_PROFILE, METHOD.POST, body).then((response) => {
            const { object: { profile } } = response;
            const infoCustomer = profile;
            let customerProfile = null;
            console.log("getCustomerProfile success", infoCustomer);
            if (!helper.IsEmptyObject(infoCustomer) && helper.IsNonEmptyArray(infoCustomer[typeProfile])) {
                customerProfile = infoCustomer[typeProfile];
                resolve(customerProfile);
            }
            else {
                resolve(customerProfile);
                // reject("Lấy thông tin người nhận bị lỗi...")
            }
        }).catch(error => {
            console.log("getCustomerProfile error", error);
            reject(error.msgError);
        });
    });
};

export const updateProfileReceive = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_UPDATE_CUSTOMER_RECEIVE, METHOD.POST, body).then((response) => {
            const { object: { profile } } = response;
            console.log("updateProfileReceive success", profile);
            if (!helper.IsEmptyObject(profile) && helper.IsNonEmptyArray(profile[body.typeProfile])) {
                resolve(profile[body.typeProfile][0]);
                console.log("updateProfileReceive object", profile[body.typeProfile]);
            }
            else {
                reject("Cập nhật thông tin người nhận bị lỗi...");
            }
        }).catch(error => {
            reject(error.msgError);
            console.log("updateProfileReceive error", error);
        });
    });
};


export const insertProfileReceive = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_INSERT_CUSTOMER_RECEIVE, METHOD.POST, body).then((response) => {
            const { object: { profile } } = response;
            console.log("insertProfileReceive success", profile);
            if (!helper.IsEmptyObject(profile) && helper.IsNonEmptyArray(profile[body.typeProfile])) {

                resolve(profile[body.typeProfile][0]);
                console.log("insertProfileReceive object", profile[body.typeProfile]);
            }
            else {
                reject("Thêm thông tin người nhận bị lỗi...");
            }
        }).catch(error => {
            reject(error.msgError);
            console.log("insertProfileReceive error", error);
        });
    });
};

export const modifyCustomerProfile = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_MODIFY_CUSTOMER_PROFILE, METHOD.POST, body).then((response) => {
            const { object: { profile } } = response;
            console.log("modifyCustomerProfile success", profile);
            if (!helper.IsEmptyObject(profile)) {
                resolve(profile);
                console.log("modifyCustomerProfile object", profile);
            }
            else {
                reject("lỗi lấy profile");
            }
        }).catch(error => {
            reject(error.msgError);
            console.log("modifyCustomerProfile error", error);
        });
    });
};

export const getSOProfile = function (body) {
    return new Promise((resolve, reject) => {
        let customerProfile = {};
        apiBase(API_GET_PROFILE_SO, METHOD.POST, body).then((response) => {
            const { object: { profile } } = response;
            const infoCustomer = profile;
            console.log("getSOProfile success", infoCustomer);
            if (!helper.IsEmptyObject(infoCustomer) && helper.IsNonEmptyArray(infoCustomer[body.type])) {
                customerProfile = infoCustomer[body.type];
                resolve(customerProfile);
            }
            else {
                resolve(customerProfile);
                // reject("Lấy thông tin người nhận bị lỗi...")
            }
        }).catch(error => {
            console.log("getSOProfile error", error);
            // reject(error)
            resolve(customerProfile);
        });
    });
};

export const getQuestions = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_QUESTIONS, METHOD.POST, body).then((response) => {
            const { object } = response;
            console.log("getQuestions success", object);
            if (!helper.IsEmptyObject(object)) {
                resolve(object);
                console.log("getQuestions object", object);
            }
            else {
                reject("lỗi lấy Questions");
            }
        }).catch(error => {
            reject(error.msgError);
            console.log("getQuestions error", error);
        });
    });
};

export const checkPromotionIMEI = function (promoRequireInfor) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "promoRequireInfor": promoRequireInfor
            };
            apiBase(API_CHECK_IMEI_PROMOTION, METHOD.POST, body).then((response) => {
                resolve();
            }).catch(error => {
                console.log("checkPromotionIMEI error", error);
                reject(error.msgError);
            });
        });
    };
};

export const checkCandidateDiscount = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "discountCode": data.discountCode,
                "candidateNo": data.candidateNo
            };
            apiBase(API_CHECK_STUDENT_DISCOUNT, METHOD.POST, body).then((response) => {
                resolve();
            }).catch(error => {
                console.log("checkCandidateDiscount error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getPriceNearStore = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_PRICE_NEAR_STORE, METHOD.POST, body).then((response) => {
            const { object: { MinAdjustPrice, MaxAdjustPrice } } = response;
            if (MinAdjustPrice >= 0 && MaxAdjustPrice >= 0) {
                resolve({ MinAdjustPrice, MaxAdjustPrice });
                console.log("getPriceNearStore object", response);
            }
            else {
                reject(" Không thể lấy giá trị chiến giá từ các kho lân cận...")
            }
        }).catch(error => {
            reject(error.msgError)
            console.log("getPriceNearStore error", error);
        })
    });
}



export const setDefaultCustomer = (defaultCustomer) => {
    return (dispatch, getState) => {
        dispatch(set_default_customer(defaultCustomer));
    };
};

export const setSupplementImageCart = (data) => {
    return (dispatch, getState) => {
        dispatch(set_supplement_image_cart(data));
    };
};

export const start_add_to_shopping_cart = () => {
    return ({
        type: START_ADD_TO_SHOPPING_CART
    });
};

export const stop_add_to_shopping_cart = (dataShoppingCart = {}) => {
    return ({
        type: STOP_ADD_TO_SHOPPING_CART,
        dataShoppingCart,
    });
};

export const getInfoLoyaltyOld = async function (identityData, branchId = null) {
    try {
        const bodyVerify = {
            "principal": "",
            "identifier": "",
            "identityData": identityData,
            "opts": {
                IDENTITY_QUERY_ATTR: "CUSTOMERID"
            },
            "createNotFound": true,
            "branchId": branchId
        };
        const dataVerify = await apiBase(API_VERIFY_IDENTIFY, METHOD.POST, bodyVerify);
        const { object } = dataVerify;
        let result = {
            "customerId": object.datas.CUSTOMERID,
            "requestId": object.id,
            "customerPhone": object.principal,
            "gender": 1,
            "customerName": "",
            "customerAddress": "",
            "registertime": object.datas.createCredential
        };

        if (
            helper.hasProperty(object, 'principal') &&
            helper.hasProperty(object, 'datas') &&
            helper.hasProperty(object, 'id') &&
            helper.hasProperty(object.datas, 'CUSTOMERID')
        ) {
            const bodyQuery = { "phoneNumber": object.principal };
            const dataQuery = await apiBase(API_GET_CUSTOMER_INFO_BARCODE, METHOD.POST, bodyQuery);
            const {
                customerName,
                gender,
                customerAddress
            } = dataQuery.object;
            result = {
                "customerId": object.datas.CUSTOMERID,
                "requestId": object.id,
                "customerPhone": object.principal,
                "gender": gender,
                "customerName": customerName,
                "customerAddress": customerAddress,
                "registertime": object.datas.createCredential
            };
        }
        return result;
    } catch (error) {
        console.log("getInfoLoyalty", error);
        throw " Ghi nhận thất bại - Mã định danh không hợp lệ hoặc đã hết hạn. Vui lòng thử lại.";
    }
};

export const getInfoLoyalty = async function (identityData, branchId = null) {
    try {
        const bodyVerify = {
            "principal": "",
            "identifier": "",
            "identityData": identityData,
            "opts": {
                IDENTITY_QUERY_ATTR: "CUSTOMERID"
            },
            "createNotFound": true,
            "branchId": branchId
        };
        const dataVerify = await apiBase(API_VERIFY_IDENTIFY, METHOD.POST, bodyVerify);
        const { object } = dataVerify;
        let result = {
            "customerId": object.datas.CUSTOMERID,
            "requestId": object.id,
            "customerPhone": object.principal,
            "gender": 1,
            "customerName": "",
            "customerAddress": "",
            "registertime": object.datas.createCredential
        };
        let customerProfile = null;
        if (
            helper.hasProperty(object, 'principal') &&
            helper.hasProperty(object, 'datas') &&
            helper.hasProperty(object, 'id') &&
            helper.hasProperty(object.datas, 'CUSTOMERID')
        ) {
            // const bodyQuery = { "phoneNumber": object.principal };
            // const dataQuery = await apiBase(API_GET_CUSTOMER_INFO_BARCODE, METHOD.POST, bodyQuery);
            const bodyQuery = { "keyword": object.principal, "type": TYPE_PROFILE.CUSTOMER };
            const dataQuery = await apiBase(API_GET_CUSTOMER_PROFILE, METHOD.POST, bodyQuery);
            const { object: { profile } } = dataQuery;
            if (!helper.IsEmptyObject(profile) && helper.IsNonEmptyArray(profile[TYPE_PROFILE.CUSTOMER])) {
                customerProfile = profile[TYPE_PROFILE.CUSTOMER];
                const {
                    customerName,
                    gender,
                } = profile[TYPE_PROFILE.CUSTOMER][0];
                result = {
                    "customerId": object.datas.CUSTOMERID,
                    "requestId": object.id,
                    "customerPhone": object.principal,
                    "gender": getGender(gender),
                    "customerName": customerName,
                    "registertime": object.datas.createCredential
                };
            }


        }
        return { result, customerProfile };
    } catch (error) {
        console.log("getInfoLoyalty", error);
        throw " Ghi nhận thất bại - Mã định danh không hợp lệ hoặc đã hết hạn. Vui lòng thử lại.";
    }
};

export const getInfoLoyaltyNew = async function (identityData, branchId = null) {
    let result = {};
    let customerProfile = null;
    try {
        const bodyVerify = {
            "principal": "",
            "identifier": "",
            "identityData": identityData,
            "opts": {
                IDENTITY_QUERY_ATTR: "CUSTOMERID"
            },
            "createNotFound": true,
            "branchId": branchId
        };
        const dataVerify = await apiBase(API_VERIFY_IDENTIFY, METHOD.POST, bodyVerify);
        const { object } = dataVerify;
        if (
            helper.hasProperty(object, 'principal') &&
            helper.hasProperty(object, 'datas') &&
            helper.hasProperty(object, 'id') &&
            helper.hasProperty(object.datas, 'CUSTOMERID')
        ) {
            result = {
                "customerId": object.datas.CUSTOMERID,
                "requestId": object.id,
                "customerPhone": object.principal,
                "gender": 1,
                "customerName": "",
                "customerAddress": "",
                "listAddress": []
            };
            const bodyQuery = { "keyword": object.principal, "type": TYPE_PROFILE.CUSTOMER };
            const dataQuery = await apiBase(API_GET_CUSTOMER_PROFILE, METHOD.POST, bodyQuery);
            const { object: { profile } } = dataQuery;
            if (!helper.IsEmptyObject(profile) && helper.IsNonEmptyArray(profile[TYPE_PROFILE.CUSTOMER])) {
                customerProfile = profile[TYPE_PROFILE.CUSTOMER];
                const {
                    customerName,
                    gender,
                } = profile[TYPE_PROFILE.CUSTOMER][0];
                result = {
                    ...dataQuery,
                    customerId: object.datas.CUSTOMERID,
                    requestId: object.id,
                    customerPhone: object.principal || "",
                    customerName: customerName || "",
                    gender: getGender(gender),
                };
            }
        }
        return { result, customerProfile };
    } catch (error) {
        console.log("getInfoLoyalty", error);
        throw "Mã định danh không hợp lệ hoặc đã hết hạn. Vui lòng thử lại.";
    }
};

export const searchPromotionProducts = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const { userName, storeID, languageID, moduleID, brandID, provinceID } =
                getState().userReducer;
            const { saleScenarioTypeID } = getState().specialSaleProgramReducer;
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "StoreIDList": getState().userReducer.storeID,
                "promotionListGroupID": data.promotionListGroupID,
                "promotionId": data.promotionID,
                "Keyword": data.barcode,
            };
            const isPromotionEngine = (saleScenarioTypeID == SALE) || (saleScenarioTypeID == AN_KHANG_PHARMACY);
            const hasConfig = helper.checkConfigStoreEGPromotion(storeID) ||
                (helper.checkConfigBrandEGPromotion(brandID) && helper.checkConfigProvinceEGPromotion(provinceID));
            let url = API_SEARCH_BARCODE_CART_PROMOTION;
            // if (isPromotionEngine && hasConfig) {
            //     url = API_SEARCH_BARCODE_CART_PROMOTION_NEW;
            // }
            apiBase(url, METHOD.POST, body).then((response) => {
                console.log("searchPromotionProducts success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve({
                        isEmpty: !EMPTY,
                        description: "",
                        products: object
                    });
                }
                else {
                    resolve({
                        isEmpty: EMPTY,
                        description: translate('detail.no_product_information'),
                        products: []
                    });
                }
            }).catch(error => {
                reject(error.msgError);
                console.log("searchPromotionProducts error", error);
            });
        });
    };
};

export const getSuggestPromotion = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "promotionId": data.promotionListGroupID,
                "storeId": getState().userReducer.storeID,
                "top": 5

            };
            apiBase(API_GET_SUGGUEST_PROMOTION, METHOD.POST, body).then((response) => {
                console.log("getSuggestPromotion success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve({
                        products: object
                    });
                }
                else {
                    resolve({
                        products: []
                    });
                }
            }).catch(error => {
                reject(error.msgError);
                console.log("getSuggestPromotion error", error);
            });
        });
    };
};

export const checkInstalledQTV = (phoneNumber) => {
    return new Promise((resolve, reject) => {
        let body = {
            "principal": phoneNumber
        };
        apiBase(API_CHECK_CREDENTIAL, METHOD.POST, body, { setTimeOut: 1500 }).then((response) => {
            console.log("checkInstalledQTV success", response);
            const { object } = response;
            resolve(object);
        }).catch(error => {
            console.log("checkInstalledQTV error", error);
        });
    });
};

export const getSummerFee = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "mainProduct": data.mainProduct,
                "storeRequests": data.storeRequests,
                "delivery": data.delivery,
                "promotionGroups": data.promotionGroups,
                "cartRequest": data.cartRequest,
                "discountCode": null,
                "giftCode": null,
                "profile": data.profile
            };
            apiBase(API_ADD_TO_SHOPPING_CART, METHOD.POST, body).then(async (response) => {
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    resolve({});
                }
            }).catch(error => {
                console.log("addToShoppingCart error", error);
                reject(error);
            });
        });
    };
};

export const checkNumberPhoneApplyVoucher = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_NUMBER_PHONE_APPLY_VOUCHER, METHOD.POST, body).then((response) => {
            console.log("checkNumberPhoneApplyVoucher success", response);
            const { object } = response;
            resolve(object?.Data);
        }).catch(error => {
            reject(error.msgError)
            console.log("checkNumberPhoneApplyVoucher error", error);
        })
    });
}

export const getCustomerProfileForPayment = function (data) {
    return new Promise((resolve, reject) => {
        const { phoneNumber, typeProfile, loginStoreId, languageID, moduleID } = data;
        let body = {
            "keyword": phoneNumber,
            "type": typeProfile,
            loginStoreId,
            languageID,
            moduleID
        };
        apiBase(API_GET_CUSTOMER_PROFILE, METHOD.POST, body).then((response) => {
            const { object: { profile } } = response;
            const infoCustomer = profile;
            let customerProfile = null;
            console.log("getCustomerProfile success", infoCustomer);
            if (!helper.IsEmptyObject(infoCustomer) && helper.IsNonEmptyArray(infoCustomer[typeProfile])) {
                customerProfile = infoCustomer[typeProfile];
                resolve(customerProfile);
            }
            else {
                resolve(customerProfile);
                // reject("Lấy thông tin người nhận bị lỗi...")
            }
        }).catch(error => {
            console.log("getCustomerProfile error", error);
            resolve(null);
        });
    });
};
export const checkNumberPhoneApplyMedicine = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_NUMBER_PHONE_APPLY_MEDICINE, METHOD.POST, body).then((response) => {
            console.log("checkNumberPhoneApplyMedicine success", response);
            const { object } = response;
            resolve(object ?? {});
        }).catch(error => {
            resolve({});
            console.log("checkNumberPhoneApplyMedicine error", error);
        })
    });
}

export const checkApplyGiftVoucher = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_STATUS_APPLY_GIFT_AMOUNT, METHOD.POST, body).then((response) => {
            console.log("checkApplyGiftVouche success", response);
            resolve(true);
        }).catch(error => {
            reject(error.msgError);
            console.log("checkApplyGiftVouche error", error);
        })
    });
}
export const checkSendOTPSamSung = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_SEND_OTP_PARTNER, METHOD.POST, body).then((response) => {
            console.log("checkSendOTPSamSung success", response);
            const { object } = response;
            resolve(object?.IsSendOTP);
        }).catch(error => {
            resolve(false)
            console.log("checkSendOTPSamSung error", error);
        })
    });
}
export const saveInfoCustomer = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_SAVE_INFO_CUSTOMER, METHOD.POST, body).then((response) => {
            console.log("saveInfoCustomer success", response);
            const { object } = response;
            resolve(object?.IsSaveSuccess);
        }).catch(error => {
            resolve(false)
            console.log("saveInfoCustomer error", error);
        })
    });
}

export const checkCouponExpired = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_COUPON_EXPIRED_BY_PHONE, METHOD.POST, body).then((response) => {
            console.log("checkCouponExpiried success", response);
            const coupons = JSON.parse(response.object || "{}")?.CouponQuantity
            resolve(coupons > 0 ? coupons : 0);
        }).catch(error => {
            resolve(0);
            console.log("checkCouponExpiried error", error);
        })
    });
}

export const getGiftVoucherCustomer = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_VOUCHER_CUSTOMER, METHOD.POST, body).then((response) => {
            console.log("getGiftVoucherCustomer success", response);
            const vouchers = response?.object?.ACCEPT
            resolve(vouchers?.length > 0 ? vouchers : []);
        }).catch(error => {
            resolve([]);
            console.log("getGiftVoucherCustomer error", error);
        })
    });
}

const start_modify_shopping_cart = () => {
    return ({
        type: START_MODIFY_SHOPPING_CART
    });
};

export const stop_modify_shopping_cart = (dataShoppingCart = {}) => {
    return ({
        type: STOP_MODIFY_SHOPPING_CART,
        dataShoppingCart,
    });
};

const start_get_cart_promotion = () => {
    return ({
        type: START_GET_CART_PROMOTION
    });
};

export const stop_get_cart_promotion = (
    cartPromotion,
    applyDetailIDs,
    dataSalePromotion,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_CART_PROMOTION,
        cartPromotion,
        applyDetailIDs,
        dataSalePromotion,
        isError,
        description,
    });
};

const set_data_adjust_price = (dataCartAdjust) => {
    return ({
        type: SET_DATA_CART_ADJUST,
        dataCartAdjust
    });
};

const set_data_loyalty = (dataCartLoyalty) => {
    return ({
        type: SET_DATA_LOYALTY,
        dataCartLoyalty
    });
};

const set_data_member = (dataCartMember) => {
    return ({
        type: SET_DATA_MEMBER,
        dataCartMember
    });
};

export const reset_shopping_cart = () => {
    return ({
        type: RESET_SHOPPING_CART
    });
};

const reset_cart_promotion = () => {
    return ({
        type: RESET_CART_PROMOTION
    });
};

const set_data_cart_installment = (dataCartInstallment, typeOTP) => {
    return ({
        type: SET_DATA_CART_INSTALLMENT,
        dataCartInstallment,
        typeOTP
    });
};

const set_data_voucher_promotion = (voucherPromotion) => {
    return ({
        type: SET_DATA_VOUCHER_PROMOTION,
        voucherPromotion
    });
};

const set_default_customer = (defaultCustomer) => {
    return ({
        type: SET_DEFAULT_CUSTOMER,
        defaultCustomer
    });
};

const set_supplement_image_cart = (supImages) => {
    return ({
        type: SET_SUPPLEMENT_IMAGE_CART,
        supImages
    });
};

export const set_map_customer_confirm_policy = ({ type, infoCustomerCRM }) => {
    return ({
        type: SET_MAP_CUSTOMER_CONFIRM_POLICY,
        customerType: type,
        infoCustomerCRM
    });
};

export const reset_map_customer_confirm_policy = () => {
    return ({
        type: RESET_MAP_CUSTOMER_CONFIRM_POLICY,
    });
};
const OTP_CODE = {
    "Your OTP is exists": "Mã OTP đã tạo trước đó vẫn còn hiệu lực. Vui lòng chờ sau 1 phút để lấy lại mã mới.",
    "Your OTP is verified": "Mã OTP đã được xác thực trước đó. Vui lòng lấy lại mã mới để xác thực.",
    "Your OTP is expired": "Mã OTP đã hết hiệu lực. Vui lòng lấy lại mã mới để xác thực.",
    "Your OTP is not found": "Mã OTP không hợp lệ.",
    "Error occurred while sending params to sms crm": "Số điện thoại đã được gửi tin nhắn nhiều lần trong ngày."
};

const IDENTIFY_CODE = {
    "IDENTIFY_CODE_NOT_FOUND": "Mã định danh không tồn tại. Vui lòng thử lại.",
    "PRINCIPAL_VERIFICATION_INVALID": "Mã định danh không hợp lệ. Vui lòng thử lại.",
    "IDENTIFY_WRONG_KEY": "Mã định danh đã hết hiệu lực. Vui lòng thử lại.",
};

export const getGender = (gender) => {
    if (gender == 1) return 1;
    if (gender == 0) return 0;
    return null;
};