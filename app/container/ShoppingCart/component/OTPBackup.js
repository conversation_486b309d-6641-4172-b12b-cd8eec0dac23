import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Pressable } from 'react-native';
import CheckboxOTP from './CheckboxOTP';
import { COLORS } from '@styles';
import { MyText } from '@components';
import { constants } from '@constants';

const OTPBackup = (
    {
        label = "Đồng ý với chính sách sử dụng thông tin OTP",
        onPress = () => { },
        children
    }
) => {
    const [isChecked, setIsChecked] = useState(false);
    const handleCheckboxChange = () => {
        setIsChecked(!isChecked);
    };
    return (
        <View style={{ paddingTop: 5 }}>
            {children}
            <CheckboxOTP
                label={label}
                checked={isChecked}
                onChange={handleCheckboxChange}
            />
            <View style={{ alignItems: 'center', paddingTop: 15 }}>
                <Pressable
                    disabled={!isChecked}
                    style={{
                        backgroundColor: !isChecked ? COLORS.txtAAAAAA : '#007BFF',
                        width: constants.width / 2,
                        padding: 10,
                        borderRadius: 10,
                        alignItems: 'center',
                        borderColor: COLORS.bg2FB47C
                    }}
                    onPress={onPress}>
                    <MyText addSize={2} style={{
                        color: COLORS.txtFFFFFF,
                        fontWeight: '600',
                    }}>
                        {"Tiếp Tục"}
                    </MyText>
                </Pressable>
            </View>
        </View>
    );
};

export default OTPBackup;
