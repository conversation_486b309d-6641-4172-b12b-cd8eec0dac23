import React, { useState } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Alert
} from 'react-native';
import {
    Icon,
    showB<PERSON>UI,
    hideBlockUI,
    MyText
} from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import PromotionHeader from "./PromotionHeader";
import PromotionExpand from "./PromotionExpand";
import ModalSearchSim from "./Modal/ModalSearchSim";
import { translate } from '@translate';
import { COLORS } from "@styles";

const SaleProduct = ({
    salePromotion,
    onDelete,
    marginTop,
    onSearchSim,
    isApplyCoupon,
    applyDetailIDs,
    isShowLot,
    onShowLot
}) => {
    const {
        ProductName,
        Quantity,
        QuantityUnitName,
        SalePriceBKVAT,
        AdjustPriceTypeID,
        AdjustPrice,
        giftSaleOrders,
        BrandIDOfSIM,
        IMEI,
        SaleOrderDetailID,
        cus_objProductServicePackageBO,
        PackagesTypeName
    } = salePromotion;
    const isSIM = !!BrandIDOfSIM;
    const isAdjust = !isApplyCoupon && (AdjustPrice != 0);
    const isNonCartPromotion = (applyDetailIDs.size == 0);
    const IsApplyTotalPromotion = isNonCartPromotion || applyDetailIDs.has(SaleOrderDetailID);
    const labelKey = constants.ADJUST_PRICE_LABEL[AdjustPriceTypeID];

    const servicePackName = cus_objProductServicePackageBO?.ServicePackName
    const labelIMEI = helper.IsEmptyObject(cus_objProductServicePackageBO) ? translate('shoppingCart.IMEI') : "Số thuê bao:"



    const onPressDelete = () => {
        Alert.alert("",
            `${translate("common.slugon_delete")} "${ProductName}" ${translate("common.remove_list")}`,
            [
                {
                    text: translate("common.btn_skip"),
                    style: "cancel",
                },
                {
                    text: translate("common.btn_continue"),
                    style: "default",
                    onPress: onDelete
                }
            ]
        );
    };
    return (
        <View style={{
            backgroundColor: COLORS.bgFFFFFF,
            marginTop: marginTop,
            paddingVertical: 4,
            borderWidth: StyleSheet.hairlineWidth,
            borderColor: COLORS.bdE4E4E4,
            width: constants.width - 10,
            alignSelf: "center",
            borderRadius: 4
        }}>
            <View style={{
                width: constants.width - 10,
                flexDirection: "row",
                paddingHorizontal: 10,
            }}>
                <Icon
                    iconSet={"Ionicons"}
                    name={"checkmark"}
                    size={16}
                    color={COLORS.ic288AD6}
                    style={{ marginTop: 2 }}
                />
                <MyText
                    text={servicePackName || ProductName}
                    style={{
                        color: COLORS.txt333333,
                        width: constants.width - 84
                    }}
                />
            </View>

            {
                !IsApplyTotalPromotion &&
                <View style={{
                    paddingRight: 10,
                    paddingLeft: 26,
                    width: constants.width - 10,
                    justifyContent: "center",
                }}>
                    <MyText
                        text={
                            translate("shoppingCart.product_intend")
                        }
                        addSize={-2}
                        style={{
                            color: COLORS.txtFF8900,
                            fontStyle: 'italic'
                        }}
                    />
                </View>
            }
            {
                isSIM &&
                <View style={{
                    paddingRight: 10,
                    paddingLeft: 26,
                    marginTop: 4,
                    width: constants.width - 10,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}>
                    <View style={{ flex: 5 }}>
                        <MyText
                            text={`${labelIMEI} ${IMEI}`}
                            style={{
                                color: COLORS.txt333333
                            }} />

                        {
                            !!PackagesTypeName &&
                            <View style={{
                                paddingVertical: 3,
                            }}>
                                <MyText
                                    text={`Gói cước: ${PackagesTypeName}`}
                                    style={{
                                        color: COLORS.txt333333
                                    }}
                                />
                            </View>
                        }
                    </View>

                    <TouchableOpacity style={{
                        flexDirection: "row",
                        alignItems: "center",
                        flex: 3,
                        justifyContent: "flex-end"

                    }}
                        onPress={onSearchSim}
                    >
                        <Icon
                            iconSet={"MaterialIcons"}
                            name={"sim-card"}
                            color={COLORS.ic147EFB}
                            size={16}
                        />
                        <MyText
                            text={translate('shoppingCart.type_IMEI_SIM')}
                            style={{
                                color: COLORS.txt808080,
                                color: COLORS.txt147EFB,
                                textDecorationLine: 'underline'
                            }}
                        />
                    </TouchableOpacity>
                </View>
            }
            <View style={{
                paddingRight: 10,
                paddingLeft: 26,
                marginTop: 4,
                width: constants.width - 10,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
            }}>
                <MyText
                    text={`${translate('shoppingCart.quantity_full')} ${Quantity} ${QuantityUnitName}`}
                    style={{
                        color: COLORS.txt333333
                    }}
                />

                {isShowLot && (
                    <TouchableOpacity
                        style={{ paddingHorizontal: 10, marginLeft: 32 }}
                        onPress={onShowLot}>
                        <MyText
                            text={translate('pharmacy.lot_date')}
                            style={{
                                color: COLORS.txt0000FF,
                                textDecorationLine: 'underline'
                            }}
                        />
                    </TouchableOpacity>
                )}
                <MyText
                    text={translate('common.price')}
                    style={{
                        color: COLORS.txt333333,
                        fontWeight: 'bold'
                    }}>
                    <MyText
                        text={helper.convertNum(SalePriceBKVAT)}
                        style={{
                            color: COLORS.txtD0021B,
                            fontWeight: 'normal'
                        }}
                    />
                </MyText>
            </View>
            {
                isAdjust &&
                <View style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    paddingRight: 10,
                    paddingLeft: 26,
                    marginTop: 4,
                    width: constants.width - 10,
                }}>
                    <MyText
                        text={translate(labelKey)}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                    />

                    <MyText
                        text={helper.convertNum(AdjustPrice)}
                        style={{
                            color: COLORS.txtD0021B
                        }}
                    />
                </View>
            }
            {
                !!giftSaleOrders &&
                <PromotionExpand
                    giftSaleOrders={giftSaleOrders}
                    mainProduct={salePromotion}
                />
            }
            <TouchableOpacity style={{
                width: 38,
                height: 24,
                top: 0,
                right: 0,
                borderBottomLeftRadius: 20,
                alignItems: 'center',
                justifyContent: "center",
                backgroundColor: COLORS.btnF5F5F5,
                position: 'absolute',
            }}
                onPress={onPressDelete}
            >
                <Icon
                    iconSet={"MaterialIcons"}
                    name={"close"}
                    color={COLORS.icD0021B}
                    size={20}
                />
            </TouchableOpacity>
        </View>
    );
};

const PromotionSale = ({
    saleSaleOrders,
    onDeleteSalePromotion,
    title,
    onUpdateSim,
    isApplyCoupon,
    applyDetailIDs,
    actionShoppingCart,
    onShowLot
}) => {
    const [isShowDetail, setIsShowDetail] = useState(true);
    const [isVisibleSearchSim, setIsVisibleSearchSim] = useState(false);
    const [promotionTempData, setPromotionTempData] = useState({});
    const [indexSim, setIndexSim] = useState(0);
    const isNonEmty = (saleSaleOrders.length > 0);

    const onShowDetail = () => {
        setIsShowDetail(!isShowDetail);
    };

    return (
        isNonEmty &&
        <View style={[{
            width: constants.width,
            backgroundColor: COLORS.bgFFFFFF,
            paddingVertical: 4,
        }]}>
            <PromotionHeader
                title={title}
                onShow={onShowDetail}
                isShowDetail={isShowDetail}
            />
            {isShowDetail &&
                saleSaleOrders.map((salePromotion, index) => {
                    const {
                        SaleOrderDetailID,
                        ProductID,
                        cus_SaleOrderDetailInfoBOList,
                        ProductName,
                        Quantity,
                        PromotionListID,
                        PromotionID
                    } = salePromotion;
                    const showLot =
                        cus_SaleOrderDetailInfoBOList?.length > 0;
                    const keyExtractor = `${SaleOrderDetailID} Sale${index}`;
                    const marginTop = index !== 0 ? 4 : 0;
                    return (
                        <SaleProduct
                            salePromotion={salePromotion}
                            marginTop={marginTop}
                            key={keyExtractor}
                            onDelete={() => {
                                const newSaleSaleOrders =
                                    saleSaleOrders.filter(
                                        (promtion, position) =>
                                            position !== index
                                    );
                                onDeleteSalePromotion(newSaleSaleOrders, salePromotion);
                            }}
                            onSearchSim={() => {
                                setPromotionTempData({
                                    productID: ProductID,
                                    promotionListId: PromotionListID,
                                    promotionID: PromotionID,
                                    index
                                });
                                setIsVisibleSearchSim(true);
                            }}
                            isApplyCoupon={isApplyCoupon}
                            applyDetailIDs={applyDetailIDs}
                            onShowLot={() =>
                                onShowLot(
                                    ProductName,
                                    Quantity,
                                    SaleOrderDetailID
                                )
                            }
                            isShowLot={showLot}
                        />
                    );
                })}
            {
                isVisibleSearchSim &&
                <ModalSearchSim
                    isVisible={isVisibleSearchSim}
                    hideModal={() => {
                        setIsVisibleSearchSim(false);
                    }}
                    simInfo={saleSaleOrders[indexSim]}
                    updateSim={(promtion) => {
                        const newSaleSaleOrders = [...saleSaleOrders];
                        newSaleSaleOrders[indexSim] = promtion;
                        onUpdateSim(newSaleSaleOrders);
                        setIsVisibleSearchSim(false);
                    }}
                    actionShoppingCart={actionShoppingCart}
                    promotionTempData={promotionTempData}
                    setIndexSim={setIndexSim}
                />
            }
        </View>
    );
};

export default PromotionSale;