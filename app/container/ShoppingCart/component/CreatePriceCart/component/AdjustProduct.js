import React, { useState, useEffect } from 'react';
import {
    View,
    TouchableOpacity,
    Alert
} from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import DocumentPicker from 'react-native-document-picker';
import {
    Icon,
    CaptureCamera,
    Button,
    showBlockUI,
    hideBlockUI,
    MyText,
    ImageCDN,
    NumberInput,
    Picker
} from "@components";
import { constants, API_CONST, ENUM } from "@constants";
import { helper, dateHelper } from "@common";
import { getImageCDN, getPriceNearStore } from "../../../action";
const { API_GET_IMAGE_CDN, API_GET_IMAGE_CDN_NEW } = API_CONST;
import { translate } from '@translate';
import { COLORS } from "@styles";
import { useSelector } from 'react-redux';
const { FILE_PATH: { ADJUST_PRICE } } = ENUM;

const AdjustProduct = ({ product, updateTotalAdjust, isAnkhang }) => {
    const {
        ProductName,
        cus_SalePriceAfterProDeliveryVAT,
        AdjustPriceTypeID,
        AdjustPrice,
        UrlFilesAdjustPrice,
        AllowUseCreatePrice,
        MinSalePriceProductForCreatePrice,
        MaxSalePriceProductForCreatePrice,
        cus_CompanyCompetitorBOList,
        SaleProgramInfo,
        MessageCreatePrice,
        InventoryStatusID,
        OutputTypeID,
        RetailPriceVAT,
        ProductID
    } = product;
    const { storeID } = useSelector((state) => state.userReducer);
    const [isCheck, setIsCheck] = useState(false);
    const [adjustValue, setAdjustValue] = useState(0);
    const [isVisible, setIsVisible] = useState(false);
    const [indexSelected, setIndexSelected] = useState(0);
    const [uriImages, setUriImages] = useState([]);
    const [uriFile, setUriFile] = useState("");
    const [competitor, setCompetitor] = useState({});
    const salePriceAdjust = cus_SalePriceAfterProDeliveryVAT - adjustValue;
    const disabled = !AllowUseCreatePrice;
    const minCreatePrice = helper.convertNum(MinSalePriceProductForCreatePrice, false);
    const maxCreatePrice = helper.convertNum(MaxSalePriceProductForCreatePrice, false);
    const placeholder = `[${minCreatePrice} - ${maxCreatePrice}]`;
    const companyCompetitor = cus_CompanyCompetitorBOList || [];
    const labelCreatePrice = !helper.IsEmptyObject(SaleProgramInfo) ? translate('editSaleOrder.discount_value') : (helper.configStoreCreatePrice(storeID) ? "Phiếu mua hàng hỗ trợ chiến giá" : translate('editSaleOrder.discount_value'))
    const didMount = () => {
        if (AdjustPriceTypeID != 1) {
            const isAdjust = (AdjustPrice != 0);
            const valueAdjust = isAdjust ? -AdjustPrice : 0;
            setIsCheck(isAdjust);
            product.isCheck = isAdjust;
            setAdjustValue(valueAdjust);
            const value = companyCompetitor.find(ele => ele.CompanyCompetitorID == product.CompanyCompetitorID);
            if (value) {
                setCompetitor(value);
            }
        }
    }

    useEffect(
        didMount,
        []
    )

    const openCamera = (index) => () => {
        setIsVisible(true);
        setIndexSelected(index);
    }

    const deleteImage = (index) => () => {
        const newUriImages = [...uriImages]
        newUriImages[index] = '';
        product.UrlFilesAdjustPrice[index] = '';
        setUriImages(newUriImages);
    }

    const closeCamera = () => {
        setIsVisible(false)
    }

    const takePicture = (photo) => {
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper.resizeImage(photo).then(({ path, uri, size, name }) => {
                setIsVisible(false);
                const bodyFromData = new FormData();
                bodyFromData.append('file', {
                    uri,
                    type: 'image/jpg',
                    name
                });
                getImageCDN(bodyFromData)
                    .then((res) => {
                        const remoteURI =
                            API_CONST.API_GET_IMAGE_CDN + res[0];
                        let newUriImages = [...uriImages];
                        newUriImages[indexSelected] = remoteURI;
                        product.UrlFilesAdjustPrice[indexSelected] = remoteURI;
                        setUriImages(newUriImages);
                        hideBlockUI();
                    })
                    .catch((error) => {
                        hideBlockUI();
                        console.log('uploadPicture', error);
                    });
            }
            ).catch((error) => {
                hideBlockUI();
                console.log("resizeImage", error);
            });
        }
    }

    const onPickerPhoto = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper.resizeImage(response).then(({ path, uri, size, name }) => {
                        setIsVisible(false);
                        const bodyFromData = new FormData();
                        bodyFromData.append('file', {
                            uri,
                            type: 'image/jpg',
                            name
                        });
                        getImageCDN(bodyFromData)
                            .then((res) => {
                                const remoteURI =
                                    API_CONST.API_GET_IMAGE_CDN + res[0];
                                let newUriImages = [...uriImages];
                                newUriImages[indexSelected] = remoteURI;
                                product.UrlFilesAdjustPrice[indexSelected] = remoteURI;
                                setUriImages(newUriImages);
                                hideBlockUI();
                            })
                            .catch((error) => {
                                hideBlockUI();
                                console.log('uploadPicture', error);
                            });
                    }).catch((error) => {
                        hideBlockUI();
                        console.log("resizeImage", error);
                    });
                }
            });
    }

    const getFromDataImage = () => {
        const formData = new FormData();
        const mapIndex = [];
        uriImages.forEach((ele, index) => {
            if (!UrlFilesAdjustPrice[index] && !!ele) {
                const uri = ele
                const type = 'image/jpg'
                const name = `${dateHelper.getTimestamp()}_${index}.jpg`
                const path = "/AdjustPrice"

                formData.append('file', { uri, type, name });
                formData.append('path', path);
                formData.append('isGenDate', "false");
                formData.append('isGenName', "false");
                mapIndex.push(index);
            }
        })
        uploadPicture(formData, mapIndex);
    }

    const uploadPicture = (fromData, mapIndex) => {
        showBlockUI();
        getImageCDN(fromData).then(cdnImages => {
            hideBlockUI();
            let newUriImages = [...uriImages];
            cdnImages.forEach((name, index) => {
                const uri = API_GET_IMAGE_CDN_NEW + name;
                const position = mapIndex[index];
                newUriImages[position] = uri;
                product.UrlFilesAdjustPrice[position] = uri;
            });
            setUriImages(newUriImages);
        }).catch(msgError => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        onPress: hideBlockUI,
                        style: "cancel"
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        onPress: () => uploadPicture(fromData, mapIndex),
                        style: "default"
                    },
                ],
                { cancelable: false },
            );
        })
    }

    const deleteFile = () => {
        product.UrlFilesAdjustPrice[3] = '';
        setUriFile("");
        setIndexSelected(-1);
    }

    const uploadFileAttach = (fromData) => {
        showBlockUI();
        getImageCDN(fromData).then(cdnFiles => {
            hideBlockUI();
            const uri = API_GET_IMAGE_CDN_NEW + cdnFiles[0];
            setUriFile(uri);
            product.UrlFilesAdjustPrice[3] = uri;
        }).catch(msgError => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        onPress: hideBlockUI,
                        style: "cancel"
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        onPress: () => uploadFileAttach(fromData),
                        style: "default"
                    },
                ],
                { cancelable: false },
            );
        })
    }

    const onPickerDocument = () => {
        DocumentPicker.pick({
            type: MIME_TYPE,
        }).then(fileInfo => {
            console.log("onPickerDocument success", fileInfo);
            const { isValidatesize, isValidateType } = checkFilePermission(fileInfo);
            if (!isValidatesize) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('shoppingCart.attachment_must_less_than_3MB'),
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: () => { },
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: onPickerDocument,
                            style: "default"
                        }
                    ],
                    { cancelable: false },
                );
            }
            else if (!isValidateType) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    `${translate('shoppingCart.file_format')} "${fileInfo.name}" ${translate('shoppingCart.not_supported')}`,
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: () => { },
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: onPickerDocument,
                            style: "default"
                        }
                    ],
                    { cancelable: false },
                );
            }
            else {
                const bodyFromData = helper.createFormData({ uri: fileInfo.uri, type: fileInfo.type, name: fileInfo.name, path: ADJUST_PRICE });
                uploadFileAttach(bodyFromData);
            }
        }).catch(error => {
            console.log("onPickerDocument error", error);
        });
    }

    const onCheckProduct = () => {
        setIsCheck(!isCheck)
        product.isCheck = !isCheck;
    }

    const handleChangeCompetitor = (competitor) => {
        const updatedCompetitor = { ...competitor };

        if (updatedCompetitor.cus_IsCheckCreatePriceNearestStore) {
            updatePriceFromNearestStore(updatedCompetitor);
        }
        else {
            processAdjustedPrice(updatedCompetitor);
        }
    };

    const updatePriceFromNearestStore = async (competitor) => {
        showBlockUI();
        try {
            const body = {
                storeID,
                productID: ProductID,
                outputTypeID: OutputTypeID,
                inventoryStatusID: InventoryStatusID,
                retailPriceVAT: RetailPriceVAT,
                loginStoreId: storeID
            };

            const { MaxAdjustPrice, MinAdjustPrice } = await getPriceNearStore(body);

            competitor.cus_MaxSalePriceProductForCreatePrice = MaxAdjustPrice;
            competitor.cus_MinSalePriceProductForCreatePrice = MinAdjustPrice;

            processAdjustedPrice(competitor);

        }
        catch (error) {
            Alert.alert(translate('common.notification_uppercase'),
                error,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "default",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => {
                            updatePriceFromNearestStore(competitor)

                        }
                    }
                ]);
        }
        finally {
            hideBlockUI();
        }
    };

    const processAdjustedPrice = (competitor) => {
        const {
            cus_MaxSalePriceProductForCreatePrice,
            cus_MinSalePriceProductForCreatePrice,
            CompanyCompetitorID,
            cus_AdjustPriceByStoreID,
        } = competitor;
        Object.assign(product, {
            AdjustPrice: 0,
            MinSalePriceProductForCreatePrice: cus_MinSalePriceProductForCreatePrice,
            MaxSalePriceProductForCreatePrice: cus_MaxSalePriceProductForCreatePrice,
            CompanyCompetitorID,
            cus_AdjustPriceByStoreID,
        });
        setAdjustValue("");
        setCompetitor(competitor);
    };


    return (
        <View style={{
            width: constants.width,
        }}>
            <FieldTitle
                title={ProductName}
                isCheck={isCheck}
                onCheck={onCheckProduct}
                disabled={disabled}
                MessageCreatePrice={MessageCreatePrice}
            />
            {
                isCheck &&
                <View style={{
                    width: constants.width,
                    paddingVertical: 8
                }}>
                    <View style={{
                        width: constants.width,
                        justifyContent: "center",
                        alignItems: "center",
                        flexDirection: "row",
                        paddingHorizontal: 10,
                        marginBottom: 10,
                    }}>
                        <MyText
                            text={translate('shoppingCart.competitive_price_opponent')}
                            style={{
                                color: COLORS.txt333333,
                                fontWeight: 'bold',
                                width: 140
                            }}
                        />
                        <Picker
                            style={{
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                height: 36,
                                borderRadius: 4,
                                borderWidth: 1,
                                borderColor: COLORS.bdE4E4E4,
                                width: constants.width - 160,
                                backgroundColor: COLORS.btnFFFFFF,
                                paddingHorizontal: 4
                            }}
                            label={"CompetitorName"}
                            value={"CompanyCompetitorID"}
                            defaultLabel={translate('shoppingCart.picker_opponent')}
                            valueSelected={competitor.CompanyCompetitorID}
                            data={companyCompetitor}
                            onChange={handleChangeCompetitor}
                            onEmpty={() => {
                                Alert.alert("", translate('shoppingCart.no_information_adjust_price'));
                            }}
                        />
                    </View>
                    <View style={{
                        width: constants.width,
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        paddingHorizontal: 10,
                        marginBottom: 4
                    }}>
                        <MyText
                            text={translate('shoppingCart.current_price')}
                            style={{
                                color: COLORS.txt444444,
                                fontWeight: 'bold'
                            }}
                        />
                        <MyText
                            text={helper.convertNum(cus_SalePriceAfterProDeliveryVAT)}
                            addSize={2}
                            style={{
                                color: COLORS.txtD0021B
                            }}
                        />
                    </View>

                    <View style={{
                        width: constants.width,
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        paddingHorizontal: 10,
                        marginBottom: 4
                    }}>
                        <MyText
                            text={labelCreatePrice}
                            style={{
                                color: COLORS.txt444444,
                                fontWeight: 'bold',
                                marginRight: 2
                            }}
                        />
                        <NumberInput
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                backgroundColor: COLORS.bgFFFFFF,
                                color: COLORS.txt333333,
                                height: 40,
                                justifyContent: "flex-end",
                                textAlign: 'right',
                                width: 130,
                                marginLeft: 2,
                                fontSize: 14
                            }}
                            placeholder={placeholder}
                            value={adjustValue}
                            maxValue={MaxSalePriceProductForCreatePrice}
                            onChangeText={(value) => {
                                setAdjustValue(value);
                                product.AdjustPrice = -value;
                                updateTotalAdjust();
                            }}
                        />
                    </View>
                    {(!helper.IsEmptyObject(SaleProgramInfo) || !helper.configStoreCreatePrice(storeID)) && <View style={{
                        width: constants.width,
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        paddingHorizontal: 10,
                        marginBottom: 8
                    }}>
                        <MyText
                            text={translate('shoppingCart.price_after_competition')}
                            style={{
                                color: COLORS.txt444444,
                                fontWeight: 'bold'
                            }}
                        />
                        <MyText
                            text={helper.convertNum(salePriceAdjust)}
                            addSize={2}
                            style={{
                                color: COLORS.txtD0021B
                            }}
                        />
                    </View>
                    }

                    <View style={{
                        width: constants.width,
                        paddingHorizontal: 10
                    }}>
                        <MyText
                            text={translate('shoppingCart.attach_files')}
                            style={{
                                color: COLORS.txt444444,
                                fontWeight: 'bold',
                                marginBottom: 2
                            }}>
                            <MyText
                                text={translate('shoppingCart.max_attached_files_quantity')}
                                style={{
                                    color: COLORS.txt288AD6,
                                    fontWeight: 'normal'
                                }}
                            />
                        </MyText>

                        <View style={{
                            flexDirection: "row",
                            width: constants.width - 20,
                        }}>
                            <ImageAdjust
                                onCamera={openCamera(0)}
                                onDelete={deleteImage(0)}
                                onUpload={getFromDataImage}
                                urlLocal={uriImages[0]}
                                urlRemote={UrlFilesAdjustPrice[0]}
                                title={translate('shoppingCart.opponent_price')}
                                isRequire={!isAnkhang}
                            />
                            <ImageAdjust
                                onCamera={openCamera(1)}
                                onDelete={deleteImage(1)}
                                onUpload={getFromDataImage}
                                urlLocal={uriImages[1]}
                                urlRemote={UrlFilesAdjustPrice[1]}
                                title={translate('shoppingCart.MWG_price')}
                                isRequire={!isAnkhang}
                            />
                            <ImageAdjust
                                onCamera={openCamera(2)}
                                onDelete={deleteImage(2)}
                                onUpload={getFromDataImage}
                                urlLocal={uriImages[2]}
                                urlRemote={UrlFilesAdjustPrice[2]}
                                title={translate('shoppingCart.opponent_inStock')}
                            />
                        </View>
                    </View>

                    <FileAdjust
                        onPicker={onPickerDocument}
                        onDelete={deleteFile}
                        urlLocal={uriFile}
                        urlRemote={UrlFilesAdjustPrice[3]}
                    />
                </View>
            }

            <CaptureCamera
                isVisibleCamera={isVisible}
                takePicture={takePicture}
                closeCamera={closeCamera}
                selectPicture={onPickerPhoto}
            />
        </View>
    );
}

export default AdjustProduct;


const FieldTitle = ({
    title,
    isCheck,
    onCheck,
    disabled,
    MessageCreatePrice
}) => {
    return (
        <TouchableOpacity
            style={{
                flexDirection: "row",
                width: constants.width,
                // height: 40,
                paddingHorizontal: 10,
                borderBottomWidth: 2,
                borderBottomColor: COLORS.bdFFFFFF,
                backgroundColor: disabled ? COLORS.btnEEEEEE : COLORS.btnFDF9E5,
                opacity: disabled ? 0.8 : 1,
                alignItems: "center",
                paddingVertical: 5
            }}
            onPress={onCheck}
            disabled={disabled}
        >
            <View>
                <Icon
                    iconSet={"Ionicons"}
                    name={
                        isCheck
                            ? "checkbox-outline"
                            : "square-outline"
                    }
                    color={COLORS.ic288AD6}
                    size={20}
                />
            </View>

            <View >
                <MyText
                    text={title}
                    style={{
                        width: constants.width - 45,
                        color: COLORS.txt288AD6,
                        fontWeight: 'bold',
                        marginLeft: 5
                    }}
                    numberOfLines={2}
                />
                {!!MessageCreatePrice && <MyText
                    addSize={-2.5}
                    text={`(${MessageCreatePrice})`}
                    style={{
                        width: constants.width - 45,
                        color: COLORS.txtF50537,
                        fontWeight: 'bold',
                        marginLeft: 5
                    }}
                />}

            </View>

        </TouchableOpacity>
    );
}

const ImageAdjust = ({
    onCamera,
    onDelete,
    onUpload,
    urlLocal,
    urlRemote,
    title,
    isRequire
}) => {
    const uriImage = urlRemote ? urlRemote : urlLocal;
    const isNonImage = !uriImage;
    return (
        <View style={{
            flex: 1,
        }}>
            <MyText
                text={title}
                addSize={-1.5}
                style={{
                    color: COLORS.txt333333,
                    textAlign: 'center'
                }}>
                {isRequire && (
                    <MyText text={'*'} style={{ color: COLORS.txtFF0000 }}></MyText>
                )}
            </MyText>
            {
                isNonImage
                    ? <TouchableOpacity style={{
                        height: 120,
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: COLORS.btnF5F5F5,
                        marginHorizontal: 1
                    }}
                        onPress={onCamera}
                    >
                        <Icon
                            iconSet={"Ionicons"}
                            name={"ios-camera"}
                            color={COLORS.icFFB23F}
                            size={60}
                        />
                    </TouchableOpacity>
                    : <View style={{
                        flex: 1,
                        justifyContent: "flex-start",
                        alignItems: "center",
                        marginHorizontal: 1
                    }}>
                        <View style={{
                            width: 100,
                            height: 120,
                        }}>
                            <ImageCDN
                                style={{
                                    width: 100,
                                    height: 120,
                                }}
                                uri={uriImage}
                                resizeMode={"contain"}
                            />
                        </View>
                        {
                            !urlRemote &&
                            <Button
                                text={translate('shoppingCart.btn_update')}
                                styleContainer={{
                                    width: 70,
                                    height: 30,
                                    backgroundColor: COLORS.btn288AD6,
                                    borderRadius: 4,
                                    marginVertical: 10
                                }}
                                styleText={{
                                    color: COLORS.txtFFFFFF,
                                    fontSize: 12
                                }}
                                onPress={onUpload}
                            />
                        }
                        <TouchableOpacity style={{
                            padding: 5,
                            justifyContent: "center",
                            alignItems: "center",
                            position: "absolute",
                            top: 0,
                            right: 0,
                            backgroundColor: COLORS.btnF2F2F2
                        }}
                            onPress={onDelete}
                        >
                            <MyText
                                text={translate('shoppingCart.delete')}
                                style={{
                                    color: COLORS.txtD0021B
                                }}
                            />
                        </TouchableOpacity>
                    </View>
            }
        </View>
    );
}

const FileAdjust = ({
    onPicker,
    onDelete,
    urlLocal,
    urlRemote
}) => {
    const uriFile = urlRemote ? urlRemote : urlLocal;
    const isNonFile = !uriFile;
    return (isNonFile
        ? <TouchableOpacity style={{
            flexDirection: 'row',
            borderRadius: 4,
            paddingVertical: 2,
            alignItems: "center",
            alignSelf: "flex-start",
            marginLeft: 10,
            marginTop: 8
        }}
            onPress={onPicker}
        >
            <Icon
                iconSet={"MaterialIcons"}
                name={"attachment"}
                size={25}
                color={COLORS.icEB1478}
            />
            <MyText
                text={translate('shoppingCart.add_files')}
                addSize={2}
                style={{
                    color: COLORS.txtEB1478,
                    marginLeft: 2
                }}
            />
        </TouchableOpacity>
        : <View style={{
            width: constants.width,
            paddingHorizontal: 10,
            flexDirection: "row",
            marginTop: 8,
        }}>
            <TouchableOpacity style={{
                alignItems: "center",
                width: 40,
                height: 30,
            }}
                onPress={onDelete}
            >
                <Icon
                    iconSet={"MaterialIcons"}
                    name={"delete-forever"}
                    size={24}
                    color={COLORS.icEB1478}
                />
            </TouchableOpacity>
            <View style={{
                width: constants.width - 60
            }}>
                <MyText
                    text={uriFile}
                    style={{
                        color: COLORS.txt2529D8
                    }}
                />
            </View>
        </View>
    );
}

const checkFilePermission = (fileInfo) => {
    const { type, size } = fileInfo;
    const sizeMb = size / (1024 * 1024);
    const isValidatesize = (sizeMb < 3);
    const isValidateType = TYPE_REDUCE.some(typeReduce => type.includes(typeReduce))
    return { isValidatesize, isValidateType };
}

const MIME_TYPE = [
    DocumentPicker.types.images,
    DocumentPicker.types.audio,
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/msword",
    "application/vnd.ms-excel"
];

const TYPE_REDUCE = [
    "image/",
    "audio/",
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/msword",
    "application/vnd.ms-excel"
]
