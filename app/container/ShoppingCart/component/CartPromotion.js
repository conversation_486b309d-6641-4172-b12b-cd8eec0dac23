import React, { useState, useEffect } from 'react';
import {
    View,
    StyleSheet,
    ScrollView,
    Alert
} from 'react-native';
import {
    BaseLoading,
    MyText,
    hideBlockUI,
    showBlockUI
} from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import Promotion from "./Promotion/index";
import { translate } from '@translate';
import { COLORS } from "@styles";
import { NonePromotion } from '../../AnKhangNew/frames/CartPromotionSheet';
import { TabBar, TabView } from 'react-native-tab-view';
import { getListCSPromotion } from '../../AnKhangNew/action';
import { useSelector } from 'react-redux';

const { STATUS_PROMOTION } = constants

const CartPromotion = ({
    csPromotion = [],
    promotion,
    statePromotion,
    applyPromotion,
    onRetryCartPromotion,
    isVisibleVoucher,
    voucherPromotion,
    keyPromotion,
    actionDetail
}) => {
    const {
        isFetching,
        description,
        isError
    } = statePromotion;
    const { storeID, languageID, moduleID, brandID, provinceID
    } = useSelector(
        (state) => state.userReducer
    );
    const baseRequest = {
        moduleID,
        languageID,
        loginStoreId: storeID,
    };
    const [index, setIndex] = useState(0);
    const [routes, setRoutes] = useState([
        { key: 'giftpromotion', title: 'Quà tặng' },
        { key: 'cspromotion', title: 'Bán kèm' },
    ]);
    const [groupIDCondition, setGroupIDCondition] = useState(new Set());
    const [dataPromotion, setDataPromotion] = useState([]);
    const isVisiblePromotion = (dataPromotion.length > 0);
    const isVisibleCSPromotion = csPromotion.length > 0;
    const isVisible = (isFetching || isError || isVisiblePromotion || isVisibleVoucher || isVisibleCSPromotion);
    const [csPromotions, setCSPromotions] = useState([]);
    const [reRender, setReRender] = useState(1);


    const effChangeVoucher = () => {
        if (!isVisibleVoucher) {
            keepKeyPromotionSelected(dataPromotion);
        }
    }

    useEffect(
        effChangeVoucher,
        [isVisibleVoucher]
    )

    const effChangeDataPromotion = () => {
        setGroupIDCondition(new Set());
        const newDataPromotion = promotion.filter(ele => !ele.invisibleByCustomerPhone).map((promotion) => {
            return promotion.isViewBarcode ? { ...promotion, statusPromotion: STATUS_PROMOTION.INIT } : promotion

        });
        setDataPromotion(newDataPromotion);
        setReRender(reRender + 1)
    }

    useEffect(
        effChangeDataPromotion,
        [promotion]
    )

    const keepKeyPromotionSelected = (data) => {
        const {
            allKeyPromotion,
            allGroupID,
        } = helper.getAllKeyPromotion(data, []);
        const isEmptyKeySelected = (keyPromotion.size == 0);
        const isEmptyAllKey = (allKeyPromotion.size == 0);
        let newKeyPromotion = new Set();
        let newGroupIDCondition = new Set();

        if (!isEmptyAllKey && !isEmptyKeySelected) {
            newKeyPromotion = cloneKeySet(keyPromotion);
            newGroupIDCondition = cloneKeySet(groupIDCondition);
            for (let key of newKeyPromotion) {
                if (!allKeyPromotion.has(key)) {
                    newKeyPromotion.delete(key);
                }
            }
            for (let key of newGroupIDCondition) {
                if (!allGroupID.has(key)) {
                    newGroupIDCondition.delete(key);
                }
            }
        }
        setGroupIDCondition(newGroupIDCondition);
        onChangeKeyPromotion(newKeyPromotion);
    }

    const getListPromotionSelected = (allPromotion, setKeyPromotionSelected) => {
        let listPromotion = [];
        if (setKeyPromotionSelected.size > 0) {
            allPromotion.forEach(groupPromotion => {
                const { promotionProducts, promotionGroupID } = groupPromotion;
                let productSelected = promotionProducts.filter((product, index) => {
                    const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    return setKeyPromotionSelected.has(key);
                });
                if (productSelected.length > 0) {
                    listPromotion.push({
                        ...groupPromotion,
                        promotionProducts: productSelected
                    })
                }
            })
        }
        return listPromotion;
    }

    const onChangeKeyPromotion = (newKeyPromotion) => {
        const isHasChange = isNotEqual(keyPromotion, newKeyPromotion);
        if (isHasChange) {
            const allPromotion = [...promotion, ...csPromotion]
            const promotionGroups = getListPromotionSelected(allPromotion, newKeyPromotion);
            applyPromotion(promotionGroups, newKeyPromotion);
        }
    }

    const updateKeyPromotionSelected = (setKeyPromotionSelected, excludePromotionIDs, saleProductGroupID) => {
        if (helper.IsEmptyArray(excludePromotionIDs)) {
            onChangeKeyPromotion(setKeyPromotionSelected);
        }
        else {
            const newSetKeyPromotionSelected = helper.excludeKeyPromotionSelected(
                promotion,
                [],
                setKeyPromotionSelected,
                excludePromotionIDs,
                saleProductGroupID,
                csPromotion
            );
            onChangeKeyPromotion(newSetKeyPromotionSelected);
        }
    }
    const onGetCSProducts = (info) => {
        const { promotionListGroupID, promotionID } = info;
        const index = csPromotions.findIndex((pro) => pro.promotionID === promotionID);
        if (index !== -1) {
            showBlockUI()
            getListCSPromotion({
                ...baseRequest,
                'promotionType': csPromotions[index].promotionType,
                'promotionListGroupID': promotionListGroupID,
                'promotionId': promotionID,
                isAdvise: true
            })
                .then(({ products: newProducts }) => {
                    hideBlockUI()
                    const newCSPromotions = [...csPromotions];
                    if (helper.IsEmptyArray(newProducts)) {
                        Alert.alert("",
                            'Chương trình không có khuyến mãi.',
                            [
                                {
                                    text: "Oke",
                                    style: "default",
                                    onPress: hideBlockUI
                                }
                            ]
                        )
                    }
                    else {
                        // const lowerCase = str => str[0].toLowerCase() + str.slice(1);
                        // const newProductList = newProducts.map(
                        //     obj => Object.fromEntries(Object.entries(obj).map(
                        //         ([k, v]) => [lowerCase(k), v])
                        //     )
                        // );

                        // const newPromotionProducts = newProductList.map(({
                        //     vAT: vat,
                        //     vATPercent: vatPercent,
                        //     ...rest
                        // }) => ({
                        //     vat,
                        //     vatPercent,
                        //     ...rest
                        // }));
                        newCSPromotions[index].promotionProducts = newProducts;
                        setCSPromotions(newCSPromotions);
                    }

                })
                .catch((errorMsg) => {
                    Alert.alert("THÔNG BÁO",
                        errorMsg,
                        [
                            {
                                text: "Oke",
                                style: "cancel",
                                onPress: hideBlockUI

                            }
                            ,
                            {
                                text: translate('common.btn_notify_try_again'),
                                style: "default",
                                onPress: () => { onGetCSProducts(info) }
                            }
                        ]
                    )
                });
        }
    };


    const renderTabBar = props => (
        <TabBar
            {...props}
            activeColor={COLORS.bg5B9A68}
            inactiveColor={'black'}
            style={{ backgroundColor: COLORS.bgFFFFFF }}
        />
    );

    const renderScene = ({ route }) => {
        switch (route.key) {
            case 'giftpromotion':
                return <GiftPromotionRoute
                    dataPromotion={dataPromotion}
                    setKeyPromotionSelected={cloneKeySet(keyPromotion)}
                    updateKeyPromotionSelected={updateKeyPromotionSelected}
                    setGroupIDCondition={cloneKeySet(groupIDCondition)}
                    updateGroupIDCondition={(
                        newSetGroupIDCondition,
                        newSetKeyPromotionSelected,
                    ) => {
                        setGroupIDCondition(newSetGroupIDCondition);
                        onChangeKeyPromotion(newSetKeyPromotionSelected);
                    }}
                    actionDetail={actionDetail}
                    isVisibleVoucher={isVisibleVoucher}
                    isVisiblePromotion={isVisiblePromotion}
                    voucherPromotion={voucherPromotion}
                />;
            case 'cspromotion':
                return <CSromotionRoute
                    dataCSPromotion={csPromotion}
                    setKeyPromotionSelected={cloneKeySet(keyPromotion)}
                    updateKeyPromotionSelected={updateKeyPromotionSelected}
                    setGroupIDCondition={cloneKeySet(groupIDCondition)}
                    updateGroupIDCondition={(
                        newSetGroupIDCondition,
                        newSetKeyPromotionSelected,
                    ) => {
                        setGroupIDCondition(newSetGroupIDCondition);
                        onChangeKeyPromotion(newSetKeyPromotionSelected);
                    }}
                    actionDetail={actionDetail}
                    onGetCSProducts={onGetCSProducts}
                />;
            default:
                return null;
        }
    };



    useEffect(() => {
        if (csPromotion.length > 0) {
            setCSPromotions(csPromotion);
            setReRender(reRender + 1)

        }
    }, [csPromotion]);


    return (
        isVisible &&
        <View style={{
            width: constants.width
        }}>
            <View style={{
                backgroundColor: COLORS.bg70B29C,
                width: constants.width,
                justifyContent: 'center',
                paddingHorizontal: 10,
                height: 40,
                borderTopWidth: StyleSheet.hairlineWidth,
                borderTopColor: COLORS.bdFFFFFF
            }}>
                <MyText
                    text={translate('shoppingCart.total_promotion')}
                    style={{
                        color: COLORS.txtFFFF00,
                        fontWeight: 'bold'
                    }}
                />
            </View>
            <BaseLoading
                isLoading={isFetching}
                isEmpty={false}
                textLoadingError={description}
                isError={isError}
                onPressTryAgains={onRetryCartPromotion}
                content={
                    reRender && (dataPromotion.length > 0 || csPromotions.length > 0) &&
                    <View style={{
                        width: constants.width,
                        height: helper.IsNonEmptyArray(dataPromotion?.concat(csPromotions)) ? 300 : 100,
                    }}>
                        <TabView
                            navigationState={{
                                index, routes,
                            }}
                            renderTabBar={renderTabBar}
                            renderScene={renderScene}
                            onIndexChange={setIndex}
                        />
                    </View>
                }
            />
        </View>
    );
}

export default CartPromotion;

const cloneKeySet = (keySet) => {
    return new Set(keySet);
}

const isNotEqual = (set_1, set_2) => {
    if (set_1.size !== set_2.size) {
        return true;
    }
    for (const ele of set_1) {
        if (!set_2.has(ele)) {
            return true;
        }
    }
    return false;
}



const GiftPromotionRoute = (props) => {
    const {
        dataPromotion,
        setKeyPromotionSelected,
        updateKeyPromotionSelected,
        setGroupIDCondition,
        updateGroupIDCondition,
        actionDetail,
        isVisibleVoucher,
        isVisiblePromotion,
        voucherPromotion
    } = props
    return (
        <View style={{
            backgroundColor: COLORS.bgFDF9E5, flex: 1
        }}>
            {
                isVisiblePromotion || isVisibleVoucher
                    ?
                    <View>
                        {
                            isVisiblePromotion &&
                            <Promotion
                                dataPromotion={dataPromotion}
                                setKeyPromotionSelected={setKeyPromotionSelected}
                                updateKeyPromotionSelected={updateKeyPromotionSelected}
                                setGroupIDCondition={setGroupIDCondition}
                                updateGroupIDCondition={updateGroupIDCondition}
                                actionDetail={actionDetail}
                                presentTab={"GIFT"}
                            />
                        }
                        {
                            isVisibleVoucher &&
                            <Promotion
                                dataPromotion={voucherPromotion}
                                setKeyPromotionSelected={setKeyPromotionSelected}
                                updateKeyPromotionSelected={updateKeyPromotionSelected}
                                setGroupIDCondition={setGroupIDCondition}
                                updateGroupIDCondition={updateGroupIDCondition}
                                actionDetail={actionDetail}
                                presentTab={"GIFT"}
                            />
                        }
                    </View>
                    :
                    <NonePromotion text="Không có khuyến mãi tặng quà." />

            }

        </View>
    );
}


const CSromotionRoute = (props) => {
    const {
        dataCSPromotion,
        setKeyPromotionSelected,
        updateKeyPromotionSelected,
        setGroupIDCondition,
        updateGroupIDCondition,
        actionDetail,
        onGetCSProducts
    } = props
    return (
        <View style={{
            backgroundColor: COLORS.bgFDF9E5, flex: 1
        }} >
            {
                helper.IsNonEmptyArray(dataCSPromotion)
                    ?
                    <Promotion
                        dataPromotion={dataCSPromotion}
                        setKeyPromotionSelected={setKeyPromotionSelected}
                        updateKeyPromotionSelected={updateKeyPromotionSelected}
                        setGroupIDCondition={setGroupIDCondition}
                        updateGroupIDCondition={updateGroupIDCondition}
                        actionDetail={actionDetail}
                        onGetCSProducts={onGetCSProducts}
                        presentTab={"CROSSSALE"}
                    />
                    :
                    <NonePromotion text="Không có khuyến mãi bán kèm." />
            }
        </View>
    )
}

