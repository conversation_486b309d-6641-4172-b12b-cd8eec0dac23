import React, { useState, useCallback } from 'react';
import { Alert, Keyboard, StyleSheet, View } from 'react-native';
import { COLORS } from '@styles';
import { translate } from '@translate';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
    BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { helper } from '@common';
import { hideBlockUI, showBlockUI } from '@components';
import Guide from '../../ShoppingCart/component/InstallmentOTP/component/Guide';
import useCountdown from '../../../common/hooks/useCountdown';
import Customer from './InstallmentOTP/component/Customer';
import OtpCode from './InstallmentOTP/component/OtpCode';
import { createOTP, verifyOTP as apiVerifyOTP } from '../action';
import OTPBackup from "../../ShoppingCart/component/OTPBackup"

const OTPInner = ({ onConfirm, customerInfo, typeOTP, price = 0, isHideBlock = false }) => {
    const [otpCode, setOtpCode] = useState('');
    const [expireTime, setExpireTime] = useState(0);
    const [onlySms, setOnlySms] = useState(false);
    const [isCall, setIsCall] = useState(false);
    const userInfo = useSelector((_state) => _state.userReducer);
    const { startCountdown, resetCountdown } = useCountdown(setExpireTime, 60);

    useEffect(() => {
        hideBlockUI();
    }, []);

    const onCreateOTP = useCallback(
        async (type) => {

            startCountdown();


            // const { customerPhone } = customerInfo;
            // const { brandID } = userInfo;
            // setIsCall(type === 'CALLCENTER');
            // startCountdown();
            // try {
            //     showBlockUI();
            //     const isSMS = await createOTP({
            //         type,
            //         phoneNumber: customerPhone,
            //         typeContent: typeOTP == "LATTER" ? "INSTALLMENT" : typeOTP,
            //         lenOtp: 4,
            //         brandID,
            //         onlySms,
            //         price
            //     });
            //     if (!isCall) {
            //         setOnlySms(isSMS);
            //     }
            //     hideBlockUI();
            // } catch (msgError) {
            //     setOtpCode('');
            //     resetCountdown();
            //     Alert.alert(
            //         translate('common.notification_uppercase'),
            //         msgError,
            //         [
            //             {
            //                 text: translate('common.btn_skip'),
            //                 style: 'cancel',
            //                 onPress: hideBlockUI
            //             },
            //             {
            //                 text: translate('common.btn_notify_try_again'),
            //                 style: 'default',
            //                 onPress: () => onCreateOTP(type)
            //             }
            //         ]
            //     );
            // }
        },
        [customerInfo, isCall, startCountdown, resetCountdown]
    );

    const onCheckOTP = useCallback(() => {
        Keyboard.dismiss();
        if (validateOTP(otpCode)) {
            verifyOTP(otpCode, customerInfo.customerPhone);
        }
    }, [otpCode, customerInfo.customerPhone, validateOTP]);

    const verifyOTP = useCallback(async (otpCode, customerPhone) => {

        showBlockUI()
        setTimeout(() => {
            setOtpCode('');
            resetCountdown();
            onConfirm();
            isHideBlock && hideBlockUI()
        }, 200)

        // showBlockUI();
        // try {
        //     const data = await apiVerifyOTP(otpCode, customerPhone);
        //     onConfirm();
        // } catch (msgError) {
        //     Alert.alert(translate('common.notification_uppercase'), msgError, [
        //         {
        //             text: translate('common.btn_skip'),
        //             style: 'cancel',
        //             onPress: hideBlockUI
        //         },
        //         {
        //             text: translate('common.btn_notify_try_again'),
        //             style: 'default',
        //             onPress: () => verifyOTP(otpCode, customerPhone)
        //         }
        //     ]);
        // }
    }, []);

    const validateOTP = useCallback((code) => {
        const regExpOTP = /^\d{4}$/;
        if (!helper.IsNonEmptyString(code)) {
            Alert.alert('', translate('shoppingCart.validate_empty_otp'));
            return false;
        }
        if (!regExpOTP.test(code)) {
            Alert.alert('', translate('shoppingCart.validate_otp'));
            return false;
        }
        return true;
    }, []);

    return (
        <BottomSheetScrollView style={styles.scrollView}>
            {
                helper.validateOtpSend() ? <View style={{ marginTop: 10, paddingBottom: 10 }}>
                    <OTPBackup
                        onPress={() => onConfirm()}
                        label={helper.getGuideOTPBackup(typeOTP)}
                    >
                        <Customer
                            info={customerInfo}
                        />
                    </OTPBackup>
                </View> : <View style={{
                }}>
                    <Guide type={typeOTP} />
                    <Customer info={customerInfo} />
                    <OtpCode
                        onCreate={onCreateOTP}
                        expireTime={expireTime}
                        code={otpCode}
                        onChange={(text) => {
                            const regExpOTP = /^\d{0,4}$/;
                            if (regExpOTP.test(text)) {
                                setOtpCode(text);
                            }
                        }}
                        onVerify={onCheckOTP}
                        onlySms={onlySms}
                        isBottomSheet={true}
                    />
                </View>
            }

        </BottomSheetScrollView>
    );
};

export default OTPInner;

const styles = StyleSheet.create({
    scrollView: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    safeAreaView: {
        flex: 1
    }
});
