import React, { useState } from 'react';
import { SafeAreaView, Alert, View, StyleSheet } from 'react-native';
import { CaptureCamera, showBlockUI, hideBlockUI, BarcodeCamera, MyText } from '@components';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { API_CONST } from "@constants";
import { COLORS } from '@styles';
import { helper } from '@common';
import {
    FormButton,
    FormInput,
    FormImage,
    FromRadio
} from "./conponents";
import * as actionShoppingCartCreator from "../../action";
const { API_GET_IMAGE_CDN } = API_CONST;

function FormCoupon({ route, navigation, actionShoppingCart, dataShoppingCart }) {
    console.log('FormCoupon', route.params);
    const [formData, setFormData] = useState(route.params.cus_CouponAttachmentRequireList);
    const [idCam, setIDCam] = useState(-1);
    const [idBarcode, setIDBarcode] = useState(-1);

    const onChangeValue = (id) => (value) => {
        formData[id].Value = value;
        setFormData([...formData]);
    }
    const onClearValue = (id) => () => {
        formData[id].Value = "";
        setFormData([...formData]);
    }
    const onCapture = (id) => () => {
        setIDCam(id)
    }
    const onBarcode = (id) => () => {
        setIDBarcode(id)
    }
    const onTakePicture = (photo) => {
        const id = idCam;
        setIDCam(-1);
        const body = new FormData();
        body.append('file', {
            uri: photo.uri,
            type: 'image/jpeg',
            name: `${Date.now()}FormCoupon.jpeg`,
        });
        showBlockUI();
        actionShoppingCartCreator.getImageCDN(body).then(cdnImages => {
            hideBlockUI();
            formData[id].Value = API_GET_IMAGE_CDN + cdnImages[0];
            setFormData([...formData]);
        }).catch(error => {
            hideBlockUI();
            alert("onTakePicture error");
        })
    }
    const onResultBarcode = (value) => {
        const id = idBarcode;
        console.log("🚀 ~ onResultBarcode ~ idBarcode:", idBarcode)
        setIDBarcode(-1);
        formData[id].Value = value;
        setFormData([...formData]);
    }
    const rederItem = (ele, id) => {
        const { Control, TypeID, ProductName, isDisplayName } = ele;
        switch (Control) {
            case INPUT_CONTROL.camera:
                return (
                    <FormImage
                        title={ele.RequireInforDescriptionName}
                        value={ele.Value}
                        onChange={onCapture(id)}
                        onClear={onClearValue(id)}
                        isRequired={ele.RequireInforRequireTypeID == 1}
                        key={`${TypeID}_${id}`}
                        index={id}
                    />
                );
            case INPUT_CONTROL.radio:
                return (
                    <FromRadio
                        title={ele.RequireInforDescriptionName}
                        value={ele.Value}
                        onChange={onChangeValue(id)}
                        onClear={onClearValue(id)}
                        isRequired={ele.RequireInforRequireTypeID == 1}
                        key={`${TypeID}_${id}`}
                        index={id}
                    />
                )
            case INPUT_CONTROL.textbox:
                return (
                    <FormInput
                        title={ele.RequireInforDescriptionName}
                        value={ele.Value}
                        onChange={onChangeValue(id)}
                        onClear={onClearValue(id)}
                        isRequired={ele.RequireInforRequireTypeID == 1}
                        key={`${TypeID}_${id}`}
                        onOpenBarcode={onBarcode(id)}
                        index={id}
                    />
                )
            default:
                return null;
        }
    }

    const onSubmit = () => {
        const msg = checkDataInvalid(formData);
        console.log('onSubmit', msg);
        if (msg) {
            Alert.alert('Thông báo', `Vui lòng nhập thông tin: ${msg}`);
        }
        else {
            const candidate = formData.find(ele => (ele.RequireInforRequireTypeID == 1) && (ele.Control == INPUT_CONTROL.textbox));
            if (candidate) {
                showBlockUI();
                const data = {
                    discountCode: route.params.discountCode,
                    candidateNo: candidate.Value
                }
                actionShoppingCart.checkStudentDiscount(data).then(() => {
                    hideBlockUI();
                    updatePromoRequireInfors();
                }).catch(msgError => {
                    Alert.alert('Thông báo', msgError, [{ onPress: hideBlockUI }]);
                });
            }
            else {
                updatePromoRequireInfors();
            }
        }
    }

    const updatePromoRequireInfors = () => {
        const newDataShoppingCart = helper.deepCopy(dataShoppingCart);
        newDataShoppingCart.cus_CouponAttachmentRequireList = formData;
        console.log('formData', newDataShoppingCart);
        actionShoppingCart.setDataShoppingCart(newDataShoppingCart);
        navigation.goBack();

    }

    return (<SafeAreaView style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
        <KeyboardAwareScrollView
            style={{ flex: 1, paddingTop: 10 }}
            enableResetScrollToCoords={false}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={60}
        >
            {formData.map(rederItem)}
        </KeyboardAwareScrollView>
        {(idCam != -1) && <CaptureCamera
            isVisibleCamera={true}
            takePicture={onTakePicture}
            closeCamera={() => {
                setIDCam(-1);
            }}
            selectPicture={() => { }}
            disabledUploadImage={true}
        />}
        {(idBarcode != -1) && <BarcodeCamera
            isVisible={true}
            closeCamera={() => {
                setIDBarcode(-1);
                onResultBarcode("475934")
            }}
            resultScanBarcode={onResultBarcode}
        />}
        <FormButton
            onSubmit={onSubmit}
            title={"Tiếp tục"}
        />

    </SafeAreaView>);
}


const mapStateToProps = function (state) {
    return {
        dataShoppingCart: state.shoppingCartReducer.dataShoppingCart,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(FormCoupon);


const INPUT_CONTROL = {
    'camera': 'CAMERA',
    'textbox': 'TEXTBOX',
    'radio': 'RADIO',
}
export const checkDataInvalid = (data) => {
    let msg = '';
    data.forEach(ele => {
        if (!ele.Value && (ele.RequireInforRequireTypeID == 1)) {
            msg += `\n-${ele.RequireInforDescriptionName}`;
        }
    })
    return msg;
}