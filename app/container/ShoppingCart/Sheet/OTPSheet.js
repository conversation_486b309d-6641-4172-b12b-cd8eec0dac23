import React, { useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { COLORS } from '@styles';
import { MyText, BottomSheet } from '@components';
import OTPInner from '../component/OTPInner';
import { Icon } from '@components';
import { TouchableWithoutFeedback } from 'react-native-gesture-handler';
const OTPSheet = ({
    bottomSheetRef,
    onConfirm,
    onChangeStatusSheet,
    customerInfo
}) => {
    const handleComponent = () => {
        return (
            <View style={styles.handle}>
                <View style={{ flex: 1 }} />
                <View
                    style={{
                        flex: 6,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <MyText
                        addSize={1}
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.txt000000,
                            fontVariant: ''
                        }}
                        text="Xác thực OTP"
                    />
                </View>
                <View style={{ flex: 1 }}>
                    <TouchableWithoutFeedback
                        style={{ marginLeft: 10 }}
                        onPress={() => {
                            bottomSheetRef.current?.dismiss();
                        }}>
                        <Icon
                            iconSet={'MaterialIcons'}
                            name={'clear'}
                            color={COLORS.txt000000}
                            size={22}
                        />
                    </TouchableWithoutFeedback>
                </View>
            </View>
        );
    };
    const snapPoints = useMemo(() => ['70%'], []);

    return (
        <BottomSheet
            bs={bottomSheetRef}
            handleComponent={handleComponent}
            snapPoints={snapPoints}
            onChangeStatusSheet={onChangeStatusSheet}
            enableHandlePanningGesture={false}
            enableContentPanningGesture={false}
            disabledBackdrop={true}
        >
            <OTPInner
                bottomSheetRef={bottomSheetRef}
                onConfirm={onConfirm}
                customerInfo={customerInfo}
            />
        </BottomSheet>
    );
};

const styles = StyleSheet.create({
    handle: {
        backgroundColor: COLORS.bgFFFFFF,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 50,
        elevation: 2,
        borderTopStartRadius: 22,
        borderTopEndRadius: 22,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4
    }
});

export default OTPSheet;
