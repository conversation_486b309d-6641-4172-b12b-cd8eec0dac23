import React, { Component } from 'react';
import {
    View,
    FlatList,
    StyleSheet,
    TouchableOpacity,
    Alert,
    Keyboard,
    BackHandler,
    TextInput,
    Image
} from 'react-native';
import SafeAreaView from 'react-native-safe-area-view';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Toast from 'react-native-toast-message';
import {
    Icon,
    Button,
    showBlockUI,
    hideBlockUI,
    TitleInput,
    MyText,
    FieldInput,
    Picker,
    BarcodeCamera,
    OTPSheet
} from "@components";
import { constants, STORAGE_CONST, DEVICE, ENUM, CONFIG } from '@constants';
import { helper, storageHelper } from "@common";
import { v4 as uuidv4 } from 'uuid';
import ProductInfo from "./component/ProductInfo";
import Coupon from "./component/Coupon";
import CartPromotion from "./component/CartPromotion";
import RadioGender from "./component/Radio/RadioGender";
import RadioRelation from "./component/Radio/RadioRelation";
import ModalDeposit from "./component/Modal/ModalDeposit";
import ModalLoyalty from "./component/Modal/ModalLoyalty";
import PopupPhone from "./component/PopupPhone";
import TitleInputMoney from "./component/TitleInputMoney";
import ModalFee from "./component/Modal/ModalFee";
import InsuranceInfo from "./component/InsuranceInfo";
import ContractInfo from "./component/ContractInfo";
import ModalProfitPromotion from "./component/Modal/ModalProfitPromotion";
import CartPromotionProfit from "./component/CartPromotionProfit";
import AnimationFloatingButton from './component/AnimationFloatingButton';
import * as actionShoppingCartCreator from "./action";
import * as actionPouchCreator from "../PouchRedux/action";
import * as actionSaleOrderCreator from "../SaleOrderCart/action";
import * as actionPaymentOrderCreator from "../SaleOrderPayment/action";
import * as actionPharmacyCreator from '../AnKhangPharmacy/action';
import * as actionDetailCreator from '../Detail/action';
import * as staffPromotionActionCreator from '../StaffPromotion/action';
import * as specialSaleProgramActionCreator from '../SpecialSaleProgram/action';
import * as loyaltyActionCreator from '../Loyalty/action';
import * as saleActionCreator from '../Sale/action';
import { translate } from '@translate';
import { COLORS } from "@styles";
import PrescriptionImages from '../AnKhangPharmacy/components/PrescriptionImages';
import { PrescriptionScreen, SearchMedical } from '../AnKhangPharmacy/screens';
import InputLoyalty from './component/InputLoyalty';
import {
    SearchProducts as MedicalProducts,
    BottomSheet
} from '../AnKhangPharmacy/components';
import PrescriptionQuantity from '../AnKhangPharmacy/components/PrescriptionQuantity';
import {
    validateStudentCouponImages,
    validateStudentImages
} from './component/StudentInfo/helper';

import { showMessage } from "react-native-flash-message";
import DropDown from './component/DropDown';
import InputField from '../AnKhangNew/components/InputField';
import { TIMEOUT_WOW_POINTS } from '../Loyalty/constants';
import { PackagePriceSheet } from '../Detail/Sheets';
import { BlockUI } from '../Detail';
import { ProductSuggestSheet } from './Sheet';
import CheckBoxPolicy from './component/CheckBoxPolicy';
import QuestionModal from './component/QuestionModal';
import ModalFees from '../Detail/component/Modal/ModalFees';
import { checkDataInvalid } from './component/FormPromotion';
import ProductServiceItem from './component/ProductServiceItem';
import SaleProgramInfo from './component/SaleProgramInfo';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import OTPInner from './component/OTPInner';
import VoucherSheet from '../SaleOrderPayment/Sheet/VoucherSheet'
import BNPLInformation from './component/BNPLInformation';

const { BRAND_ID_OF_SIM, CONNECTED_TYPE_SIM, STORE_ALLOW_VIETTEL_SIM, MAX_QUANTITY, TYPE_PROFILE, PARTNER_ID } = constants

const { STAFF_PROMOTION } = ENUM.SALE_SCENARIO_TYPE;
const SCAN_TYPE = {
    LOYALTY: 'LOYALTY',
    COUPON: 'COUPON',
};
class ShoppingCart extends Component {

    constructor(props) {
        super(props);
        this.state = {
            discountCode: "",
            candidateNo: "",

            gender: null,
            customerPhone: "",
            customerName: "",
            customerAddress: "",
            taxID: "",
            contactPhone: "",
            contactName: "",
            contactAddress: "",

            customerIDCard: "",
            totalPrePaid: 0,
            termLoan: 0,
            isInsuranceFee: false,
            isInsuranceGood: false,
            goodInsuranceID: 0,
            paymentMonthly: 0,

            relationShipType: null,
            isCompany: false,

            isVisibleDeposit: false,
            isVisibleLoyalty: false,

            isRequirePhone: false,
            isVisibleVoucher: false,

            isHasSaleProgram: false,
            isLockCustomer: false,
            isAllowInvoice: true,
            isLockPhone: false,
            isLockName: false,
            isLockAddress: false,
            isLockTax: false,

            contractID: "",
            dataContract: {},
            isVisiblerofit: false,
            isHadCheck: false,

            isShowDrug: false,
            isShowCustomer: false,

            dataImageBatch: [],
            isVisibleScan: false,
            strBarcode: null,

            isProductSelected: false,
            keyPromotion: new Set(),
            isVisibleQuantity: false,
            isOriginQuantityChange: true,
            idOriginProduct: null,

            isRequireCustomerInfo: false,
            // clone state from redux, cannot find diffs between prevProps and this.props
            propsCartChange: -1,
            dataCartPromotion: [],
            typeScan: "",
            isOpenBarcode: false,
            dataCustomerInfo: [],
            customerInfoSelected: null,
            blockUI: false,
            dataPackagePrice: [],
            productSuggests: [],
            isSelectedPolicy: false,
            disabledPolicy: false,
            isShowModalQuestion: false,
            questionList: [],
            tempSaleOrders: {},
            typeOTP: "",
            couponExpired: 0,
            vouchers: [],
            isFetchingGiftVoucher: false,
            isPresented: false
        };
        this.dataVerifyInfo = {};
        this.searchProductsRef = React.createRef();
        this.productSuggestRef = React.createRef();
        this.isExcludeCoupon = false;
        this.timeoutApplyPromotion = null;
        this.timeOutAlertMessage = 0;
        this.customerInfoCoordinate = {
            x: 0,
            y: 0
        };
        this.scrollViewRef = null;
        this.packagePriceSheetRef = React.createRef(null);
        this.isFirstRenderProfile = true;
        this.onFocus = false;
        this.isValidCandidateNo = true;
        this.OTPSheetRef = React.createRef(null)
        this.voucherSheetRef = React.createRef(null)
        this.voucherCustomerPhone = React.createRef(null)
    }

    componentDidMount() {
        this.getMultiSalePromotion(true);
        // customer default
        this.getDefaultCustomerInfo();
        //addEventListener "addEventListener"
        BackHandler.addEventListener('addEventListener', this.onBackButtonPressed);

    }

    onBackButtonPressed = () => {
        return true;
    }

    componentWillUnmount() {
        // this.backHandler.remove();
        BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressed);
        this.props.actionShoppingCart.resetCartPromotion();
    }

    componentDidUpdate(preProps, preState) {
        const {
            stateSearchPharmacy: { isFetching },
            shouldCallMultiSale
        } = this.props.pharmacy;
        const {
            customerConfirmPolicy,
            actionShoppingCart,
            userInfo: { userName }
        } = this.props
        if (
            (this.props.cartChange !== this.state.propsCartChange ||
                this.state.isRequireCustomerInfo !== preState.isRequireCustomerInfo) &&
            this.props.dataShoppingCart.IsSOAnKhang
        ) {
            const {
                SaleOrderDetails: mainProducts,
                giftShoppingCartSaleOrders: giftProducts,
                saleShoppingCartSaleOrders: saleProducts
            } = this.props.dataShoppingCart;
            const hasSpecialSpecsMainProduct = checkMedicineControlSpecs(mainProducts);
            const hasSpecialSpecsGiftProduct = checkMedicineControlSpecs(giftProducts);
            const hasSpecialSpecsSaleProduct = checkMedicineControlSpecs(saleProducts);
            const isRequireCustomerInfo =
                hasSpecialSpecsMainProduct ||
                hasSpecialSpecsGiftProduct ||
                hasSpecialSpecsSaleProduct;

            this.setState((prevState) => {
                return {
                    ...prevState,
                    isRequireCustomerInfo,
                    propsCartChange: this.props.cartChange
                };
            });
        }
        if (preState.customerPhone !== this.state.customerPhone) {
            this.checkGiftVoucher();
            if (helper.configCouponUser(userName)) {
                this.checkCouponExpired()
            }
            if (!this.onFocus && !this.state.isCompany) {
                this.handleAPIGetCustomerProfile(this.state.customerPhone, this.props.dataShoppingCart.CustomerInfo)
            }
        }
        if (preProps.cartPromotion !== this.props.cartPromotion) {
            const { cartPromotion } = this.props;
            if (helper.IsNonEmptyArray(cartPromotion)) {
                this.setState((prevState) => {
                    return {
                        ...prevState,
                        isVisibleVoucher: false,
                        keyPromotion: new Set()
                    }
                }, this.checkGiftVoucher);
            }
        }
        if (isFetching) {
            Keyboard.dismiss();
            this.searchProductsRef.current.snapToIndex(1);
        }
        if (shouldCallMultiSale) {
            this.getMultiSalePromotion();
            this.props.actionPharmacy.setShouldCallMultiSale(false);
        }
        if (preProps.customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId !== customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId) {
            this.getGiftVoucherCustomer()
        }
        this.handleChangeValueProfiles({ preProps, preState })
    }
    handleChangeValueProfiles = ({ preProps, preState }) => {
        const { customerConfirmPolicy } = this.props;

        if (!helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]) || this.isFirstRenderProfile) return

        const { isChange, oldInfoCustomer, infoCustomer } = this.handleGetValueChange({ preProps, preState })
        if (isChange) {
            const {
                isSigned,
            } = customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0] ?? {}
            const hasChangeValueProfile = helper.checkChangeValueOfPrototype(infoCustomer, oldInfoCustomer)
            if (hasChangeValueProfile) {
                this.setState({
                    isSelectedPolicy: false,
                    disabledPolicy: false
                })

            }
            else {
                this.setState({
                    isSelectedPolicy: isSigned,
                    disabledPolicy: isSigned == 1 ? true : false
                })

            }

        }
        if (preState.isCompany !== this.state.isCompany && !this.state.isCompany) {
            delete this.props.customerConfirmPolicy?.[TYPE_PROFILE.COMPANY]
        }
    }
    handleGetValueChange = ({ preState, preProps }) => {
        const {
            isCompany,
            customerName,
            customerPhone,
            gender,
            contactName,
            contactPhone,
            customerIDCard
        } = this.state
        const { customerConfirmPolicy } = this.props
        let isChange = false;
        let infoCustomer = {}
        let oldInfoCustomer = {
            "customerName": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.customerName,
            "customerPhone": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber,
            "gender": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.gender,
            "cardCustomerId": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.cardCustomerId
        }
        if (!helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER])) return {
            isChange,
            infoCustomer,
            oldInfoCustomer
        }

        if (
            !isCompany &&
            (preState.customerPhone !== customerPhone ||
                preState.customerName !== customerName ||
                preState.gender !== gender ||
                preState.customerIDCard !== customerIDCard)) {

            return {
                isChange: true,
                infoCustomer: {
                    "customerName": customerName,
                    "customerPhone": customerPhone,
                    "gender": gender,
                    "cardCustomerId": customerIDCard
                },
                oldInfoCustomer
            }
        }
        else if (
            isCompany &&
            (preState.contactPhone != contactPhone ||
                preState.contactName != contactName ||
                preState.gender != gender ||
                preState.customerIDCard !== customerIDCard)) {
            console.log("Please select", contactPhone);
            return {
                isChange: true,
                infoCustomer: {
                    "customerName": contactName,
                    "customerPhone": contactPhone,
                    "gender": gender,
                    "cardCustomerId": customerIDCard
                },
                oldInfoCustomer
            }
        }
        return {
            isChange,
            infoCustomer,
            oldInfoCustomer
        }
    }
    renderCustomerInfo = (CustomerInfo) => {
        const {
            gender,
            customerPhone,
            customerName,
            customerAddress,
            relationShipType,
            taxID,
            contactPhone,
            contactName,
            contactAddress,
            isCompany,
            isLockCustomer,
            isAllowInvoice,
            isLockPhone,
            isLockName,
            isLockAddress,
            isLockTax,
            customerIDCard,
            isHasSaleProgram,
            isShowCustomer,
            // Khách hàng mua bill có thuốc kiểm soát đặc biệt
            // bắt buộc phải nhập thông tin khách hàng
            isRequireCustomerInfo,
            dataCustomerInfo,
            customerInfoSelected
        } = this.state;
        const { dataShoppingCart, staffInfo, wowPoints, userInfo: { storeID } } = this.props;
        const { IsSOScreenSticker } = dataShoppingCart;
        const {
            SaleOrderDetails,
            SaleOrderCusPromotion,
            IsSOAnKhang,
            cus_UrlFilesShoppingCart
        } = dataShoppingCart;
        const existStaffInfo = !helper.IsEmptyObject(staffInfo)
        const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
        const isVisibleChangeType = (!isLockCustomer && !isLockTax && isAllowInvoice);
        const isDisabledTax = isLockTax || IsSOScreenSticker || existStaffInfo;
        const isDisabledPhone = isLockPhone || IsSOScreenSticker || existStaffInfo;
        const isDisabledPhoneContact = isLockPhone || IsSOScreenSticker || existStaffInfo;
        const isDisabledName = isLockName || IsSOScreenSticker || existStaffInfo;
        const isDisabledAddress = isLockAddress || IsSOScreenSticker || existStaffInfo;
        const isSupImages =
            helper.hasProperty(SaleOrderCusPromotion, "UrlFiles") ||
            helper.isArray(cus_UrlFilesShoppingCart);
        // had sim
        const isHadVinaSim = checkHadSim(SaleOrderDetails, BRAND_ID_OF_SIM.VINA);
        const isHadItelSim = checkHadSim(SaleOrderDetails, BRAND_ID_OF_SIM.ITEL);
        const isHadViettelSim = checkHadSim(SaleOrderDetails, BRAND_ID_OF_SIM.VIETTEL);
        // visible not Check
        const isVisibleCardIDViettel = (isHadViettelSim) && !isHasSaleProgram && !isSupImages;
        const isVisibleCardId = isVisibleCardIDViettel;
        // visible check
        const isCheckVisibleCardIDVina = (isHadVinaSim) && !isHasSaleProgram && !isSupImages;
        const isCheckVisibleCardIDItel = (isHadItelSim) && !isHasSaleProgram && !isSupImages;
        const isCheckVisibleCardId = isCheckVisibleCardIDVina || isCheckVisibleCardIDItel;
        const isVisibleCustomer = (IsSOAnKhang && !isRequireCustomerInfo) ? isShowCustomer : !isShowCustomer;
        return (
            <View
                onLayout={(event) => {
                    // event.nativeEvent.layout.y
                    this.customerInfoCoordinate = {
                        x: event.nativeEvent.layout.x,
                        y: event.nativeEvent.layout.y
                    }
                }}
                style={{
                    width: constants.width,
                    alignItems: "center",
                    paddingBottom: 2
                }}>
                <View
                    style={{
                        alignItems: 'center',
                        backgroundColor: COLORS.btn5B9A68,
                        height: 40,
                        paddingHorizontal: 10
                    }}>
                    <Button
                        text={translate('shoppingCart.customer_info')}
                        onPress={() => { this.setState({ isShowCustomer: !isShowCustomer }) }}
                        styleContainer={{
                            flexDirection: 'row',
                            width: constants.width,
                            height: 40,
                            justifyContent: 'space-between',
                            width: constants.width - 10
                        }}
                        styleText={{
                            color: COLORS.txtFFFFFF,
                            fontSize: 16,
                            marginRight: 8,
                            fontWeight: 'bold'
                        }}
                        iconRight={{
                            iconSet: 'FontAwesome',
                            name: isShowCustomer ? 'chevron-up' : 'chevron-down',
                            size: 14,
                            color: COLORS.icFFFFBC
                        }}
                    />
                </View>
                {
                    isVisibleCustomer && <View>
                        {
                            isVisibleChangeType &&
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: constants.width - 20
                                }}>
                                <TouchableOpacity style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginTop: 10,
                                    alignSelf: "flex-start"
                                }}
                                    onPress={this.onChangTypeCustomer(!isCompany, CustomerInfo)}
                                    disabled={IsSOScreenSticker || existStaffInfo}
                                >
                                    <Icon
                                        iconSet={"Ionicons"}
                                        name={
                                            isCompany
                                                ? "ios-checkbox-outline"
                                                : "ios-square-outline"
                                        }
                                        color={isCompany ? COLORS.icF89000 : COLORS.ic147EFB}
                                        size={16}
                                    />
                                    <MyText
                                        text={translate('shoppingCart.customer_print_company_bill')}
                                        style={{
                                            color: isCompany ? COLORS.txtF89000 : COLORS.txt147EFB,
                                            fontWeight: 'bold',
                                            marginLeft: 5
                                        }}
                                    />
                                </TouchableOpacity>
                            </View>
                        }
                        {
                            isCompany
                                ? <View style={{
                                    width: constants.width - 20,
                                    marginTop: 10,
                                }}>
                                    <TitleInput
                                        title={translate('shoppingCart.text_input_customer_tax')}
                                        isRequired={true}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            paddingVertical: 8,
                                            backgroundColor: isDisabledTax ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        placeholder={translate('shoppingCart.placeholder_customer_tax')}
                                        value={taxID}
                                        onChangeText={(text) => {
                                            const regExpTax = new RegExp(/^[0-9-KL]{0,14}$/);
                                            const isValidate = regExpTax.test(text) || (text == "");
                                            if (isValidate) {
                                                this.setState({ taxID: text });
                                            }
                                        }}
                                        keyboardType={DEVICE.isIOS ? "numbers-and-punctuation" : "visible-password"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        // onBlur={() => this.getCompanyInfo(taxID)}
                                        onBlur={() => this.getCompanyProfile(taxID)}
                                        width={constants.width - 20}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ taxID: "" });
                                        }}
                                        editable={!isDisabledTax}
                                    />

                                    <TitleInput
                                        title={translate('shoppingCart.text_input_customer_company_name')}
                                        isRequired={true}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            justifyContent: 'center',
                                            paddingVertical: 8,
                                            backgroundColor: isDisabledName ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        textAlignVertical={'center'}
                                        underlineColorAndroid={'transparent'}
                                        placeholder={translate('shoppingCart.placeholder_customer_company_name')}
                                        value={customerName}
                                        onChangeText={(text) => {
                                            if (helper.isValidateCharVN(text)) {
                                                this.setState({ customerName: text });
                                            }
                                        }}
                                        keyboardType={"default"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        onSubmitEditing={Keyboard.dismiss}
                                        width={constants.width - 20}
                                        multiline={true}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ customerName: "" });
                                        }}
                                        editable={!isDisabledName}
                                        key={"companyName"}
                                        maxLength={300}
                                    />

                                    <TitleInput
                                        title={translate('shoppingCart.text_input_customer_company_address')}
                                        isRequired={true}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            justifyContent: 'center',
                                            paddingVertical: 8,
                                            backgroundColor: isDisabledAddress ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        textAlignVertical={'center'}
                                        underlineColorAndroid={'transparent'}
                                        placeholder={translate('shoppingCart.placeholder_address')}
                                        value={customerAddress}
                                        onChangeText={(text) => {
                                            if (helper.isValidateCharVN(text)) {
                                                this.setState({ customerAddress: text });
                                            }
                                        }}
                                        keyboardType={"default"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        onSubmitEditing={Keyboard.dismiss}
                                        width={constants.width - 20}
                                        multiline={true}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ customerAddress: "" });
                                        }}
                                        editable={!isDisabledAddress}
                                        key={"companyAddress"}
                                        maxLength={300}
                                    />

                                    {/* <TitleInput
                                        title={translate('shoppingCart.text_input_customer_company_phone')}
                                        // isRequired={true}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            paddingVertical: 8,
                                            backgroundColor: isDisabledPhoneContact ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        placeholder={translate('shoppingCart.placeholder_phone')}
                                        value={customerPhone}
                                        onChangeText={(text) => {
                                            let regExpPhone = new RegExp(/^[0]\d{0,10}$/);
                                            const isValidate = regExpPhone.test(text) || (text == "");
                                            if (isValidate) {
                                                this.setState({
                                                    customerPhone: text,
                                                    // contactPhone: text.slice(0, 10)
                                                });
                                            }
                                        }}
                                        keyboardType={"numeric"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        // onBlur={this.getContactInfo(contactPhone)}
                                        width={constants.width - 20}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ customerPhone: "" });
                                        }}
                                        editable={!isDisabledPhoneContact}
                                        key={"companyPhone"}
                                    /> */}

                                    <View style={{
                                        width: constants.width - 20,
                                        flexDirection: "row",
                                        marginBottom: 4,
                                        justifyContent: "space-between"
                                    }}>
                                        <RadioGender
                                            gender={gender}
                                            onSwitchGender={(value) => {
                                                this.setState({ gender: value });
                                            }}
                                            disabled={existStaffInfo}
                                        />
                                        <TouchableOpacity style={{
                                            justifyContent: "center",
                                            alignItems: "center",
                                        }}
                                            onPress={this.getOldCompanyInfo}
                                            disabled={existStaffInfo}
                                        >
                                            <MyText
                                                text={translate('shoppingCart.old_customer')}
                                                style={{
                                                    color: COLORS.txtFFA500,
                                                    textDecorationLine: 'underline',
                                                    fontWeight: 'bold'
                                                }}
                                            />
                                        </TouchableOpacity>
                                    </View>

                                    <TitleInput
                                        title={translate('shoppingCart.text_input_buyer_phone')}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            backgroundColor: COLORS.bgFFFFFF,
                                            paddingVertical: 8,
                                            backgroundColor: existStaffInfo || isLockPhone ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        placeholder={translate('shoppingCart.placeholder_phone')}
                                        value={contactPhone}
                                        onChangeText={(text) => {
                                            const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                            const isValidate = regExpPhone.test(text) || (text == "");
                                            if (isValidate) {
                                                this.setState({ contactPhone: text, customerPhone: text });
                                            }
                                        }}
                                        keyboardType={"numeric"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        onBlur={() => {
                                            this.isFirstRenderProfile = true;
                                            this.handleAPIGetCustomerProfile(contactPhone, CustomerInfo)
                                        }}
                                        width={constants.width - 20}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ contactPhone: "" });
                                        }}
                                        key={"contactPhone"}
                                        disabled={existStaffInfo}
                                        editable={!isLockPhone}

                                    />

                                    <TitleInput
                                        title={translate('shoppingCart.text_input_buyer_name')}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            backgroundColor: COLORS.bgFFFFFF,
                                            paddingVertical: 8,
                                            backgroundColor: existStaffInfo ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        placeholder={translate('shoppingCart.placeholder_name')}
                                        value={contactName}
                                        onChangeText={(text) => {
                                            if (helper.isValidateCharVN(text)) {
                                                this.setState({ contactName: text });
                                            }
                                        }}
                                        keyboardType={"default"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        onSubmitEditing={Keyboard.dismiss}
                                        width={constants.width - 20}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ contactName: "" });
                                        }}
                                        key={"contactName"}
                                        maxLength={50}
                                        disabled={existStaffInfo}
                                    />

                                    {/* <TitleInput
                                        title={translate('shoppingCart.text_input_contact_address')}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            backgroundColor: COLORS.bgFFFFFF,
                                            justifyContent: 'center',
                                            paddingVertical: 8,
                                            backgroundColor: existStaffInfo ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        textAlignVertical={'center'}
                                        underlineColorAndroid={'transparent'}
                                        placeholder={translate('shoppingCart.placeholder_address')}
                                        value={contactAddress}
                                        onChangeText={(text) => {
                                            if (helper.isValidateCharVN(text)) {
                                                this.setState({ contactAddress: text });
                                            }
                                        }}
                                        keyboardType={"default"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        onSubmitEditing={Keyboard.dismiss}
                                        width={constants.width - 20}
                                        multiline={true}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ contactAddress: "" });
                                        }}
                                        key={"contactAddress"}
                                        maxLength={300}
                                        disabled={existStaffInfo}
                                    /> */}



                                </View>
                                : <View style={{
                                    width: constants.width - 20,
                                    marginTop: 10,
                                }}>
                                    <View style={{
                                        width: constants.width - 20,
                                        flexDirection: "row",
                                        marginBottom: 4,
                                        justifyContent: "space-between"
                                    }}>
                                        <RadioGender
                                            gender={gender}
                                            onSwitchGender={(value) => {
                                                this.setState({ gender: value });
                                                CustomerInfo.Gender = value;
                                            }}
                                            disabled={IsSOScreenSticker || existStaffInfo}
                                        />
                                        <TouchableOpacity style={{
                                            justifyContent: "center",
                                            alignItems: "center",
                                        }}
                                            onPress={this.getOldCustomerInfo(CustomerInfo)}
                                            disabled={IsSOScreenSticker || existStaffInfo}
                                        >
                                            <MyText
                                                text={translate('shoppingCart.old_customer')}
                                                style={{
                                                    color: COLORS.txtFFA500,
                                                    textDecorationLine: 'underline',
                                                    fontWeight: 'bold'
                                                }}
                                            />
                                        </TouchableOpacity>
                                    </View>

                                    <InputLoyalty
                                        title={translate('shoppingCart.text_input_phone')}
                                        isRequired={isRequireCustomerInfo}
                                        // isRequired={true}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            paddingVertical: 8,
                                            backgroundColor: isDisabledPhone ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        placeholder={translate('shoppingCart.placeholder_phone')}
                                        value={customerPhone}
                                        onChangeText={(text) => {
                                            const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                            const isValidate = regExpPhone.test(text) || (text == "");
                                            if (isValidate) {
                                                this.setState({ customerPhone: text });
                                                CustomerInfo.CustomerPhone = text;
                                            }
                                        }}
                                        keyboardType={"numeric"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        onBlur={() => {
                                            this.onFocus = false
                                            this.handleAPIGetCustomerProfile(customerPhone, CustomerInfo)
                                        }}
                                        width={constants.width - 20}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ customerPhone: "" });
                                            CustomerInfo.CustomerPhone = "";
                                        }}
                                        editable={!isDisabledPhone}
                                        key={"customerPhone"}
                                        IsSOScreenSticker
                                        isVisibleBarCodePhone
                                        handleGetLoyaltyPhone={() => {
                                            this.onOpenBarcode(SCAN_TYPE.LOYALTY)
                                        }}
                                        alertMessage={isDisabledPhone ? '' : wowPoints.message}
                                        onTooltipClose={() => {
                                            if (this.timeOutAlertMessage) {
                                                clearTimeout(this.timeOutAlertMessage);
                                            }
                                            this.props.loyaltyAction.setWowPointsMessage({
                                                phoneNumber: '',
                                                message: ''
                                            });
                                        }}
                                        onFocus={() => {
                                            this.onFocus = true
                                        }}
                                    />

                                    <TitleInput
                                        title={translate('shoppingCart.text_input_name')}
                                        isRequired={!IsSOAnKhang || isRequireCustomerInfo}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            paddingVertical: 8,
                                            backgroundColor: isDisabledName ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        placeholder={translate('shoppingCart.placeholder_name')}
                                        value={customerName}
                                        onChangeText={(text) => {
                                            if (helper.isValidateCharVN(text)) {
                                                this.setState({ customerName: text });
                                                CustomerInfo.CustomerName = text;
                                            }
                                        }}
                                        keyboardType={"default"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        onSubmitEditing={Keyboard.dismiss}
                                        width={constants.width - 20}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ customerName: "" });
                                            CustomerInfo.CustomerName = "";
                                        }}
                                        editable={!isDisabledName}
                                        key={"customerName"}
                                        maxLength={50}
                                    />
                                    {/* Nhập căn cước công dân */}
                                    {
                                        isCheckVisibleCardId &&
                                        <View>
                                            <MyText
                                                text={"CCCD/GCC của khách hàng"}
                                                addSize={-1.5}
                                                style={{
                                                    color: COLORS.txt333333,
                                                    fontWeight: 'bold',
                                                    fontStyle: 'italic',
                                                }}>
                                                <MyText
                                                    text={'*'}
                                                    addSize={-1.5}
                                                    style={{
                                                        color: COLORS.txtFF0000
                                                    }}
                                                />
                                            </MyText>
                                            <View
                                                style={{
                                                    width: constants.width - 30,
                                                    flexDirection: 'row'
                                                }}>
                                                <TextInput
                                                    style={{
                                                        width: '70%',
                                                        borderWidth: 1,
                                                        borderRadius: 4,
                                                        borderColor: COLORS.bdCCCCCC,
                                                        marginBottom: 5,
                                                        paddingHorizontal: 10,
                                                        paddingVertical: 8,
                                                        backgroundColor: isDisabledName
                                                            ? COLORS.bgF0F0F0
                                                            : COLORS.bgFFFFFF
                                                    }}
                                                    placeholder={"Vui lòng nhập CCCD/GCC khách hàng"}
                                                    value={customerIDCard}
                                                    onChangeText={(text) => {
                                                        const regExpIDCard = new RegExp(/^\d{0,12}$/);
                                                        const isValidate =
                                                            regExpIDCard.test(text) || text == '';
                                                        if (isValidate) {
                                                            this.setState({ customerIDCard: text, isHadCheck: false });
                                                        }
                                                    }}
                                                    keyboardType={'numeric'}
                                                    returnKeyType={'done'}
                                                    blurOnSubmit={true}
                                                    onSubmitEditing={Keyboard.dismiss}
                                                    clearText={() => {
                                                        this.setState({ customerIDCard: '' });
                                                    }}
                                                    key={'customerIDCard'}
                                                />
                                                <Button
                                                    text={translate('shoppingCart.btn_check')}
                                                    onPress={this.handleCheckDocument}
                                                    styleContainer={{
                                                        width: '30%',
                                                        borderRadius: 4,
                                                        backgroundColor: COLORS.txt147EFB,
                                                        height: 44,
                                                        marginLeft: 10
                                                    }}
                                                    styleText={{
                                                        color: COLORS.txtFFFFFF,
                                                        fontSize: 14,
                                                        fontWeight: 'bold'
                                                    }}
                                                />
                                            </View>
                                        </View>
                                    }
                                    {
                                        isVisibleCardId &&
                                        <View >
                                            <MyText
                                                text={"CCCD/GCC/HC"}
                                                addSize={-1.5}
                                                style={{
                                                    color: COLORS.txt333333,
                                                    fontWeight: 'bold',
                                                    fontStyle: 'italic',
                                                }}>
                                                <MyText
                                                    text={'*'}
                                                    addSize={-1.5}
                                                    style={{
                                                        color: COLORS.txtFF0000
                                                    }}
                                                />
                                            </MyText>
                                            <TitleInput
                                                styleInput={{
                                                    borderWidth: 1,
                                                    borderRadius: 4,
                                                    borderColor: COLORS.bdCCCCCC,
                                                    marginBottom: 5,
                                                    paddingHorizontal: 10,
                                                    justifyContent: 'center',
                                                    paddingVertical: 8,
                                                    marginTop: -10,
                                                    backgroundColor: isDisabledAddress ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                    width: '100%',

                                                }}
                                                width={constants.width - 20}
                                                multiline={true}
                                                height={40}
                                                placeholder={"Nhập CCCD/GCC/HC của khách hàng"}
                                                value={customerIDCard}
                                                onChangeText={(text) => {
                                                    // const regExpIDCardViettel = new RegExp('^[a-zA-Z0-9]{1}[0-9]*$');
                                                    const regExpIDCard = new RegExp(/^\w{0,12}$/);
                                                    const isValidate =
                                                        (regExpIDCard.test(text)) || text == '';
                                                    if (isValidate) {
                                                        this.setState({ customerIDCard: text, isHadCheck: true });
                                                    }
                                                }}
                                                keyboardType={"default"}
                                                returnKeyType={'done'}
                                                blurOnSubmit={true}
                                                onSubmitEditing={Keyboard.dismiss}
                                                clearText={() => {
                                                    this.setState({ customerIDCard: '' });
                                                }}
                                                key={'customerIDCard'}
                                            />
                                        </View>
                                    }
                                    {/* <TitleInput
                                        title={translate('shoppingCart.text_input_address')}
                                        isRequired={isRequireCustomerInfo}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            justifyContent: 'center',
                                            paddingVertical: 8,
                                            backgroundColor: isDisabledAddress ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                        }}
                                        textAlignVertical={'center'}
                                        underlineColorAndroid={'transparent'}
                                        placeholder={translate('shoppingCart.placeholder_address')}
                                        value={customerAddress}
                                        onChangeText={(text) => {
                                            if (helper.isValidateCharVN(text)) {
                                                this.setState({ customerAddress: text });
                                                CustomerInfo.CustomerAddress = text;
                                            }
                                        }}
                                        keyboardType={"default"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        onSubmitEditing={Keyboard.dismiss}
                                        width={constants.width - 20}
                                        multiline={true}
                                        height={40}
                                        clearText={() => {
                                            this.setState({ customerAddress: "" });
                                            CustomerInfo.CustomerAddress = "";
                                        }}
                                        editable={!isDisabledAddress || !existStaffInfo}
                                        key={"customerAddress"}
                                        maxLength={300}
                                    /> */}

                                </View>
                        }
                    </View>
                }
                {this.renderAddress(dataCustomerInfo, customerInfoSelected, CustomerInfo, isCompany)}
                {this.renderCustomerIDCard(dataShoppingCart, saleProgramInfo)}
                {this.renderAttachmentCoupon(dataShoppingCart)}
                {this.renderInstallment(dataShoppingCart, saleProgramInfo)}
                {this.renderContractInfo(dataShoppingCart, saleProgramInfo)}
            </View>
        );
    }
    renderAddress = (dataCustomerInfo, customerInfoSelected, CustomerInfo, isCompany) => {
        return <View style={{
            width: constants.width - 20,
            alignItems: "flex-end",
        }}>
            {
                (dataCustomerInfo.length > 1) &&
                <DropDown
                    valueSelect={customerInfoSelected}
                    onSelect={(info) => {
                        this.handleCustomerInfo(info)
                    }}
                    onCreateNewAddress={() => {
                        isCompany
                            ?
                            this.setState({ contactAddress: "" })
                            :
                            this.setState({ customerAddress: "" });
                        CustomerInfo.CustomerAddress = ""
                    }}
                    customerInfo={dataCustomerInfo}
                    fieldName={isCompany ? "ContactAddress" : "CustomerAddress"}
                />
            }
        </View>
    }

    renderAttachmentCoupon = (dataShoppingCart) => {
        const { candidateNo } = this.state;
        const {
            SaleOrderCusPromotion,
            cus_UrlFilesShoppingCart,
            cus_IsRequireAttachmentSMSPromotion,
            cus_IsPromotionType19Exist,
            cus_IsRequiredCandidateInput,
            cus_IsDisplayCandidateInput
        } = dataShoppingCart;
        return cus_IsRequireAttachmentSMSPromotion &&
            <View style={{
                width: constants.width,
                alignItems: 'center'
            }}>
                {!cus_IsPromotionType19Exist && <View style={{
                    width: constants.width - 20,
                    flexDirection: "row",
                    alignItems: "flex-end",
                    justifyContent: "space-between"
                }}>
                    <View style={{ width: 120 }} />
                    <MyText
                        addSize={-2}
                        style={{
                            color: COLORS.txtEB1478,
                            width: constants.width - 140,
                            textDecorationLine: 'underline',
                            fontWeight: 'bold',
                            textAlign: 'right'
                        }}
                        onPress={this.onSupplementImage(
                            SaleOrderCusPromotion,
                            cus_UrlFilesShoppingCart
                        )}
                        text={translate('editSaleOrder.take_ID_and_student_card_pictures')}
                    />
                </View>}
                {cus_IsDisplayCandidateInput && <TitleInput
                    title={"Số báo danh:"}
                    isRequired={cus_IsRequiredCandidateInput}
                    styleInput={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        paddingVertical: 8,
                        backgroundColor: COLORS.bgFFFFFF,
                    }}
                    placeholder={"Nhập số báo danh"}
                    value={candidateNo}
                    onChangeText={(text) => {
                        if (helper.isValidateCharVN(text)) {
                            this.isValidCandidateNo = false;
                            this.setState({ candidateNo: text });
                        }
                    }}
                    keyboardType={"default"}
                    returnKeyType={"done"}
                    blurOnSubmit={true}
                    width={constants.width - 20}
                    height={40}
                    clearText={() => {
                        this.setState({ candidateNo: "" });
                    }}
                    key={"customerName"}
                    maxLength={50}
                    onFocus={() => {
                        this.onFocus = true
                    }}
                    onBlur={() => {
                        this.onFocus = false
                        this.checkCandidateNo();
                    }}
                />}
            </View>
    }

    renderCustomerIDCard = (dataShoppingCart, saleProgramInfo) => {
        const { customerIDCard, isHasSaleProgram } = this.state;
        const { userInfo: { storeID } } = this.props
        const {
            SaleOrderCusPromotion,
            cus_UrlFilesShoppingCart,
            SaleOrderDetails,
            cus_IsRequireAttachmentSMSPromotion,
            cus_IsPromotionType19Exist
        } = dataShoppingCart;
        const { IsCardPartner } = saleProgramInfo;
        const isRequired = !IsCardPartner;
        const isRequiredPhotos = cus_IsPromotionType19Exist;
        const isVisble = isHasSaleProgram || isRequiredPhotos;
        const isHadVinaSim = checkHadSim(SaleOrderDetails, BRAND_ID_OF_SIM.VINA);
        const isHadViettelSim = checkHadSim(SaleOrderDetails, BRAND_ID_OF_SIM.VIETTEL);
        const isHadItelSim = checkHadSim(SaleOrderDetails, BRAND_ID_OF_SIM.ITEL);
        const isVisibleCardID = (isHadVinaSim || isHadViettelSim || isHadItelSim);
        return (
            isVisble &&
            <View style={{
                width: constants.width,
                alignItems: "center",
                backgroundColor: COLORS.bgFAFAFA
            }}>
                <View style={{
                    width: constants.width - 20,
                    backgroundColor: COLORS.bgFFFFFF,
                    flexDirection: "row",
                    alignItems: "flex-end",
                    justifyContent: "space-between"
                }}>
                    <MyText
                        text={translate('shoppingCart.ID_card_number')}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold',
                            fontStyle: 'italic',
                            width: 120
                        }}>
                        {isRequired && (
                            <MyText
                                text={'*'}
                                addSize={-1.5}
                                style={{
                                    color: COLORS.txtFF0000
                                }}
                            />
                        )}
                    </MyText>
                    {
                        isRequiredPhotos &&
                        <MyText
                            text={translate('shoppingCart.take_ID_and_student_card_pictures')}
                            addSize={-2}
                            style={{
                                color: COLORS.txtEB1478,
                                width: constants.width - 140,
                                textDecorationLine: 'underline',
                                fontWeight: 'bold',
                                textAlign: 'right'
                            }}
                            onPress={this.onSupplementImage(
                                SaleOrderCusPromotion,
                                cus_UrlFilesShoppingCart
                            )}
                        />
                    }
                </View>
                {
                    isVisibleCardID
                        ?
                        <View >
                            <View
                                style={{
                                    width: constants.width - 20,
                                    flexDirection: 'row',
                                    height: 46
                                }}>
                                <TextInput
                                    style={{
                                        width: '70%',
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 5,
                                        paddingHorizontal: 10,
                                        paddingVertical: 8,
                                        backgroundColor: COLORS.bgFFFFFF
                                    }}
                                    placeholder={translate('shoppingCart.placeholder_cccd')}
                                    value={customerIDCard}
                                    onChangeText={(text) => {
                                        const regExpIDCard = new RegExp(/^\d{0,12}$/);
                                        const isValidate =
                                            regExpIDCard.test(text) || text == '';
                                        if (isValidate) {
                                            this.setState({ customerIDCard: text, isHadCheck: false });
                                        }
                                    }}
                                    keyboardType={'numeric'}
                                    returnKeyType={'done'}
                                    blurOnSubmit={true}
                                    onSubmitEditing={Keyboard.dismiss}
                                    clearText={() => {
                                        this.setState({ customerIDCard: '' });
                                    }}
                                    key={'customerIDCard'}
                                />
                                <Button
                                    text={translate('shoppingCart.btn_check')}
                                    onPress={
                                        this.handleCheckDocument
                                    }
                                    styleContainer={{
                                        width: '27%',
                                        borderRadius: 4,
                                        backgroundColor: COLORS.txt147EFB,
                                        height: 39,
                                        marginLeft: 10
                                    }}
                                    styleText={{
                                        color: COLORS.txtFFFFFF,
                                        fontSize: 14,
                                        fontWeight: 'bold'
                                    }}
                                />
                            </View>
                            <MyText style={{ paddingBottom: 5, color: COLORS.txtF50537 }} text={"( Nhấn kiểm tra khi có sim Vinaphone hoặc ITEL )"}></MyText>
                        </View>
                        :
                        <FieldInput
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                                backgroundColor: COLORS.bgFFFFFF
                            }}
                            placeholder={translate('shoppingCart.placeholder_ID_card_number')}
                            value={customerIDCard}
                            onChangeText={(text) => {
                                const regExpIDCard = new RegExp(/^\d{0,12}$/);
                                const isValidate = regExpIDCard.test(text) || (text == "");
                                if (isValidate) {
                                    this.setState({ customerIDCard: text });
                                }
                            }}
                            keyboardType={"numeric"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                this.setState({ customerIDCard: "" });
                            }}
                            key={"customerIDCard"}
                        />
                }
            </View>
        );
    }

    renderInstallment = (dataShoppingCart, saleProgramInfo) => {
        const {
            totalPrePaid,
            termLoan,
            isHasSaleProgram,
            isInsuranceFee,
            isInsuranceGood,
            goodInsuranceID,
            paymentMonthly
        } = this.state;
        const { SHAmount, IsAutoCreateEP, ShippingCost } = dataShoppingCart;
        const totalAmount = (SHAmount - ShippingCost);
        const {
            SaleProgramName,
            SaleProgramID,
            RecordsProcessingFee,
            IsCardPartner,
            MinPrepaid,
            MaxPrepaid,
            TermLoanList,
            InsuranceFees,
            MasterGoodsInsurance,
        } = saleProgramInfo;
        const percent = (parseFloat(totalPrePaid) / totalAmount) * 100;
        const isVisble = isHasSaleProgram && IsAutoCreateEP;
        return (
            isVisble &&
            <View style={{
                width: constants.width,
                alignItems: "center",
                backgroundColor: COLORS.bgFDF9E5,
                paddingVertical: 8
            }}>
                <MyText
                    text={`${SaleProgramID} - ${SaleProgramName}`}
                    style={{
                        width: constants.width - 20,
                        marginBottom: 2,
                        fontWeight: 'bold',
                        color: COLORS.txt288AD6
                    }}
                />
                <TitleInputMoney
                    title={translate('shoppingCart.text_input_cost_contract')}
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        backgroundColor: COLORS.bgFFFFFF,
                        color: COLORS.txt333333,
                        height: 40,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        width: constants.width - 20,
                        backgroundColor: COLORS.bgF0F0F0,
                    }}
                    value={RecordsProcessingFee}
                    editable={false}
                    key={"processingFee"}
                    placeholder={"0"}
                />
                <TitleInputMoney
                    title={translate('shoppingCart.text_input_deposit')}
                    percent={percent}
                    isRequired={true}
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        backgroundColor: COLORS.bgFFFFFF,
                        color: COLORS.txt333333,
                        height: 40,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        width: constants.width - 20,
                        backgroundColor: IsCardPartner ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                    }}
                    placeholder={"0"}
                    value={totalPrePaid}
                    onChange={(value) => {
                        this.setState({ totalPrePaid: value });
                    }}
                    onBlur={() => {
                        let prePaid = totalPrePaid;
                        if (prePaid < MinPrepaid) {
                            prePaid = MinPrepaid;
                        }
                        else if (prePaid > MaxPrepaid) {
                            prePaid = MaxPrepaid;
                        }
                        else {
                            prePaid = (Math.round(prePaid / 1000) * 1000);
                        }
                        this.setState({ totalPrePaid: prePaid }, this.getPaymentMonthly);
                    }}
                    editable={!IsCardPartner}
                    key={"totalPrePaid"}
                />
                {
                    helper.isArray(TermLoanList) &&
                    <Picker
                        label={"TermLoanName"}
                        value={"TermLoanNumber"}
                        defaultLabel={translate('shoppingCart.picker_loan_term')}
                        valueSelected={termLoan}
                        data={TermLoanList}
                        onChange={(item) => {
                            this.setState({
                                termLoan: item.TermLoanNumber,
                            }, this.getPaymentMonthly);
                        }}
                        numColumns={4}
                        title={translate('shoppingCart.picker_loan_term')}
                        isRequired={true}
                    />
                }
                <InsuranceInfo
                    dataFee={InsuranceFees}
                    dataMasterGood={MasterGoodsInsurance}
                    isCheckFee={isInsuranceFee}
                    isCheckGood={isInsuranceGood}
                    goodId={goodInsuranceID}
                    onPressFee={() => {
                        this.setState({
                            isInsuranceFee: !isInsuranceFee,
                            // paymentMonthly: 0
                        }, this.getPaymentMonthly);
                    }}
                    onPressGood={(goodID) => {
                        this.setState({
                            isInsuranceGood: !isInsuranceGood,
                            goodInsuranceID: goodID,
                            // paymentMonthly: 0
                        }, this.getPaymentMonthly);
                    }}
                    onPressInsurance={(value) => {
                        this.setState({
                            goodInsuranceID: value,
                            // paymentMonthly: 0
                        }, this.getPaymentMonthly);
                    }}
                />
                <TitleInputMoney
                    title={translate('shoppingCart.text_input_payment_per_month')}
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        backgroundColor: COLORS.bgFFFFFF,
                        color: COLORS.txt333333,
                        height: 40,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        width: constants.width - 20,
                        backgroundColor: COLORS.bgF0F0F0,
                    }}
                    value={paymentMonthly}
                    editable={false}
                    key={"amountMonthly"}
                    placeholder={""}
                />
            </View>
        );
    }

    renderContractInfo = (dataShoppingCart, saleProgramInfo) => {

        const isNewFollowPartner = helper.isNewFollowPartner(saleProgramInfo.PartnerInstallmentID);
        const isBNPLInformation = saleProgramInfo.PartnerInstallmentID == 34 || saleProgramInfo.PartnerInstallmentID == 35;
        const { SHAmount, IsAutoCreateEP, ShippingCost } = dataShoppingCart;
        const { contractID, dataContract, isHasSaleProgram, termLoan } = this.state;
        const isVisble = isHasSaleProgram && !IsAutoCreateEP;
        const totalAmount = (SHAmount - ShippingCost);

        if (!isVisble) return null
        if (isBNPLInformation) return (
            <BNPLInformation
                info={saleProgramInfo}
                data={dataContract}
                contractID={contractID}
                updateData={(data) => {
                    this.setState({ dataContract: data });
                }}
            />
        )
        if (isNewFollowPartner) return <SaleProgramInfo
            totalAmount={totalAmount}
            saleProgramInfo={saleProgramInfo}
        />

        return (
            <ContractInfo
                info={saleProgramInfo}
                data={dataContract}
                contractID={contractID}
                onChange={(text) => {
                    this.setState({ contractID: text });
                }}
                onClear={() => {
                    this.setState({
                        contractID: "",
                        dataContract: {}
                    });
                }}
                getData={this.getContractInfo}
                totalAmount={totalAmount}
                updateData={(data) => {
                    this.setState({ dataContract: data });
                }}
            />
        )
    }

    getContractInfo = (data) => {
        showBlockUI();
        this.props.actionPaymentOrder.getContractInfo(data).then(info => {
            hideBlockUI();
            this.setState({
                dataContract: {
                    ContractID: info.contactID,
                    PGProcessUserID: info.pgProcessUserID,
                    PGProcessUserName: info.pgProcessUserName,
                    packageRates: info.packageRates,
                    TotalPrePaid: info.totalPrePaid,
                    TermLoan: info.termLoan,
                    PaymentAmountMonthly: info.paymentAmountMonthly,
                    isPartnerConnectAPI: info.isPartnerConnectAPI
                }
            });
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('shoppingCart.btn_skip_uppercase'),
                    onPress: () => {
                        hideBlockUI();
                        this.setState({
                            contractID: "",
                            dataContract: {}
                        });
                    }
                },
                {
                    text: translate('shoppingCart.btn_retry_uppercase'),
                    onPress: () => this.getContractInfo(data)
                }
            ]);
        });
    }

    onSupplementImage = (studentPromotion, imageShoppingCart) => () => {
        const { dataShoppingCart, actionShoppingCart: { setDataShoppingCart } } = this.props;
        const urlFiles = [
            ...studentPromotion?.UrlFiles ?? [],
            ...imageShoppingCart ?? []
        ];
        this.props.navigation.navigate("StudentInfo",
            {
                supImages: helper.deepCopy(urlFiles),
                dataShoppingCart,
                setDataShoppingCart,
                isStudentCoupon: dataShoppingCart.cus_IsRequireAttachmentSMSPromotion,
                isStudentPromotion: dataShoppingCart.cus_IsPromotionType19Exist
            }
        );
    }

    handleUpdateSaleOrderWithNewBatch = (newSaleOrder, index, newQuantity) => {
        const { dataShoppingCart } = this.props;
        const cartShoppingModify = helper.deepCopy(dataShoppingCart);
        if (cartShoppingModify.SaleOrderDetails[index].Quantity !== newQuantity) {
            if (newQuantity === 0) {
                Alert.alert("", "Số lượng bạn nhập không hợp lệ. Vui lòng kiểm tra lại");
            }
            else {
                this.setState({ idOriginProduct: newSaleOrder.ProductID })
                cartShoppingModify.SaleOrderDetails[index] = newSaleOrder;
                cartShoppingModify.SaleOrderDetails[index].Quantity = newQuantity;
                cartShoppingModify.cus_PromDiscountExtraMaster = null;
                this.modifyShoppingCart({
                    cartRequest: cartShoppingModify,
                    discountCode: "",
                    giftCode: "",
                    promotionGroups: [],
                    isChangeQuantity: true
                });
            }
        } else {
            cartShoppingModify.SaleOrderDetails[index] = newSaleOrder;
            this.props.actionShoppingCart.stop_modify_shopping_cart(cartShoppingModify);
        }
    }

    handleGetProductSuggest = (products) => {

        this.setState({ productSuggests: products }, () => {
            this.productSuggestRef.current.snapToIndex(0)
        })

    }

    handleSubmitQuantity = (newSaleOrder) => {
        const { dataShoppingCart } = this.props;
        const { SaleOrderDetails } = dataShoppingCart;;
        const map = {};
        for (const itemSaleOrder of SaleOrderDetails) {
            map[itemSaleOrder.SaleOrderDetailID] = itemSaleOrder.Quantity;
        }

        const resultSaleOrderDetails = newSaleOrder.filter(
            (newItemSaleOrder) =>
                map[newItemSaleOrder.SaleOrderDetailID] !== newItemSaleOrder.Quantity
        );
        if (resultSaleOrderDetails.length > 0) {
            const filterSaleOrderDetail = SaleOrderDetails.filter((item) => (!item.cus_IsEditQuantity));
            const newSaleOrderDetail =
                filterSaleOrderDetail.concat(newSaleOrder);
            const cartShoppingModify = helper.deepCopy(dataShoppingCart);
            cartShoppingModify.SaleOrderDetails = newSaleOrderDetail;;
            cartShoppingModify.cus_PromDiscountExtraMaster = null;
            this.modifyShoppingCart({
                cartRequest: cartShoppingModify,
                discountCode: '',
                giftCode: '',
                promotionGroups: []
            });;
        }
    };

    handleSelectProduct = () => {
        this.searchProductsRef.current.close();
        this.setState({
            isProductSelected: true
        });
    };

    handleSearching = () => {
        this.setState({
            isProductSelected: false
        });
    };
    onCloseBarcode = () => {
        this.setState({ isOpenBarcode: false });

    }
    onOpenBarcode = (type) => {
        this.setState({ isOpenBarcode: true, typeScan: type });
    }
    handleBarcode = (barcode) => {
        const { typeScan } = this.state
        const { LOYALTY, COUPON } = SCAN_TYPE
        if (typeScan === LOYALTY) {
            this.setState({ isOpenBarcode: false }, () => {
                this.getInfoLoyalty(barcode);
            });
        }
        else if (typeScan === COUPON) {
            this.setState({ isOpenBarcode: false, discountCode: barcode }, () => {
                this.checkDiscountCode();
            });
        }
    }
    render() {
        const {
            dataShoppingCart: {
                SaleOrderDetails,
                SHCouponDiscountAmount,
                SHAmount,
                SHDepositAmount,
                TotalPointLoyalty,
                SHChangeTranferAmountFee,
                SHForwarderFeeAmount,
                TotalAdvance,
                ShippingCost,
                CustomerInfo,
                cus_PromDiscountExtraMaster,
                IsSOAdditionalPromotion,
                IsBrandBlueWorld,
                IsSOScreenSticker,
                IsAllowParticipationLoyalty,
                IsSOAnKhang,
                cus_SaleOrderDoSageBOList,
                FeeAndDepositBO,
                cus_SaleOrderDetailForwardFeeList
            },
            cartPromotion,
            voucherPromotion,
            stateCartPromotion,
            applyDetailIDs,
            userInfo: { storeID, brandID, provinceID },
            actionShoppingCart,
            saleScenarioTypeID,
            cartSalePromotion,
            dataShoppingCart
        } = this.props;
        const {
            discountCode,
            isVisibleDeposit,
            isVisibleLoyalty,
            isRequirePhone,
            isVisibleVoucher,
            relationShipType,
            isVisibleFee,
            isVisiblerofit,
            isShowDrug,
            isProductSelected,
            keyPromotion,
            isVisibleQuantity,
            isOriginQuantityChange,
            idOriginProduct,
            isOpenBarcode,
            vouchers,
            isPresented,
            customerPhone,
            customerName,
            gender
        } = this.state;
        const isApplyCoupon = (SHCouponDiscountAmount > 0);
        const isLoyalty = IsAllowParticipationLoyalty;
        const isShowDoSage = IsSOAnKhang && cus_SaleOrderDoSageBOList?.length > 0;
        const dataEditQuantity = SaleOrderDetails?.filter(item => item.cus_IsEditQuantity);
        const isEditQuantity = IsSOAnKhang && dataEditQuantity?.length > 0;
        const showButtonAdjust = IsSOScreenSticker || saleScenarioTypeID === STAFF_PROMOTION;
        const promoRequireInfors = this.getPromoRequireInfors(dataShoppingCart);
        const feePlusMoneyBO = this.calculateFeePlusMoneyBO(FeeAndDepositBO, SHChangeTranferAmountFee)
        const isVisibleVoucherCustomer = vouchers?.length > 0 && this.voucherCustomerPhone.current == customerPhone && !isPresented

        const valueDiscountIsRandoms = () => {
            let valueDiscount = [];
            SaleOrderDetails?.forEach(detail => {
                if (detail.giftSaleOrders) {
                    const giftSaleOrders = detail.giftSaleOrders;
                    giftSaleOrders?.forEach(giftSaleOrder => {
                        if (giftSaleOrder.IsRandomDiscount && !helper.IsEmptyObject(giftSaleOrder.ExtensionProperty)) {
                            valueDiscount.push(Number(giftSaleOrder?.ExtensionProperty?.DiscountValue));
                        }
                    });
                }
            });
            return valueDiscount;
        }

        const dataSum = valueDiscountIsRandoms();
        let SumValueDiscount = 0;
        if (dataSum) {
            dataSum.forEach(function (element) {
                SumValueDiscount += element;
            });
        } else {
            return SumValueDiscount;
        }

        const amountAdjust = helper.getTotalAmountAdjustPrice(SaleOrderDetails)

        return (
            <View style={{ flex: 1 }}>
                <BottomSheetModalProvider>
                    <KeyboardAwareScrollView
                        ref={r => this.scrollViewRef = r}
                        style={{
                            flex: 1,
                        }}
                        enableResetScrollToCoords={false}
                        keyboardShouldPersistTaps="always"
                        bounces={false}
                        overScrollMode="always"
                        showsHorizontalScrollIndicator={false}
                        showsVerticalScrollIndicator={false}
                        extraScrollHeight={60}
                    >
                        <SafeAreaView
                            style={{
                                flex: 1,
                                backgroundColor: COLORS.bgFAFAFA,
                            }}
                        >
                            {IsSOAnKhang && (
                                <SearchMedical
                                    isProductSelected={isProductSelected}
                                    onSearching={this.handleSearching}
                                />
                            )}

                            {helper.IsNonEmptyArray(cus_SaleOrderDetailForwardFeeList) && (
                                cus_SaleOrderDetailForwardFeeList.map((_item, index) => {
                                    return (
                                        <View key={index}>
                                            <ProductServiceItem product={_item} />

                                        </View>
                                    )
                                })
                            )}
                            <FlatList
                                data={SaleOrderDetails}
                                renderItem={({ item, index }) => (<ProductInfo
                                    onChangeSheet={() => {
                                        if (!helper.IsEmptyObject(item.PricePolicyApplyBO)) {
                                            const dataPackagePrice = [
                                                {
                                                    ...item.PricePolicyApplyBO,
                                                    DetailPolicies: item.PricePolicyApplyBO.lstSaleOrderPricePolicyApplyDetailBO
                                                }
                                            ]
                                            this.setState({ dataPackagePrice: dataPackagePrice }, () => {
                                                this.packagePriceSheetRef.current?.present();
                                            })
                                        }
                                        else {
                                            Alert.alert(translate("common.notification"), "Không có thông tin dịch vụ.", [
                                                {
                                                    text: 'OK',
                                                    style: 'cancel'
                                                }
                                            ]);
                                        }
                                    }}
                                    onchangeSheet={(products) => {
                                        this.handleGetProductSuggest(products)
                                    }}
                                    isAnnKhang={IsSOAnKhang}
                                    mainProduct={item}
                                    onDelete={this.removeItemMainProduct(SaleOrderDetails, index)}
                                    onDeleteSaleProduct={(data, fieldName, removeItem) => {
                                        this.removeItemSaleProduct(data, fieldName, index, removeItem);
                                    }}
                                    couponDiscount={SHCouponDiscountAmount}
                                    onUpdateSimProduct={(data, fieldName) => {
                                        this.onUpdateSimProduct(data, fieldName, index);
                                    }}
                                    applyDetailIDs={applyDetailIDs}
                                    isBK={IsSOAdditionalPromotion}
                                    actionShoppingCart={actionShoppingCart}
                                    onUpdateSaleOrder={(newSaleOrder, newQuantity) =>
                                        this.handleUpdateSaleOrderWithNewBatch(
                                            newSaleOrder,
                                            index,
                                            newQuantity
                                        )
                                    }
                                    onChangeQuantityProduct={(data) => {
                                        this.changeQuantityItemSaleProduct(data, index);
                                    }}
                                />)}
                                keyExtractor={(item, index) => item.SaleOrderDetailID}
                                keyboardShouldPersistTaps={"always"}
                                removeClippedSubviews={false}
                                ListFooterComponent={
                                    <View style={{
                                        width: constants.width
                                    }}>
                                        {
                                            !showButtonAdjust &&
                                            <ButtonAdjust
                                                onAdjustPrice={this.onPressAdjustPrice(isApplyCoupon)}
                                                onCreatePrice={this.onPressCreatePrice(isApplyCoupon)}
                                                isBlue={IsBrandBlueWorld}
                                            />
                                        }
                                        <Coupon
                                            discountCode={discountCode}
                                            onchangeDiscountCode={(text) => {
                                                if (helper.isValidateCharVN(text)) {
                                                    this.setState({ discountCode: text });
                                                }
                                            }}
                                            couponDiscount={SHCouponDiscountAmount}
                                            applyDiscountCode={this.checkDiscountCode}
                                            cancelDiscountCode={this.cancelDiscountCode}
                                            openBarcode={() => { this.onOpenBarcode(SCAN_TYPE.COUPON) }}
                                            isCartCoupon
                                            couponExpired={this.state.couponExpired}
                                        />
                                        <CartPromotionProfit
                                            data={cus_PromDiscountExtraMaster}
                                            onShow={() => {
                                                this.setState({ isVisiblerofit: true })
                                            }}
                                        />
                                        {/* <CartPromotion
                                            dataShoppingCart={dataShoppingCart}
                                            csPromotion={cartSalePromotion}
                                            promotion={cartPromotion}
                                            statePromotion={stateCartPromotion}
                                            applyPromotion={(promotionGroups, setKeyPromotion) => {
                                                if (this.timeoutApplyPromotion) {
                                                    clearTimeout(this.timeoutApplyPromotion);
                                                }
                                                this.timeoutApplyPromotion = setTimeout(this.applyCartPromotion(promotionGroups, setKeyPromotion), 200);
                                            }}
                                            onRetryCartPromotion={this.getCartPromotion}
                                            isVisibleVoucher={isVisibleVoucher}
                                            voucherPromotion={voucherPromotion}
                                            keyPromotion={keyPromotion}
                                            actionDetail={actionShoppingCart}
                                        /> */}
                                        <FeeAmount
                                            total={ShippingCost}
                                            onShow={() => {
                                                this.setState({ isVisibleFee: true })
                                            }}
                                        />
                                        <TotalAmount
                                            total={SHAmount - SumValueDiscount}
                                        />
                                        <DepositAmount
                                            total={TotalAdvance}
                                            onShow={() => {
                                                this.setState({ isVisibleDeposit: true })
                                            }}
                                        />
                                        {
                                            isLoyalty &&
                                            <PointLoyalty
                                                total={TotalPointLoyalty}
                                                onShow={() => {
                                                    this.setState({ isVisibleLoyalty: true })
                                                }}
                                            />
                                        }
                                        {helper.IsNonEmptyArray(promoRequireInfors) && <View style={{
                                            width: constants.width,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            backgroundColor: COLORS.btn5482AB,

                                        }}>

                                            <Button
                                                text={"Chụp hình bổ sung"}
                                                onPress={() => {
                                                    this.props.navigation.navigate("FormPromotion", { promoRequireInfors: promoRequireInfors });
                                                }}
                                                styleContainer={{
                                                    flexDirection: 'row',
                                                    width: constants.width - 10,
                                                    height: 40,
                                                    backgroundColor: COLORS.btn5482AB,
                                                    borderTopWidth: StyleSheet.hairlineWidth,
                                                    borderTopColor: COLORS.bdFFFFFF,
                                                    justifyContent: 'space-between',
                                                }}
                                                styleText={{
                                                    color: "white",
                                                    fontSize: 16,
                                                    marginRight: 8,
                                                    fontWeight: "bold",
                                                }}
                                                iconRight={{
                                                    iconSet: "FontAwesome",
                                                    name: "chevron-right",
                                                    size: 16,
                                                    color: COLORS.icFFFFBC
                                                }}
                                            />
                                        </View>}
                                        {this.renderCustomerInfo(CustomerInfo)}
                                        {
                                            IsSOAnKhang &&
                                            <PrescriptionImages
                                                handleStringImage={(listImage) => {
                                                    this.setState({
                                                        dataImageBatch: listImage
                                                    })
                                                }}
                                            />
                                        }
                                        <CheckBoxPolicy
                                            isSelected={this.state.isSelectedPolicy}
                                            onSelectPolicy={() => {
                                                this.setState({ isSelectedPolicy: !this.state.isSelectedPolicy })
                                            }}
                                            disabled={this.state.disabledPolicy}
                                        />
                                        {
                                            !IsSOAnKhang &&
                                            <RadioRelationShip
                                                type={relationShipType}
                                                onChangeType={(value) => {
                                                    this.setState({ relationShipType: value })
                                                }}
                                            />
                                        }
                                        <View style={{
                                            width: constants.width,
                                            alignItems: "center",
                                            justifyContent: "center",
                                            flexDirection: "row",
                                            paddingVertical: 10
                                        }}>
                                            <Button
                                                text={translate('shoppingCart.btn_delete_cart')}
                                                onPress={() => {
                                                    Keyboard.dismiss();
                                                    Alert.alert("",
                                                        `${translate('shoppingCart.delete_information_cart')}`,
                                                        [
                                                            {
                                                                text: translate('common.btn_skip'),
                                                                style: "cancel",
                                                            },
                                                            {
                                                                text: translate('common.btn_continue'),
                                                                style: "default",
                                                                onPress: this.onDeleteCart
                                                            }
                                                        ]
                                                    )
                                                }}
                                                styleContainer={{
                                                    borderRadius: 4,
                                                    backgroundColor: COLORS.btn6A9C84,
                                                    height: 44,
                                                    width: 140,
                                                    marginRight: 10,
                                                }}
                                                styleText={{
                                                    color: COLORS.txtFFFFFF,
                                                    fontSize: 14,
                                                    fontWeight: "bold"
                                                }}
                                            />
                                            <Button
                                                text={translate('shoppingCart.btn_create_sale_order')}
                                                onPress={this.onCreateSaleOrder}
                                                styleContainer={{
                                                    borderRadius: 4,
                                                    backgroundColor: COLORS.btnF49B0C,
                                                    height: 44,
                                                    width: 140,
                                                    marginLeft: 10
                                                }}
                                                styleText={{
                                                    color: COLORS.txtFFFFFF,
                                                    fontSize: 14,
                                                    fontWeight: "bold"
                                                }}
                                                disabled={stateCartPromotion.isFetching}
                                            />
                                        </View>
                                    </View>
                                }
                            />
                            <ModalDeposit
                                isVisible={isVisibleDeposit}
                                hideModal={() => {
                                    this.setState({ isVisibleDeposit: false })
                                }}
                                saleOrderDetails={SaleOrderDetails}
                            />
                            {/* <ModalFee
                            isVisible={isVisibleFee}
                            hideModal={() => {
                                this.setState({ isVisibleFee: false })
                            }}
                            SHChangeTranferAmountFee={SHChangeTranferAmountFee}
                            SHForwarderFeeAmount={SHForwarderFeeAmount}
                            ShippingCost={ShippingCost}
                        /> */}
                            {
                                isVisibleFee &&
                                <ModalFees
                                    isShow={isVisibleFee}
                                    hideModal={() => { this.setState({ isVisibleFee: false }) }}
                                    feePlusMoneyBO={feePlusMoneyBO}
                                    title={"Phụ phí tạm tính"}
                                />
                            }
                            <ModalLoyalty
                                isVisible={isVisibleLoyalty}
                                hideModal={() => {
                                    this.setState({ isVisibleLoyalty: false })
                                }}
                                saleOrderDetails={SaleOrderDetails}
                            />
                            <ModalProfitPromotion
                                isVisible={isVisiblerofit}
                                hideModal={() => {
                                    this.setState({ isVisiblerofit: false })
                                }}
                                data={cus_PromDiscountExtraMaster}
                            />
                            {
                                isRequirePhone &&
                                <PopupPhone
                                    isVisible={isRequirePhone}
                                    title={translate('shoppingCart.popup_phone_coupon_requires_phone')}
                                    onCancel={() => {
                                        this.setState({ isRequirePhone: false });
                                    }}
                                    onConFirm={(phone) => {
                                        this.setState({
                                            customerPhone: phone,
                                            isRequirePhone: false,
                                        }, () => {
                                            this.checkDiscountCode()
                                            this.handleAPIGetCustomerProfile(phone, dataShoppingCart.CustomerInfo)
                                        }
                                        );
                                    }}
                                />
                            }
                            <BlockUI
                                visible={this.state.blockUI}
                                onChangeVisible={(value) => {
                                    this.setState({
                                        blockUI: value
                                    });
                                    this.packagePriceSheetRef.current?.dismiss();
                                }}
                            />
                        </SafeAreaView >
                    </KeyboardAwareScrollView >
                    <View style={{ position: 'absolute', bottom: 55 }}>
                        {
                            isEditQuantity &&
                            <AnimationFloatingButton screenWidth={constants.width}>
                                <PrescriptionQuantity
                                    data={dataEditQuantity}
                                    isShowModal={isVisibleQuantity}
                                    onClose={() =>
                                        this.setState({ isVisibleQuantity: false })
                                    }
                                    onSubmit={(objBatch) => {
                                        this.handleSubmitQuantity(objBatch);
                                    }}
                                    isOriginQuantityChange={isOriginQuantityChange}
                                    idOriginProduct={idOriginProduct}
                                />
                                <TouchableOpacity
                                    onPress={() => {
                                        this.setState({ isVisibleQuantity: true });
                                    }}
                                    style={styles.fabMedicine}>
                                    <Icon
                                        name="edit"
                                        iconSet="MaterialIcons"
                                        size={22}
                                        color={COLORS.txtFFFFFF}
                                    />
                                </TouchableOpacity>
                            </AnimationFloatingButton>
                        }
                        {
                            isShowDoSage &&
                            <AnimationFloatingButton screenWidth={constants.width}>
                                <PrescriptionScreen
                                    dosage={cus_SaleOrderDoSageBOList}
                                    isVisible={isShowDrug}
                                    hideModal={() => this.setState({ isShowDrug: false })}
                                    handleGetDataDrug={(dataDrug) => {
                                        this.handleGetDataDrug(dataDrug);
                                    }}
                                    disabled={false}
                                />
                                <TouchableOpacity
                                    onPress={() => {
                                        this.setState({ isShowDrug: true })
                                    }}
                                    style={styles.fabMedicine}>
                                    <Icon
                                        name={"pill"}
                                        iconSet={"MaterialCommunityIcons"}
                                        size={22}
                                        color={COLORS.txtFFFFFF}
                                    />
                                </TouchableOpacity>
                            </AnimationFloatingButton>
                        }
                    </View>
                    {/* An Khang  */}
                    {
                        IsSOAnKhang &&
                        <BottomSheet ref={this.searchProductsRef}>
                            <MedicalProducts
                                onSelectProduct={this.handleSelectProduct}
                                isShoppingCart
                            />
                        </BottomSheet>
                    }
                    {
                        isOpenBarcode &&
                        <BarcodeCamera
                            isVisible={isOpenBarcode}
                            closeCamera={this.handleBarcode}
                            resultScanBarcode={this.handleBarcode}
                        />
                    }
                    {
                        <PackagePriceSheet
                            ref={this.packagePriceSheetRef}
                            snapPoints={['99.99999%']}
                            product={this.state.dataPackagePrice}
                            listPackageService={this.state.dataPackagePrice}
                            onGoNext={(data) => { }}
                            getCurrentIndex={() => {
                                console.log('');
                            }}
                            disabled={false}
                            topInset={200}
                            onCloseSheet={() => {
                                this.setState({ blockUI: false });
                            }}
                        />
                    }

                    {/* productSuggestRef */}
                    <ProductSuggestSheet
                        ref={this.productSuggestRef}
                        products={this.state.productSuggests}
                        snapPoints={['99.99%']}
                        onGoNext={(product) => {
                            const {
                                actionSale,
                                navigation
                            } = this.props;
                            const productSearch = {
                                productIDERP: product.ProductID,
                                imageUrl: product.ProductIMG,
                                imei: "",
                                inventoryStatusID: product.InventoryStatusID,
                                inventoryStatusName: "",
                                isImeiSim: false,
                                price: 0,
                                priceSpecified: null,
                                productID: 0,
                                productIdSpecified: null,
                                productInstallationConsultancyUrl: "",
                                productInstallmentUrl: "",
                                productName: product.ProductName,
                                webStatus: 0,
                                webStatusSpecified: null,
                            }
                            actionSale.setProductSearch(productSearch);
                            navigation.push(ENUM.SCREENS.PRODUCT_DETAIL, {
                                goToDetail: true
                            });
                            this.productSuggestRef.current.close()
                        }}

                        getCurrentIndex={() => {

                        }}
                        topInset={200}

                    />
                    <QuestionModal
                        isShow={this.state.isShowModalQuestion}
                        onPress={this.applyAnswerQuestion}
                        questions={this.state.questionList}
                        onSwitchAnswer={this.handleChangeAnswer}
                    />

                    <OTPSheet
                        bottomSheetRef={this.OTPSheetRef}
                        onChangeStatusSheet={() => { }}

                    >
                        <OTPInner
                            customerInfo={{
                                customerPhone: this.state.customerPhone,
                                customerName: this.state.customerName,
                            }}
                            onConfirm={this.handleAddToCartWithAdjustPrice}
                            typeOTP={this.state.typeOTP}
                            price={amountAdjust}
                        />
                    </OTPSheet>
                    <VoucherSheet
                        voucherSheetRef={this.voucherSheetRef}
                        onChangeStatusSheet={(index) => {
                            this.setState({ isPresented: index !== -1 })
                        }}
                        vouchers={this.state.vouchers}
                        getGiftVoucherCustomer={this.getGiftVoucherCustomer}
                        isFetchingGiftVoucher={this.state.isFetchingGiftVoucher}
                        customerInfo={{ customerName: customerName, gender: gender }}
                    />
                </BottomSheetModalProvider >
                {
                    isVisibleVoucherCustomer && <TouchableOpacity
                        style={styles.scrollTopButton}
                        onPress={() => { this.voucherSheetRef.current?.present() }}>
                        <Image
                            source={require('../../../assets/tag_price.png')}
                            style={styles.productImage}
                        />
                    </TouchableOpacity>
                }
            </View >
        );
    }

    getInfoLoyalty = (barcode) => {
        if (helper.IsNonEmptyString(barcode)) {
            const { userInfo: { brandID } } = this.props;
            showBlockUI();
            actionShoppingCartCreator.getInfoLoyaltyNew(barcode, brandID).then(({ result, customerProfile }) => {
                hideBlockUI();
                if (!helper.IsEmptyObject(result)) {
                    this.dataVerifyInfo = result;
                    const CustomerName = result.customerName
                    const CustomerAddress = result.customerAddress
                    this.setState({
                        customerPhone: result.customerPhone,
                        gender: result.gender,
                        customerName: CustomerName,
                        customerAddress: CustomerAddress,
                    }, () => {
                        if (customerProfile == null) return this.props.actionShoppingCart.set_map_customer_confirm_policy({
                            type: TYPE_PROFILE.CUSTOMER,
                            infoCustomerCRM: []
                        });
                        const customerInfo = customerProfile[0]
                        this.setState({
                            isSelectedPolicy: customerInfo.isSigned,
                            disabledPolicy: customerInfo.isSigned == 1 ? true : false
                        }, () => {
                            this.props.actionShoppingCart.set_map_customer_confirm_policy({
                                type: TYPE_PROFILE.CUSTOMER,
                                infoCustomerCRM: customerProfile
                            });
                        });
                    });
                }
            }).catch(msgError => {
                Alert.alert("", msgError, [{
                    text: "OK",
                    onPress: hideBlockUI
                }])
            })
        }
    }

    onChangTypeCustomer = (isCompany, CustomerInfo) => () => {
        const {
            taxID,
            customerName,
            customerAddress,
            customerPhone,
            isAllowInvoice,
            isLockTax
        } = this.state;
        this.setState({
            isCompany: isCompany,
            taxID: isLockTax ? taxID : "",
            customerPhone: (!isCompany && !!customerPhone && customerPhone.length > 10) ? "" : customerPhone,
            customerName: customerName,
            // customerAddress: customerAddress,
            contactPhone: customerPhone,
            // contactName: customerName,
            // contactAddress: customerAddress,
            gender: null,
            isSelectedPolicy: false,
            disabledPolicy: false
        }, () => {
            helper.IsNonEmptyString(this.state.contactPhone) && this.handleAPIGetCustomerProfile(this.state.contactPhone, CustomerInfo)
        });
        // if (!isAllowInvoice && isCompany) {
        //     Alert.alert("", "Đơn hàng không cho phép xuất hóa đơn công ty");
        // }
        // else {
        // }
    }

    getCustomerInfo = (phoneNumber, CustomerInfo) => {
        const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            const {
                customerName,
                customerAddress,
                isLockName,
                isLockAddress,
            } = this.state;
            this.checkAppLoyalty(phoneNumber);
            actionShoppingCartCreator.getCustomerByPhoneNew(phoneNumber).then(info => {
                const NewCustomerInfo = info.map((item) => {
                    return (
                        {
                            ...item,
                            id: uuidv4()
                        }
                    )
                })
                const CustomerName = isLockName ? customerName : (NewCustomerInfo[0].CustomerName || customerName);
                const CustomerAddress = isLockAddress ? customerAddress : (NewCustomerInfo[0].CustomerAddress || customerAddress);
                this.setState({
                    customerPhone: phoneNumber,
                    gender: NewCustomerInfo[0].Gender,
                    customerName: CustomerName,
                    customerAddress: CustomerAddress,
                    taxID: "",
                    contactPhone: "",
                    contactName: "",
                    contactAddress: "",
                    dataCustomerInfo: NewCustomerInfo,
                    customerInfoSelected: NewCustomerInfo[0].id
                });
                CustomerInfo.CustomerPhone = phoneNumber;
                CustomerInfo.CustomerName = CustomerName;
                CustomerInfo.CustomerAddress = CustomerAddress;
            })
        }
    }

    handleAPIGetCustomerProfile = (phoneNumber, CustomerInfo) => {
        const {
            userInfo: { storeID, languageID, moduleID },
        } = this.props;
        const baseBody = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
        }
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            showBlockUI()
            const {
                customerName,
                isLockName,
                isCompany,
                contactName,
                gender
            } = this.state;
            const { customerConfirmPolicy } = this.props;
            this.checkAppLoyalty(phoneNumber);
            if (customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber != phoneNumber) {
                this.props.actionShoppingCart.set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.CUSTOMER,
                    infoCustomerCRM: []
                });
            }
            actionShoppingCartCreator.getCustomerProfile({ ...baseBody, phoneNumber, typeProfile: TYPE_PROFILE.CUSTOMER }).then(customerProfile => {
                hideBlockUI()
                if (customerProfile == null) return this.props.actionShoppingCart.set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.CUSTOMER,
                    infoCustomerCRM: []
                });
                const customerInfo = customerProfile[0]
                const CustomerName = isLockName ? customerName : (customerInfo.customerName || customerName);
                if (isCompany) {
                    this.setState({
                        contactPhone: phoneNumber,
                        gender: getGender(customerInfo.gender),
                        contactName: customerInfo.customerName || contactName,
                        isSelectedPolicy: customerInfo.isSigned,
                        disabledPolicy: customerInfo.isSigned == 1 ? true : false,
                        customerIDCard: customerInfo.cardCustomerId || "",
                        customerPhone: phoneNumber
                    }, () => {
                        this.props.actionShoppingCart.set_map_customer_confirm_policy({
                            type: TYPE_PROFILE.CUSTOMER,
                            infoCustomerCRM: customerProfile
                        });
                    });
                }
                else {
                    this.setState({
                        customerPhone: phoneNumber,
                        gender: getGender(customerInfo.gender) == null ? (gender || 1) : getGender(customerInfo.gender),
                        customerName: CustomerName,
                        taxID: "",
                        contactPhone: "",
                        contactName: "",
                        customerIDCard: customerInfo.cardCustomerId || "",
                        isSelectedPolicy: customerInfo.isSigned,
                        disabledPolicy: customerInfo.isSigned == 1 ? true : false
                    }, () => {
                        this.props.actionShoppingCart.set_map_customer_confirm_policy({
                            type: TYPE_PROFILE.CUSTOMER,
                            infoCustomerCRM: customerProfile
                        });
                    });
                }
                CustomerInfo.CustomerPhone = phoneNumber;
                CustomerInfo.CustomerName = CustomerName;
                // CustomerInfo.CustomerAddress = CustomerAddress;
            }).catch(() => {
                hideBlockUI();
            }).finally((error) => {
                console.log("lỗi profile", error);
                this.isFirstRenderProfile = false;
            })


        }
    }

    handleCustomerInfo = (customerInfo) => {
        const {
            dataShoppingCart: {
                CustomerInfo
            }
        } = this.props;
        const {
            CustomerName,
            CustomerAddress,
            CustomerPhone,
            Gender,
            TaxID,
            ContactPhone,
            ContactName,
            ContactAddress
        } = customerInfo
        this.setState({
            customerPhone: CustomerPhone,
            gender: Gender,
            customerName: CustomerName,
            customerAddress: CustomerAddress,
            taxID: TaxID,
            contactPhone: ContactPhone,
            contactName: ContactName,
            contactAddress: ContactAddress,
            customerInfoSelected: customerInfo.id
        });
        CustomerInfo.CustomerPhone = CustomerPhone;
        CustomerInfo.CustomerName = CustomerName;
        CustomerInfo.CustomerAddress = CustomerAddress;
    }

    getContactInfo = (phoneNumber) => () => {
        const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            const {
                contactName,
                contactAddress
            } = this.state;
            this.checkAppLoyalty(phoneNumber);
            actionShoppingCartCreator.getCustomerByPhoneNew(phoneNumber).then(info => {
                const NewCustomerInfo = info.map((item) => {
                    return (
                        {
                            ...item,
                            id: uuidv4()
                        }
                    )
                })
                this.setState({
                    contactPhone: phoneNumber,
                    gender: NewCustomerInfo[0].Gender,
                    contactName: NewCustomerInfo[0].CustomerName || contactName,
                    contactAddress: NewCustomerInfo[0].CustomerAddress || contactAddress,
                    dataCustomerInfo: NewCustomerInfo,
                    customerInfoSelected: NewCustomerInfo[0].id
                })
            })
        }
    }

    checkAppLoyalty = (phoneNumber) => {
        const {
            dataShoppingCart: { TotalPointLoyalty, IsAllowParticipationLoyalty }
        } = this.props;
        const isCheckInstall =
            IsAllowParticipationLoyalty && TotalPointLoyalty > 0;
        if (isCheckInstall) {
            actionShoppingCartCreator
                .checkInstalledQTV(phoneNumber)
                .then((isExistApp) => {
                    const message = isExistApp
                        ? translate('shoppingCart.existed_app_loyalty')
                        : translate('shoppingCart.no_install_app_loyalty');
                    const type = isExistApp ? 'info' : 'warning';
                    if (!isExistApp) {
                        this.checkWowPoints(phoneNumber);
                    }
                    showMessage({ message, type, duration: 5000 });
                });
        }
    };

    checkWowPoints = (phoneNumber) => {
        // this.props.loyaltyAction.checkWowPoints(phoneNumber)
        //     .then((message) => {
        //         if (message) {
        //             // const { dataShoppingCart } = this.props;
        //             // const {
        //             //     ApplyPromotionToCustomerPhone: phoneValidate,
        //             // } = dataShoppingCart;
        //             // if (phoneValidate) {
        //             //     this.scrollViewRef.scrollToPosition(
        //             //         this.customerInfoCoordinate.x,
        //             //         this.customerInfoCoordinate.y,
        //             //         true
        //             //     );
        //             // }
        //             if (this.state.isLockPhone) {
        //                 Toast.show({
        //                     type: 'info',
        //                     text1: message,
        //                     visibilityTime: TIMEOUT_WOW_POINTS,
        //                     position: 'top',
        //                     topOffset: 54 + constants.heightTopSafe + 4 // HEADER + margin (4)
        //                 });
        //             }
        //             // clear message after 5s
        //             this.timeOutAlertMessage = setTimeout(() => {
        //                 this.props.loyaltyAction.setWowPointsMessage({
        //                     phoneNumber: '',
        //                     message: ''
        //                 });
        //             }, TIMEOUT_WOW_POINTS);
        //         }
        //     })
        //     .catch((error) => {
        //         !CONFIG.isPRODUCTION && Toast.show({
        //             type: 'error',
        //             text1: error ?? 'Lỗi: Hỏi Trần Nghĩa - 165059'
        //         });
        //         console.log("checkWowPoints ", error);
        //     });
    }

    getCompanyInfo = (taxID) => {
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidate = (isValidateTax10 || isValidateTax14);
        if (isValidate) {
            actionShoppingCartCreator.getCompanyByTax(taxID).then(info => {
                const {
                    customerPhone,
                    customerName,
                    customerAddress,
                    contactPhone,
                    contactName,
                    contactAddress,
                    isLockPhone,
                    isLockName,
                    isLockAddress,
                } = this.state;
                this.setState({
                    taxID: taxID,
                    gender: info.gender,
                    customerPhone: isLockPhone
                        ? customerPhone
                        : (info.customerPhone || customerPhone),
                    customerName: isLockName
                        ? customerName
                        : (info.customerName || customerName),
                    customerAddress: isLockAddress
                        ? customerAddress
                        : (info.customerAddress || customerAddress),
                    contactPhone: info.contactPhone || contactPhone,
                    contactName: info.contactName || contactName,
                    contactAddress: info.deliveryAddress || contactAddress,
                })
            })
        }
    }
    getCompanyProfile = (taxID) => {
        const {
            userInfo: { storeID, languageID, moduleID },
        } = this.props;
        const {
            customerPhone,
            isLockPhone
        } = this.state;
        const baseBody = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
        }
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidate = (isValidateTax10 || isValidateTax14);
        if (isValidate) {
            actionShoppingCartCreator.getCustomerProfile({ ...baseBody, phoneNumber: taxID, typeProfile: TYPE_PROFILE.COMPANY }).then(customerProfile => {
                if (customerProfile == null) return this.props.actionShoppingCart.set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.COMPANY,
                    infoCustomerCRM: []
                });
                const customerInfo = customerProfile[0]
                this.setState({
                    taxID: taxID,
                    customerName: customerInfo.companyName,
                    customerAddress: customerInfo.address
                    // customerPhone: isLockPhone
                    //     ? customerPhone
                    //     : customerInfo.companyPhone
                }, () => {
                    this.props.actionShoppingCart.set_map_customer_confirm_policy({
                        type: TYPE_PROFILE.COMPANY,
                        infoCustomerCRM: customerProfile
                    });
                })
            })
        }
    }

    getOldCustomerInfo = (CustomerInfo) => () => {
        const {
            customerPhone,
            customerName,
            customerAddress,
            isLockName,
            isLockAddress
        } = this.state;
        if (customerPhone) {
            // this.getCustomerInfo(customerPhone, CustomerInfo);
            this.handleAPIGetCustomerProfile(customerPhone, CustomerInfo);
        }
        else {
            storageHelper.getItem(STORAGE_CONST.CUSTOMER_INFO).then(result => {
                if (helper.IsNonEmptyString(result)) {
                    const dataTopInfo = JSON.parse(result);
                    const customerInfo = dataTopInfo.find(ele => helper.IsEmptyString(ele.taxID));
                    if (customerInfo) {
                        const CustomerPhone = customerInfo.customerPhone;
                        const CustomerName = isLockName ? customerName : customerInfo.customerName;
                        const CustomerAddress = isLockAddress ? customerAddress : customerInfo.customerAddress;
                        this.setState({
                            gender: customerInfo.gender,
                            customerPhone: CustomerPhone,
                            customerName: CustomerName,
                            customerAddress: CustomerAddress,
                            customerIDCard: customerInfo.customerIDCard,
                            dataCustomerInfo: []
                        });
                        this.checkAppLoyalty(CustomerPhone);
                        CustomerInfo.CustomerPhone = CustomerPhone;
                        CustomerInfo.CustomerName = CustomerName;
                        CustomerInfo.CustomerAddress = CustomerAddress;
                        this.handleAPIGetCustomerProfile(CustomerPhone, CustomerInfo);
                    }
                }
            }).catch(error => {
                console.log("getOldCustomerInfo error", error);
            });
        }
    }

    getOldCompanyInfo = () => {
        const {
            taxID,
            customerPhone,
            customerName,
            customerAddress,
            isLockPhone,
            isLockName,
            isLockAddress,
        } = this.state;
        if (taxID) {
            this.getCompanyInfo(taxID);
        }
        else {
            storageHelper.getItem(STORAGE_CONST.CUSTOMER_INFO).then(result => {
                if (helper.IsNonEmptyString(result)) {
                    const dataTopInfo = JSON.parse(result);
                    const customerInfo = dataTopInfo.find(ele => helper.IsNonEmptyString(ele.taxID));
                    if (customerInfo) {
                        this.setState({
                            taxID: customerInfo.taxID,
                            customerPhone: isLockPhone
                                ? customerPhone
                                : customerInfo.customerPhone,
                            customerName: isLockName
                                ? customerName
                                : customerInfo.customerName,
                            customerAddress: isLockAddress
                                ? customerAddress
                                : customerInfo.customerAddress,
                            customerIDCard: customerInfo.customerIDCard,
                            gender: customerInfo.contactGender,
                            contactAddress: customerInfo.deliveryAddress,
                            contactPhone: customerInfo.contactPhone,
                            contactName: customerInfo.contactName,
                        });
                    }
                }
            }).catch(error => {
                console.log("getOldCompanyInfo error", error);
            });
        }
    }

    getCartPromotion = () => {
        const { dataShoppingCart } = this.props;
        this.props.actionShoppingCart.getCartPromotion(dataShoppingCart);
    }

    getMultiSalePromotion = async (isWarning) => {
        if (this.props.shouldCallPromotion) {
            const { discountCode } = this.state;
            const { dataShoppingCart, numberPhoneCreateAtHome } = this.props;
            const newCart = { ...dataShoppingCart, CustomerInfo: { ...dataShoppingCart.CustomerInfo, CustomerPhone: numberPhoneCreateAtHome } }
            const isApplyCoupon = (newCart.SHCouponDiscountAmount > 0);
            setTimeout(() => {
                showBlockUI();
                this.props.actionShoppingCart.getMultiSalePromotion({
                    "discountCode": isApplyCoupon ? discountCode : "",
                    "cartRequest": newCart,
                    "giftCode": "",
                    "promotionGroups": [],
                }, isWarning).then(success => {
                    hideBlockUI();
                });
            }, 200);
        }
    }

    modifyShoppingCart = (data) => {
        showBlockUI();
        this.props.actionShoppingCart.modifyShoppingCart(data).then(dataCart => {
            hideBlockUI();
            this.getMultiSalePromotion();
            this.updateCartInfo(dataCart);
            if (data.isChangeQuantity) {
                this.setState({ isOriginQuantityChange: !this.state.isOriginQuantityChange })
            }
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.modifyShoppingCart(data)
                    }
                ]
            )
        })
    }

    removeItemMainProduct = (SaleOrderDetails, indexProduct) => () => {
        const isResetCart = (SaleOrderDetails.length == 1);
        if (isResetCart) {
            this.onDeleteCart();
        }
        else {
            const { dataShoppingCart } = this.props;
            this.unlockInstockCart([SaleOrderDetails[indexProduct]]);
            const cartShoppingModify = helper.deepCopy(dataShoppingCart);
            cartShoppingModify.SaleOrderDetails = SaleOrderDetails.filter(
                (saleOrder, position) => position != indexProduct
            );
            cartShoppingModify.cus_PromDiscountExtraMaster = null;
            this.modifyShoppingCart({
                cartRequest: cartShoppingModify,
                discountCode: "",
                giftCode: "",
                promotionGroups: []
            })
        }
    }

    removeItemSaleProduct = (data, fieldName, indexProduct, removeItem) => {
        const { dataShoppingCart } = this.props;
        this.unlockInstockCart([removeItem]);
        const cartShoppingModify = helper.deepCopy(dataShoppingCart);
        cartShoppingModify.SaleOrderDetails[indexProduct][fieldName] = data;
        cartShoppingModify.cus_PromDiscountExtraMaster = null;
        this.modifyShoppingCart({
            cartRequest: cartShoppingModify,
            discountCode: "",
            giftCode: "",
            promotionGroups: []
        })
    }

    onUpdateSimProduct = (data, fieldName, indexProduct) => {
        const { dataShoppingCart } = this.props;
        const cartShoppingModify = helper.deepCopy(dataShoppingCart);
        cartShoppingModify.SaleOrderDetails[indexProduct][fieldName] = data;
        this.modifyShoppingCart({
            cartRequest: cartShoppingModify,
            discountCode: "",
            giftCode: "",
            promotionGroups: []
        })
    }

    checkDiscountCode = () => {
        Keyboard.dismiss();
        const { discountCode, customerPhone } = this.state;
        const { dataShoppingCart } = this.props;
        const { SaleOrderDetails, cus_OldVoucherConcernType } = dataShoppingCart;
        const isValidateCode = helper.IsNonEmptyString(discountCode);
        const isValidateAdjust = checkApplyCoupon(SaleOrderDetails);
        const isAdditionalSale = cus_OldVoucherConcernType == 4;

        if (!isValidateCode) {
            Alert.alert("", translate('shoppingCart.please_enter_coupon'));
        }
        else if (!isValidateAdjust) {
            Alert.alert("", translate('shoppingCart.notification_update_cart'));
        }
        else {
            const customerInfo = isAdditionalSale ? dataShoppingCart.CustomerInfo : {}
            const cartRequest = {
                ...dataShoppingCart,
                "CustomerInfo": {
                    // "CustomerIDCard": "",
                    ...customerInfo,
                    "CustomerPhone": customerPhone,
                },
                cus_SaleOrderDetailRemoveByDiscountCode: null,
                cus_MesDeletePromoApplyConfirm: null

            };
            this.applyDiscountCode({
                cartRequest: cartRequest,
                discountCode: discountCode,
                giftCode: "",
                promotionGroups: []
            });
        }
    }

    applyDiscountCode = async (data) => {
        showBlockUI();
        const { discountCode, customerPhone } = this.state;
        const { actionShoppingCart } = this.props;
        try {
            const dataCart = await actionShoppingCart.giftVoucher(data);
            const {
                cus_SaleOrderDetailRemoveByDiscountCode,
                cus_MesDeletePromoApplyConfirm
            } = dataCart
            if (helper.IsNonEmptyString(cus_SaleOrderDetailRemoveByDiscountCode)) {
                const cartRequest = {
                    ...dataCart,
                    "CustomerInfo": {
                        // "CustomerIDCard": "",
                        "CustomerPhone": customerPhone,
                    },
                    cus_SaleOrderDetailRemoveByDiscountCode: null,
                    cus_MesDeletePromoApplyConfirm: null

                };
                if (this.isExcludeCoupon) {
                    // this.isExcludeCoupon = false;
                    this.applyCouponExcludePromotion({
                        cartRequest: cartRequest,
                        discountCode: discountCode,
                        giftCode: "",
                        promotionGroups: []
                    })
                }
                else {
                    Alert.alert(translate('common.notification_uppercase'), cus_MesDeletePromoApplyConfirm,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: "cancel",
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_accept'),
                                style: "default",
                                onPress: () => this.applyCouponExcludePromotion({
                                    cartRequest: cartRequest,
                                    discountCode: discountCode,
                                    giftCode: "",
                                    promotionGroups: []
                                })
                            }
                        ]
                    )
                }
            }
            else {
                hideBlockUI();
                this.getMultiSalePromotion();
                this.updateCartInfo(dataCart);
            }
        } catch (error) {
            const { msgError } = error;
            if (msgError == 'IsRequirePhone') {
                hideBlockUI();
                this.setState({ isRequirePhone: true });
            }
            else {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.applyDiscountCode(data)
                        }
                    ]
                )
            }
        }
    }

    applyCouponExcludePromotion = async (data) => {
        const { discountCode, customerPhone } = this.state;
        const {
            userInfo: { storeID },
            dataShoppingCart,
            actionShoppingCart
        } = this.props;

        const { SaleOrderDetails, cus_SaleOrderDetailRemoveByDiscountCode } = dataShoppingCart;
        try {
            const object = await actionShoppingCart.applyCouponExcludePromotion(data);
            const { MainProduct, ProductListGroups, DeliveryInfo, StoreChangeRequest } = object;
            const cartShoppingModify = helper.deepCopy(dataShoppingCart);
            cartShoppingModify.SaleOrderDetails = SaleOrderDetails.filter(saleOrder => saleOrder.SaleOrderDetailID != cus_SaleOrderDetailRemoveByDiscountCode);
            const Delivery = {
                deliveryStoreID: storeID,
                deliveryTypeID: DeliveryInfo.DeliveryTypeID,
                deliveryVehicles: DeliveryInfo.DeliveryVehicles,
                deliveryDistance: DeliveryInfo.DeliveryDistance,
                shippingCost: DeliveryInfo.ShippingCost,
                deliveryTime: DeliveryInfo.DeliveryTime,
                deliveryProvinceID: DeliveryInfo.DeliveryProvinceID,
                deliveryDistrictID: DeliveryInfo.DeliveryDistrictID,
                deliveryWardID: DeliveryInfo.DeliveryWardID,
                deliveryAddress: DeliveryInfo.DeliveryAddress,
                contactGender: DeliveryInfo.ContactGender,
                contactPhone: DeliveryInfo.ContactPhone,
                contactName: DeliveryInfo.ContactName,
                customerNote: DeliveryInfo.CustomerNote
            };
            let storeRequests = [];
            if (helper.IsNonEmptyArray(StoreChangeRequest)) {
                storeRequests = StoreChangeRequest.map((item) => {
                    return {
                        stockStoreID: item.StockStoreID,
                        storeChangeQuantity: item.StoreChangeQuantity,
                        transportTypeID: item.TransportTypeID,
                        getStockType: item.GetStockType,
                        isReplacedByPO: item.IsReplacedByPO,
                        specialSaleProgramID: item.SpecialSaleProgramID
                    }
                });
            }
            const cartRemove = {
                mainProduct: {
                    "productID": MainProduct.ProductID,
                    "imei": MainProduct.IMEI,
                    "inventoryStatusID": MainProduct.InventoryStatusID,
                    "pointLoyalty": MainProduct.PointLoyalty,
                    "outputTypeID": MainProduct.OutputTypeID,
                    "appliedQuantity": MainProduct.AppliedQuantity,
                    "outputStoreID": MainProduct.OutputStoreID,
                    "deliveryTypeID": MainProduct.DeliveryTypeID,
                    "saleProgramID": MainProduct.SaleProgramID,
                    "packagesTypeId": MainProduct.PackagesTypeID,
                    "extensionProperty": MainProduct.ExtensionProperty
                },
                promotionGroups: ProductListGroups,
                storeRequests: storeRequests,
                delivery: Delivery,
                cartRequest: cartShoppingModify
            }
            const newDataCart = await actionShoppingCart.newAddToShoppingCartForCoupon(cartRemove);
            this.isExcludeCoupon = true;
            newDataCart.cus_SaleOrderDetailRemoveByDiscountCode = null;
            newDataCart.cus_MesDeletePromoApplyConfirm = null;
            newDataCart.CustomerInfo.CustomerPhone = customerPhone;
            this.applyDiscountCode({
                cartRequest: newDataCart,
                discountCode: discountCode,
                giftCode: "",
                promotionGroups: []
            })
        } catch (error) {
            const { msgError } = error;
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: "OK",
                        onPress: hideBlockUI
                    }
                ]
            )
        }
    }

    cancelDiscountCode = () => {
        const { dataShoppingCart } = this.props;
        this.modifyShoppingCart({
            cartRequest: {
                ...dataShoppingCart,
                "CustomerInfo": null
            },
            discountCode: "",
            giftCode: "",
            promotionGroups: []
        })
    }

    applyCartPromotion = (promotionGroups, setKeyPromotion) => () => {
        const { discountCode } = this.state;
        const { dataShoppingCart } = this.props;
        const { SHCouponDiscountAmount } = dataShoppingCart;
        const isApplyCoupon = (SHCouponDiscountAmount > 0);
        showBlockUI();
        this.props.actionShoppingCart.modifyShoppingCart({
            cartRequest: dataShoppingCart,
            discountCode: isApplyCoupon ? discountCode : "",
            giftCode: "",
            promotionGroups: promotionGroups
        }).then(dataCart => {
            hideBlockUI();
            this.updateCartInfo(dataCart);
            this.setState({
                keyPromotion: setKeyPromotion,
                dataCartPromotion: promotionGroups
            });
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: this.applyCartPromotion(promotionGroups, setKeyPromotion)
                    }
                ]
            )
        })
    }

    onPressAdjustPrice = (isApplyCoupon) => () => {
        if (isApplyCoupon) {
            Alert.alert("",
                translate('shoppingCart.notification_cart_coupon')
            )
        }
        else {
            this.setState({
                totalPrePaid: 0,
                termLoan: 0,
                isInsuranceFee: false,
                isInsuranceGood: false,
                goodInsuranceID: 0,
                paymentMonthly: 0,
            });
            const { dataShoppingCart } = this.props;
            const dataAjust = helper.deepCopy(dataShoppingCart);
            this.props.actionShoppingCart.getDataAdjustPrice(dataAjust).then(
                success => {
                    this.props.navigation.navigate('CartAdjustPrice');
                }
            )
        }
    }

    getdataCreatePrice = (dataCreate) => {
        showBlockUI();
        this.props.actionShoppingCart.getDataCreatePrice(dataCreate).then(success => {
            hideBlockUI();
            this.props.navigation.navigate('CreatePriceCart');
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.getdataCreatePrice(dataCreate)
                    }
                ]
            )
        })
    }

    onPressCreatePrice = (isApplyCoupon) => () => {
        if (isApplyCoupon) {
            Alert.alert("",
                translate('shoppingCart.notification_cart_coupon')
            )
        }
        else {
            this.setState({
                totalPrePaid: 0,
                termLoan: 0,
                isInsuranceFee: false,
                isInsuranceGood: false,
                goodInsuranceID: 0,
                paymentMonthly: 0,
            });
            const { dataShoppingCart } = this.props;
            const dataCreate = helper.deepCopy(dataShoppingCart);
            this.getdataCreatePrice(dataCreate);
        }
    }

    onDeleteCart = () => {
        const {
            navigation,
            actionShoppingCart,
            actionPouch,
            initScreen,
            staffPromotionAction,
            saleScenarioTypeID,
            specialSaleProgramAction,
            dataShoppingCart
        } = this.props;
        this.unlockInstockCart(dataShoppingCart?.SaleOrderDetails);
        actionShoppingCart.deleteShoppingCart();
        this.props.actionShoppingCart.reset_map_customer_confirm_policy()
        actionPouch.setDataCartApply();
        staffPromotionAction.reset_staff_info();
        const nonResetScenarioSaleType = saleScenarioTypeID === ENUM.SALE_SCENARIO_TYPE.PRE_ORDER || saleScenarioTypeID === ENUM.SALE_SCENARIO_TYPE.SALE
        !nonResetScenarioSaleType &&
            specialSaleProgramAction.setScenarioSaleType(
                ENUM.SALE_SCENARIO_TYPE.SALE
            );
        navigation.reset({
            index: 0,
            routes: [{ name: initScreen }],
        });
    }

    checkValidateCustomerInfo = () => {
        const {
            customerPhone,
            customerName,
            customerAddress,
            relationShipType,
            taxID,
            isCompany,
            isHasSaleProgram,
            customerIDCard,
            totalPrePaid,
            termLoan,
            isHadCheck,
            isRequireCustomerInfo,
            dataContract,
            gender,
            contactPhone,
            candidateNo
        } = this.state;
        const {
            dataShoppingCart,
            stateCartPromotion,
            userInfo: { brandID, userName, storeID }
        } = this.props;
        const {
            packageRates,
            TotalPrePaid,
            TermLoan,
            isPartnerConnectAPI
        } = dataContract;
        const {
            SaleOrderDetails,
            IsAutoCreateEP,
            SaleOrderCusPromotion,
            IsSOScreenSticker,
            IsSOAnKhang,
            cus_IsPromotionType19Exist,
            cus_IsRequireAttachmentSMSPromotion,
            cus_UrlFilesShoppingCart,
            cus_IsRequiredCandidateInput,
            cus_IsDisplayCandidateInput
        } = dataShoppingCart;
        const isSupImages =
            helper.hasProperty(SaleOrderCusPromotion, "UrlFiles") ||
            helper.isArray(cus_UrlFilesShoppingCart);
        const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
        // const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
        // const regExpPhone11 = new RegExp(/^[0]\d{10}$/);
        // const isValidatePhone11 = isCompany && regExpPhone11.test(customerPhone);
        // const isValidatePhone = regExpPhone.test(customerPhone) || isValidatePhone11;
        const hasAdjustPrice = helper.isNewPricingCampaign(SaleOrderDetails, storeID)
        const isValidatePhone = helper.isValidatePhone(customerPhone);
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const regExpTax12 = new RegExp(/^\d{12}$/);
        const regExpTaxCAM = new RegExp(/^[KL]\d{3}[-]\d{9}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidateTax12 = regExpTax12.test(taxID);
        const isValidateTax = isValidateTax10 || isValidateTax14 || isValidateTax12;
        const isValidateTaxCAM = isValidateTax || regExpTaxCAM.test(taxID);
        const regExpIDCard9 = new RegExp(/^\d{9}$/);
        const regExpIDCard12 = new RegExp(/^\d{12}$/);
        const isValidateIDCard9 = regExpIDCard9.test(customerIDCard);
        const isValidateIDCard12 = regExpIDCard12.test(customerIDCard);
        const isValidateIDCard = isValidateIDCard9 || isValidateIDCard12;
        // had sim
        const isRequireImei = checkRequireImeiSim(SaleOrderDetails);
        const isHadVinaSim = checkHadSim(SaleOrderDetails, BRAND_ID_OF_SIM.VINA);
        const isHadViettelSim = checkHadSim(SaleOrderDetails, BRAND_ID_OF_SIM.VIETTEL);
        const isHadItelSim = checkHadSim(SaleOrderDetails, BRAND_ID_OF_SIM.ITEL);
        // visible sim
        const isCheckVisibleCardId = isHadVinaSim || isHadItelSim;
        const isVisibleCardId = isHadViettelSim || isCheckVisibleCardId;
        const isTPBankEvo = saleProgramInfo.PartnerInstallmentID == 34;
        const isCakeQTV = saleProgramInfo.PartnerInstallmentID == 35;


        if (
            !helper.checkConfigCreateSO(userName) &&
            !helper.checkConfigTimeSale(brandID) &&
            !helper.checkConfigTimeSalePreOrder(storeID)
        ) {
            Alert.alert("", "Bạn chỉ được phép thao tác chức năng này trong giờ bán hàng");
            return false;
        }
        if (stateCartPromotion.isError) {
            Alert.alert("", "Dữ liệu khuyến mãi tổng đơn không hợp lệ. Vui lòng kiểm tra lại.");
            return false;
        }
        if (isCompany) {
            if (!helper.IsNonEmptyString(taxID)) {
                Alert.alert("", translate('shoppingCart.please_enter_tax_code'));
                return false;
            };
            if (global.isVN && !isValidateTax) {
                Alert.alert("", translate('shoppingCart.validation_tax'));
                return false;
            };
            if (!global.isVN && !isValidateTaxCAM) {
                Alert.alert("", translate('shoppingCart.validation_tax_cam'));
                return false;
            };
            if (!helper.IsNonEmptyString(customerName) && !IsSOAnKhang) {
                Alert.alert("", translate('shoppingCart.validation_company_name'));
                return false;
            };
            if (!helper.IsNonEmptyString(customerAddress)) {
                Alert.alert("", translate('shoppingCart.validation_company_address'));
                return false;
            };
            if (!this.state.isSelectedPolicy && helper.IsNonEmptyString(contactPhone)) {
                Alert.alert("", "Khách hàng vui lòng đồng ý với chính sách xử lý dữ liệu cá nhân.");
                return false;
            }
        };
        if (!helper.IsNonEmptyString(customerName) && !IsSOAnKhang) {
            Alert.alert("", translate('shoppingCart.validation_customer_name'));
            return false;
        };
        if (isRequireCustomerInfo) {
            const validCustomerInfo = customerInfoValidation({ customerAddress, customerName, customerPhone });
            if (!validCustomerInfo) {
                return false;
            }
        }
        if (isVisibleCardId && !helper.IsNonEmptyString(customerIDCard)) {
            Alert.alert('', translate('shoppingCart.validation_ID_number'));
            return false;
        };
        if (isCheckVisibleCardId && !isHadCheck) {
            Alert.alert('', translate('shoppingCart.validation_CARD_ID_number'));
            return false;
        };
        // Không validate SĐT khi Bảo hành Miếng dán (Lỗi: Dữ liệu 11 số -> không Thể tạo đơn)
        if ((helper.IsNonEmptyString(customerPhone) || !checkApplyDiscount(dataShoppingCart, brandID) || hasAdjustPrice) && !IsSOScreenSticker) {
            if (!isValidatePhone) {
                Alert.alert("", translate('shoppingCart.validation_phone_number'));
                return false;
            };
            if (!helper.isValidatePhonePrefix(customerPhone)) {
                Alert.alert("", translate('shoppingCart.validation_phone_number_1'));
                return false;
            }
        };
        if (isSupImages) {
            if (
                !helper.IsNonEmptyString(customerIDCard) &&
                cus_IsPromotionType19Exist
            ) {
                // Chỉ kiểm tra CMND khi có tham gia KM 19
                Alert.alert("", translate('editSaleOrder.validation_CMND_rim'));
                return false;
            };
            let isValidStudentImage = true;
            let isValidStudentCouponImage = true;
            if (cus_IsPromotionType19Exist) {
                isValidStudentImage = validateStudentImages(
                    SaleOrderCusPromotion.UrlFiles
                );
            }
            if (cus_IsRequireAttachmentSMSPromotion) {
                if (cus_IsRequiredCandidateInput || !cus_IsDisplayCandidateInput) {
                    isValidStudentCouponImage = validateStudentCouponImages(
                        cus_UrlFilesShoppingCart
                    );
                }
                if (cus_IsRequiredCandidateInput && cus_IsDisplayCandidateInput && !helper.IsNonEmptyString(candidateNo)) {
                    Alert.alert("", "Vui lòng nhập số báo danh");
                    return false;
                }
                if (helper.IsNonEmptyString(candidateNo) && !this.isValidCandidateNo) {
                    Alert.alert("", "Số báo danh nhập vào không khớp với số báo danh phát hành coupon");
                    return false;
                }
            }
            const isEnoughFile = isValidStudentImage && isValidStudentCouponImage;
            if (!isEnoughFile) {
                Alert.alert("", translate('shoppingCart.validation_image'));
                return false;
            }
        }
        if (isHasSaleProgram) {
            const {
                MinPrepaid,
                MaxPrepaid,
                IsCardPartner,
                PartnerInstallmentID
            } = saleProgramInfo;
            if (!IsCardPartner) {
                if (!helper.IsNonEmptyString(customerIDCard)) {
                    Alert.alert("", translate('shoppingCart.validation_CMND_rim'));
                    return false;
                };
                if (!isValidateIDCard) {
                    Alert.alert("", translate('shoppingCart.validation_CMND_number'));
                    return false;
                }
            }
            if (TermLoan === undefined && (isTPBankEvo || isCakeQTV)) {
                Alert.alert("", translate('shoppingCart.please_choose_term_loan'));
                return false;
            }
            if (IsAutoCreateEP) {
                const isValidateMin = MinPrepaid <= totalPrePaid;
                const isValidateMax = totalPrePaid <= MaxPrepaid;
                const isValidatePrePaid = isValidateMin && isValidateMax;
                if (!helper.IsNonEmptyString(customerPhone)) {
                    Alert.alert("", translate('shoppingCart.validation_phone_number_rim'));
                    return false;
                };
                if (!isValidatePrePaid) {
                    Alert.alert("", translate('shoppingCart.validation_prepayment'));
                    return false;
                }
                if (termLoan == 0) {
                    Alert.alert("", translate('shoppingCart.please_choose_term_loan'));
                    return false;
                }
            }
            if (isPartnerConnectAPI) {
                if (TermLoan === undefined) {
                    Alert.alert("", translate('shoppingCart.please_choose_term_loan'));
                    return false;
                }
                if (packageRates === undefined || helper.IsEmptyString(packageRates)) {
                    Alert.alert("", translate('shoppingCart.please_enter_id'));
                    return false;
                }
                if (TotalPrePaid === undefined || helper.IsEmptyString(TotalPrePaid)) {
                    Alert.alert("", translate('shoppingCart.pls_enter_total_prepaid'));
                    return false;
                }
            }

        }
        if (relationShipType == null && !IsSOAnKhang) {
            Alert.alert("", translate('shoppingCart.please_choose_relation_ship_type'));
            return false;
        };
        if (isRequireImei) {
            Alert.alert("", translate('shoppingCart.please_enter_information_IMEI_SIM'));
            return false;
        }
        if (gender == null && !IsSOAnKhang) {
            Alert.alert("", translate('shoppingCart.validation_gender'));
            return false;
        }
        if (!this.state.isSelectedPolicy && helper.IsNonEmptyString(customerPhone) && !isCompany) {
            Alert.alert("", "Khách hàng vui lòng đồng ý với chính sách xử lý dữ liệu cá nhân.");
            return false;
        }
        return true;
    }
    /// check số lần hữu dụng
    handleCheckValue = (data) => {
        Alert.alert(
            translate('common.notification_uppercase'),
            data.Message,
            [
                {
                    text: translate('common.btn_accept'),
                    style: 'cancel',
                    onPress: hideBlockUI
                },

            ]
        );
    }
    /// callAPI check điều kiện giáy tờ
    handleCheckDocument = () => {
        const {
            customerIDCard,
        } = this.state;
        const { dataShoppingCart } = this.props;
        const { SaleOrderDetails } = dataShoppingCart
        const isNonEmpty = helper.IsNonEmptyArray(SaleOrderDetails);
        const regExpIDCard9 = new RegExp(/^\d{9}$/);
        const regExpIDCard12 = new RegExp(/^\d{12}$/);
        const isValidateIDCard12 = regExpIDCard12.test(customerIDCard);
        const isValidateIDCard = isValidateIDCard12;
        if (isNonEmpty) {
            if (!isValidateIDCard) {
                Alert.alert("", "Vui lòng nhập số CCCD/GCC đúng 12 chữ số");
                return false;
            }
            else {
                const quantitySimVina = SaleOrderDetails.filter(item => item.BrandIDOfSIM == BRAND_ID_OF_SIM.VINA).length;
                const quantitySimItel = SaleOrderDetails.filter(item => item.BrandIDOfSIM == BRAND_ID_OF_SIM.ITEL).length;
                showBlockUI();
                this.props.actionShoppingCart
                    .checkDocumentInformation({
                        "customerIDCard": customerIDCard,
                        "BrandID": quantitySimVina > 0 ? BRAND_ID_OF_SIM.VINA : BRAND_ID_OF_SIM.ITEL,
                        "Quantity": quantitySimVina > 0 ? quantitySimVina : quantitySimItel,
                    })
                    .then((data) => {
                        hideBlockUI();
                        this.setState({ isHadCheck: true })
                        this.handleCheckValue(data)
                    })
                    .catch((error) => {
                        Alert.alert(
                            translate('common.notification_uppercase'),
                            error.msgError,
                            [
                                {
                                    text: translate('common.btn_skip'),
                                    style: 'cancel',
                                    onPress: hideBlockUI
                                },
                                {
                                    text: translate('common.btn_notify_try_again'),
                                    style: 'default',
                                    onPress: () => this.handleCheckDocument()
                                }
                            ]
                        );
                    });
            }

        }
    }

    handleGetDataDrug = (dataDrug) => {
        this.setState({ isShowDrug: false });
        const { dataShoppingCart } = this.props;
        dataShoppingCart.cus_SaleOrderDoSageBOList = dataDrug;
    }

    changeQuantityItemSaleProduct = (data, indexProduct) => {
        const { dataShoppingCart } = this.props;
        const cartShoppingModify = helper.deepCopy(dataShoppingCart);
        cartShoppingModify.SaleOrderDetails[indexProduct].Quantity = data;
        cartShoppingModify.cus_PromDiscountExtraMaster = null;
        this.modifyShoppingCart({
            cartRequest: cartShoppingModify,
            discountCode: "",
            giftCode: "",
            promotionGroups: []
        })
    }

    checkCandidateNo = () => {
        showBlockUI();
        const { candidateNo, discountCode } = this.state;
        this.props.actionShoppingCart.checkCandidateDiscount({ candidateNo, discountCode }).then(() => {
            hideBlockUI();
            this.isValidCandidateNo = true;
        }).catch(msgError => {
            Alert.alert('Thông báo', msgError, [{ onPress: hideBlockUI }]);
        });
    }

    onCreateSaleOrder = () => {
        const isValidatePhone = helper.isValidatePhone(this.state.customerPhone);
        if (this.onFocus && isValidatePhone && helper.IsNonEmptyArray(this.props.customerConfirmPolicy[TYPE_PROFILE.CUSTOMER])) return Keyboard.dismiss();
        Keyboard.dismiss();
        const {
            gender,
            customerPhone,
            customerName,
            customerAddress,
            relationShipType,
            taxID,
            contactPhone,
            contactName,
            contactAddress,
            isCompany,
            isHasSaleProgram,
            customerIDCard,
            totalPrePaid,
            termLoan,
            isInsuranceFee,
            isInsuranceGood,
            goodInsuranceID,
            paymentMonthly,
            isVisibleVoucher,
            dataContract,
            contractID,
            keyPromotion,
            candidateNo
        } = this.state;
        const { dataShoppingCart, cartPromotion, voucherPromotion } = this.props;
        const { IsSOAnKhang, cus_SaleOrderDoSageBOList } = dataShoppingCart;
        // const isWarningDrugBatch = helper.IsEmptyArray(cus_SaleOrderDoSageBOList);
        // const checkNotify = handleEditDosage(cus_SaleOrderDoSageBOList);
        const isWarningPhone = !helper.IsNonEmptyString(customerPhone) && !IsSOAnKhang;
        const alwaysPromotion = cartPromotion.filter(ele => !ele.invisibleByCustomerPhone);
        const dataPromotion = isVisibleVoucher
            ? [...alwaysPromotion, ...voucherPromotion]
            : alwaysPromotion;
        const {
            isValidatePromotion,
            msgRequirePromotion,
            isWarningPromotion
        } = getMsgRequirePromotion(dataPromotion, keyPromotion);
        const msgWarningQuantity = checkMaxQuantity(dataShoppingCart);
        // const existDoSageList = (IsSOAnKhang && cus_SaleOrderDoSageBOList?.length > 0)
        // const alertNotify = existDoSageList && (checkNotify || isWarningDrugBatch)
        let msgConfirm = "";
        if (isWarningPhone) {
            msgConfirm = translate('shoppingCart.warning_no_enter_phone_number_customer');
        }
        // else if (alertNotify) {
        //     msgConfirm = "Bạn đang tạo đơn hàng mà chưa nhập liều dùng. Bạn có muốn tiếp tục?";
        // }
        else if (isWarningPromotion) {
            msgConfirm = translate('shoppingCart.warning_no_choose_promotion');
        }
        else {
            msgConfirm = "";
        }
        /*  */
        // const { userInfo: { storeID } } = this.props;
        // if (storeID == '7614') {
        //     msgConfirm += `\n${RELATION[`${relationShipType}`]}`;
        // }
        /*  */
        const isValidateCustomerInfo = this.checkValidateCustomerInfo();
        const promoRequireInfors = this.getPromoRequireInfors(dataShoppingCart);

        if (isValidateCustomerInfo) {
            if (!!msgWarningQuantity) {
                Alert.alert("", msgWarningQuantity);
            }
            else if (!isValidatePromotion) {
                Alert.alert(translate('shoppingCart.validation_promotion'), msgRequirePromotion);
            }
            else if (checkDataInvalid(promoRequireInfors)) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    `Vui lòng chụp hình ảnh bổ sung`,
                    [

                        {
                            text: "OK",
                            style: 'cancel',
                            onPress: () => this.props.navigation.navigate("FormPromotion", { promoRequireInfors: promoRequireInfors })

                        }
                    ]
                );
            }
            else {
                const defaultGender = gender == null && IsSOAnKhang ? 1 : gender;
                if (isHasSaleProgram) {
                    const { IsAutoCreateEP, SHAmount, ShippingCost } = dataShoppingCart;
                    const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
                    const { IsCardPartner, PartnerInstallmentID } = saleProgramInfo;
                    const totalAmount = (SHAmount - ShippingCost);
                    const isNewFollowPartner = helper.isNewFollowPartner(PartnerInstallmentID);
                    const newSaleProgramInfo = IsAutoCreateEP
                        ? {
                            ...saleProgramInfo,
                            "customerIDCard": customerIDCard,
                            'TotalPrePaid': totalPrePaid,
                            "TermLoan": termLoan,
                            "IsSelectedLifeInsurance": isInsuranceFee,
                            "IsSelectedGoodsInsurance": isInsuranceGood,
                            "GoodsInsuranceID": goodInsuranceID,
                            "PaymentAmountMonthly": paymentMonthly
                        }
                        : {
                            ...saleProgramInfo,
                            ...dataContract,
                            "customerIDCard": customerIDCard,
                            'TotalPrePaid':
                                (isNewFollowPartner || (IsCardPartner && !helper.IsNonEmptyString(contractID)))
                                    ? totalAmount
                                    : dataContract.TotalPrePaid,
                        };
                    dataShoppingCart.SaleOrderDetails.forEach(saleOrder => {
                        saleOrder.SaleProgramInfo = newSaleProgramInfo;
                    });
                }
                dataShoppingCart.cus_CandidateNO = candidateNo;
                const dataSaleOrder = {
                    "customerInfo": {
                        "customerName": customerName?.trim() || "Khách lẻ",
                        "customerAddress": customerAddress || "x",
                        "customerPhone": customerPhone,
                        "taxID": taxID,
                        "gender": defaultGender,
                        "contactName": taxID ? contactName : "",
                        "contactPhone": taxID ? contactPhone : "",
                        "deliveryAddress": taxID ? contactAddress : "",
                        "contactGender": defaultGender,
                        "customerIDCard": customerIDCard,
                        "ageID": "",
                        "birthday": ""
                    },
                    "relationShip": {
                        "relationShipType": IsSOAnKhang ? -1 : relationShipType
                    },
                    "cartRequest": dataShoppingCart,
                    "requestIDLoyalty": null,
                    "customerIDLoyalty": null,
                };
                if (!helper.IsEmptyObject(this.dataVerifyInfo)) {
                    if (customerPhone == this.dataVerifyInfo.customerPhone) {
                        dataSaleOrder.requestIDLoyalty = this.dataVerifyInfo.requestId;
                        dataSaleOrder.customerIDLoyalty = this.dataVerifyInfo.customerId;
                        dataSaleOrder.cartRequest.cus_LoyaltyCustomerPhone = customerPhone;
                    }
                }
                if (msgConfirm) {
                    Alert.alert("", msgConfirm,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: "cancel",
                            },
                            {
                                text: translate('shoppingCart.btn_continue_uppercase'),
                                style: "default",
                                onPress: () => this.handleModifyCustomerrofile(dataSaleOrder)
                            }
                        ]
                    )
                }
                else {
                    this.handleModifyCustomerrofile(dataSaleOrder);
                }
            }
        }
    }

    handleModifyCustomerrofile = async (dataSaleOrder) => {
        const { userInfo: { brandID } } = this.props
        if (!checkApplyDiscount(dataSaleOrder.cartRequest, brandID)) {
            const isDiscount = await this.handleAPICheckScreensProtector(dataSaleOrder);
            if (isDiscount == null) return
            dataSaleOrder.cartRequest.CheckNumberOfSlot = isDiscount
        }
        const profile = await this.handleAPIProfile(dataSaleOrder)
        if (helper.IsEmptyObject(profile)) return this.handleGetQuestion(dataSaleOrder)
        ///////
        if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.CUSTOMER])) {
            delete profile?.[TYPE_PROFILE.CUSTOMER]
        }
        if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.COMPANY])) {
            delete profile?.[TYPE_PROFILE.COMPANY]
        }
        if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.ADDRESS_RECEIVE])) {
            delete profile?.[TYPE_PROFILE.ADDRESS_RECEIVE]
        }
        if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.CUSTOMER_RECEIVE])) {
            delete profile?.[TYPE_PROFILE.CUSTOMER_RECEIVE]
        }
        dataSaleOrder.cartRequest.SaleOrderDetails.forEach(saleOrderDetail => {
            let newProfile = { ...profile }
            if (helper.IsNonEmptyArray(newProfile[TYPE_PROFILE.ADDRESS_RECEIVE])) {
                newProfile = { ...newProfile, [TYPE_PROFILE.ADDRESS_RECEIVE]: newProfile[TYPE_PROFILE.ADDRESS_RECEIVE].filter((item) => item.soProfileId == saleOrderDetail.SaleOrderDetailID) }
            }
            if (helper.IsNonEmptyArray(newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE])) {
                newProfile = { ...newProfile, [TYPE_PROFILE.CUSTOMER_RECEIVE]: newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE].filter((item) => item.soProfileId == saleOrderDetail.SaleOrderDetailID) }
            }
            saleOrderDetail.Profile = newProfile;
        })
        this.handleGetQuestion(dataSaleOrder)
    }

    handleGetQuestion = async (dataSaleOrder) => {
        const questions = await this.handleAnswerQuestion(dataSaleOrder)
        if (!helper.IsEmptyObject(questions)) {
            this.setState({ isShowModalQuestion: true, tempSaleOrders: dataSaleOrder, questionList: questions })
        }
        else {
            this.checkValidPromotion(dataSaleOrder)
        }

    }
    handleAnswerQuestion = async (dataSaleOrder) => {
        showBlockUI()
        try {
            const {
                userInfo: { storeID, languageID, moduleID } } = this.props;
            const body = {
                "loginStoreId": storeID,
                "languageID": languageID,
                "moduleID": moduleID,
                "shoppingCartRequest": dataSaleOrder.cartRequest
            }
            let questionList = await actionShoppingCartCreator.getQuestions(body)
            if (!helper.IsEmptyObject(this.state.questionList)) {
                questionList = this.state.questionList
                Object.entries(questionList).forEach(([key, value]) => {
                    if (!Object.keys(this.state.questionList).includes(key)) {
                        questionList = { ...this.state.questionList, key: value }
                    }

                })
            }
            return questionList
        } catch (error) {
            console.log("🚀 ~ ShoppingCart ~ handleAnswerQuestion= ~ error:", error)
            return {}
        }
    }
    handleChangeAnswer = ({ questionId, keyQuestion }) => (answerSelected) => {
        const newQuestionList = { ...this.state.questionList }
        for (const [key, value] of Object.entries(newQuestionList)) {
            if (keyQuestion == key) {
                value[questionId]?.answers?.forEach((item) => {
                    if (item.answerId == answerSelected.answerId) {
                        item.isSelected = 1
                    }
                    else {
                        item.isSelected = 0
                    }
                })
            }

        }
        this.setState({ questionList: newQuestionList })
    }

    applyAnswerQuestion = () => {
        if (this.validateAnswer()) {
            this.setState({ isShowModalQuestion: false }, () => {
                const dataSaleOrder = this.state.tempSaleOrders
                dataSaleOrder.cartRequest.QuestionData = this.state.questionList
                this.checkValidPromotion(dataSaleOrder)
            })
        }

    }
    validateAnswer = () => {
        for (const [key, questions] of Object.entries(this.state.questionList)) {
            for (const [key, value] of Object.entries(questions)) {
                const newAnswer = value.answers.filter((answers) => !answers.isSelected)
                if (newAnswer.length == value.answers.length) {
                    Alert.alert("", `Bạn chưa chọn câu trả lời cho câu hỏi ${value.questionValue}`);
                    return false;
                }
            }
        }
        return true
    }

    checkValidPromotion = (dataSaleOrder) => {
        const { cartRequest } = dataSaleOrder;
        const { SaleOrderDetails } = cartRequest
        const outputType = getOutputType(SaleOrderDetails)
        const { dataCartPromotion } = this.state
        const outputTypeIDList = outputType.filter((item, index) => outputType.indexOf(item) === index);
        const promotionIDList = dataCartPromotion.map((item) => (item.promotionID).toString())
        const checkMultiSalePromotion = !helper.IsNonEmptyArray(dataCartPromotion)
        if (checkMultiSalePromotion) {
            this.checkGiftVIP({ dataSaleOrder })
        }
        else {
            showBlockUI()
            this.props.actionShoppingCart.checkValidPromotion({ promotionIDList, outputTypeIDList })
                .then(res => {
                    const { Message } = res
                    let checkMessagePromotion = !helper.IsNonEmptyString(Message)
                    if (checkMessagePromotion) {
                        this.checkGiftVIP({ dataSaleOrder })
                    }
                    else {
                        Alert.alert(translate('common.notification_uppercase'), Message,
                            [{
                                text: "OK",
                                style: "default",
                                onPress: () => this.modifyShoppingCart({
                                    cartRequest: cartRequest,
                                    discountCode: "",
                                    giftCode: "",
                                    promotionGroups: []
                                })
                            }]
                        )
                    }
                }).catch(msgError => {
                    Alert.alert(translate('common.notification_uppercase'), msgError.msgError,
                        [{
                            text: "OK",
                            style: "default",
                            onPress: hideBlockUI
                        }]
                    )
                });
        }
    }
    handleAPIProfile = async (dataSaleOrder) => {
        showBlockUI()
        const {
            customerName,
            customerPhone,
            contactName,
            contactPhone,
            gender,
            isCompany,
            taxID,
            customerIDCard,
            customerAddress
        } = this.state
        let profileAddressAndRecieve = {}
        let profileCustomer = {}
        let profileCompany = {}
        let extraCustomerConfirmPolicy = {}
        let profileModify = {}
        let isInsertAddress = false
        let newPhoneNumber = isCompany ? contactPhone : customerPhone
        // người mua là người nhận
        let isSameCustomer = true
        const {
            userInfo: { storeID, languageID, moduleID }, customerConfirmPolicy, dataShoppingCart, numberPhoneCreateAtHome, defaultCustomer } = this.props;
        const deliveryAtHome = dataShoppingCart.SaleOrderDetails.findIndex(saleorder => {
            const { DeliveryInfoRequest: {
                DeliveryTypeID } } = saleorder;
            return (DeliveryTypeID != 1);
        });
        if ((deliveryAtHome !== -1) && helper.IsNonEmptyString(newPhoneNumber)) {
            dataShoppingCart.SaleOrderDetails.forEach(({ Profile, SaleOrderDetailID }) => {
                if (!helper.IsEmptyObject(Profile)) {
                    Object.entries(Profile).forEach(([key, value]) => {
                        if (`${TYPE_PROFILE.CUSTOMER_RECEIVE},${TYPE_PROFILE.ADDRESS_RECEIVE}`.includes(`${key}`)) {
                            for (let index = 0; index < value.length; index++) {
                                isInsertAddress = value[index].profileId == null || value[index].profileId != customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId;
                                value[index].soProfileId = SaleOrderDetailID;
                                value[index].profileId = customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId;
                                extraCustomerConfirmPolicy = { ...extraCustomerConfirmPolicy, [key]: [...extraCustomerConfirmPolicy?.[key] ?? [], value[index]] };
                            }
                            if (key == TYPE_PROFILE.CUSTOMER_RECEIVE) {
                                isSameCustomer = false
                            }
                        }

                    });
                }

            })
            /// Người mua là người nhận phải đổi thông tin của contact 
            if (isSameCustomer && !helper.IsEmptyObject(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0])) {
                const { customerName, phoneNumber, gender } = customerConfirmPolicy[TYPE_PROFILE.CUSTOMER][0]
                dataSaleOrder.cartRequest.SaleOrderDetails.forEach(saleOrderDetail => {
                    saleOrderDetail.DeliveryInfoRequest = {
                        ...saleOrderDetail.DeliveryInfoRequest,
                        ContactGender: gender,
                        ContactName: customerName,
                        ContactPhone: phoneNumber
                    }
                })
            }

        }
        let newProfile = { ...customerConfirmPolicy, ...extraCustomerConfirmPolicy }
        try {
            // if (helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER])) {
            const defaultCustomerInfo = {
                "customerName": "",
                "cardCustomerId": null,
                "gender": null,
                "profileId": 0,
                "type": 1,
                "versionCode": "",
                "phoneNumber": "",
                "isModify": 0,
                "isSigned": 0,
                "signatureId": 0,
                "soProfileId": null,
                "relationshipTypeId": 0,
                "relationshipId": 0
            }
            const companyDefault = {
                "companyId": 0,
                "companyName": "",
                "companyPhone": null,
                "address": "",
                "email": null,
                "taxNo": "",
                "profileId": 0,
                "type": 5,
                "versionCode": "",
                "phoneNumber": null,
                "isModify": 0,
                "isSigned": 0,
                "signatureId": 0,
                "soProfileId": null,
                "relationshipTypeId": 0,
                "relationshipId": 0
            }
            const customerInfo = helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]) ? { ...customerConfirmPolicy[TYPE_PROFILE.CUSTOMER][0] } : defaultCustomerInfo
            const oldInfoCustomer = {
                "customerName": customerInfo.customerName,
                "customerPhone": customerInfo.phoneNumber,
                "gender": customerInfo.gender,
                "cardCustomerId": customerInfo.cardCustomerId || ""
            }
            const infoCustomer = {
                "customerName": this.state.customerName,
                "customerPhone": this.state.customerPhone,
                "gender": this.state.gender,
                "cardCustomerId": this.state.customerIDCard
            }
            const infoCustomerContact = {
                "customerName": this.state.contactName,
                "customerPhone": this.state.contactPhone,
                "gender": this.state.gender,
                "cardCustomerId": this.state.customerIDCard
            }
            const infoSignCustomer = isCompany ? infoCustomerContact : infoCustomer
            const hasChangeValueProfile = helper.checkChangeValueOfPrototype(infoSignCustomer, oldInfoCustomer)
            if ((hasChangeValueProfile || !customerInfo.isSigned) && helper.IsNonEmptyString(newPhoneNumber)) {
                customerInfo.isModify = 1
                if (customerInfo.phoneNumber != newPhoneNumber) {
                    customerInfo.versionCode = "";
                    customerInfo.profileId = 0;
                }
                customerInfo.customerName = isCompany ? contactName : customerName
                customerInfo.phoneNumber = isCompany ? contactPhone : customerPhone
                customerInfo.gender = getGender(gender)
                customerInfo.cardCustomerId = customerIDCard
                profileCustomer = { [TYPE_PROFILE.CUSTOMER]: [customerInfo] }
            }
            if (helper.IsEmptyString(newPhoneNumber)) {
                delete newProfile?.[TYPE_PROFILE.CUSTOMER]

            }
            // }
            if (isCompany) {
                const companyInfo = helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.COMPANY]) ? { ...customerConfirmPolicy[TYPE_PROFILE.COMPANY][0] } : companyDefault
                const { companyName, companyPhone, taxNo, address } = companyInfo
                if (companyName != customerName || address != customerAddress || taxNo != taxID) {
                    companyInfo.isModify = 1
                    companyInfo.companyName = customerName
                    companyInfo.address = customerAddress
                    companyInfo.taxNo = taxID
                    profileCompany = { [TYPE_PROFILE.COMPANY]: [companyInfo] }
                }
            }
            // insert địa chỉ củ của giao về nhà cho khách hàng mới nếu như thay đổi số điện thoại
            if ((deliveryAtHome !== -1) && helper.IsNonEmptyString(newPhoneNumber)) {
                dataShoppingCart.SaleOrderDetails.forEach(({ Profile, SaleOrderDetailID }) => {
                    if (!helper.IsEmptyObject(Profile)) {
                        Object.entries(Profile).forEach(([key, value]) => {
                            if (`${TYPE_PROFILE.CUSTOMER_RECEIVE},${TYPE_PROFILE.ADDRESS_RECEIVE}`.includes(`${key}`)) {
                                for (let index = 0; index < value.length; index++) {
                                    if (isInsertAddress) {
                                        if (key == TYPE_PROFILE.ADDRESS_RECEIVE) {
                                            value[index].profileId = customerInfo.profileId;
                                            value[index].deliveryId = 0;
                                            value[index].isModify = 0;
                                        }
                                        if (key == TYPE_PROFILE.CUSTOMER_RECEIVE) {
                                            value[index].profileId = customerInfo.profileId;
                                            value[index].receiverId = 0;
                                            value[index].isModify = 0;
                                        }
                                        value[index].soProfileId = SaleOrderDetailID
                                        profileAddressAndRecieve = { ...profileAddressAndRecieve, [key]: [...profileAddressAndRecieve?.[key] ?? [], value[index]] };
                                    }
                                }
                            }

                        });
                    }
                })
            }
            const profileRequest = {
                ...profileAddressAndRecieve, ...profileCompany, ...profileCustomer
            }
            if (!helper.IsEmptyObject(profileRequest)) {
                const body = {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "profile": profileRequest
                }
                profileModify = await actionShoppingCartCreator.modifyCustomerProfile(body)
            }
            newProfile[TYPE_PROFILE.CUSTOMER] = profileModify[TYPE_PROFILE.CUSTOMER] || newProfile[TYPE_PROFILE.CUSTOMER]?.filter((_, index) => (index == 0)) || []
            newProfile[TYPE_PROFILE.COMPANY] = profileModify[TYPE_PROFILE.COMPANY] || newProfile[TYPE_PROFILE.COMPANY]?.filter((_, index) => (index == 0)) || []
            newProfile[TYPE_PROFILE.ADDRESS_RECEIVE] = profileModify[TYPE_PROFILE.ADDRESS_RECEIVE] || newProfile[TYPE_PROFILE.ADDRESS_RECEIVE] || []
            newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE] = profileModify[TYPE_PROFILE.CUSTOMER_RECEIVE] || newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE] || []
            return newProfile
        } catch (error) {
            dataSaleOrder.cartRequest.SaleOrderDetails.forEach(element => {
                element.Profile = {}
            });
            console.log("🚀 ~ ShoppingCart ~ handleAPIProfile= ~ error:", error)
            return {}
        }
        // finally {
        //     // hideBlockUI()
        //     newProfile[TYPE_PROFILE.CUSTOMER] = profileModify[TYPE_PROFILE.CUSTOMER] || newProfile[TYPE_PROFILE.CUSTOMER]?.filter((_, index) => (index == 0)) || []
        //     newProfile[TYPE_PROFILE.COMPANY] = profileModify[TYPE_PROFILE.COMPANY] || newProfile[TYPE_PROFILE.COMPANY]?.filter((_, index) => (index == 0)) || []
        //     newProfile[TYPE_PROFILE.ADDRESS_RECEIVE] = profileModify[TYPE_PROFILE.ADDRESS_RECEIVE] || newProfile[TYPE_PROFILE.ADDRESS_RECEIVE] || []
        //     newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE] = profileModify[TYPE_PROFILE.CUSTOMER_RECEIVE] || newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE] || []
        //     return newProfile
        // }

    }

    checkGiftVIP = ({ dataSaleOrder }) => {
        const { staffInfo, saleScenarioTypeID, userInfo: { storeID } } = this.props;
        const { customerInfo, cartRequest, customerIDLoyalty } = dataSaleOrder;
        const { dataImageBatch, isHasSaleProgram } = this.state;
        const { IsSOAnKhang,
            IsAutoCreateEP,
            cus_OldVoucherConcernType,
            SaleOrderDetails,
            IsAllowParticipationLoyalty,
            IsSenOTPConfirmCustomer,
            CheckNumberOfSlot
        } = cartRequest;
        const isAdditionalSale = cus_OldVoucherConcernType === 4;
        const shouldSendOTP = SaleOrderDetails.some(product => product.IsSendOTP);
        const isStaffPromotion = saleScenarioTypeID === STAFF_PROMOTION;
        const existStaffInfo = !helper.IsEmptyObject(staffInfo);
        const isLoyalty = IsAllowParticipationLoyalty && !helper.IsNonEmptyString(customerIDLoyalty);
        const hasAdjustPrice = helper.isNewPricingCampaign(SaleOrderDetails, storeID)
        // Handle staff promotion
        if (isStaffPromotion && existStaffInfo) {
            cartRequest.cus_SOStaffBOList = [{
                SaleID: staffInfo.SaleID,
                UserName: staffInfo.UserName
            }];
        }

        // Handle AnKhang specific logic
        if (IsSOAnKhang && dataImageBatch.length > 0 && dataImageBatch[0].UrlFile) {
            cartRequest.cus_UrlFilesShoppingCart = dataImageBatch;
        }

        // Handle additional sale with OTP requirement
        if (isAdditionalSale && shouldSendOTP) {
            hideBlockUI();
            this.props.navigation.navigate("CustomerAuthentication", {
                dataSaleOrder,
                customerInfo,
                oldCustomerInfo: {
                    customerName: cartRequest.CustomerInfo.OldCustomerName,
                    customerPhone: cartRequest.CustomerInfo.OldCustomerPhone
                },
                isHasSaleProgram,
                typeOTP: 'PROMOTION'
            });
        } else {
            // Normal sales flow
            if (CheckNumberOfSlot || hasAdjustPrice) {
                this.setState({ tempSaleOrders: dataSaleOrder, typeOTP: hasAdjustPrice ? "ADJUSTPRICEGIFTVOUCHER" : "SCREENPROTECTOR" }, () => {
                    this.OTPSheetRef.current?.present()
                })
            }
            else {
                this.handleSaleOrderFlow(
                    dataSaleOrder,
                    isHasSaleProgram,
                    IsSenOTPConfirmCustomer,
                    isLoyalty,
                    customerIDLoyalty
                );
            }
        }
    };

    getDataLoyalty = (customerPhone, dataSaleOrder) => {
        const { isHasSaleProgram } = this.state;
        const { cartRequest: { IsAutoCreateEP, IsSenOTPConfirmCustomer } } = dataSaleOrder;
        if (isHasSaleProgram && IsSenOTPConfirmCustomer) {
            this.getDataInstallment(dataSaleOrder);
        }
        else {
            this.props.actionShoppingCart.checkCredentialExist(customerPhone, dataSaleOrder)
                .then(success => {
                    hideBlockUI();
                    this.props.navigation.navigate("Loyalty");
                })
        }
    }

    onCheckLoyaltyPoint = (dataSaleOrder) => {
        const { isHasSaleProgram } = this.state;
        const {
            cartRequest: { TotalPointLoyalty, IsAutoCreateEP },
            customerInfo: { customerPhone }
        } = dataSaleOrder;
        const { route } = this.props;
        const typeOTP = route?.params?.typeOTP;
        const isValidatePhone = helper.IsNonEmptyString(customerPhone);
        const isValidatePoint = (TotalPointLoyalty > 0);
        const isMoveToLoyalty = isValidatePoint && isValidatePhone;

        if (helper.IsNonEmptyString(typeOTP)) {
            this.getDataInstallment(dataSaleOrder, typeOTP);
        }
        else if (isMoveToLoyalty) {
            this.getDataLoyalty(customerPhone, dataSaleOrder);
        }
        else {
            this.addToSaleOrderCart(dataSaleOrder);
        }
    }

    goToPaymentSO = (info) => {
        const { SaleOrderID, SaleOrderTypeID } = info;
        const {
            actionPaymentOrder,
            navigation,
        } = this.props;
        actionPaymentOrder.setDataSO({
            SaleOrderID: SaleOrderID,
            SaleOrderTypeID: SaleOrderTypeID
        }).then(success => {
            hideBlockUI();
            navigation.navigate('SaleOrderPayment');
            actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
            actionPaymentOrder.getReportPrinterSocket(SaleOrderTypeID);
            actionPaymentOrder.getDataQRTransaction(SaleOrderID);
            actionPaymentOrder.getDataSCTransaction(SaleOrderID);
        })
    };

    addToSaleOrderCart = async (data) => {
        const {
            dataShoppingCart: { IsSOAnKhang },
            userInfo: { storeID },
            staffPromotionAction,
            saleScenarioTypeID,
            specialSaleProgramAction
        } = this.props;
        showBlockUI();
        const isHasRight = await helper.checkStorePermission(storeID);
        if (isHasRight) {
            this.props.actionSaleOrder.addToSaleOrderCart(data).then(orderInfo => {
                const { customerInfo, customerIDLoyalty } = data;
                const { object } = orderInfo;
                const { SaleOrders } = object
                if (IsSOAnKhang) {
                    this.props.actionSaleOrder.setInitScreen('AnKhangPharmacy');
                }
                // ngưng ghi âm
                if (this.props?.isRecording) {
                    const saleOrderIDs = SaleOrders?.map((_item) => (_item?.SaleOrderID))?.join(",") ?? "";
                    this.props?.stopRecording(saleOrderIDs)
                }
                this.props.actionShoppingCart.deleteShoppingCart();
                this.props.actionPouch.setDataCartApply();
                staffPromotionAction.reset_staff_info();
                const nonResetScenarioSaleType = saleScenarioTypeID === ENUM.SALE_SCENARIO_TYPE.PRE_ORDER || saleScenarioTypeID === ENUM.SALE_SCENARIO_TYPE.SALE
                !nonResetScenarioSaleType &&
                    specialSaleProgramAction.setScenarioSaleType(
                        ENUM.SALE_SCENARIO_TYPE.SALE
                    );
                if (IsSOAnKhang && SaleOrders.length === 1) {
                    this.goToPaymentSO(SaleOrders[0])
                }
                else {
                    hideBlockUI();
                    this.props.navigation.navigate("SaleOrderCart");
                }
                storageHelper.updateTopCustomerInfo(customerInfo);
                if (helper.IsNonEmptyString(customerIDLoyalty)) {
                    storageHelper.updateVerifyLoyalty(customerInfo.customerPhone, object.CartID);
                }


                this.props.actionShoppingCart.reset_map_customer_confirm_policy()
            }).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.addToSaleOrderCart(data)
                        }
                    ]
                )
            });
        }
        else {
            Alert.alert(translate('common.notification_uppercase'), `Bạn không có quyền trên siêu thị ${storeID}`,
                [{
                    text: "OK",
                    onPress: hideBlockUI
                }]
            )
        }
    }

    getDataMemberPoint = (dataSaleOrder) => {
        const { isHasSaleProgram } = this.state;
        const {
            cartRequest: { IsAutoCreateEP, IsSenOTPConfirmCustomer },
            customerInfo: { customerPhone }
        } = dataSaleOrder;
        const isValidatePhone = helper.IsNonEmptyString(customerPhone);
        const { route } = this.props;
        const typeOTP = route?.params?.typeOTP;
        if (isHasSaleProgram && IsSenOTPConfirmCustomer) {
            this.getDataInstallment(dataSaleOrder);
        }
        // else if (isValidatePhone) {
        //     showBlockUI();
        //     this.props.actionShoppingCart.getMemberPoint(dataSaleOrder).then(memberPointInfo => {
        //         const { decUsePoint } = memberPointInfo;
        //         if (decUsePoint > 0) {
        //             this.props.actionShoppingCart.getDataInstallment(dataSaleOrder, typeOTP)
        //                 .then(success => {
        //                     hideBlockUI();
        //                     this.props.navigation.navigate("MemberPoint");
        //                 })
        //         }
        //         else if (helper.IsNonEmptyString(typeOTP)) {
        //             this.getDataInstallment(dataSaleOrder, typeOTP);
        //         }
        //         else {
        //             this.addToSaleOrderCart(dataSaleOrder);
        //         }
        //     });
        // }
        else {
            this.addToSaleOrderCart(dataSaleOrder);
        }
    }

    getDataInstallment = async (dataSaleOrder, typeOTP = 'INSTALLMENT') => {
        try {
            showBlockUI();
            const { customerConfirmPolicy } = this.props;

            const hasInstallmentSamsung = dataSaleOrder.cartRequest?.SaleOrderDetails?.some(
                item => item?.SaleProgramInfo?.PartnerInstallmentID == PARTNER_ID.SAMSUNG
            );

            const profileId = customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId;
            console.log("🚀 ~ ShoppingCart ~ getDataInstallment= ~ dataSaleOrder:", dataSaleOrder, PARTNER_ID.SAMSUNG, profileId, hasInstallmentSamsung, !!profileId)

            if (hasInstallmentSamsung && !!profileId) {
                const shouldSendOTPSamsung = await this.checkSendOTPSamsung(profileId);
                if (shouldSendOTPSamsung) {
                    await this.fetchInstallmentData(dataSaleOrder, typeOTP);
                } else {
                    this.addToSaleOrderCart(dataSaleOrder);
                }
            } else {
                await this.fetchInstallmentData(dataSaleOrder, typeOTP);
            }
        } catch (error) {
            console.log('Error in getDataInstallment:', error);
        }
    };

    fetchInstallmentData = async (dataSaleOrder, typeOTP) => {
        const { actionShoppingCart, navigation } = this.props;

        const success = await actionShoppingCart.getDataInstallment(dataSaleOrder, typeOTP);
        hideBlockUI()
        navigation.navigate('InstallmentOTP');
        return success;
    };

    checkSendOTPSamsung = async (profileID) => {
        const { userInfo: { storeID, userName, languageID, moduleID } } = this.props
        const body = {
            "loginStoreId": storeID,
            "loginUser": userName,
            "languageId": languageID,
            "moduleId": moduleID,
            "CUSTOMERID": profileID,
            "PARTNERINSTALLMENTID": 33,
            "SERVICETYPE": "OTPCREATEINSTALLMENT"
        }
        const isSendOTP = await actionShoppingCartCreator.checkSendOTPSamSung(body)
        return isSendOTP
    }

    getDefaultCustomerInfo = () => {
        const { dataShoppingCart, defaultCustomer, staffInfo, numberPhoneCreateAtHome, route } = this.props;
        const { profileSO } = route.params ?? { profileSO: {} };
        const {
            SaleOrderDetails,
            ApplyPromotionToCustomerPhone: phoneValidate,
            CustomerInfo,
            IsSOScreenSticker,
            cus_OldVoucherConcernType
        } = dataShoppingCart;
        const isApplyPhone = !!phoneValidate;
        const infoDelivery = SaleOrderDetails.find(saleorder => {
            const { DeliveryInfoRequest: {
                DeliveryTypeID, ContactPhone } } = saleorder;
            return (DeliveryTypeID != 1 || helper.IsNonEmptyString(ContactPhone));
        });
        const existStaffInfo = !helper.IsEmptyObject(staffInfo)
        let defaultInfo = {
            gender: IsSOScreenSticker ? CustomerInfo.Gender : defaultCustomer.gender,
            customerPhone: CustomerInfo.CustomerPhone || defaultCustomer.customerPhone,
            customerName: CustomerInfo.CustomerName || defaultCustomer.customerName,
            customerAddress: CustomerInfo.CustomerAddress || defaultCustomer.customerAddress,
        };
        if (infoDelivery) {
            const { DeliveryInfoRequest: {
                ContactGender,
                ContactName,
                ContactPhone,
                DeliveryAddress,
            } } = infoDelivery;
            defaultInfo = {
                // gender: ContactGender,
                // customerPhone: ContactPhone,
                // customerName: ContactName,
                // customerAddress: DeliveryAddress,
                gender: null,
                customerPhone: numberPhoneCreateAtHome,
                customerName: "",
                customerAddress: "",
            };
        }
        const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
        if (!helper.IsEmptyObject(saleProgramInfo)) {
            const {
                TaxID,
                CustomerAddress,
                CustomerName,
                DisableTaxID,
                DisableCustomerAddress,
                DisableCustomerName,
                IsLockCustomer,
                IsAllowCompanyInvoice,
                TotalPrePaid,
                TermLoan,
            } = saleProgramInfo;
            const regExpTax10 = new RegExp(/^\d{10}$/);
            const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
            const isValidateTax10 = regExpTax10.test(TaxID);
            const isValidateTax14 = regExpTax14.test(TaxID);
            const isValidateTax = isValidateTax10 || isValidateTax14;
            this.setState({
                taxID: isValidateTax ? TaxID : "",
                customerPhone: phoneValidate || staffInfo.CustomerPhone || defaultInfo.customerPhone,
                customerName: DisableCustomerName
                    ? CustomerName
                    : staffInfo.CustomerName || defaultInfo.customerName,
                customerAddress: DisableCustomerAddress
                    ? CustomerAddress
                    : staffInfo.CustomerAddress || defaultInfo.customerAddress,
                gender: existStaffInfo ? staffInfo.Gender : defaultInfo.gender,
                contactPhone: phoneValidate || staffInfo.CustomerPhone || defaultInfo.customerPhone,
                // contactName: existStaffInfo ? staffInfo.CustomerName : defaultInfo.customerName,
                // contactAddress: existStaffInfo ? staffInfo.CustomerAddress : defaultInfo.customerAddress,
                isCompany: !!TaxID,
                isHasSaleProgram: true,
                isLockCustomer: IsLockCustomer,
                isLockTax: DisableTaxID,
                isLockPhone: isApplyPhone,
                isLockName: DisableCustomerName,
                isLockAddress: DisableCustomerAddress,
                isAllowInvoice: IsAllowCompanyInvoice,
                customerIDCard: CustomerInfo.CustomerIDCard || "",
                totalPrePaid: TotalPrePaid || 0,
                termLoan: TermLoan || 0
            }, () => {
                if ((isApplyPhone && helper.IsEmptyObject(saleProgramInfo)) || existStaffInfo || this.state.contactPhone) {
                    const newNumberPhone = existStaffInfo ? staffInfo.CustomerPhone : phoneValidate || this.state.contactPhone
                    // this.getCustomerInfo(phoneValidate, CustomerInfo);
                    this.handleAPIGetCustomerProfile(newNumberPhone, CustomerInfo);
                }
                helper.IsNonEmptyString(this.state.taxID) && this.getCompanyProfile(this.state.taxID)
            })
        } else {
            if (!helper.IsEmptyObject(staffInfo)) {
                const {
                    CustomerAddress,
                    CustomerName,
                    CustomerPhone,
                    Gender
                } = staffInfo
                this.setState({
                    customerPhone: CustomerPhone,
                    gender: Gender,
                    customerName: CustomerName,
                    customerAddress: CustomerAddress,
                    contactPhone: CustomerPhone,
                    // contactName: CustomerName,
                    // contactAddress: CustomerAddress,
                }, () => {
                    if ((isApplyPhone && helper.IsEmptyObject(saleProgramInfo)) || existStaffInfo) {
                        const newNumberPhone = existStaffInfo ? staffInfo.CustomerPhone : phoneValidate
                        // this.getCustomerInfo(phoneValidate, CustomerInfo);
                        this.handleAPIGetCustomerProfile(newNumberPhone, CustomerInfo);
                    }
                })
            }
            else {
                this.setState({
                    customerPhone: phoneValidate || defaultInfo.customerPhone,
                    gender: defaultInfo.gender,
                    customerName: defaultInfo.customerName,
                    customerAddress: defaultInfo.customerAddress,
                    contactPhone: phoneValidate || defaultInfo.customerPhone,
                    // contactName: defaultInfo.customerName,
                    // contactAddress: defaultInfo.customerPhone,
                    isLockPhone: isApplyPhone,
                }, () => {
                    if ((isApplyPhone && helper.IsEmptyObject(saleProgramInfo)) || existStaffInfo) {
                        const newNumberPhone = existStaffInfo ? staffInfo.CustomerPhone : phoneValidate
                        // this.getCustomerInfo(phoneValidate, CustomerInfo);
                        this.handleAPIGetCustomerProfile(newNumberPhone, CustomerInfo);
                    }
                    else if (!!this.props.customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber) {
                        this.handleAPIGetCustomerProfile(this.props.customerConfirmPolicy[TYPE_PROFILE.CUSTOMER][0].phoneNumber, CustomerInfo);
                    }
                    else
                        /// đơn bỏ sung bán kèm
                        if (cus_OldVoucherConcernType == 4 && !!CustomerInfo.OldCustomerPhone && !infoDelivery) {
                            this.handleAPIGetCustomerProfile(CustomerInfo.OldCustomerPhone, CustomerInfo);
                        }
                })
            }
        }
    }

    checkGiftVoucher = () => {
        const { customerPhone } = this.state;
        const { cartPromotion } = this.props;
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidatePhone = regExpPhone.test(customerPhone);
        const giftVoucher = cartPromotion.find(ele => ele.invisibleByCustomerPhone);
        const isValidate = (!!giftVoucher && isValidatePhone);
        if (isValidate) {
            showBlockUI();
            this.props.actionShoppingCart.checkGiftVoucher({
                "customerPhone": customerPhone,
                "promotionGroups": cartPromotion,
                "saleOrderID": null
            }).then(isVisible => {
                hideBlockUI();
                this.setState({ isVisibleVoucher: isVisible });
            });
        }
        else {
            this.setState({ isVisibleVoucher: false });
        }
    }

    checkCouponExpired = () => {
        const { customerPhone } = this.state;
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidatePhone = regExpPhone.test(customerPhone);
        if (isValidatePhone) {
            actionShoppingCartCreator.checkCouponExpired({
                "customerPhone": customerPhone,
            }).then(number => {
                this.setState({ couponExpired: number });
            });
        }
    }

    getGiftVoucherCustomer = () => {
        const { customerConfirmPolicy, userInfo: { brandID }, PRIORITYGIFTCODES } = this.props;
        const customerId = customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId

        if (!!customerId) {
            this.setState({ isFetchingGiftVoucher: true })
            actionShoppingCartCreator.getGiftVoucherCustomer({
                "customerId": customerId,
                "queryType": 1,
                "applyBrands": [brandID]
            }).then(vouchers => {
                const sortVouchers = (vouchers) => {
                    return vouchers.sort((a, b) => {
                        // Kiểm tra voucher có thuộc config A không
                        const aInConfigA = PRIORITYGIFTCODES.includes(a.partnerGiftcode);
                        const bInConfigA = PRIORITYGIFTCODES.includes(b.partnerGiftcode);

                        // Ưu tiên voucher thuộc config A lên đầu
                        if (aInConfigA && !bInConfigA) return -1;
                        if (!aInConfigA && bInConfigA) return 1;

                        // Cùng nhóm thì sắp xếp theo expiredDate tăng dần (hết hạn sớm hơn lên trước)
                        return a.expiredDate - b.expiredDate;
                    });
                }
                this.voucherCustomerPhone.current = customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber
                this.setState({ vouchers: sortVouchers(vouchers), isFetchingGiftVoucher: false },
                    () => { vouchers.length > 0 && this.voucherSheetRef.current?.present() }
                );
            });
        }

    }

    updateCartInfo = (dataCart) => {
        const {
            ApplyPromotionToCustomerPhone: phoneValidate,
            CustomerInfo
        } = dataCart;
        const isApplyCoupon = helper.hasProperty(CustomerInfo, "ApplyPhoneNumberByDiscountCode");
        const isApplyPhone = !!phoneValidate;
        const isLockPhone = isApplyPhone || isApplyCoupon;
        this.setState({
            isLockPhone: isLockPhone,
            totalPrePaid: 0,
            termLoan: 0,
            isInsuranceFee: false,
            isInsuranceGood: false,
            goodInsuranceID: 0,
            paymentMonthly: 0,
        });
    }

    getPaymentMonthly = () => {
        const {
            totalPrePaid,
            termLoan,
            isInsuranceFee,
            isInsuranceGood,
            goodInsuranceID,
        } = this.state;
        const { dataShoppingCart } = this.props;
        const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
        const {
            MinPrepaid,
            MaxPrepaid,
        } = saleProgramInfo;
        const isValidateMin = MinPrepaid <= totalPrePaid;
        const isValidateMax = totalPrePaid <= MaxPrepaid;
        const isHasPrePaid = isValidateMin && isValidateMax;
        const isHasTermLoan = (termLoan > 0);
        const isValidate = isHasPrePaid && isHasTermLoan;
        if (isValidate) {
            const cartRequest = helper.deepCopy(dataShoppingCart);
            const newSaleProgramInfo = {
                ...saleProgramInfo,
                'TotalPrePaid': totalPrePaid,
                "TermLoan": termLoan,
                "IsSelectedLifeInsurance": isInsuranceFee,
                "IsSelectedGoodsInsurance": isInsuranceGood,
                "GoodsInsuranceID": goodInsuranceID,
            };
            cartRequest.SaleOrderDetails.forEach(saleOrder => {
                saleOrder.SaleProgramInfo = newSaleProgramInfo;
            });
            showBlockUI();
            this.props.actionShoppingCart.getPaymentMonthly(cartRequest)
                .then(value => {
                    hideBlockUI();
                    this.setState({ paymentMonthly: value });
                }).catch(msgError => {
                    Alert.alert(translate('common.notification_uppercase'), msgError,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: "cancel",
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_notify_try_again'),
                                style: "default",
                                onPress: this.getPaymentMonthly
                            }
                        ]
                    )
                });
        }
    }

    unlockInstockCart = (saleOrderDetailsRemove) => {
        const requestObjectArr = [];
        helper.getObjectArrUnlock(requestObjectArr, saleOrderDetailsRemove);
        saleOrderDetailsRemove.forEach(product => {
            const { saleSaleOrders, saleDeliverySaleOrders } = product;
            helper.getObjectArrUnlock(requestObjectArr, saleSaleOrders);
            helper.getObjectArrUnlock(requestObjectArr, saleDeliverySaleOrders);
        })
        this.props.actionShoppingCart.unlockInstockCart(requestObjectArr);
    }

    getPromoRequireInfors = ({ SaleOrderDetails }) => {
        let result = [];
        if (!helper.IsNonEmptyArray(SaleOrderDetails)) return [];

        SaleOrderDetails.forEach((mainProduct) => {
            if (mainProduct.PromoRequireInfors) {
                result = result.concat(mainProduct.PromoRequireInfors.map((ele, index) => ({
                    ...ele,
                    SaleOrderDetailID: mainProduct.SaleOrderDetailID,
                    ProductName: mainProduct.ProductName,
                    isDisplayName: index === 0,
                    isMainProduct: true
                })));
            }

            if (helper.IsNonEmptyArray(mainProduct.giftSaleOrders)) {
                mainProduct.giftSaleOrders.forEach(({ PromoRequireInfors, SaleOrderDetailID, ProductName }) => {
                    if (PromoRequireInfors) {
                        result = result.concat(PromoRequireInfors.map((ele, index) => ({
                            ...ele,
                            SaleOrderDetailID,
                            ProductName,
                            isDisplayName: index === 0,
                            isMainProduct: false
                        })));
                    }
                });
            }
        });

        return result;
    };
    calculateFeePlusMoneyBO = (FeeAndDepositBO, SHChangeTranferAmountFee) => {
        let feePlusMoneyBO = {};

        if (!helper.IsEmptyObject(FeeAndDepositBO) && helper.IsNonEmptyArray(FeeAndDepositBO?.cus_FeeBOList)) {
            let objFee = {
                BaseFeeMoneyAmount: 0,
                FeePlusMoneyAmount: 0,
                AdditionalFeeAmount: 0,
                FeeMinusMoneyAmount: 0,
                FeeMoney: 0,
                Rounding: 0
            };

            FeeAndDepositBO.cus_FeeBOList.forEach(item => {
                if (!helper.IsEmptyObject(item.cus_ForwarderFeeBO)) {
                    objFee = {
                        ...objFee,
                        BaseFeeMoneyAmount: objFee.BaseFeeMoneyAmount + item.cus_ForwarderFeeBO.BaseFeeMoneyAmount,
                        FeePlusMoneyAmount: objFee.FeePlusMoneyAmount + item.cus_ForwarderFeeBO.FeePlusMoneyAmount,
                        AdditionalFeeAmount: objFee.AdditionalFeeAmount + item.cus_ForwarderFeeBO.AdditionalFeeAmount,
                        FeeMinusMoneyAmount: objFee.FeeMinusMoneyAmount + item.cus_ForwarderFeeBO.FeeMinusMoneyAmount,
                        FeeMoney: objFee.FeeMoney + item.cus_ForwarderFeeBO.FeeMoney,
                        Rounding: objFee.Rounding + item.Rounding,
                    };
                }
            });

            feePlusMoneyBO = {
                ...FeeAndDepositBO.cus_FeeBOList[0]?.cus_ForwarderFeeBO,
                SHChangeTranferAmountFee,
                ...objFee
            };
        }

        return feePlusMoneyBO;
    }
    handleAddToCartWithAdjustPrice = () => {
        this.OTPSheetRef.current?.dismiss()
        const { tempSaleOrders, isHasSaleProgram } = this.state
        const { customerInfo = {}, cartRequest = {}, customerIDLoyalty = "" } = tempSaleOrders ?? {};
        const {
            IsAllowParticipationLoyalty,
            TotalPointLoyalty,
            IsSenOTPConfirmCustomer
        } = cartRequest;
        const isLoyalty = IsAllowParticipationLoyalty && !helper.IsNonEmptyString(customerIDLoyalty) && helper.IsNonEmptyString(customerInfo?.customerPhone) && TotalPointLoyalty > 0;
        this.handleSaleOrderFlow(
            tempSaleOrders,
            isHasSaleProgram,
            IsSenOTPConfirmCustomer,
            isLoyalty,
            customerIDLoyalty
        );
    }


    handleSaleOrderFlow = async (
        dataSaleOrder, isHasSaleProgram, IsSenOTPConfirmCustomer, isLoyalty, customerIDLoyalty
    ) => {
        if (isHasSaleProgram && IsSenOTPConfirmCustomer) {
            this.getDataInstallment(dataSaleOrder);
        } else if (isLoyalty) {
            this.onCheckLoyaltyPoint(dataSaleOrder);
        } else if (helper.IsNonEmptyString(customerIDLoyalty)) {
            this.addToSaleOrderCart(dataSaleOrder);
        } else {
            this.getDataMemberPoint(dataSaleOrder);
        }
    };
    handleAPICheckScreensProtector = async (dataSaleOrder) => {

        try {
            const {
                userInfo: { storeID, languageID, moduleID }, saleScenarioTypeID, dataShoppingCart } = this.props;
            const { IsSOScreenSticker, IsSOAdditionalPromotion } = dataShoppingCart;
            let isChecked = false;
            const isApply = !IsSOScreenSticker && !IsSOAdditionalPromotion
            if (isApply) {
                const body = {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "saleScenarioTypeID": saleScenarioTypeID,
                    "customerPhone": this.state.customerPhone
                }
                showBlockUI();
                isChecked = await actionShoppingCartCreator.checkNumberPhoneApplyVoucher(body)
            }
            return isChecked;
        } catch (error) {
            Alert.alert("", error || "Đã có lỗi xảy ra. Vui lòng thử lại!",
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI

                    },
                    {
                        text: translate('shoppingCart.btn_retry_uppercase'),
                        style: "default",
                        onPress: () => this.handleModifyCustomerrofile(dataSaleOrder)
                    }
                ]
            )
            return null
        }

    };
}

const mapStateToProps = function (state) {
    return {
        dataShoppingCart: state.shoppingCartReducer.dataShoppingCart,
        cartPromotion: state.shoppingCartReducer.cartPromotion,
        cartSalePromotion: state.shoppingCartReducer.cartSalePromotion,
        applyDetailIDs: state.shoppingCartReducer.applyDetailIDs,
        voucherPromotion: state.shoppingCartReducer.voucherPromotion,
        stateCartPromotion: state.shoppingCartReducer.stateCartPromotion,
        defaultCustomer: state.shoppingCartReducer.defaultCustomer,
        userInfo: state.userReducer,
        initScreen: state.saleOrderCartReducer.initScreen,
        pharmacy: state.pharmacyReducer,
        cartChange: state.shoppingCartReducer.cartChange,
        staffInfo: state.staffPromotionReducer.staffInfo,
        saleScenarioTypeID: state.specialSaleProgramReducer.saleScenarioTypeID,
        shouldCallPromotion: state.detailReducer.shouldCallPromotion,
        wowPoints: state.loyaltyReducer.wowPoints,
        suggestProducts: state.detailReducer.suggestProducts,
        customerConfirmPolicy: state.shoppingCartReducer.customerConfirmPolicy,
        numberPhoneCreateAtHome: state.detailReducer.numberPhoneCreateAtHome,
        PRIORITYGIFTCODES: state.appSettingReducer.PRIORITYGIFTCODES
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
        actionPharmacy: bindActionCreators(actionPharmacyCreator, dispatch),
        actionDetail: bindActionCreators(actionDetailCreator, dispatch),
        staffPromotionAction: bindActionCreators(staffPromotionActionCreator, dispatch),
        specialSaleProgramAction: bindActionCreators(specialSaleProgramActionCreator, dispatch),
        loyaltyAction: bindActionCreators(loyaltyActionCreator, dispatch),
        actionSale: bindActionCreators(saleActionCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(ShoppingCart);

const TotalAmount = ({ total }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bgF7EED6,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF,
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    text={translate('shoppingCart.cart_total_price')}
                    style={{
                        color: COLORS.txtFF6A00,
                        fontWeight: 'bold'
                    }}>
                    <MyText
                        text={translate('shoppingCart.not_rounded')}
                        style={{
                            color: COLORS.txt666666,
                            fontStyle: 'italic',
                            fontWeight: 'normal'
                        }}
                    />
                </MyText>
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    text={helper.convertNum(total)}
                    addSize={2}
                    style={{
                        color: COLORS.txtFF0000,
                        fontWeight: 'bold'
                    }}
                />
            </View>
        </View>
    );
}

const DepositAmount = ({ total, onShow }) => {
    const isShow = (total > 0);
    return (
        isShow &&
        <View style={{
            backgroundColor: COLORS.bgE4EBD5,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    text={translate('shoppingCart.total_customer_deposit')}
                    style={{
                        color: COLORS.txtFC3158,
                        fontWeight: 'bold',
                        textDecorationLine: 'underline'
                    }}
                    onPress={onShow}
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    text={helper.convertNum(total)}
                    addSize={2}
                    style={{
                        color: COLORS.txtFF6600,
                        fontWeight: 'bold'
                    }}
                />
            </View>
        </View>
    );
}

const FeeAmount = ({ total, onShow }) => {
    const isShow = (total > 0);
    return (
        isShow &&
        <View style={{
            backgroundColor: COLORS.bgE4EBD5,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    text={translate('shoppingCart.additional_fee')}
                    style={{
                        color: COLORS.txtFC3158,
                        fontWeight: 'bold',
                        textDecorationLine: 'underline'
                    }}
                    onPress={onShow}
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    text={helper.convertNum(total)}
                    addSize={2}
                    style={{
                        color: COLORS.txtFF6600,
                        fontWeight: 'bold'
                    }}
                />
            </View>
        </View>
    );
}

const PointLoyalty = ({ total, onShow }) => {
    const isShow = (total > 0);
    return (
        isShow &&
        <View style={{
            backgroundColor: COLORS.bgFFFFFF,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    text={translate('shoppingCart.provisional_total_membership_score')}
                    style={{
                        color: COLORS.txt2164F4,
                        fontWeight: 'bold',
                        textDecorationLine: 'underline'
                    }}
                    onPress={onShow}
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    text={helper.convertNum(total, false)}
                    addSize={2}
                    style={{
                        color: COLORS.txtFF00BF,
                        fontWeight: 'bold'
                    }}
                />
            </View>
        </View>
    );
}

const ButtonAdjust = ({
    onAdjustPrice,
    onCreatePrice,
    isBlue
}) => {
    return (
        <View style={{
            width: constants.width,
        }}>
            {/* {
                !isBlue &&
                <Button
                    text={translate('shoppingCart.btn_adjust_price')}
                    onPress={onAdjustPrice}
                    styleContainer={{
                        flexDirection: 'row',
                        width: constants.width,
                        height: 40,
                        backgroundColor: COLORS.btn5B9A68,
                    }}
                    styleText={{
                        color: COLORS.txtFFFFBC,
                        fontSize: 16,
                        marginRight: 8
                    }}
                    iconRight={{
                        iconSet: "FontAwesome",
                        name: "chevron-right",
                        size: 14,
                        color: COLORS.icFFFFBC
                    }}
                />
            } */}
            <Button
                text={translate('shoppingCart.btn_create_competitive_price')}
                onPress={onCreatePrice}
                styleContainer={{
                    flexDirection: 'row',
                    width: constants.width,
                    height: 40,
                    backgroundColor: COLORS.btn5482AB,
                    borderTopWidth: StyleSheet.hairlineWidth,
                    borderTopColor: COLORS.bdFFFFFF
                }}
                styleText={{
                    color: COLORS.txtFFFFBC,
                    fontSize: 16,
                    marginRight: 8
                }}
                iconRight={{
                    iconSet: "FontAwesome",
                    name: "chevron-right",
                    size: 14,
                    color: COLORS.icFFFFBC
                }}
            />
        </View>
    );
}


const checkApplyCoupon = (SaleOrderDetails) => {
    for (const { saleSaleOrders, AdjustPrice } of SaleOrderDetails) {
        if (AdjustPrice != 0) return false;
        for (const { AdjustPrice: saleAdjustPrice } of saleSaleOrders) {
            if (saleAdjustPrice != 0) return false;
        }
    }
    return true;
};



const getMsgRequirePromotion = (allPromotion, setKeyPromotionSelected) => {
    let isValidatePromotion = true;
    let msgRequirePromotion = "";
    let isWarningPromotion = false;
    allPromotion.forEach(groupPromotion => {
        const {
            promotionProducts,
            promotionGroupID,
            isRequired,
            promotionGroupName
        } = groupPromotion;
        const productSelected = promotionProducts.filter((product, index) => {
            const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
            const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
            return setKeyPromotionSelected.has(key);
        });
        if (productSelected.length == 0) {
            if (isRequired) {
                msgRequirePromotion += (
                    isValidatePromotion
                        ? `\t${promotionGroupName}`
                        : `\n\t${promotionGroupName}`
                );
                isValidatePromotion = false;
            }
            isWarningPromotion = true;
        }
    })
    return { isValidatePromotion, msgRequirePromotion, isWarningPromotion };
}

const checkRequireImeiSim = (SaleOrderDetails) => {
    let isRequire = false;

    const checkImeiSim = (saleProduct) => {
        const { IMEI, BrandIDOfSIM } = saleProduct;
        const isSIM = !!BrandIDOfSIM;
        if (isSIM && !IMEI) {
            isRequire = true;
        }
    };

    SaleOrderDetails.forEach(mainProduct => {
        const {
            giftSaleOrders,
            saleSaleOrders,
            giftDeliverySaleOrders,
            saleDeliverySaleOrders
        } = mainProduct;

        giftSaleOrders.forEach(checkImeiSim);
        saleSaleOrders.forEach(checkImeiSim);
        giftDeliverySaleOrders.forEach(checkImeiSim);
        saleDeliverySaleOrders.forEach(checkImeiSim);
    });

    return isRequire;
}


const RadioRelationShip = ({ type, onChangeType }) => {
    return (
        <View style={{
            width: constants.width,
            alignItems: "center",
        }}>
            <RadioRelation
                type={type}
                onChangeType={onChangeType}
            />
        </View>
    )
}

const getSaleProgramInfo = (dataShoppingCart) => {
    return dataShoppingCart?.SaleOrderDetails?.[0]?.SaleProgramInfo || {};
}



const RELATION = {
    "1": "(Đơn hàng của chính mình)",
    "0": "(Đơn hàng có quen biết với khách)",
    "-1": "(Đơn hàng của khách bên ngoài)"
}

const checkHadSim = (SaleOrderDetails, simBrand) => {
    if (!helper.IsNonEmptyArray(SaleOrderDetails)) return false; // If the array is empty, return false immediately
    for (const mainProduct of SaleOrderDetails) {
        if (mainProduct.ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && mainProduct.BrandIDOfSIM == simBrand) {
            return true; // If the main product matches the condition, return true immediately
        }
        // Check in all types of orders for the condition
        const allOrders = [...mainProduct.giftSaleOrders, ...mainProduct.saleSaleOrders, ...mainProduct.giftDeliverySaleOrders, ...mainProduct.saleDeliverySaleOrders];
        if (allOrders.some(order => order.ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && order.BrandIDOfSIM == simBrand)) {
            return true; // If any order matches the condition, return true immediately
        }
    }
    return false;
}


const handleEditDosage = (dataDosage) => {
    if (helper.IsNonEmptyArray(dataDosage)) {
        return dataDosage.some(
            ({
                MorningQuantity,
                NoonQuantity,
                AfternoonQuantity,
                EveningQuantity
            }) =>
                MorningQuantity + NoonQuantity + AfternoonQuantity + EveningQuantity === 0
        );
    }
    return false;
};

const styles = StyleSheet.create({
    fabMedicine: {
        backgroundColor: '#981c3d',
        opacity: 0.7,
        width: 54,
        height: 54,
        borderRadius: 27,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 15,
        marginVertical: 10,
        shadowColor: '#981c3d',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,

        elevation: 5,
    },
    scrollTopButton: {
        position: 'absolute',
        bottom: 120,
        right: 20,
        backgroundColor: '#2D8A4B',
        borderRadius: 30,
        width: 50,
        height: 50,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5
    },
    scrollTopText: {
        fontSize: 24,
        color: '#fff'
    },
    productImage: {
        width: 30,
        height: 50,
        resizeMode: 'contain'
    },
});

export const checkMaxQuantity = (dataShoppingCart) => {
    const { SaleOrderDetails, IsSOAnKhang } = dataShoppingCart;
    let msgWarning = "";
    if (IsSOAnKhang) {
        const mapData = {};
        SaleOrderDetails.forEach(product => {
            const { ProductID, ProductName, Quantity, QuantityUnitName } = product;
            const productID = ProductID.trim();
            if (helper.hasProperty(MAX_QUANTITY, productID)) {
                if (mapData[productID] > 0) {
                    mapData[productID] += Quantity;
                }
                else {
                    mapData[productID] = Quantity;
                }
                mapData[`${productID}_msgWarning`] = `Sản phẩm "${ProductName}" không được bán quá số lượng ${MAX_QUANTITY[productID]} ${QuantityUnitName}`;
            }
        })
        Object.keys(mapData).forEach(key => {
            if (mapData[key] > MAX_QUANTITY[key]) {
                if (msgWarning) {
                    msgWarning += `\n${mapData[`${key}_msgWarning`]}`;
                }
                else {
                    msgWarning = mapData[`${key}_msgWarning`];
                }
            }
        })
    }
    return msgWarning;
}

export const customerInfoValidation = (customerInfo) => {
    const { customerName, customerPhone, customerAddress } = customerInfo;
    const nameRegex = /^(?!.*(khachle)).*/;
    const freshCustomerName = helper.removeAccent(customerName)
        .toLowerCase()
        .replaceAll(" ", "");
    if (!helper.IsNonEmptyString(customerName)) {
        // Thông báo An Khang
        Alert.alert("", translate('shoppingCart.validation_customer_name'));
        return false;
    };
    if (!nameRegex.test(freshCustomerName)) {
        // Thông báo An Khang
        Alert.alert("", 'Tên khách hàng không hợp lệ (Khách lẻ).');
        return false;
    };
    if (!helper.IsNonEmptyString(customerPhone)) {
        Alert.alert("", translate('shoppingCart.validation_phone_number'));
        return false;
    };
    // if (customerAddress.length <= 3) {
    //     // Thông báo An Khang
    //     Alert.alert("", 'Địa chỉ khách hàng không đủ ký tự.');
    //     return false;
    // }
    // if (customerAddress.includes("xxx")) {
    //     // Thông báo An Khang
    //     Alert.alert("", 'Tên khách hàng không hợp lệ (xxx).');
    //     return false;
    // }
    // if (!helper.IsNonEmptyString(customerAddress)) {
    //     // Thông báo An Khang
    //     Alert.alert("", 'Vui lòng nhập địa chỉ khách hàng.');
    //     return false;
    // };
    return true;
}
export const checkMedicineControlSpecs = (products = []) => {
    const hasMedicineControlSpecs = (orders) => orders.some((order) => order.cus_IsMedicineControlSpecs);

    return products.some((product) => {
        return product.cus_IsMedicineControlSpecs ||
            hasMedicineControlSpecs(product.giftDeliverySaleOrders || []) ||
            hasMedicineControlSpecs(product.giftSaleOrders || []) ||
            hasMedicineControlSpecs(product.saleDeliverySaleOrders || []) ||
            hasMedicineControlSpecs(product.saleSaleOrders || []);
    });
};



const getOutputType = (SaleOrderDetails) => {
    if (!helper.IsNonEmptyArray(SaleOrderDetails)) return [];

    const outputTypeSet = new Set();

    SaleOrderDetails.forEach(mainProduct => {
        outputTypeSet.add(mainProduct.OutputTypeID);

        [...mainProduct.saleSaleOrders, ...mainProduct.saleDeliverySaleOrders].forEach(saleProduct => {
            outputTypeSet.add(saleProduct.OutputTypeID);
        });
    });

    return Array.from(outputTypeSet);
}


export const getGender = (gender) => {
    return gender == 1 ? 1 : (gender == 0 ? 0 : null);
}


const getTotalDiscount = (SaleOrderDetails) => {
    /// using reduce
    const INIT_VALUE = 0
    const totalDiscount = SaleOrderDetails?.reduce((accumulator, saleOrderDetail) => {
        const innerReduced = saleOrderDetail?.giftSaleOrders?.reduce((innerAccumulator, giftSaleOrder) => {
            if (giftSaleOrder?.IsRandomDiscount && !helper.IsEmptyObject(giftSaleOrder?.ExtensionProperty)) {
                return innerAccumulator + Number(giftSaleOrder.ExtensionProperty.DiscountValue);
            }
            return 0
        }, INIT_VALUE); // Initial value for inner reduce
        accumulator += innerReduced;
        return accumulator;
    }, INIT_VALUE); // Initial value for outer reduce
    return totalDiscount
}

const getFeeBO = (FeeAndDepositBO, SHChangeTranferAmountFee) => {
    let feePlusMoneyBO = {}
    if (!helper.IsEmptyObject(FeeAndDepositBO) && helper.IsNonEmptyArray(FeeAndDepositBO?.cus_FeeBOList)) {
        let objFee = {
            BaseFeeMoneyAmount: 0,
            FeePlusMoneyAmount: 0,
            AdditionalFeeAmount: 0,
            FeeMinusMoneyAmount: 0,
            FeeMoney: 0,
            Rounding: 0
        }
        FeeAndDepositBO.cus_FeeBOList.forEach(item => {
            if (!helper.IsEmptyObject(item.cus_ForwarderFeeBO)) {
                objFee = {
                    ...objFee,
                    BaseFeeMoneyAmount: objFee.BaseFeeMoneyAmount + item.cus_ForwarderFeeBO.BaseFeeMoneyAmount,
                    FeePlusMoneyAmount: objFee.FeePlusMoneyAmount + item.cus_ForwarderFeeBO.FeePlusMoneyAmount,
                    AdditionalFeeAmount: objFee.AdditionalFeeAmount + item.cus_ForwarderFeeBO.AdditionalFeeAmount,
                    FeeMinusMoneyAmount: objFee.FeeMinusMoneyAmount + item.cus_ForwarderFeeBO.FeeMinusMoneyAmount,
                    FeeMoney: objFee.FeeMoney + item.FeeMoney,
                    Rounding: objFee.Rounding + item.Rounding,
                }
            }
        });
        feePlusMoneyBO = {
            ...FeeAndDepositBO.cus_FeeBOList[0].cus_ForwarderFeeBO,
            "SHChangeTranferAmountFee": SHChangeTranferAmountFee,
            ...objFee
        }
    }
    return feePlusMoneyBO
}




export const checkApplyDiscount = (cartRequest, brandID) => {
    const eligibleProductIDs = helper.configScreenProtectorDiscount(brandID) || [];
    const hasUnmatchedProducts = cartRequest.SaleOrderDetails.some(
        ({ ProductID }) => !eligibleProductIDs.includes(String(ProductID))
    );
    return hasUnmatchedProducts;
};



