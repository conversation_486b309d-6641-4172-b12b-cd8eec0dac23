import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Icon, MyText } from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import PromotionHeader from "./PromotionHeader";
import ModalSearchSim from "./Modal/ModalSearchSim";
import { translate } from '@translate';
import { COLORS } from "@styles";
import { RandomDiscountPromotion } from '../../Detail/Promotion/component/RadioPromotion';

const GiftDiscount = ({
    giftPromotion,
    mainProduct,
    marginTop,
}) => {
    const {
        PromotionListGroupName,
        IsPercentDiscount,
        DiscountValue,
        DiscountMoneyVATSODetail,
    } = giftPromotion;
    const { Quantity } = mainProduct;
    return (
        <View style={{
            width: constants.width - 10,
            marginTop: marginTop,
        }}>
            <View style={{
                width: constants.width - 10,
                flexDirection: "row",
                paddingHorizontal: 10
            }}
            >
                <Icon
                    iconSet={"Ionicons"}
                    name={"checkmark"}
                    size={16}
                    color={COLORS.ic288AD6}
                    style={{ marginTop: 2 }}
                />
                <MyText style={{
                    color: COLORS.txt333333,
                    width: constants.width - 46,
                }}
                    text={PromotionListGroupName}
                />
            </View>

            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                width: constants.width - 10,
                paddingRight: 10,
                paddingLeft: 26
            }}>
                <MyText style={{
                    color: COLORS.txt333333,
                    fontWeight: "bold"
                }}
                    text={translate('common.discount')}>
                    <MyText style={{
                        color: COLORS.txtD0021B,
                        fontWeight: "normal"
                    }}
                        text={
                            IsPercentDiscount
                                ? `${DiscountValue}%`
                                : helper.convertNum(DiscountValue)
                        }
                    />
                </MyText>

                <MyText style={{
                    color: COLORS.txtD0021B
                }}
                    text={
                        IsPercentDiscount
                            ? `-${helper.convertNum(DiscountMoneyVATSODetail * Quantity)}`
                            : `-${helper.convertNum(DiscountValue * Quantity)}`
                    }
                />
            </View>
        </View>
    );
};

const GiftProduct = ({
    giftPromotion,
    marginTop,
    onSearchSim,
    onShowLot,
    isShowLot
}) => {
    const {
        ProductName,
        Quantity,
        QuantityUnitName,
        IMEI,
        BrandIDOfSIM
    } = giftPromotion;
    const isSIM = !!BrandIDOfSIM;
    return (
        <>
            <View style={{
                width: constants.width - 20,
                flexDirection: "row",
                paddingHorizontal: 10,
                marginTop: marginTop,
            }}>
                <Icon
                    iconSet={"Ionicons"}
                    name={"checkmark"}
                    size={16}
                    color={COLORS.ic288AD6}
                    style={{ marginTop: 2 }}
                />
                <MyText style={{
                    color: COLORS.txt333333,
                    width: constants.width - 100,
                }}
                    text={`${ProductName} (${translate('editSaleOrder.quantity_short')}: ${Quantity} ${QuantityUnitName})`}
                />
                {isShowLot && (
                    <TouchableOpacity onPress={onShowLot}>
                        <MyText
                            text={translate('pharmacy.lot_date')}
                            style={{
                                color: COLORS.txt0000FF,
                                textDecorationLine: 'underline'
                            }}
                        />
                    </TouchableOpacity>
                )}
            </View>
            {
                isSIM &&
                <View style={{
                    paddingRight: 10,
                    paddingLeft: 26,
                    marginTop: 4,
                    width: constants.width - 10,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}>
                    <MyText
                        style={{
                            color: COLORS.txt333333,
                        }}
                        text={`IMEI: ${IMEI}`}
                    />
                    <TouchableOpacity style={{
                        flexDirection: "row",
                        alignItems: "center"
                    }}
                        onPress={onSearchSim}
                    >
                        <Icon
                            iconSet={"MaterialIcons"}
                            name={"sim-card"}
                            color={COLORS.ic147EFB}
                            size={16}
                        />
                        <MyText
                            style={{
                                color: COLORS.txt808080,
                                color: COLORS.txt147EFB,
                                textDecorationLine: "underline"
                            }}
                            text={translate('editSaleOrder.type_IMEI_SIM')}
                        />
                    </TouchableOpacity>
                </View>
            }
        </>
    );
};

const PromotionGift = ({
    giftSaleOrders,
    mainProduct,
    title,
    onUpdateSim,
    actionShoppingCart,
    onShowLot
}) => {
    const [isShowDetail, setIsShowDetail] = useState(true);
    const [isVisibleSearchSim, setIsVisibleSearchSim] = useState(false);
    const [indexSim, setIndexSim] = useState(0);
    const [promotionTempData, setPromotionTempData] = useState({});
    const isNonEmty = (giftSaleOrders.length > 0);
    const onShowDetail = () => {
        setIsShowDetail(!isShowDetail);
    };

    const renderItemGift = (giftPromotion, index) => {
        const {
            PromotionID,
            ProductName,
            Quantity,
            IsPercentDiscount,
            SaleOrderDetailID,
            ProductID,
            cus_IsRequiredBatchNO,
            cus_AllowChageQuantity,
            MinRandomDiscountValue,
            MaxRandomDiscountValue,
            DiscountValue,
            IsRandomDiscount,
            PromotionListID
        } = giftPromotion;
        const showLot = cus_IsRequiredBatchNO;

        const keyExtractor = `${SaleOrderDetailID} Gift${index}`;
        const marginTop = (index != 0) ? 4 : 0;
        if (IsRandomDiscount && !DiscountValue) {
            return (
                <View style={{ width: constants.width - 10 }}>
                    <RandomDiscountPromotion
                        IconLeft={
                            <Icon
                                iconSet="Ionicons"
                                name="checkmark"
                                size={16}
                                color={COLORS.ic288AD6}
                                style={{ marginTop: 2 }}
                            />
                        }
                        min={MinRandomDiscountValue}
                        max={MaxRandomDiscountValue}
                    />
                </View>
            );
        }
        return (
            helper.IsValidateObject(IsPercentDiscount)
                ? <GiftDiscount
                    giftPromotion={giftPromotion}
                    mainProduct={mainProduct}
                    marginTop={marginTop}
                    key={keyExtractor}
                />
                : <GiftProduct
                    giftPromotion={giftPromotion}
                    mainProduct={mainProduct}
                    marginTop={marginTop}
                    key={keyExtractor}
                    onSearchSim={() => {
                        setPromotionTempData({
                            productID: ProductID,
                            promotionListId: PromotionListID,
                            promotionID: PromotionID,
                            index
                        });
                        setIsVisibleSearchSim(true);
                    }}
                    onShowLot={() => {
                        onShowLot(ProductName, Quantity, SaleOrderDetailID, cus_AllowChageQuantity);
                    }}
                    isShowLot={showLot}
                />
        );
    };

    return (
        isNonEmty &&
        <View style={[{
            width: constants.width,
            backgroundColor: COLORS.bgFFFFFF,
            paddingVertical: 4,
        }]}>
            <PromotionHeader
                title={title}
                onShow={onShowDetail}
                isShowDetail={isShowDetail}
            />
            {
                isShowDetail &&
                <View style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    paddingVertical: 10,
                    borderWidth: 1,
                    borderColor: COLORS.bdE4E4E4,
                    width: constants.width - 10,
                    alignSelf: "center",
                    borderRadius: 4
                }}>
                    {
                        giftSaleOrders.map((giftPromotion, index) => renderItemGift(giftPromotion, index))
                    }
                </View>
            }
            {
                isVisibleSearchSim &&
                <ModalSearchSim
                    isVisible={isVisibleSearchSim}
                    hideModal={() => {
                        setIsVisibleSearchSim(false);
                    }}
                    simInfo={giftSaleOrders[indexSim]}
                    updateSim={(promtion) => {
                        const newGiftSaleOrders = [...giftSaleOrders];
                        newGiftSaleOrders[indexSim] = promtion;
                        onUpdateSim(newGiftSaleOrders);
                        setIsVisibleSearchSim(false);
                    }}
                    actionShoppingCart={actionShoppingCart}
                    promotionTempData={promotionTempData}
                    setIndexSim={setIndexSim}
                />
            }
        </View>
    );
};

export default PromotionGift;