import React, { useState, useEffect } from 'react';
import {
    View,
    TouchableOpacity,
    Alert
} from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import { launchImageLibrary } from 'react-native-image-picker';
import {
    Icon,
    CaptureCamera,
    Button,
    showBlock<PERSON>,
    hideBlockUI,
    MyText,
    ImageCDN,
    NumberInput,
    Picker,
    BaseLoading
} from "@components";
import { constants, API_CONST, ENUM } from "@constants";
import { helper, dateHelper } from "@common";
import { getImageCDN, getPriceNearStore } from "../../../../ShoppingCart/action";
import { translate } from '@translate';
import { COLORS } from "@styles";
import { useDispatch, useSelector } from 'react-redux';
import { getImageSaleOrder } from '../../../../EditSaleOrder/action';

const { API_GET_IMAGE_CDN, API_GET_IMAGE_CDN_NEW } = API_CONST;
const { FILE_PATH: { ADJUST_PRICE } } = ENUM;

const AdjustProduct = ({ product, updateTotalAdjust }) => {
    const {
        ProductName,
        cus_SalePriceAfterProDeliveryVAT,
        AdjustPriceTypeID,
        AdjustPrice,
        UrlFilesAdjustPrice,
        AllowUseCreatePrice,
        MinSalePriceProductForCreatePrice,
        MaxSalePriceProductForCreatePrice,
        cus_CompanyCompetitorBOList,
        SaleProgramInfo,
        MessageCreatePrice,
        InventoryStatusID,
        OutputTypeID,
        RetailPriceVAT,
        ProductID
    } = product;
    const { storeID, moduleID, languageID } = useSelector((state) => state.userReducer);
    const dispatch = useDispatch()
    const [isCheck, setIsCheck] = useState(false);
    const [adjustValue, setAdjustValue] = useState(0);
    const [isVisible, setIsVisible] = useState(false);
    const [indexSelected, setIndexSelected] = useState(0);
    const [uriImages, setUriImages] = useState([]);
    const [uriFile, setUriFile] = useState("");
    const [competitor, setCompetitor] = useState({});
    const { dataShoppingCart } = useSelector(state => state.editSaleOrderReducer);
    const [isShouldLoad, setIsShouldLoad] = useState(false)
    const [state, setState] = useState({
        isFetching: false,
        isEmpty: false,
        description: "",
        isError: false,
    })
    const [lstImage, setLstImage] = useState([]);
    const checkHadImage = checkExistImage(UrlFilesAdjustPrice)

    const salePriceAdjust = cus_SalePriceAfterProDeliveryVAT - adjustValue;
    const disabled = !AllowUseCreatePrice;
    const minCreatePrice = helper.convertNum(MinSalePriceProductForCreatePrice, false);
    const maxCreatePrice = helper.convertNum(MaxSalePriceProductForCreatePrice, false);
    const placeholder = `[${minCreatePrice} - ${maxCreatePrice}]`;
    const companyCompetitor = cus_CompanyCompetitorBOList || [];
    const labelCreatePrice = !helper.IsEmptyObject(SaleProgramInfo) ? translate('editSaleOrder.discount_value') : (helper.configStoreCreatePrice(storeID) ? "Phiếu mua hàng hỗ trợ chiến giá" : translate('editSaleOrder.discount_value'))

    const didMount = () => {
        if (AdjustPriceTypeID != 1) {
            const isAdjust = (AdjustPrice != 0);
            const adjustedValue = isAdjust ? -AdjustPrice : 0;
            setIsCheck(isAdjust);
            product.isCheck = isAdjust;
            setAdjustValue(adjustedValue);
            const value = companyCompetitor.find(ele => ele.CompanyCompetitorID == product.CompanyCompetitorID);
            if (value) {
                setCompetitor(value);
            }
            if (product.isCheck && (!checkHadImage)) {
                getImage()
            }
            else if (!product.isCheck) {
                setIsShouldLoad(true)
            }
        }
    }

    useEffect(
        didMount,
        []
    )

    const openCamera = (index) => () => {
        setIsVisible(true);
        setIndexSelected(index);
    }

    const deleteImage = (index) => () => {
        const newUriImages = [...uriImages]
        newUriImages[index] = '';
        product.UrlFilesAdjustPrice[index] = '';
        setUriImages(newUriImages);
    }

    const closeCamera = () => {
        setIsVisible(false)
    }

    const takePicture = (photo) => {
        if (helper.hasProperty(photo, 'uri')) {
            helper.resizeImage(photo).then(({ path, uri, size, name }) => {
                setIsVisible(false);
                showBlockUI();
                const bodyFromData = helper.createFormData({ uri, type: 'image/jpg', name, path: ADJUST_PRICE });
                getImageCDN(bodyFromData)
                    .then((res) => {
                        const remoteURI =
                            API_CONST.API_GET_IMAGE_CDN + res[0];
                        let newUriImages = [...uriImages];
                        newUriImages[indexSelected] = remoteURI;
                        product.UrlFilesAdjustPrice[indexSelected] = remoteURI;
                        setUriImages(newUriImages);
                        hideBlockUI();
                    })
                    .catch((error) => {
                        hideBlockUI();
                        console.log('uploadPicture', error);
                    });
            }

            ).catch((error) => {
                hideBlockUI();
                console.log("resizeImage", error);
            });
        }
    }

    const onPickerPhoto = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                if (helper.hasProperty(response, 'uri')) {
                    helper.resizeImage(response).then(({ path, uri, size, name }) => {
                        setIsVisible(false);
                        showBlockUI();
                        const bodyFromData = helper.createFormData({ uri, type: 'image/jpg', name, path: ADJUST_PRICE });
                        getImageCDN(bodyFromData)
                            .then((res) => {
                                const remoteURI =
                                    API_CONST.API_GET_IMAGE_CDN + res[0];
                                let newUriImages = [...uriImages];
                                newUriImages[indexSelected] = remoteURI;
                                product.UrlFilesAdjustPrice[indexSelected] = remoteURI;
                                setUriImages(newUriImages);
                                hideBlockUI();
                            })
                            .catch((error) => {
                                hideBlockUI();
                                console.log('uploadPicture', error);
                            });
                    }).catch((error) => {
                        hideBlockUI();
                        console.log("resizeImage", error);
                    });
                }
            });
    }

    const getFromDataImage = () => {
        const fromData = new FormData();
        const mapIndex = [];
        uriImages.forEach((ele, index) => {
            if (!UrlFilesAdjustPrice[index] && !!ele) {
                const uri = ele
                const type = 'image/jpg'
                const name = `${dateHelper.getTimestamp()}_${index}.jpg`
                const path = "/EditAdjustPrice"

                fromData.append('file', { uri, type, name });
                fromData.append('path', path);
                fromData.append('isGenDate', "false");
                fromData.append('isGenName', "false");
                mapIndex.push(index);
            }
        })
        uploadPicture(fromData, mapIndex);
    }

    const uploadPicture = (fromData, mapIndex) => {
        showBlockUI();
        getImageCDN(fromData).then(cdnImages => {
            hideBlockUI();
            let newUriImages = [...uriImages];
            cdnImages.forEach((name, index) => {
                const uri = API_GET_IMAGE_CDN_NEW + name;
                const position = mapIndex[index];
                newUriImages[position] = uri;
                product.UrlFilesAdjustPrice[position] = uri;
            });
            setUriImages(newUriImages);
        }).catch(msgError => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        onPress: hideBlockUI,
                        style: "cancel"
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        onPress: () => uploadPicture(fromData, mapIndex),
                        style: "default"
                    },
                ],
                { cancelable: false },
            );
        })
    }

    const deleteFile = () => {
        product.UrlFilesAdjustPrice[3] = '';
        setUriFile("");
        setIndexSelected(-1);
    }

    const uploadFileAttach = (fromData) => {
        showBlockUI();
        getImageCDN(fromData).then(cdnFiles => {
            hideBlockUI();
            const uri = API_GET_IMAGE_CDN_NEW + cdnFiles[0];
            setUriFile(uri);
            product.UrlFilesAdjustPrice[3] = uri;
        }).catch(msgError => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        onPress: hideBlockUI,
                        style: "cancel"
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        onPress: () => uploadFileAttach(fromData),
                        style: "default"
                    },
                ],
                { cancelable: false },
            );
        })
    }

    const onPickerDocument = () => {
        DocumentPicker.pick({
            type: MIME_TYPE,
        }).then(fileInfo => {
            console.log("onPickerDocument success", fileInfo);
            const { isValidatesize, isValidateType } = checkFilePermission(fileInfo);
            if (!isValidatesize) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('editSaleOrder.attachment_must_less_than_3MB'),
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: () => { },
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: onPickerDocument,
                            style: "default"
                        }
                    ],
                    { cancelable: false },
                );
            }
            else if (!isValidateType) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    `${translate('editSaleOrder.file_format')} "${fileInfo.name}" ${translate('editSaleOrder.not_supported')}`,
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: () => { },
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: onPickerDocument,
                            style: "default"
                        }
                    ],
                    { cancelable: false },
                );
            }
            else {

                const bodyFromData = helper.createFormData({ uri: fileInfo.uri, type: fileInfo.type, name: fileInfo.name, path: ADJUST_PRICE });
                uploadFileAttach(bodyFromData);
            }
        }).catch(error => {
            console.log("onPickerDocument error", error);
        });
    }

    const onCheckProduct = () => {
        setIsCheck(!isCheck)
        product.isCheck = !isCheck;
    }

    const getImage = () => {
        setState({
            isFetching: true,
            isEmpty: false,
            description: "",
            isError: false,
        });
        const data = {
            saleOrderID: dataShoppingCart.SaleOrderID,
            saleOrderDetailID: product.SaleOrderDetailID,
            attachmentTypeList: "1"
        }
        dispatch(getImageSaleOrder(data)).then(res => {
            const listImage = res.map((item) => (item.UrlFile))
            setLstImage(listImage)
            product.UrlFilesAdjustPrice = listImage
            setIsShouldLoad(false)
            setState({
                isEmpty: false,
                description: "",
                isFetching: false,
                isError: false,
            })
        }).catch(msgError => {
            setState({
                description: msgError,
                isFetching: false,
                isEmpty: false,
                isError: true,
            })
        })
    }

    const handleChangeCompetitor = (competitor) => {
        const updatedCompetitor = { ...competitor };

        if (updatedCompetitor.cus_IsCheckCreatePriceNearestStore) {
            updatePriceFromNearestStore(updatedCompetitor);
        }
        else {
            processAdjustedPrice(updatedCompetitor);
        }
    };

    const updatePriceFromNearestStore = async (competitor) => {
        showBlockUI();
        try {
            const body = {
                storeID,
                productID: ProductID,
                outputTypeID: OutputTypeID,
                inventoryStatusID: InventoryStatusID,
                retailPriceVAT: RetailPriceVAT,
                loginStoreId: storeID,
                moduleID: moduleID,
                languageID: languageID,
            };

            const { MaxAdjustPrice, MinAdjustPrice } = await getPriceNearStore(body);

            competitor.cus_MaxSalePriceProductForCreatePrice = MaxAdjustPrice;
            competitor.cus_MinSalePriceProductForCreatePrice = MinAdjustPrice;

            processAdjustedPrice(competitor);

        }
        catch (error) {
            Alert.alert(translate('common.notification_uppercase'),
                error,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "default",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => {
                            updatePriceFromNearestStore(competitor)

                        }
                    }
                ]);
        }
        finally {
            hideBlockUI();
        }
    };

    const processAdjustedPrice = (competitor) => {
        const {
            cus_MaxSalePriceProductForCreatePrice,
            cus_MinSalePriceProductForCreatePrice,
            CompanyCompetitorID,
            cus_AdjustPriceByStoreID,
        } = competitor;
        Object.assign(product, {
            AdjustPrice: 0,
            MinSalePriceProductForCreatePrice: cus_MinSalePriceProductForCreatePrice,
            MaxSalePriceProductForCreatePrice: cus_MaxSalePriceProductForCreatePrice,
            CompanyCompetitorID,
            cus_AdjustPriceByStoreID,
        });
        setAdjustValue("");
        setCompetitor(competitor);
    };

    useEffect(() => {
        if (product.isCheck && isShouldLoad) {
            getImage()
        }
    }, [isCheck, isShouldLoad])


    return (
        <View style={{
            width: constants.width,
        }}>
            <FieldTitle
                title={ProductName}
                isCheck={isCheck}
                onCheck={onCheckProduct}
                disabled={disabled}
                MessageCreatePrice={MessageCreatePrice}

            />
            {
                isCheck &&
                <View style={{
                    width: constants.width,
                    paddingVertical: 8
                }}>
                    <View style={{
                        width: constants.width,
                        justifyContent: "center",
                        alignItems: "center",
                        flexDirection: "row",
                        paddingHorizontal: 10,
                        marginBottom: 10,
                    }}>
                        <MyText
                            text={translate('shoppingCart.competitive_price_opponent')}
                            style={{
                                color: COLORS.txt333333,
                                fontWeight: 'bold',
                                width: 140
                            }}
                        />
                        <Picker
                            style={{
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                height: 36,
                                borderRadius: 4,
                                borderWidth: 1,
                                borderColor: COLORS.bdE4E4E4,
                                width: constants.width - 160,
                                backgroundColor: COLORS.btnFFFFFF,
                                paddingHorizontal: 4
                            }}
                            label={"CompetitorName"}
                            value={"CompanyCompetitorID"}
                            defaultLabel={translate('editSaleOrder.picker_opponent')}
                            valueSelected={competitor.CompanyCompetitorID}
                            data={companyCompetitor}
                            onChange={handleChangeCompetitor}
                            onEmpty={() => {
                                Alert.alert("", translate('shoppingCart.no_information_adjust_price'));
                            }}
                        />
                    </View>
                    <View style={{
                        width: constants.width,
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        paddingHorizontal: 10,
                        marginBottom: 4
                    }}>
                        <MyText
                            text={translate('editSaleOrder.current_price')}
                            style={{
                                color: COLORS.txt444444,
                                fontWeight: "bold"
                            }} />
                        <MyText
                            text={helper.convertNum(cus_SalePriceAfterProDeliveryVAT)}
                            addSize={2}
                            style={{
                                color: COLORS.txtD0021B,
                            }} />
                    </View>

                    <View style={{
                        width: constants.width,
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        paddingHorizontal: 10,
                        marginBottom: 4
                    }}>
                        <MyText
                            text={labelCreatePrice}
                            style={{
                                color: COLORS.txt444444,
                                fontWeight: "bold",
                                marginRight: 2
                            }} />
                        <NumberInput
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                backgroundColor: COLORS.bgFFFFFF,
                                color: COLORS.txt333333,
                                height: 40,
                                justifyContent: "flex-end",
                                textAlign: 'right',
                                width: 130,
                                marginLeft: 2,
                                fontSize: 14
                            }}
                            placeholder={placeholder}
                            value={adjustValue}
                            maxValue={MaxSalePriceProductForCreatePrice}
                            onChangeText={(value) => {
                                setAdjustValue(value);
                                product.AdjustPrice = -value;
                                updateTotalAdjust();
                            }}
                        />
                    </View>
                    {
                        (!helper.IsEmptyObject(SaleProgramInfo) || !helper.configStoreCreatePrice(storeID)) && <View style={{
                            width: constants.width,
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "center",
                            paddingHorizontal: 10,
                            marginBottom: 8
                        }}>
                            <MyText
                                text={translate('editSaleOrder.adjusted_price')}
                                style={{
                                    color: COLORS.txt444444,
                                    fontWeight: "bold"
                                }} />
                            <MyText
                                text={helper.convertNum(salePriceAdjust)}
                                addSize={2}
                                style={{
                                    color: COLORS.txtD0021B,
                                }} />
                        </View>
                    }

                    <BaseLoading
                        isLoading={state.isFetching}
                        isEmpty={state.isEmpty}
                        textLoadingError={state.description}
                        isError={state.isError}
                        onPressTryAgains={getImage}
                        content={
                            !state.isFetching &&
                            <View style={{
                                width: constants.width,
                                paddingHorizontal: 10
                            }}>
                                <MyText
                                    text={translate('editSaleOrder.add_file')}
                                    style={{
                                        color: COLORS.txt444444,
                                        fontWeight: "bold",
                                        marginBottom: 2
                                    }}>
                                    <MyText
                                        text={translate('editSaleOrder.max_files')}
                                        style={{
                                            color: COLORS.txt288AD6,
                                            fontWeight: "normal",
                                        }} />
                                </MyText>

                                <View style={{
                                    flexDirection: "row",
                                    width: constants.width - 20,
                                }}>
                                    <ImageAdjust
                                        onCamera={openCamera(0)}
                                        onDelete={deleteImage(0)}
                                        onUpload={getFromDataImage}
                                        urlLocal={uriImages[0]}
                                        urlRemote={UrlFilesAdjustPrice[0]}
                                        title={translate('editSaleOrder.opponent_price')}
                                        isRequire={true}
                                    />
                                    <ImageAdjust
                                        onCamera={openCamera(1)}
                                        onDelete={deleteImage(1)}
                                        onUpload={getFromDataImage}
                                        urlLocal={uriImages[1]}
                                        urlRemote={UrlFilesAdjustPrice[1]}
                                        title={translate('editSaleOrder.MWG_price')}
                                        isRequire={true}
                                    />
                                    <ImageAdjust
                                        onCamera={openCamera(2)}
                                        onDelete={deleteImage(2)}
                                        onUpload={getFromDataImage}
                                        urlLocal={uriImages[2]}
                                        urlRemote={UrlFilesAdjustPrice[2]}
                                        title={translate('editSaleOrder.opponent_inventory')}
                                    />
                                </View>
                            </View>
                        }
                    />
                    <FileAdjust
                        onPicker={onPickerDocument}
                        onDelete={deleteFile}
                        urlLocal={uriFile}
                        urlRemote={UrlFilesAdjustPrice[3]}
                    />
                </View>
            }

            <CaptureCamera
                isVisibleCamera={isVisible}
                takePicture={takePicture}
                closeCamera={closeCamera}
                selectPicture={onPickerPhoto}
            />
        </View>
    );
}

export default AdjustProduct;



const FieldTitle = ({
    title,
    isCheck,
    onCheck,
    disabled,
    MessageCreatePrice
}) => {
    return (
        <TouchableOpacity
            style={{
                alignItems: "center",
                flexDirection: "row",
                width: constants.width,
                paddingHorizontal: 10,
                borderBottomWidth: 2,
                borderBottomColor: COLORS.bdFFFFFF,
                backgroundColor: disabled ? COLORS.btnEEEEEE : COLORS.btnFDF9E5,
                opacity: disabled ? 0.8 : 1,
                alignItems: "center",
                paddingVertical: 5
            }}
            onPress={onCheck}
            disabled={disabled}
        >
            <View>

                <Icon
                    iconSet={"Ionicons"}
                    name={
                        isCheck
                            ? "checkbox-outline"
                            : "square-outline"
                    }
                    color={COLORS.ic288AD6}
                    size={20}
                />
            </View>
            <View>
                <MyText
                    text={title}
                    style={{
                        width: constants.width - 45,
                        color: COLORS.txt288AD6,
                        fontWeight: "bold",
                        marginLeft: 5
                    }}
                    numberOfLines={2}
                />
                {!!MessageCreatePrice && <MyText
                    addSize={-2.5}
                    text={`(${MessageCreatePrice})`}
                    style={{
                        width: constants.width - 45,
                        color: COLORS.txtF50537,
                        fontWeight: 'bold',
                        marginLeft: 5
                    }}
                />}
            </View>

        </TouchableOpacity>
    );
}

const ImageAdjust = ({
    onCamera,
    onDelete,
    onUpload,
    urlLocal,
    urlRemote,
    title,
    isRequire
}) => {
    const uriImage = urlRemote ? urlRemote : urlLocal;
    const isNonImage = !uriImage;
    return (
        <View style={{
            flex: 1,
        }}>
            <MyText
                text={title}
                addSize={-1.5}
                style={{
                    color: COLORS.txt333333,
                    textAlign: "center",
                }}>
                {
                    isRequire &&
                    <MyText
                        text={"*"}
                        style={{
                            color: COLORS.txtFF0000,
                        }} />
                }
            </MyText>
            {
                isNonImage
                    ? <TouchableOpacity style={{
                        height: 120,
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: COLORS.btnF5F5F5,
                        marginHorizontal: 1
                    }}
                        onPress={onCamera}
                    >
                        <Icon
                            iconSet={"Ionicons"}
                            name={"ios-camera"}
                            color={COLORS.icFFB23F}
                            size={60}
                        />
                    </TouchableOpacity>
                    : <View style={{
                        flex: 1,
                        justifyContent: "flex-start",
                        alignItems: "center",
                        marginHorizontal: 1
                    }}>
                        <View style={{
                            width: 100,
                            height: 120,
                        }}>
                            <ImageCDN
                                style={{
                                    width: 100,
                                    height: 120,
                                }}
                                uri={uriImage}
                                resizeMode={"contain"}
                            />
                        </View>
                        {
                            !urlRemote &&
                            <Button
                                text={translate('editSaleOrder.btn_update')}
                                styleContainer={{
                                    width: 70,
                                    height: 30,
                                    backgroundColor: COLORS.btn288AD6,
                                    borderRadius: 4,
                                    marginVertical: 10
                                }}
                                styleText={{
                                    color: COLORS.txtFFFFFF,
                                    fontSize: 12
                                }}
                                onPress={onUpload}
                            />
                        }
                        <TouchableOpacity style={{
                            padding: 5,
                            justifyContent: "center",
                            alignItems: "center",
                            position: "absolute",
                            top: 0,
                            right: 0,
                            backgroundColor: COLORS.btnF2F2F2
                        }}
                            onPress={onDelete}
                        >
                            <MyText
                                text={translate('editSaleOrder.btn_delete')}
                                style={{
                                    color: COLORS.txtD0021B,
                                }} />
                        </TouchableOpacity>
                    </View>
            }
        </View>
    );
}

const FileAdjust = ({
    onPicker,
    onDelete,
    urlLocal,
    urlRemote
}) => {
    const uriFile = urlRemote ? urlRemote : urlLocal;
    const isNonFile = !uriFile;
    return (isNonFile
        ? <TouchableOpacity style={{
            flexDirection: 'row',
            borderRadius: 4,
            paddingVertical: 2,
            alignItems: "center",
            alignSelf: "flex-start",
            marginLeft: 10,
            marginTop: 8
        }}
            onPress={onPicker}
        >
            <Icon
                iconSet={"MaterialIcons"}
                name={"attachment"}
                size={25}
                color={COLORS.icEB1478}
            />
            <MyText
                text={translate('editSaleOrder.btn_add_file')}
                addSize={2}
                style={{
                    color: COLORS.txtEB1478,
                    marginLeft: 2,
                }} />
        </TouchableOpacity>
        : <View style={{
            width: constants.width,
            paddingHorizontal: 10,
            flexDirection: "row",
            marginTop: 8,
        }}>
            <TouchableOpacity style={{
                alignItems: "center",
                width: 40,
                height: 30,
            }}
                onPress={onDelete}
            >
                <Icon
                    iconSet={"MaterialIcons"}
                    name={"delete-forever"}
                    size={24}
                    color={COLORS.icEB1478}
                />
            </TouchableOpacity>
            <View style={{
                width: constants.width - 60
            }}>
                <MyText
                    text={uriFile}
                    style={{
                        color: COLORS.txt2529D8,
                    }} />
            </View>
        </View>
    );
}

const checkFilePermission = (fileInfo) => {
    const { type, size } = fileInfo;
    const sizeMb = size / (1024 * 1024);
    const isValidatesize = (sizeMb < 3);
    const isValidateType = TYPE_REDUCE.some(typeReduce => type.includes(typeReduce))
    return { isValidatesize, isValidateType };
}

const MIME_TYPE = [
    DocumentPicker.types.images,
    DocumentPicker.types.audio,
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/msword",
    "application/vnd.ms-excel"
];

const TYPE_REDUCE = [
    "image/",
    "audio/",
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/msword",
    "application/vnd.ms-excel"
]

const checkExistImage = (_images) => {
    let check = false
    _images?.forEach((dataImage, index) => {
        if (
            dataImage.startsWith('http') ||
            dataImage.startsWith('data:')
        ) {
            check = true
        }
    });
    return check
}