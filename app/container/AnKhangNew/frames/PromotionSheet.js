/* eslint-disable react/jsx-fragments */
import React, {
    useEffect,
    useState,
    forwardRef,
    useImperativeHandle,
    useRef
} from 'react';
import { View } from 'react-native';

import { MyText, BaseLoading } from '@components';
import { helper } from '@common';
import { translate, keys } from '@translate';
import { constants } from '@constants';

import { Sheet, CrossSellingPrice } from '../components';
import useTheme from '../useTheme';
import { PromotionContainer, PromotionGroupItem } from '../containers';

const { saleExpress, common } = keys;

const PromotionSheet = forwardRef(
    (
        {
            products,
            productId,
            onGoNext,
            getCurrentIndex,
            onCloseSheet,
            onInitPhonePromotion,
            onSelectPhonePromotion,
            ...props
        },
        ref
    ) => {
        const [promotions, setPromotions] = useState([]);
        const [isDiscountPromoSelected, setIsDiscountPromoSelected] = useState(
            {}
        );

        const sheetRef = useRef(null);

        const handleGoNext = () =>
            onGoNext({
                promotions,
                shouldCreateCart: productId.shouldCreateCart,
                isDiscountPromoSelected
            });

        useImperativeHandle(ref, () => ({
            snapToIndex: (index) => {
                sheetRef.current.snapToIndex(index);
            },
            close: () => {
                sheetRef.current.close();
            }
        }));

        useEffect(() => {
            if (productId.listId) {
                const { listId, prodId } = productId;
                const selectedList = products.find(
                    (prod) => prod.id === listId
                );
                if (selectedList) {
                    const selectedProduct =
                        selectedList.cus_ProductInfoBOList.find(
                            (prod) => prod.ProductID === prodId
                        );
                    if (selectedProduct) {
                        const promotion = {
                            listId,
                            prodId,
                            name: selectedProduct.ProductName,
                            data: helper.deepCopy(
                                selectedProduct.cus_ProductListGroupBOList ?? []
                            ),
                            inventoryStatusID:
                                selectedProduct.InventoryStatusID,
                            outputStoreID: selectedProduct.OutputStoreID,
                            outputTypeID: selectedProduct.OutputTypeID,
                            retailPriceVAT: selectedProduct.RetailPriceVAT
                        };
                        setIsDiscountPromoSelected(false);
                        setPromotions([promotion]);
                    }
                }
            } else {
                const newPromotions = [];
                products.forEach((product) => {
                    const selectedProduct =
                        product.cus_ProductInfoBOList.find(
                            (prod) => prod.IsSelected
                        ) ?? product.cus_ProductInfoBOList[0];
                    const promotion = {
                        listId: product.id,
                        prodId: selectedProduct.ProductID,
                        name: selectedProduct.ProductName,
                        data: helper.deepCopy(
                            selectedProduct.cus_ProductListGroupBOList ?? []
                        ),
                        inventoryStatusID: selectedProduct.InventoryStatusID,
                        outputStoreID: selectedProduct.OutputStoreID,
                        outputTypeID: selectedProduct.OutputTypeID,
                        retailPriceVAT: selectedProduct.RetailPriceVAT
                    };
                    selectedProduct.cus_ProductListGroupBOList &&
                        newPromotions.push(promotion);
                });

                setIsDiscountPromoSelected({});
                setPromotions(newPromotions);
            }
            onInitPhonePromotion();
        }, [productId, products]);

        const { length } = promotions;
        const isLoading = length === 0;
        return (
            <Sheet
                onGoNext={!isLoading ? handleGoNext : null}
                Header={
                    <Header title={translate(saleExpress.choose_promotion)} />
                }
                onChange={(index) => {
                    getCurrentIndex(index);
                    // onClose method => unexpected call multiple times
                    if (index === -1) {
                        onCloseSheet(promotions);
                        length > 0 && setPromotions([]);
                    }
                }}
                isLoading={isLoading}
                ref={sheetRef}
                {...props}>
                <BaseLoading
                    isLoading={isLoading}
                    content={
                        <PromotionContainer
                            promotions={promotions}
                            productId={productId}
                            discountPromoSelected={isDiscountPromoSelected}
                            onPromotionsChange={(newPromotions) =>
                                setPromotions(newPromotions)
                            }
                            onSelectPhonePromotion={onSelectPhonePromotion}
                            onDiscountPromoSelected={setIsDiscountPromoSelected}
                        />
                    }
                />
            </Sheet>
        );
    }
);

export const PromotionGroup = ({
    id,
    promotions,
    promoGroupId,
    isCartPromotion,
    onSelectPromotion,
    onVisibleSearchProduct,
    onVisibleSearchBarcode,
    onToggleCondition,
    onVisiblePhoneValidation,
    onClearPhoneNumber,
    onGetProducts,
    HeaderComponent,
    isPromotion = false,
    onGetPromotion = () => { },
    handleQuantityCSPromo = () => { },
    onSelectAllPromotion = () => { }
}) => {
    return (
        <View>
            {HeaderComponent}
            {promotions.map((promotion, index) => {
                // Bán kèm tổng đơn là PromotionID, bán kèm thường là id(UUID())
                const promotionID = id ?? promotion.PromotionID;
                return (
                    <PromotionGroupItem
                        id={promotionID}
                        index={index}
                        promotion={promotion}
                        promoGroupId={promoGroupId}
                        isCartPromotion={isCartPromotion}
                        onSelectPromotion={onSelectPromotion}
                        onVisibleSearchProduct={onVisibleSearchProduct}
                        onVisibleSearchBarcode={onVisibleSearchBarcode}
                        onToggleCondition={onToggleCondition}
                        onVisiblePhoneValidation={onVisiblePhoneValidation}
                        onClearPhoneNumber={onClearPhoneNumber}
                        onGetProducts={onGetProducts}
                        isPromotion={isPromotion}
                        onGetPromotion={onGetPromotion}
                        handleQuantityCSPromo={handleQuantityCSPromo}
                        onSelectAllPromotion={onSelectAllPromotion}
                    />
                );
            })}
        </View>
    );
};

export const Header = ({ title = 'TIÊU ĐỀ' }) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    return (
        <View
            style={{
                borderBottomWidth: 1,
                borderBottomColor: COLOR.darkGray,
                backgroundColor: COLOR.white,
                width: constants.width
            }}>
            <MyText
                selectable={false}
                style={{
                    fontSize: 15,
                    textAlign: 'center',
                    paddingBottom: 8,
                    fontWeight: '500',
                    marginHorizontal: 8
                }}
                numberOfLines={2}
                text={title}
            />
        </View>
    );
};

export const PromotionItem = ({ info }) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    const {
        name,
        inStockQuantity,
        isPercentDiscount,
        discountValue,
        isDiscount,
        quantity,
        warning,
        promotionPrice,
        salePrice
    } = info;
    return isDiscount ? (
        <View>
            <MyText
                style={{
                    fontSize: 12
                }}
                text={translate(common.discount)}>
                <MyText
                    style={{
                        fontSize: 12
                    }}
                    text={
                        !isPercentDiscount
                            ? helper.convertNum(discountValue)
                            : `${discountValue}%`
                    }
                />
            </MyText>
        </View>
    ) : (
        <View
            style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                flex: 1
            }}>
            <View style={{ flex: 1 }}>
                <MyText
                    style={{
                        marginRight: 10,
                        fontSize: 12
                    }}
                    text={name}
                />
                {!!warning && (
                    <MyText
                        text={warning}
                        style={{
                            color: COLOR.PRIMARY_700,
                            fontStyle: 'italic'
                        }}
                        addSize={-2}
                    />
                )}
                {salePrice !== 0 && (
                    <CrossSellingPrice
                        promotionPrice={promotionPrice}
                        salePrice={salePrice}
                    />
                )}
            </View>
            <View>
                <MyText
                    style={{ textAlign: 'right', fontSize: 12 }}
                    text={translate(saleExpress.quantity_acronym, { quantity })}
                />
                <MyText
                    style={{
                        textAlign: 'right',
                        fontSize: 12,
                        fontWeight: 'bold',
                        color: COLOR.primary
                    }}
                    text={translate(saleExpress.in_stock_quantity_with_params, {
                        quantity: inStockQuantity
                    })}
                />
            </View>
        </View>
    );
};

export default PromotionSheet;
