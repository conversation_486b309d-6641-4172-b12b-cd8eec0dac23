import { View, Modal, TouchableOpacity } from 'react-native';
import React, { useEffect, useState } from 'react';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import LottieView from 'lottie-react-native';

import { MyText, TitleInput } from '@components';
import { COLORS } from '@styles';
import { material_wave_loading } from '@animation';
import { constants, CONFIG } from '@constants';
import { translate, keys } from '@translate';
import { dateHelper } from '@common';

import { TYPE_ID, PERMISSION_TYPE } from '../constants';
import Button from './Button';
import { useProvisionalCarts, usePermission } from '../hooks';
import useTheme from '../useTheme';
import { Icon } from '../../../components';
import { useSelector } from 'react-redux';
import { getComboTypeByPosition, TYPE_COMBO } from '../index';

const { saleExpress, common } = keys;

const ModalConfirm = ({ visible, onClose, onSubmit, isModalLoading }) => {
    const [type, setType] = useState(null);
    const [isUpload, setIsUpload] = useState(false);
    const [cartName, setCartName] = useState('');
    const [time, setTime] = useState('');
    const { orderNumber, expiredTime } = useProvisionalCarts();
    const hasDeclarationPermission = usePermission(
        PERMISSION_TYPE.DECLARE_COMBO
    );
    const COLOR = useTheme().colors;

    const { positionID } =
        useSelector((state) => state.userReducer);
    const comboType = getComboTypeByPosition(positionID)


    const title = !!type
        ? translate(saleExpress.title_combo)
        : translate(saleExpress.title_provisional_cart_list);
    const placeholder = `${title} ${orderNumber}`;

    const handleSubmitCart = () => {
        onSubmit({
            name: cartName || placeholder,
            time: Date.now(),
            expiredTimeDev: time ? Date.now() + time * 1000 : 0,
            typeId: !!type ? TYPE_ID.COMBO : TYPE_ID.PROVISIONAL,
            category: -1,
            isUpload: !!type,
            comboType: type
        });
    };
    const resetState = () => {
        setTime('');
        setType(false);
        setIsUpload(false);
        setCartName('');
    };

    useEffect(() => {
        if (!type) {
            setIsUpload(false);
        }
    }, [type]);

    useEffect(() => {
        if (!visible) {
            resetState();
        }
    }, [visible]);

    return (
        // <TouchableWithoutFeedback
        //     onPress={() => {
        //         onClose();
        //         Keyboard.dismiss();
        //     }}>
        <View
            style={{
                justifyContent: 'center',
                alignItems: 'center'
            }}>
            <Modal animationType="fade" transparent visible={visible}>
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: COLORS.bg0000004
                    }}>
                    <View
                        style={{
                            width: constants.width - 20,
                            backgroundColor: COLORS.bgFFFFFF,
                            paddingVertical: 10,
                            paddingHorizontal: 20,
                            borderRadius: 5
                        }}>
                        <MyText
                            style={{
                                color: COLOR.lightBlack,
                                fontSize: 22,
                                fontWeight: '700',
                                textAlign: 'center'
                            }}
                            text={title}
                        />
                        {/* {!CONFIG.isPRODUCTION && (
                            <MyText
                                style={{
                                    color: COLOR.lightBlack,
                                    fontSize: 16,
                                    fontWeight: '700',
                                    textAlign: 'center'
                                }}
                                text={dateHelper.formatDateFULL(
                                    new Date(expiredTime)
                                )}
                            />
                        )} */}

                        <TitleInput
                            title="Tên giỏ hàng:"
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                                backgroundColor: COLORS.bgFFFFFF
                            }}
                            placeholder={placeholder}
                            onChangeText={setCartName}
                            value={cartName}
                            returnKeyType="done"
                            blurOnSubmit
                            width={constants.width - 60}
                            height={40}
                            clearText={() => {
                                setCartName('');
                            }}
                            editable
                            maxLength={30}
                        />
                        {/* {!CONFIG.isPRODUCTION && (
                            <TitleInput
                                title="Thời gian hết hạn (giây):"
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    paddingVertical: 8,
                                    backgroundColor: COLORS.bgFFFFFF
                                }}
                                placeholder="Đơn vị giây (Vd: 3000)"
                                onChangeText={setTime}
                                value={time}
                                keyboardType="number-pad"
                                returnKeyType="done"
                                blurOnSubmit
                                width={constants.width - 60}
                                height={40}
                                clearText={() => {
                                    setCartName('');
                                }}
                                editable
                            />
                        )} */}
                        <Checkbox
                            onToggle={setType}
                            label="Combo nhân viên"
                            value={TYPE_COMBO.BY_SELF}
                            currentValue={type}
                        />
                        {
                            comboType == TYPE_COMBO.STORE &&
                            <Checkbox
                                onToggle={setType}
                                label="Combo siêu thị"
                                value={TYPE_COMBO.STORE}
                                currentValue={type}
                            />
                        }


                        {/* {hasDeclarationPermission && type && (
                            <Checkbox
                                isChecked={isUpload}
                                onToggle={setIsUpload}
                                label="Tải lên hệ thống"
                            />
                        )} */}
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'center',
                                marginTop: 5
                            }}>
                            <Button
                                style={{ marginHorizontal: 5, width: 96 }}
                                color={COLOR.primary}
                                onPress={onClose}
                                text={translate(common.btn_cancel)}
                                outline
                            />
                            <Button
                                style={{ marginHorizontal: 5, width: 96 }}
                                color={COLOR.primary}
                                onPress={handleSubmitCart}
                                text={translate(common.btn_confirm)}
                                outline={false}
                            />
                        </View>
                    </View>
                </View>
                <Indicator visible={isModalLoading} />
            </Modal>
        </View>
        // </TouchableWithoutFeedback>
    );
};

const Checkbox = ({ value, currentValue, onToggle, label }) => {
    const COLOR = useTheme().colors;
    const isChecked = value === currentValue;

    return (

        <TouchableOpacity
            style={{
                flexDirection: 'row',
                justifyContent: 'flex-start',
                alignItems: 'center',

            }}
            activeOpacity={0.8}
            onPress={() => onToggle(isChecked ? null : value)}
        >
            <Icon
                iconSet={'Ionicons'}
                name={isChecked ? 'checkbox' : 'square-outline'}
                color={COLOR.primary}
                size={20}
            />
            <MyText
                style={{
                    padding: 5
                }}
                text={label}
            />
        </TouchableOpacity>
    );
};

const Indicator = ({ visible }) => (
    <View
        // eslint-disable-next-line react-native/no-color-literals
        style={{
            flex: 1,
            backgroundColor: '#00000033',
            zIndex: visible ? 999999 : -999999,
            position: 'absolute',
            left: 0,
            top: 0,
            width: visible ? '100%' : 0,
            height: visible ? constants.height : 0
        }}>
        {!visible ? null : (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                <LottieView
                    autoPlay
                    source={material_wave_loading}
                    style={{
                        height: 100,
                        width: 100
                    }}
                />
            </View>
        )}
    </View>
);
export default ModalConfirm;

