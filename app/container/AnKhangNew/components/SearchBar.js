import React from 'react';
import {
    View,
    TouchableOpacity,
    TextInput,
    Keyboard,
    StyleSheet
} from 'react-native';
import { Icon } from '@components';
import { COLORS } from '@styles';
import { COLOR } from '../constants';
import { styles } from '../styles';

const SearchBar = ({
    value,
    onChangeText,
    onSearch,
    setShowListSearch,
    onPressBarcode,
    onPressScan,
    isScanEyoyo,
    inputRef,
    ...props
}) => (
    <View style={{ flexDirection: 'row' }}>
        <TouchableOpacity
            style={[
                {
                    alignItems: 'center',
                    height: 38,
                    justifyContent: 'center',
                    paddingHorizontal: 10,
                    borderWidth: StyleSheet.hairlineWidth,
                    borderColor: isScanEyoyo
                        ? COLOR.PRIMARY_700
                        : COLOR.lightBlack,
                    marginRight: 5,
                    borderRadius: 5,
                    backgroundColor: isScanEyoyo ? '#e3f2fd' : COLOR.white
                },
                isScanEyoyo && {
                    elevation: 5,
                    shadowColor: COLOR.PRIMARY_700,
                    shadowOffset: {
                        width: 0,
                        height: 2
                    },
                    shadowOpacity: 0.25,
                    shadowRadius: 3.84
                }
            ]}
            onPress={onPressScan}>
            <Icon
                iconSet="MaterialCommunityIcons"
                name="magnify-scan"
                color={isScanEyoyo ? COLOR.PRIMARY_700 : COLOR.lightBlack}
                size={20}
            />
        </TouchableOpacity>
        <View
            style={[
                styles.shadowBox,
                {
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: COLORS.bdE4E4E4,
                    height: 38,
                    borderRadius: 5,
                    backgroundColor: COLORS.bgFFFFFF,
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    alignSelf: 'center',
                    flex: 1
                }
            ]}>
            <TextInput
                {...props}
                ref={inputRef}
                value={value}
                onChangeText={onChangeText}
                placeholderTextColor={COLORS.txt808080}
                onSubmitEditing={() => {
                    Keyboard.dismiss();
                    onSearch();
                }}
                style={{
                    flex: 1,
                    height: 36,
                    paddingLeft: 20,
                    fontSize: 12.5
                }}
                returnKeyType="search"
            />
            {value != null && value.length > 0 && (
                <TouchableOpacity
                    style={styles.iconRightTextInput}
                    onPress={() => {
                        Keyboard.dismiss();
                        onChangeText('');
                        setShowListSearch(false);
                    }}>
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        name="close"
                        color={COLORS.bg8E8E93}
                        size={20}
                    />
                </TouchableOpacity>
            )}
            {!value && onPressBarcode && (
                <TouchableOpacity
                    style={styles.iconRightTextInput}
                    onPress={onPressBarcode}>
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        name="barcode-scan"
                        color={COLOR.lightBlack}
                        size={20}
                    />
                </TouchableOpacity>
            )}
        </View>
    </View>
);

export default SearchBar;
