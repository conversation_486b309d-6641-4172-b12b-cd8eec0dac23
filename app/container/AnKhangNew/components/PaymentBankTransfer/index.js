import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Alert, TouchableOpacity, Image } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { COLORS } from '@styles';
import { helper } from '@common';
import {
    hideBlockUI,
    showBlockUI,
    ViewHTML,
    MyText,
    Icon,
    NumberInput
} from '@components';
import { translate } from '@translate';
import { constants } from '@constants';
import { getContentBase64View } from '../../../SaleOrderManager/action';
import { getTransactionTranfer } from '../../../SaleOrderPayment/action';
import { ImageURI } from '../../../../components';
import AlertMessage from '../AlertMessage';

const PaymentBankTransfer = ({
    payableAmount,
    saleOrderID,
    colorPrimary = "#5BB180",
    colorSecondary = COLORS.bg000000,
    colorTertiary = COLORS.ic147EFB,
    defaultShow = false,
    onReloadSOPayment,
    isCheck,
    isShowFunctionReloadSO = true,
    handleAPIBankTransfer = () => { }
}) => {
    const [htmlData, setHtmlData] = useState('');
    const [isVisible, setIsVisible] = useState(false);
    const [isShow, setIsShow] = useState(defaultShow);
    const [amount, setAmount] = useState(0);
    const [stateTransfer, setStateTransfer] = useState({
        visible: false,
        title: '',
        filePath: ''
    })
    const preAmount = useRef(0);
    const intervalId = useRef(-1)
    const dispatch = useDispatch();
    const hasHtmlData = helper.IsNonEmptyString(htmlData);
    const { PM_PreOrder_SaleOrderTypeList } = useSelector((state) => state.appSettingReducer)
    const { dataSO } = useSelector((state) => state.saleOrderPaymentReducer)
    const isPre = `,${PM_PreOrder_SaleOrderTypeList},`.includes(`,${dataSO.SaleOrderTypeID?.toString()},`);
    const handleApiGetContentHtmlView = (id) => {
        showBlockUI();
        return dispatch(
            getContentBase64View({
                reportContent: 'BankAccountContent',
                saleOrderID: id,
                paymentAmount: amount
            })
        )
            .then((html) => {
                hideBlockUI();
                preAmount.current = amount;
                return html;
            })
            .catch((msgError) => {
                hideBlockUI();
                Alert.alert(translate('common.notification'), msgError);
                return '';
            });
    };
    const handleBankTransfer = async () => {
        handleAPIBankTransfer(amount)
        // if (hasHtmlData && amount == preAmount.current) {
        //     setIsVisible(true);
        // } else {
        //     const html = await handleApiGetContentHtmlView(saleOrderID);
        //     if (html) {
        //         setHtmlData(html);
        //         setIsVisible(true);
        //     }
        // }
    };
    const intervalFunction = async () => {
        getTransactionTranfer(saleOrderID).then(() => {
            clearInterval(intervalId.current);
            setStateTransfer({ visible: true, title: "Giao dịch thành công ", filePath: require('../../../../../assets/transaction.png') })
        }).catch((msgError) => {
            clearInterval(intervalId.current);
            setStateTransfer({ visible: true, title: msgError, filePath: require('../../../../../assets/error.png') })
        })
    }
    useEffect(() => {
        if (payableAmount >= 0) {
            setAmount(payableAmount);
        }
    }, [payableAmount])
    return (
        <View
            style={{
                backgroundColor: COLORS.bgF5F5F5,
                borderTopWidth: StyleSheet.hairlineWidth,
                borderTopColor: COLORS.bdFFFFFF
            }}>
            <View
                style={{
                    paddingHorizontal: 10,
                    backgroundColor: '#F5F5F5',
                    flexDirection: 'row',
                    alignItems: 'center',
                    height: 40,
                    justifyContent: 'space-between'
                }}
                activeOpacity={0.8}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}>
                    <TouchableOpacity
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}
                        onPress={() => setIsShow(!isShow)}>
                        <MyText
                            text={translate(
                                'saleOrderManager.view_transfer_information'
                            )}
                            style={{
                                color: colorSecondary,
                                fontWeight: 'bold'
                            }}
                        />
                        <Icon
                            iconSet="Ionicons"
                            name={isShow ? 'chevron-up' : 'chevron-down'}
                            size={22}
                            color={colorTertiary}
                        />
                    </TouchableOpacity>
                </View>
                {(hasHtmlData || isCheck) && !isPre && isShowFunctionReloadSO && (
                    <View>
                        <TouchableOpacity
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'flex-end'
                            }}
                            onPress={onReloadSOPayment}>
                            <Icon
                                iconSet="MaterialCommunityIcons"
                                name="reload"
                                size={18}
                                color={COLORS.ic147EFB}
                            />
                            <MyText
                                text={translate(
                                    'saleOrderManager.transfer_validation'
                                )}
                                style={{
                                    color: COLORS.txt147EFB,
                                    textAlign: 'right',
                                    fontWeight: 'bold',
                                    marginLeft: 4
                                }}
                            />
                        </TouchableOpacity>
                    </View>
                )}
            </View>
            {isShow && (
                <View style={{ alignItems: 'center' }}>
                    <View
                        style={{
                            backgroundColor: 'white',
                            borderRadius: 8,
                            paddingBottom: 15,
                            paddingTop: 15,
                            paddingHorizontal: 25,
                            marginVertical: 10,
                            shadowColor: '#171717',
                            shadowOffset: { width: -2, height: 4 },
                            shadowOpacity: 0.2,
                            shadowRadius: 3,
                            width: constants.width - 20
                        }}>
                        <View style={{ paddingBottom: 15 }}>
                            <InputMoney
                                title="Nhập số tiền khách muốn thanh toán"
                                value={amount}
                                onChange={(value) => {
                                    setAmount(value);
                                }}
                                maxValue={payableAmount}
                            />
                        </View>

                        <View
                            style={{
                                alignItems: 'center'
                            }}>
                            <TouchableOpacity
                                onPress={handleBankTransfer}
                                style={{
                                    backgroundColor: 'white',
                                    height: 30,
                                    width: constants.width / 2,
                                    flexDirection: 'row',
                                    borderColor: colorPrimary,
                                    borderWidth: 0.5,
                                    borderRadius: 5,
                                    alignItems: 'center',
                                    shadowColor: colorPrimary,
                                    shadowOffset: { width: 0.5, height: 0.5 },
                                    shadowOpacity: 0.3,
                                    shadowRadius: 3,
                                    elevation: 1
                                }}>
                                <View style={{ alignItems: 'center', flex: 1 }}>
                                    <MyText
                                        style={{
                                            color: colorPrimary,
                                            fontSize: 13,
                                            fontWeight: '500'
                                        }}
                                        text="Xem thông tin chuyển khoản"
                                    />
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            )}

            <ViewHTML
                isVisible={isVisible}
                source={htmlData}
                hideModal={() => {
                    setIsVisible(false);
                    if (intervalId.current > 0) {
                        clearInterval(intervalId.current)
                    }
                }}
                title="Thông Tin Chuyển Khoản"
            >
                <AlertMessage
                    title={"Chi tiết giao dịch"}
                    visible={stateTransfer.visible}
                    onClose={() => {
                        setIsVisible(false);
                        setStateTransfer({ visible: false, title: "", filePath: '' })
                    }}
                    message={stateTransfer.title}
                    style={{ justifyContent: "space-evenly", flexDirection: 'row' }}
                    styleText={{ color: 'red', fontWeight: "bold" }}
                    buttons={[]}
                    isShowButton
                    maxHeight='100%'
                >
                    <View style={{
                        alignItems: 'center',
                        flex: 1,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginVertical: 4
                    }}>
                        <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
                            <Image
                                style={{
                                    width: 100,
                                    height: 100,
                                }}
                                source={stateTransfer.filePath}
                            />



                        </View>


                    </View>
                </AlertMessage>
            </ViewHTML>

        </View>
    );
};

export default PaymentBankTransfer;

const InputMoney = ({ title, value, onChange, maxValue }) => {
    return (
        <View
            style={{
                width: constants.width - 80
            }}>
            <MyText
                text={title}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: '500',
                    paddingBottom: 2
                }}
                addSize={-1}
            />
            <NumberInput
                style={{
                    height: 40,
                    backgroundColor: COLORS.bgFFFFFF,
                    borderWidth: 1,
                    borderRadius: 4,
                    borderColor: COLORS.bdCCCCCC,
                    paddingHorizontal: 10
                }}
                placeholder="0"
                value={value}
                onChangeText={onChange}
                maxValue={maxValue}
            />
        </View>
    );
};
