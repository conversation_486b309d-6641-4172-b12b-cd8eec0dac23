
import { store } from '../../../../app/store';
import * as locationAction from '../../Location/action';

const getProvinceNewMapping = async (provinceOldID) => {
    const payload = {
        mappingType: "PROVINCE",
        keyword: provinceOldID,
    };
    const responseAction = await store.dispatch(locationAction.getAddressMapping(payload));
    const provinceID = responseAction?.[0]?.ProvinceIdNew;
    const provinceName = responseAction?.[0]?.ProvinceNameNew;
    console.log(responseAction, "ProvinceMerge");

    return { provinceID, provinceName };
};

const handleGetDistrictMerge = async (provinceID) => {
    const responseAction = await store.dispatch(locationAction.getDataDistrictMerge(provinceID));
    const districtID = responseAction?.[0]?.districtID;
    const districtName = responseAction?.[0]?.districtName;
    console.log(responseAction, "DistrictMerge");

    return { districtID, districtName };
};

const getWardNewMapping = async (wardOldID) => {
    const data = {
        mappingType: "WARD",
        keyword: wardOldID,
    };
    const responseAction = await store.dispatch(locationAction.getAddressMapping(data));
    console.log(responseAction, "WardMerge");
    const wardID = responseAction?.[0]?.WardIdNew;
    const wardName = responseAction?.[0]?.WardNameNew;

    return { wardID, wardName };
};

const getProvinceNewByID = async (provinceIDNew) => {
    const responseAction = await store.dispatch(locationAction.getDataDistrictMerge(provinceIDNew));
    const provinceID = responseAction?.[0]?.provinceID;
    const provinceName = responseAction?.[0]?.provinceName;
    const districtID = responseAction?.[0]?.districtID;
    const districtName = responseAction?.[0]?.districtName;
    console.log(responseAction, provinceID, provinceName, districtID, districtName, "ProvinceMerge");

    return { provinceID, provinceName, districtID, districtName };
}

const getWardNewByID = async (provinceIDNew, districtIDNew, inputWardOcr) => {
    const responseAction = await store.dispatch(locationAction.getDataWardNew(provinceIDNew, districtIDNew));
    const filterWard = responseAction.filter(item => item.wardID == inputWardOcr);
    console.log(filterWard, "WardMerge")
    const wardID = filterWard?.[0]?.wardID;
    const wardName = filterWard?.[0]?.wardName;
    return { wardID, wardName };
}

export const mapOcrNewAddress = async (ocrinfo) => {
    let result = { ...ocrinfo };
    try {
        const isNewVersion = ocrinfo?.version == 1;
        const { provinceID, provinceName } = await (isNewVersion ? getProvinceNewByID(ocrinfo.cap_1_id_new) : getProvinceNewMapping(ocrinfo.cap_1_id));
        const { districtID, districtName } = await handleGetDistrictMerge(provinceID);
        const { wardID, wardName } = await (isNewVersion ? getWardNewByID(provinceID, districtID, ocrinfo.cap_2_id_new) : getWardNewMapping(ocrinfo.cap_3_id));
        result = {
            ...result,
            cap1: provinceName,
            cap2: districtName,
            cap3: wardName,
            cap_1_id: provinceID,
            cap_2_id: districtID,
            cap_3_id: wardID,
        }
        console.log(result, "mapOcrNewAddress");
        return result;
    } catch (error) {
        console.log("mapOcrNewAddress error", error);
    }
    return ocrinfo;
};

