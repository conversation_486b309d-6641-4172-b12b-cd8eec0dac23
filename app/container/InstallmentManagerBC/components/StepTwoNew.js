import React, { useState, useEffect } from "react";
import {
    View,
    TouchableOpacity,
    Keyboard,
    StyleSheet,
    Alert,
} from 'react-native';
import {
    MyText,
    Icon,
    RadioButton,
    hideBlockUI,
    TitleInput,
    Picker,
    PickerSearch,
    ScanQRCode,
    showBlock<PERSON>,
    DatePicker
} from "@components";
import { constants } from "@constants";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import moment from 'moment'
// import DatePicker from 'react-native-datepicker';
import * as installmentHelper from "../common/installmentHelper";
import * as installmentAction from "../action";
import { useDispatch, useSelector } from 'react-redux';
import { translate } from '@translate';
import { COLORS } from "@styles";
import { getBackProfileInformation } from '../action';
import TooltipWrapper from './TooltipWrapper';
import RadioButtonSD from "./RadioButtonSD";
import TitleInputMoney from "./TitleInputMoney";
import { helper } from "@common";
import ModalConfirmNew from "./Modal/ModalConfirmNew";
import * as dataCacheInstallmentAction from "./../action";

const StepTwoNew = function ({ parent, onNext, onPrev, validateObject, updatestate, updateprevstate, updateparent, itemInstallmentManager, SearchDataInstallment }) {
    const [state, setState] = React.useState({
        EPOSTransactionBO: parent.EPOSTransactionBO,
    })
    const dispatch = useDispatch();
    const broadcastSaleProgram = useSelector(state => state.InstallmentReducer.broadcastSaleProgram);
    const dataCacheInstallmentReducer = useSelector(state => state.DataCacheInstallmentReducer);
    const dispatchInstallmentOldRow = (IDCard, PartnerInstallmentID) => {
        getBackProfileInformation(IDCard).then((data) => {
            const newRadioGender = radioGender.map(gender => ({
                ...gender, selected: false
            }));
            const indexGender = newRadioGender.findIndex(gender => gender.value === data.Gender);
            if (indexGender !== -1) {
                newRadioGender[indexGender].selected = true;
            }
            var dateFormat = moment(data.Birthday, "YYYY-MM-DD");
            setBirthday(dateFormat);
            setRadioGender(newRadioGender)
            setState({
                ...state,
                EPOSTransactionBO: {
                    ...state.EPOSTransactionBO,
                    LastName: data.LastName,
                    MiddleName: data.MiddleName,
                    FirstName: data.FirstName,
                    Birthday: data.Birthday,
                    Gender: data.Gender,
                    Email: data.Email,
                    client_MaritalStatus: (PartnerInstallmentID == data.PartnerInstallmentID) ? data.client_MaritalStatus : 0,
                    NumberOfChildren: data.NumberOfChildren,
                    client_Education: (PartnerInstallmentID == data.PartnerInstallmentID) ? data.client_Education : 0,
                    client_SocialStatus: (PartnerInstallmentID == data.PartnerInstallmentID) ? data.client_SocialStatus : 0,
                    CompanyName: data.CompanyName,
                    HouseNumberCompany: data.HouseNumberCompany,
                    StreetCompany: data.StreetCompany,
                    ProvinceIDCompany: data.ProvinceIDCompany,
                    DistrictIDCompany: data.DistrictIDCompany,
                    WardIDCompany: data.WardIDCompany,
                    CompanyPosition: data.CompanyPosition,
                    EmployedFrom: data.EmployedFrom
                }
            })
        }).catch(err => {
            Alert.alert(
                translate('common.notification'),
                err.msgError,
                [
                    {
                        text: translate('common.btn_close'),
                        style: "default",
                        onPress: hideBlockUI
                    }
                ],
                { cancelable: false }
            );
        });
    }

    const [detailCardHometown, setDetailCardHometown] = React.useState({})
    const [radioGender, setRadioGender] = React.useState([
        { title: translate('instalmentManager.male'), selected: false, value: 1 },
        { title: translate('instalmentManager.female'), selected: false, value: 0 }
    ]);
    const [IDCardIssuePlaceList, setIDCardIssuePlaceList] = React.useState([]);
    //const [isShowModalDate, setIsShowModalDate] = React.useState(false);
    //const [propertyDate, setPropertyDate] = React.useState(null);
    //const [propertyDateValue, setPropertyDateValue] = React.useState(null);
    const installmentReducer = useSelector(state => state.InstallmentReducer);
    const InternalCodeList = parent.InitData.lstInternalCode;
    const [birthday, setBirthday] = useState("");
    const [issueDate, setIssueDate] = useState("");
    const [expiriedDate, setExpiriedDate] = useState("");
    const [IDCardIssuePlaceID, setIDCardIssuePlaceID] = useState(0);
    const refScroll2 = React.useRef(null);

    const [identification, setIdentification] = React.useState([
        { title: "Có QRCode", selected: true, value: 2 },
        { title: "Không có QRCode", selected: false, value: 3 }
    ]);
    const [updateIndex, setUpdateIndex] = useState(2);
    const [isVisibleScan, setIsVisibleScan] = React.useState(false);
    const [barCode, setBarcode] = React.useState('');
    const arrayBarCode = barCode.split("|");
    const client_ListStatusMarried = parent.EPOSTransactionBO?.client_ListStatusMarried;
    const instalmentPartnerList = useSelector(state => state.InstallmentReducer.broadcastPartnerList);
    const isBroadcastInstalment = instalmentPartnerList.length > 1;
    const [isVisibleConfirm, setIsVisibleConfirm] = useState(false);
    const [block, setBlock] = useState(false);
    const { GroupPaperTypeInforBOList } = parent;
    const { isCreatedSt } = itemInstallmentManager

    const selectItemGender = (data, index) => {
        data.forEach((item) => {
            item.selected = false;
        });
        data[index].selected = true;
        setState({
            ...state,
            EPOSTransactionBO: {
                ...state.EPOSTransactionBO,
                Gender: data[index].value
            }
        });
        setRadioGender(data);
    }

    useEffect(() => {
        const resetIdentification = identification.map((item, index) => ({
            ...item,
            selected: index === 0
        }));
        setIdentification(resetIdentification);
        setUpdateIndex(resetIdentification[0].value);
    }, [state.EPOSTransactionBO.client_IDCardType]);

    const selectIdentification = (data, index) => {
        data.forEach((item) => {
            item.selected = false;
        });
        data[index].selected = true;
        setState({
            ...state,
            EPOSTransactionBO: {
                ...state.EPOSTransactionBO,
            }
        });
        setUpdateIndex(data[index].value)
        setIdentification(data);
    }

    const disables = identification.map(item =>
        state.EPOSTransactionBO.client_IDCardType === 4 && item.value === 3
    );

    const resetItemGender = (data, genderID) => {
        data.forEach((item) => {
            item.selected = false;
        });
        let newData = data.map(el => (
            el.value === genderID ? { ...el, selected: true } : { ...el, selected: false }
        ))
        setRadioGender(newData);
    }

    const getDetailCardHometown = () => {
        if (installmentReducer?.lstIDCardHomeTown?.data) {
            var item = installmentReducer?.lstIDCardHomeTown?.data.find(x => x.IDCardHometownID == state.EPOSTransactionBO.client_IDCardHometown)
            if (item != null) {
                setDetailCardHometown(item);
            }
        }
    }
    useEffect(() => {
        hideBlockUI();
    }, [])

    useEffect(() => {
        console.log("parent22222");
        console.log(parent);
        if (IDCardIssuePlaceID > 0) {
            parent.EPOSTransactionBO.client_IDCardIssuePlaceID = IDCardIssuePlaceID;
        }
        setState({
            ...state,
            EPOSTransactionBO: parent.EPOSTransactionBO,
        })
        installmentHelper.initObjectFields = parent.FieldObjects;
        resetItemGender(radioGender, state.EPOSTransactionBO.Gender);
        getDetailCardHometown();
        if (refScroll2) {
            refScroll2.current.scrollToPosition(0, 0, false);
        }

        if (parent.EPOSTransactionBO.Birthday != null) {
            var dateFormat = moment(parent.EPOSTransactionBO.Birthday, "YYYY-MM-DD");
            // var dateFormat = moment(new Date(parent.EPOSTransactionBO.Birthday));
            setBirthday(dateFormat);
        }
        if (parent.EPOSTransactionBO.client_IDCardIssueDate != null) {
            var dateFormat = moment(parent.EPOSTransactionBO.client_IDCardIssueDate, "YYYY-MM-DD");
            // var dateFormat = moment(new Date(parent.EPOSTransactionBO.client_IDCardIssueDate));
            setIssueDate(dateFormat);
        }
        if (parent.EPOSTransactionBO.client_IDCardExpiriedDate != null) {
            var dateFormat = moment(parent.EPOSTransactionBO.client_IDCardExpiriedDate, "YYYY-MM-DD");
            // var dateFormat = moment(new Date(parent.EPOSTransactionBO.client_IDCardExpiriedDate));
            setExpiriedDate(dateFormat);
        }
    }, [parent.currentPosition]);
    // //Nơi cấp theo IDCard
    // //----->
    useEffect(() => {
        getDataIssueplace(state.EPOSTransactionBO.client_IDCardType, 241, state.EPOSTransactionBO.IDCard);
    }, [state.EPOSTransactionBO.IDCard, state.EPOSTransactionBO.client_IDCardType]);
    // //<-----
    console.log(state.EPOSTransactionBO.client_IDCardType, 241, state.EPOSTransactionBO.IDCard,"2 241 '312337579' '------------->0909'------------->0909")
    // //Loại cmnd
    // //----->
    // useEffect(() => {
    //     getDataIssueplace(state.EPOSTransactionBO.client_IDCardType, 241, state.EPOSTransactionBO.IDCard);
    // },[state.EPOSTransactionBO.client_IDCardType]);
    // //<-----

    const checkValidateData = () => {
        var FieldObjectsByStep = installmentHelper.initObjectFields.filter(item => item.Step == parent.currentPosition);
        for (var i = 0; i < FieldObjectsByStep.length; i++) {
            var item = FieldObjectsByStep[i];
            if (item.FieldName == "client_IDCardExpiriedDate" && state.EPOSTransactionBO.client_IDCardType != 2) {
                continue;
            }
            if (!validateObject(item, state.EPOSTransactionBO)) {
                return false;
            }
        };
        return true;
    }

    //IdentificationType (1: CMND; 2: Căn cước công dân; 3: Hộ chiếu)
    const getDataIssueplace = (idType = 2, nationalityID = 241, CardID) => {
        if (CardID != null && CardID.length == 12) {
            dispatch(installmentAction.getIssueplace(idType, nationalityID, CardID)).then(
                res => {
                    console.log("res res ", res);
                    setIDCardIssuePlaceList(res);
                    if (res != null && res.length == 1) {
                        console.log(res.length);
                        setState({
                            ...state,
                            EPOSTransactionBO: {
                                ...state.EPOSTransactionBO,
                                client_IDCardIssuePlaceID: res[0].idCardIssuePlaceId,
                            }
                        });
                        setIDCardIssuePlaceID(res[0].idCardIssuePlaceId);
                    }
                    else setIDCardIssuePlaceID(0);
                }
            ).catch(
                err => {
                    Alert.alert(
                        translate('common.notification'),
                        err.msgError,
                        [
                            {
                                text: translate('common.btn_close'), onPress: () => {

                                }
                            }
                        ],
                        { cancelable: false }
                    )
                }
            )
        }
    }

    useEffect(() => {
        ChangeISUnExpiredDate(state.EPOSTransactionBO.client_ISUnExpiredDate);
    }, [state.EPOSTransactionBO.client_ISUnExpiredDate, !state.EPOSTransactionBO.client_ISUnExpiredDate])

    const ChangeISUnExpiredDate = function (client_ISUnExpiredDate) {
        if (client_ISUnExpiredDate) {
            if (parent.FieldObjects) {
                var idx = parent.FieldObjects.findIndex(p => p.FieldName === 'client_IDCardExpiriedDate');
                if (idx >= 0) {
                    parent.FieldObjects[idx].IsRequired = false;
                }
            }
        } else {
            if ((state.EPOSTransactionBO.client_IDCardType == 2 || state.EPOSTransactionBO.client_IDCardType == 4) && parent.FieldObjects) {
                var idx = parent.FieldObjects.findIndex(p => p.FieldName === 'client_IDCardExpiriedDate');
                if (idx >= 0) {
                    parent.FieldObjects[idx].IsRequired = true;
                }
            }
        }
        updateparent(parent);
    }

    useEffect(() => {
        ChangeBirthday();
    }, [state.EPOSTransactionBO.Birthday])

    useEffect(() => {
        if (barCode || arrayBarCode) {
            setState(currentState => ({
                ...currentState,
                EPOSTransactionBO: {
                    ...currentState.EPOSTransactionBO,
                    client_QrData: arrayBarCode.join("|"),
                    client_OldIdCard: barCode.split("|")[1]
                }
            }));
        }
    }, [barCode])

    const ChangeBirthday = () => {
        // Ngày cấp CMND so với ngày sinh phải đủ 14 năm và so với ngày hiện tại phải còn hạn là 15 năm
        if (parent.FieldObjects && state.EPOSTransactionBO.Birthday) {
            var birthday = new Date(state.EPOSTransactionBO.Birthday);
            var idx = parent.FieldObjects.findIndex(p => p.FieldName === 'client_IDCardIssueDate');
            if (idx >= 0) {
                if (parent.FieldObjects[idx].objFieldRange != undefined) {
                    var beginDateTemp_14 = new Date(birthday.getFullYear() + 14, birthday.getMonth(), birthday.getDate(), 0, 0, 0) // ngày cấp phải đủ 14 tuổi so vs ngày sinh
                    var beginDateTemp_15 = new Date();
                    var year = new Date().getFullYear() - 15; // ngày cấp phải còn hạn là 15 năm so với ngày hiện tại
                    beginDateTemp_15.setFullYear(year);
                    if (beginDateTemp_14 > beginDateTemp_15) {
                        parent.FieldObjects[idx].objFieldRange.BeginDate = moment(beginDateTemp_14).format('YYYY-MM-DDThh:mm:ss');
                    } else {
                        parent.FieldObjects[idx].objFieldRange.BeginDate = moment(beginDateTemp_15).format('YYYY-MM-DDThh:mm:ss');
                    }
                }
            }
        }
        // Chặn dưới của ngày làm việc phải đủ 15 năm tính từ ngày sinh
        if (parent.FieldObjects && state.EPOSTransactionBO.Birthday) {
            var birthday = new Date(state.EPOSTransactionBO.Birthday);
            var idx = parent.FieldObjects.findIndex(p => p.FieldName === 'EmployedFrom');
            if (idx >= 0) {
                if (parent.FieldObjects[idx].objFieldRange != undefined) {
                    var beginDateTemp_15 = new Date();
                    beginDateTemp_15.setFullYear(birthday.getFullYear() + 15)
                    parent.FieldObjects[idx].objFieldRange.BeginDate = moment(beginDateTemp_15).format('YYYY-MM-DDThh:mm:ss');
                }
            }
        }
        updateparent(parent);
    }

    const removeDataCacheEP = () => {
        var dataCacheList = dataCacheInstallmentReducer.lstCacheDataEP.data;
        if (dataCacheList != null && dataCacheList.length > 0) {
            var idx = dataCacheList.findIndex(p => p.EPTransactionID === state.EPOSTransactionBO.EPTransactionID);
            if (idx >= 0) {
                dataCacheList.splice(idx, 1);
                dispatch(dataCacheInstallmentAction.DeleteAndCreatedDataCacheEPByStep(dataCacheList));
            }
        }
    };

    const updateBroadcastTransactionNew = () => {
        showBlockUI();
        const {
            EPOSTransactionBO,
        } = state;
        const { TotalPrepaid } = EPOSTransactionBO;
        EPOSTransactionBO.TotalPrepaid = TotalPrepaid ? parseFloat(TotalPrepaid) : 0;

        if (!CheckShowReferenIsHusbandOrWife('client_ReferenceHusbandOrWifeName')) {
            EPOSTransactionBO.client_ReferenceHusbandOrWifeName = "";
        }

        if (!helper.isNumber(EPOSTransactionBO.NumberOfChildren)
            && !helper.IsNonEmptyString(EPOSTransactionBO.NumberOfChildren)) {
            EPOSTransactionBO.NumberOfChildren = 0;
        }

        if (!helper.isNumber(EPOSTransactionBO.MonthlyPaymentLoan)
            && !helper.IsNonEmptyString(EPOSTransactionBO.MonthlyPaymentLoan)) {
            EPOSTransactionBO.MonthlyPaymentLoan = 0;
        }

        if (!helper.IsNonEmptyString(EPOSTransactionBO.Note)) {
            EPOSTransactionBO.Note = ".";
        }

        const listBroadcastSaleProgram = broadcastSaleProgram.filter(element =>
            !helper.IsEmptyObject(element.broadcastSaleProgramData)
        ).map(element =>
            element.broadcastSaleProgramData.saleProgram
        );

        let data = {
            EPOSTransactionBO,
            listGroupPaperTypeInforBO: GroupPaperTypeInforBOList,
            listSaleProgramBO: listBroadcastSaleProgram,
            times: isCreatedSt ? 1 : 2
        };
        dispatch(installmentAction.updateBroadcastTransactionNew(data))
            .then(EPOSTransactionBO => {
                setBlock(false);
                hideBlockUI();
                removeDataCacheEP();
                const { client_StepSendOTP } = EPOSTransactionBO;
                if (client_StepSendOTP) {
                    updatestate(EPOSTransactionBO);
                    onNext();
                }
                else {
                    Alert.alert("", translate('instalmentManager.update_record_success'),
                        [
                            {
                                text: "OK",
                                style: "default",
                                onPress: () => {
                                    SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                                    //goBack();
                                }
                            }
                        ]
                    );
                }
            })
            .catch(error => {
                setBlock(false);
                hideBlockUI();
                const { errorType, msgError } = error;
                if (errorType == 1) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => { }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                updateBroadcastTransactionNew();
                            },
                        },
                    ]);
                }
                else if (errorType == 3) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                //hideBlockUI();
                                removeDataCacheEP();
                                SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                            },
                        },
                    ]);
                }
                else {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                //hideBlockUI();
                                if (helper.IsNonEmptyString(msgError) && msgError.includes("đối tác")) {
                                    SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                                }
                            },
                        },
                    ]);
                }
            });
    };

    const CheckShowReferenIsHusbandOrWife = function (fieldname) {
        if (installmentHelper.initObjectFields) {
            console.log("EPOSTransactionBO: ", state?.EPOSTransactionBO?.cus_EPOSTransactionReferenceBOList?.[0]?.InformationID);
            var idx = installmentHelper.initObjectFields.findIndex(p => p.FieldName === fieldname);
            if (idx >= 0) {
                if (client_ListStatusMarried?.length > 0
                    && client_ListStatusMarried.indexOf("<" + state.EPOSTransactionBO.client_MaritalStatus + ">") != -1
                    && state.EPOSTransactionBO.cus_EPOSTransactionReferenceBOList[0].InformationID != state.EPOSTransactionBO.client_InformationHusbandOrWifeID
                    && state.EPOSTransactionBO.cus_EPOSTransactionReferenceBOList[1].InformationID != state.EPOSTransactionBO.client_InformationHusbandOrWifeID) {
                    installmentHelper.initObjectFields[idx].IsRequired = true;
                    console.log("vo1");
                    return true;
                } else {
                    installmentHelper.initObjectFields[idx].IsRequired = false;
                    console.log("vo2");
                    return false;
                }
            } else {
                console.log("vo3");
                return false;
            }
        } else {
            console.log("vo4");
            return false;
        }
    };

    const updateEPosTransactionNew = () => {
        showBlockUI();
        const {
            EPOSTransactionBO,
        } = state;
        const { TotalPrepaid } = EPOSTransactionBO;
        EPOSTransactionBO.TotalPrepaid = TotalPrepaid ? parseFloat(TotalPrepaid) : 0;

        if (!CheckShowReferenIsHusbandOrWife('client_ReferenceHusbandOrWifeName')) {
            EPOSTransactionBO.client_ReferenceHusbandOrWifeName = "";
        }

        // console.log("updateEPosTransactionNew: ", EPOSTransactionBO);
        // console.log("updateEPosTransactionNew: ", GroupPaperTypeInforBOList);

        if (!helper.isNumber(EPOSTransactionBO.NumberOfChildren)
            && !helper.IsNonEmptyString(EPOSTransactionBO.NumberOfChildren)) {
            EPOSTransactionBO.NumberOfChildren = 0;
        }

        if (!helper.isNumber(EPOSTransactionBO.MonthlyPaymentLoan)
            && !helper.IsNonEmptyString(EPOSTransactionBO.MonthlyPaymentLoan)) {
            EPOSTransactionBO.MonthlyPaymentLoan = 0;
        }

        if (!helper.IsNonEmptyString(EPOSTransactionBO.Note)) {
            EPOSTransactionBO.Note = ".";
        }
        let data = {
            EPOSTransactionBO: EPOSTransactionBO,
            GroupPaperTypeInforBOList: GroupPaperTypeInforBOList,
            times: isCreatedSt ? 1 : 2
        };
        dispatch(installmentAction.updateEPosTransactionNew(data))
            .then(res => {
                console.log("client_StepSendOTP: ", res);
                setBlock(false);
                hideBlockUI();
                removeDataCacheEP();
                const EPOSTransactionBO = JSON.parse(res.EPOSTransactionBO);
                const { client_StepSendOTP } = EPOSTransactionBO;
                console.log("client_StepSendOTP: ", client_StepSendOTP);
                if (client_StepSendOTP) {
                    updatestate(EPOSTransactionBO);
                    onNext();
                }
                else {
                    Alert.alert("", translate('instalmentManager.update_record_success'),
                        [
                            {
                                text: "OK",
                                style: "default",
                                onPress: () => {
                                    SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                                    //goBack();
                                }
                            }
                        ]
                    );
                }
            })
            .catch(error => {
                setBlock(false);
                hideBlockUI();
                const { errorType, msgError } = error;
                if (errorType == 1) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => { }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                updateEPosTransactionNew();
                            },
                        },
                    ]);
                }
                else if (errorType == 2) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                //hideBlockUI();
                                removeDataCacheEP();
                                SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                            },
                        },
                    ]);
                }
                else if (errorType == 3) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                //hideBlockUI();
                                removeDataCacheEP();
                                SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                            },
                        },
                    ]);
                }
                else {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                //hideBlockUI();
                                if (helper.IsNonEmptyString(msgError) && msgError.includes("đối tác")) {
                                    SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                                }
                            },
                        },
                    ]);
                }
            });
    };



    return (
        <KeyboardAwareScrollView
            ref={refScroll2}
            //style={{width: constants.width}}
            enableResetScrollToCoords={false}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={60}
        >
            <View style={{
                paddingHorizontal: 10,
                width: constants.width,
                height: "auto"
            }}
                activeOpacity={0.7}
            >
                <View style={styles.fieldSet}>
                    <MyText
                        text={translate('instalmentManager.customer_information_2')}
                        style={{ color: COLORS.txtFF0000, fontSize: 16 }}
                    />
                </View>
                {installmentHelper.CheckShowField("LastName") ? (
                    <View>
                        <TitleInput
                            title={installmentHelper.objField.FieldDescription}
                            isRequired={installmentHelper.CheckIsRequiredField("LastName")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.text_input_surname')}
                            value={state.EPOSTransactionBO.LastName}
                            onChangeText={(text) => {
                                var hasObj = installmentHelper.GetObjField("LastName");
                                //installmentHelper.objField.CharacterInputValue = "^[a-zA-Z0-9À-ȕẠ-ỹ\xC0-\xFF.,-/' ]{0,20}$" //ACS
                                if (hasObj && installmentHelper.isValidateInput(installmentHelper.objField.CharacterInputValue, text)) {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            LastName: text
                                        }
                                    });
                                }
                                else {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            LastName: state.EPOSTransactionBO.LastName ?? ""
                                        }
                                    });
                                }
                            }}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        LastName: ""
                                    }
                                });
                            }}
                            onBlur={() => {
                            }}
                            //editable={!isLockName}
                            key={"LastName"}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("MiddleName") ? (
                    <View style={{ marginVertical: 5 }}>
                        <TitleInput
                            title={installmentHelper.objField.FieldDescription}
                            isRequired={installmentHelper.CheckIsRequiredField("MiddleName")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.text_input_middle_name')}
                            value={state.EPOSTransactionBO.MiddleName}
                            onChangeText={(text) => {
                                console.log(text);
                                if (installmentHelper.GetObjField("MiddleName") && installmentHelper.isValidateInput(installmentHelper.objField.CharacterInputValue, text)) {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            MiddleName: text
                                        }
                                    });
                                }
                                else {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            MiddleName: state.EPOSTransactionBO.MiddleName ?? ""
                                        }
                                    });
                                }
                            }}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        MiddleName: ""
                                    }
                                });
                            }}
                            //editable={!isLockName}
                            key={"LastName"}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("FirstName") ? (
                    <View>
                        <TitleInput
                            title={installmentHelper.objField.FieldDescription}
                            isRequired={installmentHelper.CheckIsRequiredField("FirstName")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.text_input_last_name')}
                            value={state.EPOSTransactionBO.FirstName}
                            onChangeText={(text) => {
                                if (installmentHelper.GetObjField("FirstName") && installmentHelper.isValidateInput(installmentHelper.objField.CharacterInputValue, text)) {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            FirstName: text
                                        }
                                    });
                                }
                                else {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            FirstName: state.EPOSTransactionBO.FirstName ?? ""
                                        }
                                    });
                                }
                            }}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        FirstName: ""
                                    }
                                });
                            }}
                            //editable={!isLockName}
                            key={"FirstName"}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("Birthday") ? (
                    <View>
                        {/* <View style={{marginTop: 3}}> */}
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("Birthday") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={styles.wrapperInputUserCreated}>
                            <DatePicker
                                date={birthday}
                                format={'YYYY-MM-DD'}
                                onDateChange={(dateStr) => {
                                    setBirthday(dateStr);
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            // Birthday: new Date(newdate),
                                            Birthday: `${dateStr}T00:00:00`
                                        }
                                    });
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("Gender") ? (
                    <View style={{ marginVertical: 5, flexDirection: 'row' }}>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("FirstName") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <RadioButton
                            style={{
                                flexDirection: 'row'
                            }}
                            containerStyle={{
                                paddingLeft: constants.getSize(10),
                                alignItems: 'center'
                            }}
                            dataItems={radioGender}
                            selectItem={
                                (index) => { selectItemGender(radioGender, index) }
                            }
                            mainComponent={(item) => {
                                return (
                                    <MyText
                                        text={item.title}
                                        style={{
                                            color: item.selected ? COLORS.txtFF8900 : COLORS.txt333333,
                                            marginLeft: 2
                                        }} />
                                )
                            }}
                        />
                    </View>
                ) : null}
                <View style={{
                    width: constants.width - 20,
                    flexDirection: "row",
                    marginBottom: 4,
                    justifyContent: "space-between"
                }}>
                    <View style={{ flex: 1 }}></View>
                    <TouchableOpacity style={{
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                        onPress={() => dispatchInstallmentOldRow(state.EPOSTransactionBO.IDCard, state.EPOSTransactionBO.PartnerInstallmentID)}
                    >
                        <MyText
                            text={'Lấy lại thông tin hồ sơ cũ'}
                            style={{
                                color: COLORS.txtFFA500,
                                textDecorationLine: 'underline',
                                fontWeight: 'bold',
                                fontStyle: 'italic'
                            }}
                        />
                    </TouchableOpacity>
                </View>
                {installmentHelper.CheckShowField("IDCard") ? (
                    <View>
                        <TitleInput
                            title={translate('instalmentManager.number') + `${state.EPOSTransactionBO.client_IDCardType == 1 ? translate('instalmentManager.id_card') : translate('instalmentManager.id_card_2')}`}
                            isRequired={installmentHelper.CheckIsRequiredField("IDCard")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.enter') + `${state.EPOSTransactionBO.client_IDCardType == 1 ? translate('instalmentManager.id_card') : translate('instalmentManager.id_card_2')}`}
                            value={state.EPOSTransactionBO.IDCard}
                            onChangeText={(text) => {
                                let validate = state.EPOSTransactionBO.client_IDCardType == 1 ? new RegExp(/^\d{0,9}$/) : new RegExp(/^\d{0,12}$/);
                                if (validate.test(text) || text == "") {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            IDCard: text
                                        }
                                    });
                                }
                            }}
                            keyboardType={"numeric"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        IDCard: ""
                                    }
                                });
                            }}
                            onBlur={() => {
                                if (state.EPOSTransactionBO.IDCard.length < 9 || state.EPOSTransactionBO.IDCard.length > 9 && state.EPOSTransactionBO.IDCard.length < 12) {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            client_IDCardIssuePlaceID: -1,
                                        }
                                    });
                                }
                            }}
                            //editable={!isLockName}
                            key={"IDCard"}
                        />
                    </View>
                ) : null}
                {(installmentHelper.CheckShowField("client_QrData") && installmentHelper.CheckShowField("client_OldIdCard") && state.EPOSTransactionBO.client_IDCardType == 2) || installmentHelper.CheckShowField("client_QrData") && installmentHelper.CheckShowField("client_OldIdCard") && state.EPOSTransactionBO.client_IDCardType == 4 ||
                    (installmentHelper.CheckShowField("client_QrData") && installmentHelper.CheckShowField("client_OldIdCard") && state.EPOSTransactionBO.PartnerInstallmentID == 10) && state.EPOSTransactionBO.client_IDCardType == 2 || (installmentHelper.CheckShowField("client_QrData") && installmentHelper.CheckShowField("client_OldIdCard") && state.EPOSTransactionBO.PartnerInstallmentID == 10) && state.EPOSTransactionBO.client_IDCardType == 4
                    ? (
                        <View style={{ marginVertical: 5, flexDirection: "column" }}>
                            <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                                text={"Căn cước công dân"}
                                children={<MyText text={installmentHelper.CheckIsRequiredField("FirstName") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                            />
                            <RadioButtonSD
                                style={{
                                    flexDirection: 'row',
                                }}
                                containerStyle={{
                                    paddingLeft: constants.getSize(10),
                                    alignItems: 'center'
                                }}
                                disables={disables}
                                dataItems={identification}
                                selectItem={
                                    (index) => { selectIdentification(identification, index) }
                                }
                                mainComponent={(item) => {
                                    return (
                                        <MyText
                                            text={item.title}
                                            style={{
                                                color: item.selected ? COLORS.txtFF8900 : COLORS.txt333333,
                                                marginLeft: 2,
                                                opacity: state.EPOSTransactionBO.client_IDCardType === 4 && item.value === 3 ? 0.5 : 1
                                            }}
                                        />
                                    )
                                }}
                            />
                            <View style={{
                                marginTop: 10
                            }}>
                                {(updateIndex == 2) &&
                                    <View style={{}}>
                                        <MyText
                                            addSize={-1.5}
                                            style={{
                                                color: COLORS.txt333333,
                                                fontWeight: "bold",
                                                fontStyle: "italic",
                                                width: constants.width - 20,
                                            }}
                                            text="Dữ liệu QR:"
                                        />
                                        <View style={{
                                            flexDirection: "row",
                                            backgroundColor: COLORS.bgFFFFFF,
                                            justifyContent: "space-between",
                                            alignItems: "center",
                                            marginBottom: 5,
                                        }}>
                                            <View
                                                style={{
                                                    borderWidth: 1,
                                                    borderColor: COLORS.bdCCCCCC,
                                                    borderRadius: 5,
                                                }}
                                            >
                                                <MyText
                                                    adjustsFontSizeToFit={true}
                                                    numberOfLines={10}
                                                    style={{
                                                        width: constants.width - 70,
                                                        paddingHorizontal: 10,
                                                        marginVertical: 12,
                                                        color: (arrayBarCode.join("|")) ? COLORS.bg000000 : COLORS.bg8E8E93
                                                    }}
                                                    text={arrayBarCode.join("|") || "Quét mã QRCode ở mặt trước CCCD: "} />
                                            </View>
                                            <TouchableOpacity
                                                style={{
                                                    justifyContent: "center",
                                                    alignItems: "flex-end",
                                                    flex: 1
                                                }}
                                                onPress={() => setIsVisibleScan(true)}
                                            >
                                                <Icon
                                                    iconSet={"MaterialCommunityIcons"}
                                                    name={"barcode-scan"}
                                                    color={COLORS.icFFD400}
                                                    size={40}
                                                />
                                            </TouchableOpacity>
                                        </View>
                                        <TitleInput
                                            title="Nhập chứng minh nhân dân cũ:"
                                            isRequired={installmentHelper.CheckIsRequiredField("client_OldIdCard")}
                                            styleInput={{
                                                borderWidth: 1,
                                                borderRadius: 4,
                                                borderColor: COLORS.bdCCCCCC,
                                                marginBottom: 5,
                                                paddingHorizontal: 10,
                                                paddingVertical: 8,
                                            }}
                                            placeholder="Nhập chứng minh nhân dân cũ: "
                                            value={state.EPOSTransactionBO.client_OldIdCard}
                                            onChangeText={(text) => {
                                                let validate = state.EPOSTransactionBO.client_IDCardType == 1 ? new RegExp(/^\d{0,9}$/) : new RegExp(/^\d{0,12}$/);
                                                if (validate.test(text) || text == "") {
                                                    setState({
                                                        ...state,
                                                        EPOSTransactionBO: {
                                                            ...state.EPOSTransactionBO,
                                                            client_OldIdCard: text
                                                        }
                                                    });
                                                }
                                            }}
                                            keyboardType={"numeric"}
                                            returnKeyType={"done"}
                                            blurOnSubmit={true}
                                            onSubmitEditing={Keyboard.dismiss}
                                            width={constants.width - 20}
                                            height={40}
                                            clearText={() => {
                                                setState({
                                                    ...state,
                                                    EPOSTransactionBO: {
                                                        ...state.EPOSTransactionBO,
                                                        client_OldIdCard: ""
                                                    }
                                                });
                                            }}
                                            onBlur={() => {
                                                if (state.EPOSTransactionBO.client_OldIdCard.length < 9 || state.EPOSTransactionBO.client_OldIdCard.length > 9 && state.EPOSTransactionBO.client_OldIdCard.length < 12) {
                                                    setState({
                                                        ...state,
                                                        EPOSTransactionBO: {
                                                            ...state.EPOSTransactionBO,
                                                            client_OldIdCard: -1,
                                                        }
                                                    });
                                                }
                                            }}
                                            //editable={!isLockName}
                                            key={"client_OldIdCard"}
                                        />
                                    </View>
                                }
                                {(updateIndex == 3) &&
                                    <TitleInput
                                        title="Nhập chứng minh nhân dân cũ:"
                                        isRequired={installmentHelper.CheckIsRequiredField("client_OldIdCard")}
                                        styleInput={{
                                            borderWidth: 1,
                                            borderRadius: 4,
                                            borderColor: COLORS.bdCCCCCC,
                                            marginBottom: 5,
                                            paddingHorizontal: 10,
                                            paddingVertical: 8,
                                        }}
                                        placeholder="Nhập chứng minh nhân dân cũ: "
                                        value={state.EPOSTransactionBO.client_OldIdCard}
                                        onChangeText={(text) => {
                                            let validate = state.EPOSTransactionBO.client_IDCardType == 1 ? new RegExp(/^\d{0,9}$/) : new RegExp(/^\d{0,12}$/);
                                            if (validate.test(text) || text == "") {
                                                setState({
                                                    ...state,
                                                    EPOSTransactionBO: {
                                                        ...state.EPOSTransactionBO,
                                                        client_OldIdCard: text
                                                    }
                                                });
                                            }
                                        }}
                                        keyboardType={"numeric"}
                                        returnKeyType={"done"}
                                        blurOnSubmit={true}
                                        onSubmitEditing={Keyboard.dismiss}
                                        width={constants.width - 20}
                                        height={40}
                                        clearText={() => {
                                            setState({
                                                ...state,
                                                EPOSTransactionBO: {
                                                    ...state.EPOSTransactionBO,
                                                    client_OldIdCard: ""
                                                }
                                            });
                                        }}
                                        onBlur={() => {
                                            if (state.EPOSTransactionBO.client_OldIdCard.length < 9 || state.EPOSTransactionBO.client_OldIdCard.length > 9 && state.EPOSTransactionBO.client_OldIdCard.length < 12) {
                                                setState({
                                                    ...state,
                                                    EPOSTransactionBO: {
                                                        ...state.EPOSTransactionBO,
                                                        client_OldIdCard: -1,
                                                    }
                                                });
                                            }
                                        }}
                                        //editable={!isLockName}
                                        key={"client_OldIdCard"}
                                    />
                                }
                            </View>
                        </View>
                    ) : null}
                {installmentHelper.CheckShowField("client_IDCardIssuePlaceID") ? (
                    <View>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("client_IDCardIssuePlaceID") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={[styles.view_picker]}>
                            <Picker
                                label={"idCardIssuePlaceName"}
                                value={"idCardIssuePlaceId"}
                                data={IDCardIssuePlaceList}
                                valueSelected={state.EPOSTransactionBO.client_IDCardIssuePlaceID}
                                onChange={(item) => {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            client_IDCardIssuePlaceID: item.idCardIssuePlaceId,
                                        }
                                    });
                                }}
                                defaultLabel={translate('instalmentManager.header_place')}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 30,
                                    marginVertical: 5,
                                    marginLeft: -5,
                                    marginRight: -10,
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("client_IDCardIssueDate") ? (
                    <View style={{ marginTop: 4 }}>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("client_IDCardIssueDate") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={styles.wrapperInputUserCreated}>
                            <DatePicker
                                date={issueDate}
                                format={'YYYY-MM-DD'}
                                onDateChange={(dateStr) => {
                                    setIssueDate(dateStr);
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            // client_IDCardIssueDate: new Date(newdate),
                                            client_IDCardIssueDate: `${dateStr}T00:00:00`
                                        }
                                    });
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField('client_IDCardExpiriedDate') && (state.EPOSTransactionBO.client_IDCardType == 2 || state.EPOSTransactionBO.client_IDCardType == 4) ? (
                    <View>
                        {!state.EPOSTransactionBO.client_ISUnExpiredDate && (
                            <>

                                <View style={{
                                    width: constants.width - 20,
                                    flexDirection: "row",
                                    alignItems: "center",
                                    paddingVertical: 8
                                }}>
                                    <TooltipWrapper
                                        placement={"top"}
                                        content={
                                            <MyText style={{ color: COLORS.txtFFFFFF }}
                                                text={"Thời hạn sử dụng: in ở mặt trước (phía dưới hình KH)"}
                                            />
                                        }
                                        wrapper={
                                            <View style={{ flexDirection: "row", alignItems: "center" }}>
                                                <MyText style={{ fontWeight: '700', fontStyle: 'italic' }}
                                                    text={installmentHelper.objField.FieldDescription}
                                                    addSize={-1}
                                                    children={<MyText text={installmentHelper.CheckIsRequiredField("client_IDCardIssueDate") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                                                />
                                                <Icon
                                                    iconSet={"FontAwesome"}
                                                    name={"question-circle"}
                                                    size={16}
                                                    color={COLORS.txtFF0000}
                                                    style={{
                                                        marginLeft: 3
                                                    }}
                                                />
                                            </View>
                                        }
                                    />
                                </View>
                                <View style={styles.wrapperInputUserCreated}>
                                    <DatePicker
                                        date={expiriedDate}
                                        format={'YYYY-MM-DD'}
                                        onDateChange={(dateStr) => {
                                            setExpiriedDate(dateStr);
                                            setState({
                                                ...state,
                                                EPOSTransactionBO: {
                                                    ...state.EPOSTransactionBO,
                                                    // client_IDCardExpiriedDate: new Date(newdate),
                                                    client_IDCardExpiriedDate: `${dateStr}T00:00:00`,
                                                }
                                            });
                                        }}
                                    />
                                </View>
                            </>
                        )}
                        {installmentHelper.CheckShowField("client_ISUnExpiredDate") ? (
                            <View style={{ marginTop: 3 }}>
                                <TouchableOpacity
                                    style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                        //marginBottom: 10
                                    }}
                                    onPress={async () => {
                                        await setState({
                                            ...state,
                                            EPOSTransactionBO: {
                                                ...state.EPOSTransactionBO,
                                                client_ISUnExpiredDate: !state.EPOSTransactionBO.client_ISUnExpiredDate,
                                                client_IDCardExpiriedDate: state.EPOSTransactionBO.client_ISUnExpiredDate ? null : state.EPOSTransactionBO.client_IDCardExpiriedDate
                                            }
                                        })
                                        setExpiriedDate(null);
                                        //ChangeISUnExpiredDate(!state.EPOSTransactionBO.client_ISUnExpiredDate);
                                    }}
                                    activeOpacity={1}
                                >
                                    <Icon
                                        iconSet="Ionicons"
                                        name={state.EPOSTransactionBO.client_ISUnExpiredDate ? "md-checkbox-outline" : "ios-square-outline"}
                                        style={{
                                            fontSize: 18,
                                            color: state.EPOSTransactionBO.client_ISUnExpiredDate ? COLORS.icFF8900 : COLORS.txt333333,
                                            borderRadius: 20,
                                            backgroundColor: COLORS.bgFFFFFF,
                                        }}
                                    />
                                    <MyText
                                        text={translate('instalmentManager.no_term')}
                                        style={{
                                            marginLeft: 5,
                                            color: COLORS.txt543729,
                                            fontWeight: "bold"
                                        }}
                                    />
                                </TouchableOpacity>
                            </View>
                        ) : null}
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("client_IDCardHometown") ? (
                    <View>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("client_IDCardHometown") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <PickerSearch
                            label={"IDCardHometownName"}
                            value={"IDCardHometownID"}
                            valueSelected={state.EPOSTransactionBO.client_IDCardHometown}
                            data={installmentReducer?.lstIDCardHomeTown?.data}
                            onChange={(item) => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        client_IDCardHometown: item.IDCardHometownID,
                                    }
                                })
                                setDetailCardHometown(item);
                            }}
                            style={{
                                flex: 1,
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                height: 40,
                                borderRadius: 4,
                                borderWidth: 1,
                                borderColor: COLORS.bdE4E4E4,
                                width: constants.width - 20,
                                backgroundColor: COLORS.btnFFFFFF
                            }}
                            defaultLabel={translate('instalmentManager.header_hometown')}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField('PhoneNumber') ? (
                    <View style={{ marginVertical: 3 }}>
                        <TitleInput
                            title={installmentHelper.objField.FieldDescription}
                            isRequired={installmentHelper.CheckIsRequiredField("PhoneNumber")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                                backgroundColor: COLORS.bgF5F5F5
                            }}
                            placeholder={translate('instalmentManager.text_input_phone_number')}
                            value={state.EPOSTransactionBO.PhoneNumber}
                            onChangeText={(text) => {
                                if (installmentHelper.GetObjField("PhoneNumber") && installmentHelper.isValidateInput(installmentHelper.objField.CharacterInputValue, text)) {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            PhoneNumber: text
                                        }
                                    })
                                }
                            }}
                            keyboardType={"numeric"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        PhoneNumber: ""
                                    }
                                })
                            }}
                            editable={false}
                            key={"PhoneNumber"}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField('Email') ? (
                    <View style={{ marginVertical: 3 }}>
                        <TitleInput
                            title={installmentHelper.objField.FieldDescription}
                            isRequired={installmentHelper.CheckIsRequiredField("Email")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.text_input_email')}
                            value={state.EPOSTransactionBO.Email}
                            onChangeText={(text) => {
                                const cleanedText = text.trim().replace(/\s/g, '');
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        Email: cleanedText
                                    }
                                });
                            }}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                Keyboard.dismiss();
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        Email: ""
                                    }
                                });
                            }}
                            maxLength={60}
                            //editable={!isLockName}
                            key={"Email"}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField('client_InternalCode') ? (
                    <View>
                        <View style={{
                            width: constants.width - 20,
                            flexDirection: "row",
                            alignItems: "center",
                            paddingVertical: 8
                        }}>
                            <TooltipWrapper
                                placement={"top"}
                                content={
                                    <View>
                                        <MyText style={{ color: COLORS.txtFFFFFF }}
                                            text={"01: KH bình thường"}
                                        />
                                        <MyText style={{ color: COLORS.txtFFFFFF }}
                                            text={"02: KH có thông tin xấu"}
                                        />
                                        <MyText style={{ color: COLORS.txtFFFFFF }}
                                            text={"03: KH có dấu hiệu gian lận"}
                                        />
                                        <MyText style={{ color: COLORS.txtFFFFFF }}
                                            text={"04: Nghiện hoặc say xỉn"}
                                        />
                                        <MyText style={{ color: COLORS.txtFFFFFF }}
                                            text={"05: Từ chối bởi các đối thủ"}
                                        />
                                    </View>
                                }
                                wrapper={
                                    <View style={{ flexDirection: "row", alignItems: "center" }}>
                                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', color: COLORS.txtFF0000 }}
                                            text={installmentHelper.objField.FieldDescription}
                                            addSize={-1}
                                            children={<MyText text={installmentHelper.CheckIsRequiredField("client_InternalCode") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                                        />
                                        <Icon
                                            iconSet={"FontAwesome"}
                                            name={"question-circle"}
                                            size={16}
                                            color={COLORS.txtFF0000}
                                            style={{
                                                marginLeft: 3
                                            }}
                                        />
                                    </View>
                                }
                            />
                        </View>
                        <View style={[styles.view_picker]}>
                            <Picker
                                label={"InformationName"}
                                value={"InformationID"}
                                data={InternalCodeList}
                                valueSelected={state.EPOSTransactionBO.client_InternalCode}
                                onChange={(item) => {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            client_InternalCode: item.InformationID,
                                            client_InternalCodeName: item.InformationName
                                        }
                                    });
                                }}
                                defaultLabel={`-- ${installmentHelper.objField.FieldDescription} --`}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 30,
                                    marginVertical: 5,
                                    marginLeft: -5,
                                    marginRight: -10,
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("MonthlyIncome") ? (
                    <View style={{ marginVertical: 5 }}>
                        <View style={{
                            width: constants.width - 20,
                            flexDirection: "row",
                            alignItems: "center"
                        }}>
                            <TooltipWrapper
                                placement={"bottom"}
                                content={
                                    <MyText style={{ color: COLORS.txtFFFFFF }}
                                        text={"Tối thiểu 3 triệu đến dưới 90 triệu"}
                                    />
                                }
                                wrapper={
                                    <View style={{ flexDirection: "row", alignItems: "center" }}>
                                        <MyText style={{ fontWeight: '700', fontStyle: 'italic' }}
                                            text={installmentHelper.objField.FieldDescription}
                                            addSize={-1}
                                            children={<MyText text={installmentHelper.CheckIsRequiredField('MonthlyIncome') ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                                        />
                                        <Icon
                                            iconSet={"FontAwesome"}
                                            name={"question-circle"}
                                            size={16}
                                            color={COLORS.txtFF0000}
                                            style={{
                                                marginLeft: 3
                                            }}
                                        />
                                    </View>
                                }
                            />
                        </View>
                        <TitleInputMoney
                            // title={installmentHelper.objField.FieldDescription}
                            // isRequired={installmentHelper.CheckIsRequiredField('MonthlyIncome')}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                backgroundColor: COLORS.bgFFFFFF,
                                color: COLORS.txt333333,
                                height: 40,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                width: constants.width - 20,
                            }}
                            placeholder={translate('instalmentManager.enter') + installmentHelper.objField.FieldDescription.toLowerCase()}
                            value={state.EPOSTransactionBO.MonthlyIncome}
                            onChange={(value) => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        MonthlyIncome: value
                                    },
                                });
                            }}
                            length={11}
                        //editable={false}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("DayOfSalary") ? (
                    <View>
                        <TitleInput
                            title={installmentHelper.objField.FieldDescription}
                            isRequired={installmentHelper.CheckIsRequiredField('DayOfSalary')}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.enter') + installmentHelper.objField.FieldDescription.toLowerCase()}
                            value={state.EPOSTransactionBO.DayOfSalary}
                            onChangeText={(text) => {
                                if (text == "" || text == "0") {
                                    if (text == "") {
                                        setState({
                                            ...state,
                                            EPOSTransactionBO: {
                                                ...state.EPOSTransactionBO,
                                                DayOfSalary: ""
                                            },
                                        });
                                    } else if (text == 0) {
                                        setState({
                                            ...state,
                                            EPOSTransactionBO: {
                                                ...state.EPOSTransactionBO,
                                                DayOfSalary: 0
                                            },
                                        });
                                    }
                                }

                                let newText = helper.removeMaskString(text);
                                newText = parseInt(newText, 10);
                                let validatePrice = new RegExp(/^([1-9]|[12]\d|3[01])$/);
                                if (validatePrice.test(newText)) {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            DayOfSalary: newText.toString()
                                        },
                                    });
                                }
                            }}
                            maxLength={2}
                            keyboardType={"numeric"}
                            returnKeyType={"done"}
                            onSubmitEditing={Keyboard.dismiss}
                            blurOnSubmit={true}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        DayOfSalary: ""
                                    },
                                });
                            }}
                            key={"DayOfSalary"}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("MonthlyExpense") ? (
                    <View>
                        <TitleInputMoney
                            title={installmentHelper.objField.FieldDescription}
                            isRequired={installmentHelper.CheckIsRequiredField('MonthlyExpense')}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                backgroundColor: COLORS.bgFFFFFF,
                                color: COLORS.txt333333,
                                height: 40,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                width: constants.width - 20,
                            }}
                            placeholder={translate('instalmentManager.enter') + installmentHelper.objField.FieldDescription.toLowerCase()}
                            value={state.EPOSTransactionBO.MonthlyExpense}
                            onChange={(value) => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        MonthlyExpense: value
                                    },
                                });
                            }}
                        //editable={false}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("MonthlyPaymentLoan") ? (
                    <View>
                        <TitleInputMoney
                            title={installmentHelper.objField.FieldDescription}
                            isRequired={installmentHelper.CheckIsRequiredField('MonthlyPaymentLoan')}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                backgroundColor: COLORS.bgFFFFFF,
                                color: COLORS.txt333333,
                                height: 40,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                width: constants.width - 20,
                            }}
                            placeholder={translate('instalmentManager.enter') + installmentHelper.objField.FieldDescription.toLowerCase()}
                            value={state.EPOSTransactionBO.MonthlyPaymentLoan}
                            onChange={(value) => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        MonthlyPaymentLoan: value
                                    },
                                });
                            }}
                            length={11}
                        //editable={false}
                        />
                    </View>
                ) : null}
                <View
                    style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        paddingHorizontal: 20,
                        marginVertical: 10,
                    }}
                >
                    <TouchableOpacity
                        style={[styles.btn, { backgroundColor: COLORS.btnFFFFFF, }]}
                        activeOpacity={0.7}
                        onPress={() => {
                            updateprevstate(state.EPOSTransactionBO);
                            onPrev();
                        }}
                    >
                        <MyText text={translate('common.btn_back')} style={{ fontWeight: "bold", backgroundColor: COLORS.btnFFFFFF, color: COLORS.txt4DA6FF }} />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[styles.btn, { backgroundColor: COLORS.btn4DA6FF }]}
                        activeOpacity={0.7}
                        onPress={() => {
                            if (checkValidateData()) {
                                const itemIssuePlace = IDCardIssuePlaceList.find(ele => ele.idCardIssuePlaceId == state.EPOSTransactionBO.client_IDCardIssuePlaceID);
                                state.EPOSTransactionBO.client_IDCardIssuePlaceName = itemIssuePlace.idCardIssuePlaceName;
                                state.EPOSTransactionBO.client_IDCardHometownName = detailCardHometown.IDCardHometownName;
                                setIsVisibleConfirm(true);
                            }
                        }}
                        disabled={(block ? true : false)}
                    >
                        <MyText text={"Cập nhật hồ sơ lần 1"} style={styles.txtBtn}
                        />
                    </TouchableOpacity>
                </View>

            </View>
            {
                isVisibleScan &&
                <ScanQRCode
                    isVisible={isVisibleScan}
                    closeCamera={() => setIsVisibleScan(false)}
                    resultScanBarcode={(barcode) => {
                        setBarcode(barcode)
                        setIsVisibleScan(false)
                    }}
                />
            }
            {
                <ModalConfirmNew
                    isVisible={isVisibleConfirm}
                    hideModal={() => {
                        setIsVisibleConfirm(false);
                    }}
                    transactionInfo={{...state.EPOSTransactionBO}}
                    paperTypeInfor={GroupPaperTypeInforBOList}
                    onConfirm={() => {
                        // updatestate(state.EPOSTransactionBO);
                        setBlock(true);
                        if (isBroadcastInstalment) {
                            updateBroadcastTransactionNew();
                        } else {
                            updateEPosTransactionNew();
                        }
                        setIsVisibleConfirm(false);
                    }}
                    installmentHelper={installmentHelper}
                    itemInstallmentManager={itemInstallmentManager}
                />

            }
        </KeyboardAwareScrollView >
    );
}

export default StepTwoNew;
const styles = StyleSheet.create({
    fieldSet: {
        flexDirection: "row",
        marginVertical: 5,
        justifyContent: "space-between",
    },
    view_picker: {
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        height: "auto",
        backgroundColor: COLORS.bgFFFFFF,
    },
    picker: {
        height: 30,
        marginVertical: 5,
        marginLeft: -5,
        marginRight: -10,
    },
    btn: {
        padding: 10,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        width: "47%",
        borderWidth: 1,
        borderColor: COLORS.bd4DA6FF,
        //shadowColor: COLORS.sd000000,
        // shadowOffset: {
        //     width: 0,
        //     height: 1,
        // },
        // shadowOpacity: 0.15,
        // elevation: 3,
        height: "auto",
        //margin: 10
    },
    txtBtn: {
        fontWeight: "bold",
        color: COLORS.txtFFFFFF,
        textAlign: 'center'
    },
    wrapperInputUserCreated: {
        width: "100%",
        flexDirection: "row",
        justifyContent: "center",
        paddingBottom: 3,
        alignItems: "center",
    },
    wrapperSearch: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 5,
        width: "100%",
        padding: 5,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        height: "auto",
        backgroundColor: COLORS.bgFFFFFF,
        shadowColor: COLORS.sd000000,
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        elevation: 3,
    },
    inputUser: {
        width: "87%",
        paddingHorizontal: 5,
    },
});
