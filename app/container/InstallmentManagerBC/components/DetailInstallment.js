import React, { useEffect, useState, useRef } from "react"
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Alert,
    FlatList,
    Animated,
    Easing,
    Linking
} from 'react-native';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useDispatch } from 'react-redux';
import {
    MyText,
    showB<PERSON>UI,
    hideBlock<PERSON>,
    Picker,
    Icon,
    BaseLoading
} from "@components";
import { helper } from "@common";
import { constants } from "@constants";
import TitleInputMoney from "./TitleInputMoney";
import InsuranceInfo from "./InsuranceInfo";
import * as installmentAction from "./../action";
import * as installmentHelper from "../common/installmentHelper";
import { translate } from '@translate';
import { COLORS } from "@styles";
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux';
import * as actionInstalmentCreator from "../action";
import ModalSaleProgram from './Modal/ModalSaleProgram';
import InnerQRContext from "./InnerQRContext";
import { useBottomSheet } from "@context";

const { PARTNER_ID } = constants

const DetailInstallment = function ({
    parent,
    onNext,
    onPrev,
    validateObject,
    updatestate,
    updateprevstate,
    onGetBroadcastInformation,
    broadcastPaperType,
    stateBroadcastPaperType,
    actionInstalment,
    broadcastSaleProgram,
    stateBroadcastSaleProgram,
    stateBroadcastLoanPackage,
    broadcastPartnerList,
    initialData,
    dataRewardInstallment,
    dataRewardInstallmentBC,
    dataUserCode,
    itemInstallmentManager,
    storeID
}) {
    const { showBottomSheet } = useBottomSheet();

    const [state, setState] = useState({
        EPOSTransactionBO: parent.EPOSTransactionBO,
        SaleProgramInfo: parent.SaleProgramInfo
    })
    const dispatch = useDispatch();
    const [percent, setPercent] = useState({
        prepaidPercentage: -1,
    });
    const [TermLoanList, setTermLoanList] = useState(parent.InitData.lstTermLoan);
    const [IsShowMinMaxPercen, setIsShowMinMaxPercen] = useState(false);
    const [GoodsInsuranceID, setGoodsInsuranceID] = useState(0);
    const {
        SaleProgramName,
        SaleProgramID,
        RecordsProcessingFee,
        IsCardPartner,
        PrepaidPercent,
        PrepaidPercentMax,
        InsuranceFees,
        MasterGoodsInsurance,
    } = getSaleProgramInfo(state);
    const {
        IsSelectedInsuranceFees
    } = getInsuranceFees(state);
    const {
        IsSelectedGoodsInsurance,
        GoodsInsuranceList
    } = getMasterGoodsInsurance(state);
    const [isUseEffect, setIsUseEffect] = useState(false);
    const [isBroadCast, setIsBroadCast] = useState(false);
    const [isShowPaperType, setIsShowPaperType] = useState(true);
    const [isShowPartner, setIsShowPartner] = useState(false);
    const [isVisibleModal, setIsVisibleModal] = useState(false);
    const [saleProgramData, setSaleProgramData] = useState({})
    const [selectedPaperType, setSelectedPaperType] = useState([{
        "PaperTypeID": 1,
        "PaperTypeName": "CMND/CCCD",
        "IsActived": true
    }]);
    const statusCode = dataUserCode?.userCodeStatus;
    const refScroll0 = React.useRef(null);
    const partnerSendData = broadcastSaleProgram.reduce((current, element) => {
        if (!helper.IsEmptyObject(element.broadcastSaleProgramData)) {
            return [...current, element.PartnerInstallmentID]
        } else return current
    }, [state.EPOSTransactionBO.PartnerInstallmentID])
    const paperTypeViewHeight = stateBroadcastPaperType.isFetching || stateBroadcastPaperType.isEmpty || stateBroadcastPaperType.isError ?
        constants.getSize(80) :
        (
            broadcastPaperType.length < 5 ?
                broadcastPaperType * constants.getSize(30) + 15 :
                4 * constants.getSize(30) + 15
        );

    const rotateAnim = useRef(new Animated.Value(0)).current;
    const { isCreatedSt, isCreatedNd } = itemInstallmentManager ?? {};

    useEffect(() => {
        Animated.timing(
            rotateAnim,
            {
                toValue: isShowPaperType ? 1 : 0,
                duration: 150,
                easing: Easing.linear,
                useNativeDriver: true
            }
        ).start()
    }, [isShowPaperType])

    const rotateValue = rotateAnim.interpolate({
        inputRange: [0, 1],
        outputRange: ['0deg', '-180deg']
    })

    useEffect(() => {
        hideBlockUI();
    }, [])
    useEffect(() => {
        console.log("parent step 0: ", parent);
        setState({
            ...state,
            EPOSTransactionBO: parent.EPOSTransactionBO,
        })
        setIsUseEffect(false);
        installmentHelper.initObjectFields = parent.FieldObjects;
        if (refScroll0) {
            refScroll0.current.scrollToPosition(0, 0, false);
        }
    }, [parent.currentPosition]);
    //
    useEffect(() => {
        setIsShowMinMaxPercen(false);
        var percent = 0;
        if (state.EPOSTransactionBO.Debt > 0) {
            percent = ((state.EPOSTransactionBO.TotalPrepaid - state.EPOSTransactionBO.cus_DefferenceTotalPrepaid) * 100 / state.EPOSTransactionBO.Debt);
            setPercent({
                ...percent,
                prepaidPercentage: percent
            });
            setState({
                ...state,
                EPOSTransactionBO: {
                    ...state.EPOSTransactionBO,
                    cus_DefferenceTotalPrepaid: 0
                }
            });
        }
        if (percent < state.SaleProgramInfo.PrepaidPercent || percent > state.SaleProgramInfo.PrepaidPercentMax) {
            setIsShowMinMaxPercen(true);
        }
    }, [state.SaleProgramInfo, state.EPOSTransactionBO.TotalPrepaid]);
    //
    useEffect(() => {
        getPaymentMonthly()
    }, [isUseEffect && state.EPOSTransactionBO.TermLoan,
    isUseEffect && state.EPOSTransactionBO?.client_InsuranceFeesBO?.IsSelectedInsuranceFees,
    isUseEffect && state.EPOSTransactionBO?.client_MasterGoodsInsuranceBO?.GoodsInsuranceList]);

    useEffect(() => {
        if (isBroadCast) {
            actionInstalment.getBroadcastPaperType();
        }
    }, [isBroadCast])

    const getPaymentMonthly = () => {
        if (isUseEffect) {
            showBlockUI();
            const { EPOSTransactionBO } = state
            const { TotalPrepaid } = EPOSTransactionBO
            const newEPOSTransactionBO = {
                ...EPOSTransactionBO,
                "TotalPrepaid": TotalPrepaid
            }
            dispatch(installmentAction.getPaymentMonthly(newEPOSTransactionBO))
                .then((value) => {
                    hideBlockUI();
                    setState({
                        ...state,
                        EPOSTransactionBO: {
                            ...state.EPOSTransactionBO,
                            MonthlyPayment: value
                        }
                    });
                })
                .catch(msgError => {
                    Alert.alert(translate('common.notification_uppercase'), msgError ?? translate('instalmentManager.no_payment_per_month_information'),
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: "cancel",
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_notify_try_again'),
                                style: "default",
                                onPress: getPaymentMonthly
                            }
                        ]
                    )
                });
        }
    }

    const checkValidateData = () => {
        var FieldObjectsByStep = installmentHelper.initObjectFields.filter(item => item.Step == parent.currentPosition);
        if (percent.prepaidPercentage < state.SaleProgramInfo.PrepaidPercent || percent.prepaidPercentage > state.SaleProgramInfo.PrepaidPercentMax) {
            Alert.alert(
                translate('common.notification'),
                translate('instalmentManager.invalid_deposit'),
                [
                    {
                        text: translate('common.btn_close'), onPress: () => {

                        }
                    }
                ],
                { cancelable: false }
            )
            return false;
        }
        for (var i = 0; i < FieldObjectsByStep.length; i++) {
            var item = FieldObjectsByStep[i];
            if (item.FieldName == "TotalPrepaid" && state.SaleProgramInfo.PrepaidPercent == 0 && state.EPOSTransactionBO.TotalPrepaid == 0) {
                continue;
            }
            if (!validateObject(item, state.EPOSTransactionBO)) {
                return false;
            }
        };
        return true;
    }

    const getPartner = () => {
        setIsShowPaperType(false);
        setIsShowPartner(true);
        const data = {
            epTransactionID: state.EPOSTransactionBO.EPTransactionID,
            listPaperType: selectedPaperType.map(element => element.PaperTypeID).join(","),
            partnerInstallmentID: state.EPOSTransactionBO.PartnerInstallmentID,
            saleProgramID: state.EPOSTransactionBO.SaleProgramID
        }
        actionInstalment.getBroadcastInstalmentPartner(data);
    }

    const getSaleProgram = (partner, index) => {
        const {
            PartnerInstallmentName,
            DeActived,
        } = partner

        if (DeActived) {
            Alert.alert(
                translate('common.notification_uppercase'),
                translate('detail.deactivated_installment_partner_notice', { partnerInstallmentName: PartnerInstallmentName })
            )
        }
        else {
            showBlockUI();
            const data = {
                EPTransactionID: state.EPOSTransactionBO.EPTransactionID,
                ListPaperType: selectedPaperType.map(element => element.PaperTypeID).join(","),
                PartnerInstallmentID: partner.PartnerInstallmentID
            }
            actionInstalment.getBroadcastSaleProgram(data, index).then(
                (saleProgram) => {
                    hideBlockUI();
                    setSaleProgramData(saleProgram[index])
                    setIsVisibleModal(true);
                }
            ).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => { getSaleProgram(partner, index) }
                        }
                    ]
                )
            })
        }
    }

    const onSelectBroadcastSaleProgram = (broadcastSaleProgramData, dataRewardInstallmentBC) => {
        setIsVisibleModal(false);
        const { saleProgram: { PartnerInstallmentID } } = broadcastSaleProgramData;
        const newBroadcastSaleProgram = broadcastSaleProgram.map(saleProgram => {
            return (saleProgram.PartnerInstallmentID == PartnerInstallmentID) ?
                { ...saleProgram, broadcastSaleProgramData, dataRewardInstallmentBC } : saleProgram
        })
        const updatedPartnerSendData = newBroadcastSaleProgram.reduce((current, element) => {
            if (!helper.IsEmptyObject(element.broadcastSaleProgramData)) {
                return [...current, element.PartnerInstallmentID]
            } else return current
        }, [state.EPOSTransactionBO.PartnerInstallmentID])
        actionInstalment.setBroadcastPartnerList(updatedPartnerSendData);
        actionInstalment.updateBroadcastSaleProgram(newBroadcastSaleProgram)
    }

    const onRemoveBroadcastSaleProgram = (index) => {
        let newBroadcastSaleProgram = broadcastSaleProgram;
        newBroadcastSaleProgram[index].broadcastSaleProgramData = {};
        const updatedPartnerSendData = newBroadcastSaleProgram.reduce((current, element) => {
            if (!helper.IsEmptyObject(element.broadcastSaleProgramData)) {
                return [...current, element.PartnerInstallmentID]
            } else return current
        }, [state.EPOSTransactionBO.PartnerInstallmentID])
        actionInstalment.setBroadcastPartnerList(updatedPartnerSendData);
        actionInstalment.updateBroadcastSaleProgram(newBroadcastSaleProgram);
    }

    getBroadcastInfo = () => {
        const listPaperTypeID = selectedPaperType.map(element => element.PaperTypeID).join(",");
        showBlockUI();
        actionInstalment.getBroadcastInformation(state.EPOSTransactionBO, listPaperTypeID).then(
            (broadcastInfo) => {
                hideBlockUI();
                onGetBroadcastInformation(broadcastInfo);
                updatestate(broadcastInfo.objEPOSTransactionBO);
                // onNext();
            }
        ).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: getBroadcastInfo
                    }
                ]
            )
        })
    }

    getBroadcastInfoNew = () => {
        const listPaperTypeID = selectedPaperType.map(element => element.PaperTypeID).join(",");
        const data = {
            EPOSTransactionBO: state.EPOSTransactionBO,
            ListPaperTypeID: listPaperTypeID,
            times: isCreatedSt ? 1 : 2
        }
        showBlockUI();
        actionInstalment.getBroadcastInformationNew(data).then(
            (broadcastInfo) => {
                hideBlockUI();
                onGetBroadcastInformation(broadcastInfo);
                updatestate(broadcastInfo.objEPOSTransactionBO);
            }
        ).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => getBroadcastInfoNew()
                    }
                ]
            )
        })
    }

    const renderItem = ({ item, index }) => {
        const { PaperTypeID, PaperTypeName } = item;
        const isSelected = selectedPaperType.findIndex(element => element.PaperTypeID == PaperTypeID) != -1;
        return (
            <TouchableOpacity style={{
                marginTop: 10,
                height: constants.getSize(20),
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 10
            }}
                activeOpacity={0.7}
                onPress={() => {
                    setIsShowPartner(false);
                    if (isSelected) {
                        setSelectedPaperType(selectedPaperType.filter(element => element.PaperTypeID != PaperTypeID))
                    } else {
                        setSelectedPaperType([...selectedPaperType, item])
                    }
                }}
                disabled={PaperTypeID == 1}
            >
                <Icon
                    iconSet={"Feather"}
                    name={
                        isSelected ? "check-square"
                            : "square"
                    }
                    color={COLORS.ic147EFB}
                    size={18}
                />
                <MyText style={{
                    color: COLORS.txt333333,
                    marginLeft: constants.getSize(8),
                }}
                    text={PaperTypeName}
                />
            </TouchableOpacity>
        )
    }

    const renderSaleProgram = (item, index) => {
        const { PartnerInstallmentName, broadcastSaleProgramData, Warning } = item;
        const isSelectedBroadcastSaleProgram = !helper.IsEmptyObject(broadcastSaleProgramData);

        const checkPartnerStatus = () => {
            if (helper.IsNonEmptyString(Warning)) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    Warning,
                    [
                        { text: translate('common.btn_skip'), style: "default" },
                        { text: 'OK', style: "default", onPress: () => getSaleProgram(item, index) }
                    ]
                );
            }
            else {
                getSaleProgram(item, index)
            }
        }

        return (
            <View>
                <TouchableOpacity
                    key={index.toString()}
                    style={{
                        padding: 10,
                        backgroundColor: isSelectedBroadcastSaleProgram ? COLORS.btn4DA6FF : COLORS.btnFDF9E5,
                        width: constants.getSize(300),
                        borderColor: COLORS.bd4DA6FF,
                        borderRadius: 5,
                        borderWidth: 1,
                        marginTop: 5,
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderBottomStartRadius: isSelectedBroadcastSaleProgram ? 0 : 5,
                        borderBottomEndRadius: isSelectedBroadcastSaleProgram ? 0 : 5
                    }}
                    activeOpacity={0.7}
                    onPress={checkPartnerStatus}
                    disabled={isSelectedBroadcastSaleProgram}
                >
                    <MyText
                        text={PartnerInstallmentName}
                        style={{
                            color: isSelectedBroadcastSaleProgram ? COLORS.txtFFFFFF : COLORS.txt2C8BD7,
                            fontWeight: 'bold'
                        }}
                        addSize={1}
                    />
                </TouchableOpacity>
                {isSelectedBroadcastSaleProgram &&
                    <View style={{
                        width: constants.getSize(300),
                        borderColor: COLORS.bd4DA6FF,
                        borderWidth: 1,
                        borderTopWidth: 0,
                        borderBottomStartRadius: 5,
                        borderBottomEndRadius: 5,
                        padding: 10
                    }}>
                        <View style={{
                            flexDirection: 'row',
                            marginLeft: 5
                        }}>
                            <MyText
                                text={"ĐT: "}
                                style={{ color: COLORS.bg000000, fontSize: 16 }}
                            />
                            <MyText
                                text={item?.dataRewardInstallmentBC?.rewardPointStaffUser}
                                style={{ color: COLORS.txtFF0000, fontSize: 16 }}
                            />
                        </View>
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            paddingRight: constants.getSize(50)
                        }}>
                            <Icon
                                iconSet={"Ionicons"}
                                name={"checkmark-done-sharp"}
                                color={COLORS.ic147EFB}
                                size={20}
                            />
                            <MyText
                                text={broadcastSaleProgramData.saleProgram.SaleProgramName}
                                style={{
                                    fontWeight: 'bold',
                                    paddingLeft: 5,
                                    color: COLORS.txt333333,
                                    paddingRight: 20
                                }}
                            />
                        </View>
                        <FieldText
                            label={"Số tiền trả trước: "}
                            value={helper.formatMoney(broadcastSaleProgramData.prepaid)}
                        />
                        <FieldText
                            label={"Kỳ hạn vay: "}
                            value={broadcastSaleProgramData.termLoan.TermLoanName}
                        />
                        <FieldText
                            label={"Số tiền trả hàng tháng: "}
                            value={helper.formatMoney(broadcastSaleProgramData.monthlyPayment)}
                        />
                        <TouchableOpacity style={{
                            flexDirection: 'row',
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            alignItems: 'center',
                            backgroundColor: COLORS.bgEEEEEE,
                            borderBottomStartRadius: 5,
                            padding: 2
                        }}
                            activeOpacity={0.8}
                            onPress={() => {
                                onRemoveBroadcastSaleProgram(index)
                            }}
                        >
                            <Icon
                                iconSet={"Ionicons"}
                                name={"ios-trash"}
                                color={COLORS.icFF0000}
                                size={20}
                            />
                            <MyText
                                text={"Xóa"}
                                style={{
                                    fontWeight: 'bold',
                                    paddingLeft: 5,
                                    color: COLORS.txtFF0000
                                }}
                            />
                        </TouchableOpacity>
                    </View>}
            </View>
        )
    }

    const handleEPPartner = () => {
        showBlockUI();
        let data = {
            EPOSTransactionBO: state.EPOSTransactionBO,
            GroupPaperTypeInforBOList: null,
            times: isCreatedSt ? 1 : 2
        };
        dispatch(installmentAction.updateEPosTransactionNew(data))
            .then(res => {
                const EPOSTransactionBO = JSON.parse(res.EPOSTransactionBO);
                const { LinkPrintContract } = EPOSTransactionBO;
                const url = LinkPrintContract ?? `samsungfinance`;
                Linking.openURL(url).catch(() => {
                    // Alert.alert('', 'Vui lòng cài app SAMSUNG trước khi gọi.');
                    handleShowBottomSheet(url)
                });
                hideBlockUI()
            })
            .catch(error => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError ?? "Đã có lỗi xảy ra. Vui lòng thử lại!",
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => handleEPPartner()
                        }
                    ]
                );
            });

    }

    const handleShowBottomSheet = (url) => {
        showBottomSheet({
            component: <InnerQRContext data={{ url: url }} />,
            title: "Mã QR"
        });
    };

    return (
        <KeyboardAwareScrollView
            ref={refScroll0}
            style={{ width: constants.width }}
            contentContainerStyle={{ flexGrow: 1 }}
            enableResetScrollToCoords={false}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={60}
        >
            {/* <View style={{
                flex: 1,
                paddingVertical: 10,
                justifyContent: "space-between"
            }}> */}
            <View style={{
                width: constants.width,
                paddingHorizontal: 10
            }}>
                {
                    statusCode ?
                        <View style={{
                            marginTop: 10,
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                            <Icon
                                iconSet={'MaterialCommunityIcons'}
                                name={'information-outline'}
                                color={COLORS.txt147EFB}
                                size={20}
                            />
                            <View style={{
                                marginLeft: 5
                            }}>
                                <MyText
                                    text={statusCode ? statusCode : "Không tìm thấy trạng thái từ đối tác trả góp"}
                                    style={{
                                        color: COLORS.txt147EFB,
                                        fontStyle: 'italic',
                                        width: constants.width - 30,
                                    }}
                                />
                            </View>
                        </View>
                        :
                        null
                }
                <View style={styles.fieldSet}>
                    <MyText
                        text={translate('instalmentManager.loan_information')}
                        style={{ color: COLORS.txtFF0000, fontSize: 16 }}
                    />
                </View>
                <View style={[styles.fieldSet, { justifyContent: "center", alignItems: "center" }]}>
                    <MyText
                        text={`${state.EPOSTransactionBO.SaleProgramID} - ${state.EPOSTransactionBO.cus_SaleProgramName}`}
                        style={{
                            color: COLORS.txt147EFB,
                            fontSize: 18,
                            fontWeight: "bold",

                        }}
                    />
                </View>
                {!helper.IsEmptyObject(dataRewardInstallment) &&
                    <View style={{
                        flexDirection: 'row',
                        alignSelf: 'flex-end'
                    }}>
                        <MyText
                            text={"ĐT: "}
                            style={{ color: COLORS.bg000000, fontSize: 16 }}
                        />
                        <MyText
                            text={!helper.IsEmptyObject(dataRewardInstallment) ? dataRewardInstallment.rewardPointStaffUser : 0}
                            style={{ color: COLORS.txtFF0000, fontSize: 16 }}
                        />
                    </View>
                }
                <View style={{ marginVertical: 5 }}>
                    <TitleInputMoney
                        title={translate('instalmentManager.total_order_amount')}
                        style={{
                            borderWidth: 1,
                            borderRadius: 4,
                            borderColor: COLORS.bdCCCCCC,
                            backgroundColor: COLORS.bgFFFFFF,
                            color: COLORS.txt333333,
                            height: 40,
                            marginBottom: 5,
                            paddingHorizontal: 10,
                            width: constants.width - 20,
                            backgroundColor: COLORS.bgF0F0F0
                        }}
                        placeholder={"0"}
                        value={state.EPOSTransactionBO.Debt}
                        onChange={(text) => {
                            if (installmentHelper.isValidatePrice(text)) {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        Debt: text
                                    },
                                });
                            }
                        }}
                        editable={false}
                    />
                </View>
                {installmentHelper.CheckShowField('TotalPrepaid') ? (
                    <View>
                        <TitleInputMoney
                            title={translate('instalmentManager.amount_of_prepayment')}
                            percent={percent.prepaidPercentage}
                            isRequired={installmentHelper.CheckIsRequiredField('TotalPrepaid')}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                backgroundColor: COLORS.bgFFFFFF,
                                color: COLORS.txt333333,
                                height: 40,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                width: constants.width - 20,
                            }}
                            placeholder={"0"}
                            value={state.EPOSTransactionBO.TotalPrepaid}
                            maxValue={state.EPOSTransactionBO.Debt}
                            onChange={(value) => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        TotalPrepaid: value
                                    }
                                })
                            }}
                            onBlur={() => {
                                // let prePaid = installmentHelper.convertString2Number(state.EPOSTransactionBO.TotalPrepaid);
                                // if (prePaid < PrepaidPercent) {
                                //     prePaid = PrepaidPercent;
                                // }
                                // else if (prePaid > PrepaidPercentMax) {
                                //     prePaid = PrepaidPercentMax;
                                // }
                                // else {
                                //     prePaid = (Math.round(prePaid / 1000) * 1000);
                                // }
                                setIsUseEffect(true);
                                getPaymentMonthly();
                            }}
                            editable={!state.SaleProgramInfo.IsCardPartner}
                            key={"TotalPrePaid"}
                        />
                        {IsShowMinMaxPercen ? (
                            <MyText
                                text={`${translate('instalmentManager.min_deposit')} ${state.SaleProgramInfo.PrepaidPercent}% - ${translate('instalmentManager.max_deposit')} ${state.SaleProgramInfo.PrepaidPercentMax}%)`}
                                style={{ color: COLORS.txtFF0000, paddingVertical: 5, paddingTop: 0, fontSize: 12, alignSelf: 'flex-end', fontStyle: 'italic' }}
                            />
                        ) : null}
                    </View>
                ) : null}
                {installmentHelper.CheckShowField('TermLoan') ? (
                    <View style={{ marginTop: 3 }}>
                        {
                            helper.isArray(TermLoanList) &&
                            <Picker
                                style={{
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 40,
                                    borderRadius: 4,
                                    borderWidth: 1,
                                    borderColor: COLORS.bdE4E4E4,
                                    marginBottom: 10,
                                    width: constants.width - 20,
                                    backgroundColor: COLORS.btnFFFFFF
                                }}
                                label={"TermLoanName"}
                                value={"TermLoanNumber"}
                                defaultLabel={translate('instalmentManager.loan_term')}
                                valueSelected={state.EPOSTransactionBO.TermLoan}
                                data={TermLoanList}
                                onChange={(item) => {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            TermLoan: item.TermLoanNumber
                                        }
                                    });
                                    setIsUseEffect(true);
                                }}
                                numColumns={4}
                                title={translate('instalmentManager.loan_term')}
                                isRequired={true}
                            />
                        }
                    </View>
                ) : null}
                <View>
                    <InsuranceInfo
                        dataFee={state.EPOSTransactionBO.client_InsuranceFeesBO}
                        dataMasterGood={state.EPOSTransactionBO.client_MasterGoodsInsuranceBO}
                        isCheckFee={IsSelectedInsuranceFees}
                        isCheckGood={IsSelectedGoodsInsurance}
                        goodId={GoodsInsuranceID}
                        onPressFee={() => {
                            setState({
                                ...state,
                                EPOSTransactionBO: {
                                    ...state.EPOSTransactionBO,
                                    client_InsuranceFeesBO: {
                                        ...state.EPOSTransactionBO.client_InsuranceFeesBO,
                                        IsSelectedInsuranceFees: !IsSelectedInsuranceFees
                                    }
                                },
                            });
                            setIsUseEffect(true);
                        }}
                        onPressGood={(goodID) => {
                            let newGoodsInsuranceList = GoodsInsuranceList.map(el => (
                                el.GoodsInsuranceID === goodID ? { ...el, IsSelected: true } : { ...el, IsSelected: false }
                            ))
                            setGoodsInsuranceID(goodID);
                            setState({
                                ...state,
                                EPOSTransactionBO: {
                                    ...state.EPOSTransactionBO,
                                    client_MasterGoodsInsuranceBO: {
                                        ...state.EPOSTransactionBO.client_MasterGoodsInsuranceBO,
                                        IsSelectedGoodsInsurance: !IsSelectedGoodsInsurance,
                                        GoodsInsuranceList: newGoodsInsuranceList,
                                    }
                                },
                            });
                            setIsUseEffect(true);
                        }}
                        onPressInsurance={(value) => {
                            let newGoodsInsuranceList = GoodsInsuranceList.map(el => (
                                el.GoodsInsuranceID === value ? { ...el, IsSelected: true } : { ...el, IsSelected: false }
                            ))
                            setGoodsInsuranceID(value);
                            setState({
                                ...state,
                                EPOSTransactionBO: {
                                    ...state.EPOSTransactionBO,
                                    client_MasterGoodsInsuranceBO: {
                                        ...state.EPOSTransactionBO.client_MasterGoodsInsuranceBO,
                                        GoodsInsuranceList: newGoodsInsuranceList,
                                    }
                                },
                            });
                            setIsUseEffect(true);
                        }}
                    />
                </View>
                {
                    state.EPOSTransactionBO?.cus_MessageGoodsInsuranceChanged != null && (
                        <View style={{
                            padding: 5,
                            flexDirection: 'row',
                            width: '99%'
                        }}>
                            <MyText
                                text={state.EPOSTransactionBO?.cus_MessageGoodsInsuranceChanged}
                                style={{
                                    color: COLORS.txtFF0000,
                                    fontSize: 12,
                                    fontStyle: 'italic'
                                }}
                            />
                        </View>
                    )
                }
                {installmentHelper.CheckShowField('MonthlyPayment') ? (
                    <View>
                        <TitleInputMoney
                            title={translate('instalmentManager.monthly_payment_amount')}
                            isRequired={installmentHelper.CheckIsRequiredField('MonthlyPayment')}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                backgroundColor: COLORS.bgFFFFFF,
                                color: COLORS.txt333333,
                                height: 40,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                width: constants.width - 20,
                                backgroundColor: COLORS.bgF0F0F0,
                            }}
                            value={state.EPOSTransactionBO.MonthlyPayment}
                            editable={false}
                            key={"MonthlyPayment"}
                        // placeholder={""}
                        />
                    </View>
                ) : null}
                <View
                    style={{
                        height: 2,
                        backgroundColor: COLORS.bgE4E4E4,
                        marginVertical: 10,
                    }}
                />
            </View>
            {
                state.EPOSTransactionBO.PartnerInstallmentID == PARTNER_ID.SAMSUNG ? <TouchableOpacity
                    style={{
                        backgroundColor: COLORS.bd4DA6FF,
                        marginHorizontal: 10,
                        padding: 10,
                        alignItems: "center",
                        borderRadius: 5,
                        borderWidth: 1,
                        borderColor: COLORS.bd4DA6FF,
                        width: "50%",
                        alignSelf: "flex-end"

                    }}
                    activeOpacity={0.7}
                    onPress={handleEPPartner}
                >
                    <MyText text={"Nhập liệu hồ sơ trả góp"} style={{ fontWeight: "bold", color: COLORS.txtFFFFFF }} />
                </TouchableOpacity> : <>
                    {
                        (state.EPOSTransactionBO.EPOSStatusID != 0) &&
                        (
                            <TouchableOpacity style={{
                                width: constants.width,
                                padding: 10,
                                flexDirection: 'row',
                                alignItems: 'center'
                            }}
                                activeOpacity={0.7}
                                onPress={() => {
                                    setIsBroadCast(!isBroadCast);
                                    setIsShowPaperType(true);
                                    setIsShowPartner(false);
                                    setSelectedPaperType([{
                                        "PaperTypeID": 1,
                                        "PaperTypeName": "CMND/CCCD",
                                        "IsActived": true
                                    }]);
                                    actionInstalment.setBroadcastPartnerList([]);
                                    if (isBroadCast) {
                                        onGetBroadcastInformation(initialData);
                                    }
                                }}
                                disabled={stateBroadcastPaperType.isFetching || stateBroadcastSaleProgram.isFetching}
                            >
                                <Icon
                                    iconSet={"Feather"}
                                    name={
                                        isBroadCast ? "check-square"
                                            : "square"
                                    }
                                    color={COLORS.ic147EFB}
                                    size={18}
                                />
                                <MyText style={{
                                    color: COLORS.txt147EFB,
                                    marginLeft: constants.getSize(8),
                                    fontStyle: "italic",
                                    fontWeight: "bold",
                                    textDecorationLine: 'underline'
                                }}
                                    text={`Gửi hồ sơ trả góp cho nhiều công ty tài chính`}
                                />
                            </TouchableOpacity>
                        )
                    }

                    {isBroadCast &&
                        <View style={{
                            width: constants.width
                        }}>
                            <BaseLoading
                                isLoading={stateBroadcastPaperType.isFetching}
                                isError={stateBroadcastPaperType.isError}
                                isEmpty={stateBroadcastPaperType.isEmpty}
                                textLoadingError={stateBroadcastPaperType.description}
                                onPressTryAgains={actionInstalment.getBroadcastPaperType}
                                content={
                                    helper.IsNonEmptyArray(broadcastPaperType) && <View style={{
                                        flex: 1,
                                        justifyContent: 'center',
                                        paddingVertical: 10,
                                        alignItems: 'center'
                                    }}>
                                        <View style={{
                                            justifyContent: 'center',
                                            alignItems: "center",
                                            borderRadius: 10,
                                            marginBottom: 5,
                                            alignContent: 'center',
                                            shadowColor: "#000",
                                            shadowOffset: {
                                                width: 0,
                                                height: 2
                                            },
                                            shadowOpacity: 0.25,
                                            shadowRadius: 4,
                                            elevation: 5
                                        }}>
                                            <TouchableOpacity
                                                style={{
                                                    width: constants.width - 20,
                                                    padding: 10,
                                                    backgroundColor: COLORS.bg00A98F,
                                                    borderTopStartRadius: 10,
                                                    borderTopEndRadius: 10,
                                                    flexDirection: 'row',
                                                    justifyContent: 'space-between'
                                                }}
                                                activeOpacity={0.9}
                                                onPress={() => setIsShowPaperType(!isShowPaperType)}
                                            >
                                                <MyText
                                                    text={`Giấy tờ khách hàng có${isShowPaperType ? "" : ` (${selectedPaperType.length}/${broadcastPaperType.length})`}:`}
                                                    addSize={2}
                                                    style={{
                                                        fontWeight: 'bold',
                                                        color: COLORS.txtFFFDEF
                                                    }}
                                                />
                                                <Animated.View style={{ transform: [{ rotateX: rotateValue }] }}>
                                                    <Icon
                                                        iconSet={"Ionicons"}
                                                        name={"chevron-down"}
                                                        color={COLORS.icFFFFBC}
                                                        size={20}
                                                    />
                                                </Animated.View>
                                            </TouchableOpacity>
                                            {isShowPaperType ?
                                                <View style={{
                                                    backgroundColor: COLORS.bgFFFFFF,
                                                    paddingVertical: 3,
                                                    width: constants.width - 20,
                                                    height: paperTypeViewHeight,
                                                    borderBottomStartRadius: 10,
                                                    borderBottomEndRadius: 10
                                                }}
                                                >
                                                    <FlatList
                                                        data={broadcastPaperType}
                                                        keyExtractor={(item, index) => index.toString()}
                                                        renderItem={renderItem}
                                                        bounces={false}
                                                        scrollEventThrottle={16}
                                                        nestedScrollEnabled={true}
                                                        persistentScrollbar={true}
                                                    />
                                                </View>
                                                :
                                                <View style={{
                                                    backgroundColor: COLORS.bgFFFFFF,
                                                    paddingVertical: 3,
                                                    width: constants.width - 20,
                                                    borderBottomStartRadius: 10,
                                                    borderBottomEndRadius: 10,
                                                    flexWrap: 'wrap',
                                                    flexDirection: 'row',
                                                    padding: 5
                                                }}>
                                                    {
                                                        selectedPaperType.map(item => (
                                                            <View style={{
                                                                padding: 5,
                                                                margin: 3,
                                                                backgroundColor: COLORS.bgFFFFFF,
                                                                borderColor: COLORS.bd288AD6,
                                                                borderRadius: 5,
                                                                borderWidth: StyleSheet.hairlineWidth
                                                            }}
                                                                key={item.PaperTypeID.toString()}
                                                            >
                                                                <MyText
                                                                    text={item.PaperTypeName}
                                                                />
                                                            </View>
                                                        ))
                                                    }
                                                </View>
                                            }
                                        </View>
                                        {isShowPaperType && <TouchableOpacity
                                            style={{
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                paddingHorizontal: 10,
                                                paddingVertical: 6,
                                                backgroundColor: COLORS.btnDDF4F1,
                                                borderRadius: 5,
                                                borderColor: COLORS.bd147EFB,
                                                borderWidth: 1,
                                                marginTop: 10
                                            }}
                                            onPress={getPartner}
                                            activeOpacity={0.7}
                                        >
                                            <Icon
                                                iconSet={"Ionicons"}
                                                name={"md-search"}
                                                color={COLORS.ic147EFB}
                                                size={18}
                                            />
                                            <MyText style={{
                                                color: COLORS.txt333333,
                                                marginLeft: constants.getSize(8),
                                                fontWeight: "bold"
                                            }}
                                                text={"Tìm kiếm chương trình trả góp"}
                                            />
                                        </TouchableOpacity>}
                                        {isShowPartner && <BaseLoading
                                            isLoading={stateBroadcastSaleProgram.isFetching}
                                            isError={stateBroadcastSaleProgram.isError}
                                            isEmpty={stateBroadcastSaleProgram.isEmpty}
                                            textLoadingError={stateBroadcastSaleProgram.description}
                                            onPressTryAgains={getPartner}
                                            content={
                                                <View style={{
                                                    justifyContent: 'center',
                                                    alignItems: "center",
                                                    borderRadius: 10,
                                                    marginTop: 10,
                                                    alignContent: 'center',
                                                    shadowColor: "#000",
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2
                                                    },
                                                    shadowOpacity: 0.25,
                                                    shadowRadius: 4,
                                                    elevation: 5
                                                }}>
                                                    <View
                                                        style={{
                                                            width: constants.width - 20,
                                                            padding: 10,
                                                            backgroundColor: COLORS.bg00A98F,
                                                            borderTopStartRadius: 10,
                                                            borderTopEndRadius: 10,
                                                            flexDirection: 'row',
                                                            justifyContent: 'space-between'
                                                        }}
                                                    >
                                                        <MyText
                                                            text={"Chọn thêm các chương trình trả góp khác để gửi hồ sơ:"}
                                                            addSize={2}
                                                            style={{
                                                                fontWeight: 'bold',
                                                                color: COLORS.txtFFFDEF
                                                            }}
                                                        />
                                                    </View>
                                                    <View style={{
                                                        backgroundColor: COLORS.bgFFFFFF,
                                                        width: constants.width - 20,
                                                        borderBottomStartRadius: 10,
                                                        borderBottomEndRadius: 10,
                                                        alignItems: 'center',
                                                        paddingBottom: 5
                                                    }}>
                                                        {broadcastSaleProgram.map(renderSaleProgram)}
                                                    </View>
                                                </View>
                                            }
                                        />}
                                    </View>
                                }
                            />
                            <View style={{
                                width: constants.width,
                                padding: 10,
                                flexDirection: 'row',
                                backgroundColor: COLORS.bgDDF4F1,
                                marginBottom: 10
                            }}>
                                <Icon
                                    iconSet={"Feather"}
                                    name={"alert-circle"}
                                    color={COLORS.ic147EFB}
                                    size={18}
                                />
                                <View style={{
                                    paddingLeft: 5,
                                    width: constants.width - 33,
                                    flexDirection: 'row'
                                }}>
                                    <MyText style={{
                                        color: COLORS.txt555555,
                                    }}
                                        text={"Bạn đang thu thập thông tin khách hàng để gửi cho "}
                                    >
                                        <MyText style={{
                                            fontWeight: 'bold',
                                            color: COLORS.txtFF0000,
                                        }}
                                            text={partnerSendData.length}>
                                            <MyText style={{
                                                color: COLORS.txt555555,
                                            }}
                                                text={" công ty tài chính "}>
                                                <MyText style={{
                                                    fontWeight: 'bold',
                                                    color: COLORS.txtFF0000,
                                                }}
                                                    text={partnerSendData.map(id => GetPartnerInstallmentName(id)).join(", ")}>
                                                </MyText>
                                            </MyText>
                                        </MyText>
                                    </MyText>
                                </View>
                            </View>
                        </View>
                    }

                    <View
                        style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            paddingHorizontal: 20,
                            marginBottom: 30,
                        }}
                    >
                        <TouchableOpacity
                            style={[styles.btn, { backgroundColor: COLORS.btnFFFFFF, }]}
                            activeOpacity={0.7}
                            onPress={() => {
                                updateprevstate(state.EPOSTransactionBO);
                                onPrev();
                            }}
                        >
                            <MyText text={translate('instalmentManager.record_manager')} style={{ fontWeight: "bold", backgroundColor: COLORS.btnFFFFFF, color: COLORS.txt4DA6FF }} />
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[styles.btn, { backgroundColor: COLORS.btn2C8BD7, opacity: (broadcastPartnerList.length <= 1 && isBroadCast) ? 0.6 : 1, }]}
                            activeOpacity={0.7}
                            disabled={(broadcastPartnerList.length <= 1 && isBroadCast)}
                            onPress={() => {
                                if (checkValidateData()) {
                                    const storeIDNew = helper.checkConfigStoreInstallmentNew(storeID);
                                    if (isBroadCast && storeIDNew == false) {
                                        getBroadcastInfo();
                                    } else if (isBroadCast && storeIDNew == true) {
                                        getBroadcastInfoNew();
                                    } else {
                                        updatestate(state.EPOSTransactionBO);
                                        onNext();
                                    }
                                }
                            }}
                        >
                            <MyText text={translate('common.btn_continue')} style={styles.txtBtn}
                            />
                        </TouchableOpacity>
                    </View>
                </>
            }
            {isVisibleModal && <ModalSaleProgram
                isVisible={isVisibleModal}
                hideModal={() => {
                    setIsVisibleModal(false);
                }}
                saleProgramData={saleProgramData}
                actionInstalment={actionInstalment}
                stateBroadcastLoanPackage={stateBroadcastLoanPackage}
                EPOSTransactionBO={state.EPOSTransactionBO}
                onSelectSaleProgram={onSelectBroadcastSaleProgram}
                selectedPaperType={selectedPaperType}
            />}
        </KeyboardAwareScrollView>
    );
}

const mapStateToProps = function (state) {
    return {
        broadcastPaperType: state.InstallmentReducer.broadcastPaperType,
        stateBroadcastPaperType: state.InstallmentReducer.stateBroadcastPaperType,
        broadcastSaleProgram: state.InstallmentReducer.broadcastSaleProgram,
        stateBroadcastSaleProgram: state.InstallmentReducer.stateBroadcastSaleProgram,
        stateBroadcastLoanPackage: state.InstallmentReducer.stateBroadcastLoanPackage,
        broadcastPartnerList: state.InstallmentReducer.broadcastPartnerList,
        dataRewardInstallmentBC: state.InstallmentReducer.dataRewardInstallmentBC,
        dataUserCode: state.InstallmentReducer.dataUserCode
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInstalment: bindActionCreators(actionInstalmentCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(DetailInstallment);

const getSaleProgramInfo = (obj) => {
    let saleProgramInfo = {};
    if (helper.isObject(obj.SaleProgramInfo)) {
        saleProgramInfo = { ...obj.SaleProgramInfo };
    }
    return saleProgramInfo;
}

const getInsuranceFees = (obj) => {
    let insuranceFees = {};
    if (helper.isObject(obj.EPOSTransactionBO.client_InsuranceFeesBO)) {
        insuranceFees = { ...obj.EPOSTransactionBO.client_InsuranceFeesBO };
    }
    return insuranceFees;
}

const getMasterGoodsInsurance = (obj) => {
    let masterGoodsInsurance = {};
    if (helper.isObject(obj.EPOSTransactionBO.client_MasterGoodsInsuranceBO)) {
        masterGoodsInsurance = { ...obj.EPOSTransactionBO.client_MasterGoodsInsuranceBO };
    }
    return masterGoodsInsurance;
}

const convertString2Number = (stringValue) => {
    return stringValue ? parseFloat(stringValue) : 0
}

const GetPartnerInstallmentName = (PartnerInstallmentID) => {
    switch (PartnerInstallmentID) {
        case 1:
            return "ACS"
        case 2:
            return "HC"
        case 3:
            return "FE"
        case 10:
            return "MC"
        case 15:
            return "MAFC"
        default:
            return ""
    }
}

const styles = StyleSheet.create({
    con_header: {
        backgroundColor: COLORS.bg19A796,
        height: constants.getSize(40),
        justifyContent: "center",
        flexDirection: "row",
        marginBottom: 5,
        paddingLeft: 10
    },
    goBack: {
        height: constants.getSize(40),
        width: constants.getSize(40),
        justifyContent: "center"
    },
    img_goBack: {
        alignSelf: "center",
        height: constants.getSize(13),
        width: constants.getSize(13),
        tintColor: COLORS.imgFFFFFF
    },
    header_title: {
        flex: 1,
        justifyContent: "center"
    },
    fieldSet: {
        flexDirection: "row",
        marginVertical: 5,
        justifyContent: "space-between",
    },
    view_picker: {
        borderWidth: 1,
        borderColor: COLORS.bdCCCCCC,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        height: "auto",
        backgroundColor: COLORS.bgFFFFFF,
    },
    picker: {
        height: 30,
        marginVertical: 5,
        marginLeft: -5
    },
    btn: {
        padding: 10,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        width: "47%",
        borderWidth: 1,
        borderColor: COLORS.bd4DA6FF,
        //margin: 10
    },
    txtBtn: {
        fontWeight: "bold",
        color: COLORS.txtFFFFFF,
    },
});

const FieldText = ({ label, value }) => {
    return (
        <View style={{
            paddingVertical: 2,
            flexDirection: 'row'
        }}>
            <MyText style={{
                color: COLORS.txt333333,
                paddingLeft: 10
            }}
                text={label}
            />
            <MyText style={{
                fontWeight: "bold",
                color: COLORS.txtFF0000,
            }}
                text={value}
            />
        </View>
    );
}
