import {
    MyText,
    TextAreaInput,
    CaptureCamera,
    Icon,
    showBlockUI,
    hideBlockUI
} from "@components";
import { ImageProcess, ImageVideoProcess } from '../Inventory/ReportProductState/components';
import React, { useState, useEffect } from "react";
import { useSelector } from 'react-redux';
import {
    Image,
    TouchableOpacity,
    View,
    Alert,
    Platform,
    PermissionsAndroid,
    ScrollView,
    SafeAreaView
} from "react-native";
import { translate } from "@translate";
import { COLORS } from "@styles";
import { constants } from "@constants";
import { helper } from "@common";
import { launchImageLibrary, launchCamera } from "react-native-image-picker";
import { sendFeedback } from './action';
import { CameraRoll } from "@react-native-camera-roll/camera-roll";
import RNFS from 'react-native-fs'
import ImageResizer from 'react-native-image-resizer';

const ScreenshotFeedback = (props) => {
    const { navigation } = props
    const [isVisibleCamera, setIsVisibleCamera] = useState(false)
    const [response, setResponse] = useState("")
    const [newImage, setNewImage] = useState("")
    const [issue, setIssue] = useState("");
    const [imageUrls, setImageUrls] = useState([]);
    const currentAttachments = imageUrls;
    const userInfo = useSelector((state) => state.userReducer);

    useEffect(() => {
        getLatestScreenshot()
    }, [])

    const takePicture = () => {
        launchCamera(
            {
                mediaType: 'photo',
                cameraType: 'back',
                quality: 1,
            },
            (response) => {
                if (helper.hasProperty(response, 'uri')) {
                    showBlockUI();
                    helper.resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            const newImage = {
                                uri: uri,
                                path: path,
                                name: name,
                                type: 'image/jpg',
                                isDeleted: 0,
                                realProductStatusAttachId: 0
                            };
                            setImageUrls(prevListImage => [...prevListImage, newImage]);
                            hideBlockUI();
                            setIsVisibleCamera(false)
                        }).catch((error) => {
                            console.log("Error upload picture");
                            Alert.alert('', error, [
                                {
                                    text: 'OK',
                                    onPress: hideBlockUI
                                }
                            ]);
                        });
                }
                hideBlockUI();
            }
        );
    }

    const selectPicture = () => {
        launchImageLibrary(
            {
                mediaType: "mixed",
                quality: 1,
            },
            (response) => {
                if (helper.hasProperty(response, 'uri')) {
                    showBlockUI();
                    helper.resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            const newImage = {
                                uri: uri,
                                path: path,
                                name: name,
                                type: 'image/jpg',
                                isDeleted: 0,
                                realProductStatusAttachId: 0
                            };
                            setImageUrls(prevListImage => [...prevListImage, newImage]);
                            hideBlockUI();
                            setIsVisibleCamera(false)
                        }).catch((error) => {
                            console.log("Error upload picture");
                            Alert.alert('', error, [
                                {
                                    text: 'OK',
                                    onPress: hideBlockUI
                                }
                            ]);
                        });
                }
                hideBlockUI();
            }
        );
    }

    const getFileExtension = (uri) => {
        return uri.split('.').pop();
    }
    const openCameraForVideo = () => {
        launchCamera(
            {
                mediaType: 'video',
                videoQuality: 'medium',
                durationLimit: 15,
                saveToPhotos: false,

            }, (response) => {
                if (helper.hasProperty(response, 'uri')) {
                    showBlockUI();
                    const fileType = getFileExtension(response?.uri) == 'MOV' ? 'video/quicktime' : 'video/mp4'
                    const fileName = `video_${Date.now()}.${getFileExtension(response?.uri)}`;
                    const newImage = {
                        uri: response?.uri,
                        path: response?.uri,
                        name: fileName,
                        type: fileType,
                        isDeleted: 0,
                        realProductStatusAttachId: 0
                    };
                    setResponse(response)
                    setNewImage(newImage)
                    setImageUrls(prevListImage => [...prevListImage, newImage]);
                    hideBlockUI();
                }
                hideBlockUI();
            })
    }

    const send_feedback = () => {
        const payload = {
            "createdUserName": userInfo.userName,
            "content": issue,
            "fromSource": "MWG POS"
        };
        sendFeedback(imageUrls, JSON.stringify(payload)).then((result) => {
            Alert.alert("", `Tạo yêu cầu góp ý thành công, mã Ticket: ${result.ticketID}`, [{
                text: 'OK',
                onPress: () => {
                    hideBlockUI()
                    navigation.goBack()
                }
            }]);
        }).catch(msgError => {
            Alert.alert('', msgError, [
                {
                    text: 'OK',
                    onPress: hideBlockUI
                }
            ]);
        });
    };

    const deleteImage = (index) => {
        currentAttachments[index].isDeleted = 1
        setImageUrls(prevListImage => prevListImage.filter(image => image.isDeleted !== 1));
    };

    const requestPermission = async () => {
        if (Platform.OS === "android") {
            return await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES);
        }
        return true; // iOS asks automatically
    };

    const getLatestScreenshot = async () => {
        const hasPermission = await requestPermission();
        if (!hasPermission) return;
        try {
            const photos = await CameraRoll.getPhotos({
                first: 1,
                assetType: 'Photos',
                include: ['imageSize']
            });

            if (photos.edges.length > 0) {
                helper.resizeImage(photos.edges[0].node.image).then(({ path, uri, name }) => {
                    const newImage = {
                        uri: uri,
                        path: path,
                        name: name,
                        type: 'image/jpg',
                        isDeleted: 0,
                        realProductStatusAttachId: 0
                    };
                    setImageUrls([newImage]);
                }).catch((error) => {
                    console.log("error resize image", error);
                });
            }
        } catch (error) {
            console.log("Error get latest image", error);
        }
    };

    return (
        <SafeAreaView
            style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF
            }}>
            <ScrollView
                keyboardShouldPersistTaps={'never'}
                style={{ flex: 1 }}
            >
                <View style={{
                    marginTop: 18,
                    marginLeft: 12
                }}>
                    <TextAreaInput
                        styleInput={{
                            borderRadius: 10,
                            backgroundColor: '#F5F7FA',
                            marginVertical: 10,
                            paddingHorizontal: 10,
                            width: constants.width - 30
                        }}
                        underlineColorAndroid={'transparent'}
                        placeholder={translate('feedback.enter_problem')}
                        value={issue}
                        onChangeText={(text) => {
                            setIssue(text);
                        }}
                        width={constants.width - 30}
                        returnKeyType="default"
                        blurOnSubmit={false}
                        height={100}
                        multiline
                        maxLength={500}
                        clearText={() => {
                            setIssue("");
                        }}
                        isFocus={true}
                    />
                </View>
                <View
                    style={{
                        marginTop: 18,
                        marginLeft: 12
                    }}>
                    <View style={{
                        flexDirection: 'row'
                    }}>
                    </View>
                    <View style={{
                        flexWrap: 'wrap',
                        width: constants.width,
                        alignSelf: 'center',
                        marginRight: 12,
                        flexDirection: 'row'
                    }}>
                        {imageUrls.map((url, index) =>
                            <ImageVideoProcess
                                key={index}
                                index={index}
                                urlImageLocal={url.uri}
                                urlImageRemote={url.imageURL}
                                type={url.type}
                                deleteImage={() => {
                                    deleteImage(index);
                                }}
                            />
                        )}
                    </View>
                    {
                        imageUrls.length < 3 && <View style={{
                            width: constants.width / 2,
                            alignItems: 'center',
                            justifyContent: 'space-around',
                            paddingVertical: 5,
                            flexDirection: 'row'
                        }}>
                            <TouchableOpacity
                                style={{
                                    height: 40,
                                    width: 40,
                                    borderRadius: 20,
                                    backgroundColor: '#F5F7FA',
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}
                                activeOpacity={0.8}
                                onPress={() => selectPicture()}>
                                <Icon
                                    iconSet={"Ionicons"}
                                    name={"image"}
                                    color={COLORS.bg00AAFF}
                                    size={25} />
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={{
                                    height: 40,
                                    width: 40,
                                    borderRadius: 20,
                                    backgroundColor: '#F5F7FA',
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}
                                activeOpacity={0.8}
                                onPress={() => takePicture()}>
                                <Icon
                                    iconSet={"Ionicons"}
                                    name={"camera"}
                                    color={COLORS.bgB9DCA4}
                                    size={28} />
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={{
                                    height: 40,
                                    width: 40,
                                    borderRadius: 20,
                                    backgroundColor: '#F5F7FA',
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}
                                activeOpacity={0.8}
                                onPress={() => openCameraForVideo()}>
                                <Icon
                                    iconSet={"Ionicons"}
                                    name={"videocam"}
                                    color={COLORS.bgFFC0CB}
                                    size={28} />
                            </TouchableOpacity>
                            {/* <CaptureCamera
                            isVisibleCamera={isVisibleCamera}
                            takePicture={takePicture}
                            closeCamera={() => { setIsVisibleCamera(false); }}
                        // selectPicture={selectPicture}
                        /> */}
                        </View>}

                </View>
            </ScrollView>
            <TouchableOpacity
                style={{
                    marginVertical: 10,
                    width: constants.width - 15,
                    height: 45,
                    backgroundColor: issue ? '#0967D2' : '#E4E7EB',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignSelf: 'center',
                    borderRadius: 13,
                }}
                activeOpacity={0.6}
                disabled={issue ? false : true}
                onPress={() => { send_feedback() }}>
                <MyText
                    text={translate('feedback.send_feedback')}
                    style={{
                        fontSize: 18,
                        color: issue ? 'white' : '#616E7C'
                    }} />
            </TouchableOpacity>
        </SafeAreaView >
    );
};

export default ScreenshotFeedback;
