import { Icon } from '@components';
import { constants } from '@constants';
import { COLORS } from '@styles';
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Animatable } from 'react-native';

const ButtonTabs = ({ activeTab, setActiveTab, TabsOne, TabsTwo, TabsThree, TabsFour, disabled }) => {
    return (
        <View style={{
            width: constants.width,
            height: 50,
            alignItems: 'center',
            backgroundColor: COLORS.bgFFFFFF
        }}>
            <ScrollView
                bounces={false}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{
                    flexDirection: 'row',
                    alignSelf: 'center',
                    height: 50,
                    flexGrow: 1
                }}>
                <TouchableOpacity
                    disabled={disabled}
                    onPress={() => setActiveTab(0)}
                    style={{
                        width: 200,
                        height: 45,
                        backgroundColor: activeTab === 0 ? COLORS.bg00A98F : '#96D9BC',
                        justifyContent: 'center',
                        borderColor: activeTab === 0 ? COLORS.bgFFFFFF : COLORS.bgFFFFFF,
                        borderRightWidth: activeTab === 0 ? 0 : 1,
                    }}>

                    <Text style={{
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: activeTab === 0 ? COLORS.bgFFFFFF : COLORS.bgFFFFFF,
                        fontSize: 14
                    }}>{TabsOne}</Text>
                    <View style={{
                        position: 'absolute',
                        alignSelf: 'center',
                        bottom: -11
                    }}>
                    </View>
                </TouchableOpacity>

                <TouchableOpacity
                    disabled={disabled}
                    onPress={() => setActiveTab(1)} style={{
                        width: 200,
                        height: 45,
                        backgroundColor: activeTab === 1 ? COLORS.bg00A98F : '#96D9BC',
                        borderWidth: activeTab === 1 ? 0 : 0,
                        borderColor: activeTab === 1 ? COLORS.bgFFFFFF : COLORS.bgFFFFFF,
                        justifyContent: 'center',
                        borderRightWidth: activeTab === 1 ? 0 : 1,
                        borderLeftWidth: activeTab === 1 ? 0 : 1
                    }}>
                    <Text style={{
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: activeTab === 1 ? COLORS.bgFFFFFF : COLORS.bgFFFFFF,
                        fontSize: 14
                    }}>{TabsTwo}</Text>
                    <View style={{
                        position: 'absolute',
                        alignSelf: 'center',
                        bottom: -11
                    }}>
                    </View>
                </TouchableOpacity>

                <TouchableOpacity
                    disabled={disabled}
                    onPress={() => setActiveTab(2)} style={{
                        width: 200,
                        height: 45,
                        backgroundColor: activeTab === 2 ? COLORS.bg00A98F : '#96D9BC',
                        borderWidth: activeTab === 2 ? 0 : 0,
                        borderColor: activeTab === 2 ? COLORS.bgFFFFFF : COLORS.bgFFFFFF,
                        justifyContent: 'center',
                        borderRightWidth: activeTab === 2 ? 0 : 1,
                        borderLeftWidth: activeTab === 2 ? 0 : 1
                    }}>
                    <Text style={{
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: activeTab === 2 ? COLORS.bgFFFFFF : COLORS.bgFFFFFF,
                        fontSize: 14
                    }}>{TabsThree}</Text>
                    <View style={{
                        position: 'absolute',
                        alignSelf: 'center',
                        bottom: -11
                    }}>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity
                    disabled={disabled}
                    onPress={() => setActiveTab(3)} style={{
                        width: 200,
                        height: 45,
                        backgroundColor: activeTab === 3 ? COLORS.bg00A98F : '#96D9BC',
                        borderWidth: activeTab === 3 ? 0 : 0,
                        borderColor: activeTab === 3 ? COLORS.bgFFFFFF : COLORS.bgFFFFFF,
                        justifyContent: 'center',
                        borderRightWidth: activeTab === 3 ? 0 : 1,
                        borderLeftWidth: activeTab === 3 ? 0 : 1
                    }}>
                    <Text style={{
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: activeTab === 3 ? COLORS.bgFFFFFF : COLORS.bgFFFFFF,
                        fontSize: 14
                    }}>{TabsFour}</Text>
                    <View style={{
                        position: 'absolute',
                        alignSelf: 'center',
                        bottom: -11
                    }}>
                    </View>
                </TouchableOpacity>
            </ScrollView>
        </View>
    );
};

export default ButtonTabs;
