import { <PERSON><PERSON><PERSON>, SafeAreaView, StyleSheet, Text, Animated, TouchableOpacity, View, Alert } from 'react-native'
import React, { useState, useEffect, useCallback } from 'react'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { constants } from '@constants'
import { BaseLoading, Icon, MyText, PickerSearch, hideBlockUI, showBlockUI } from '@components'
import { COLORS } from '@styles';
import moment from 'moment';
import ModalCalendar from '../../InstallmentManager/components/ModalCalendar';
import SearchInput from '../component/SearchInput';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionInsurancePVICreator from "../../InsurancePVI/action";
import * as actionSaleOrderCreator from "../../SaleOrderPayment/action";
import * as actionManagerSOCreator from "../../SaleOrderManager/action";
import * as actionCollectionManagerCreator from "../action";
import { translate } from '@translate';
import { useFocusEffect } from '@react-navigation/native';
import { helper } from '@common'
import * as actionInsuranceBrightsideCreator from "../action";

const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;

const HistoryEditEmei = ({
    stateSearchListHistoryEditEmei,
    dataSearchListHistoryEditEmei,
    actionInsurancePVI,
    dataInfo,
    route,
    itemCatalog,
    actionInsuranceBrightside
}) => {
    const {
        ServiceCategoryID,
        AirtimeServiceGroupID
    } = itemCatalog ?? {};
    const [filter, setFilter] = useState({
        scrollY: new Animated.Value(0),
        isIncome: 0,
        isCompleteInfo: 0,
        isDelivery: 0,
    });
    const [fromDate, setFromDate] = useState(new Date())
    const [toDate, setToDate] = useState(new Date())
    const [isShowCalendar, setIsShowCalendar] = useState(false);
    const [searchByInsurance, setSearchByInsurance] = useState('');
    const [providerInsurance, setProviderInsurance] = useState('');
    const [keyword, setkeyWork] = useState('');
    const [dataInsurance, setDataInsurance] = useState([])
    const [dataProvider, setProvider] = useState([])
    const [updateDataEditEmei, setupdateDataEditEmei] = useState([]);
    const [dataViewHTREditEmei, setdataViewHTREditEmei] = useState({});
    const [isShow, setIsShow] = useState(false);
    const diffClamp = Animated.diffClamp(filter.scrollY, 0, DISTANCE);
    const { SERVICEVOUCHERID } = route?.params ?? '';

    const translateY = diffClamp.interpolate({
        inputRange: [0, DISTANCE],
        outputRange: [0, -DISTANCE],
    });

    useFocusEffect(
        useCallback(() => {
            const data = {
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                isLoadSearchBy: true,
                isSearchByEdit: true
            };
            actionInsurancePVI.getServiceListHistory(data).then((response) => {
                setDataInsurance(response[0].SearchByList)
                setProvider(response[1].ProviderList)
            }).catch((err) => console.log(err));
        }, [actionInsurancePVI])
    );

    useEffect(() => {
        if (dataSearchListHistoryEditEmei?.length > 1) {
            setupdateDataEditEmei(dataSearchListHistoryEditEmei)
        }
    }, [dataSearchListHistoryEditEmei, actionInsurancePVI])

    useEffect(() => {
        getSearchHistoryEditEmei();
    }, [actionInsurancePVI, keyword, fromDate, toDate, searchByInsurance, providerInsurance])



    const getSearchHistoryEditEmei = () => {
        const data = {
            fromDate: new Date(fromDate),
            toDate: new Date(toDate),
            keyword: SERVICEVOUCHERID ? SERVICEVOUCHERID : keyword,
            searchType: searchByInsurance,
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimetrsTypeIDList: providerInsurance,
        }
        actionInsurancePVI.getSearchHistoryEditEmei(data)
    }
    const onSubmit = (keyword) => {
        const data = {
            keyword: keyword,
            fromDate: new Date(fromDate),
            toDate: new Date(toDate),
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            searchType: searchByInsurance,
            airtimetrsTypeIDList: providerInsurance,
        }
        actionInsurancePVI.getSearchHistoryEditEmei(data)
    }

    const renderFooter = () => {
        return (
            <View
                style={{
                    height: 300,
                }}
            />
        );
    };

    const getDataViewEditEmei = (AIRTIMETRANSACTIONID) => {
        showBlockUI();
        const data = {
            airtimeTransactionID: AIRTIMETRANSACTIONID
        }
        actionInsuranceBrightside .editedContent(data).then((reponseStatus) => {
            hideBlockUI();
            setdataViewHTREditEmei(reponseStatus);
        }).catch(error => {
            Alert.alert("", error.msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI,
                },
                {
                    text: "Thử lại",
                    onPress: () => getDataViewEditEmei(AIRTIMETRANSACTIONID)
                }
            ])
        });
    }

    const onClickItem = (item) => {
        const { AIRTIMETRANSACTIONID } = item;
        const newData = dataSearchListHistoryEditEmei.map((r) => ({
            ...r,
            selected: r.EDITREQUESTID == item.EDITREQUESTID
        }));
        setupdateDataEditEmei(newData);
        getDataViewEditEmei(AIRTIMETRANSACTIONID);
    };

    const renderItem = ({ item, index }) => {
        const {
            OUTPUTRECEIPTID,
            PRODUCTNAME,
            AIRTIMESTATUSNAME,
            FULLNAME,
            ISVIEWEDITHISTORY,
            PHONENUMBER,
            CREATEDDATE,
            AIRTIMETRANSACTIONTYPENAME
        } = item;
        return (
            <View style={{
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 5,
                paddingVertical: 5,
                width: constants.width - 10,
            }}>
                <View
                    style={{
                        width: constants.width,
                        padding: 8,
                        width: '97%',
                        padding: 10,
                        borderColor: COLORS.bd218DEB,
                        backgroundColor: COLORS.bgFFFFFF,
                        borderRadius: 10,
                        shadowColor: COLORS.bg000000,
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowOpacity: 0.25,
                        shadowRadius: 4,
                        elevation: 5,
                    }}>
                    <View style={{
                        flexDirection: "row",
                    }}>
                        <TouchableOpacity
                            disabled={true}
                            onPress={() => { }}
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}
                        >
                            <MyText
                                text={OUTPUTRECEIPTID}
                                style={{
                                    color: COLORS.bg2C8BD7,
                                    fontWeight: 'bold',
                                    textAlign: 'center',
                                }}
                            />
                        </TouchableOpacity>
                    </View>
                    <TextField
                        name={translate("collection.transaction_type")}
                        value={AIRTIMETRANSACTIONTYPENAME}
                        key={"AIRTIMETRANSACTIONTYPENAME"}
                        isWarning={AIRTIMETRANSACTIONTYPENAME}
                        extraStyle={{ color: COLORS.bg000000 }}
                    />
                    <TextField
                        name={translate("collection.status")}
                        value={AIRTIMESTATUSNAME}
                        key={"AIRTIMESTATUSNAME"}
                        extraStyle={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
                    />
                    <TextField
                        name={"Nhân viên tạọ yêu cầu: "}
                        value={FULLNAME}
                        key={"FULLNAME"}
                        extraStyle={{ color: COLORS.bg000000 }}
                    />
                    <TextField
                        name={"Thời gian tạo: "}
                        value={CREATEDDATE}
                        key={"CREATEDDATE"}
                        extraStyle={{ color: COLORS.bg000000 }}
                    />
                    <TextField
                        name={"Imei: "}
                        value={PHONENUMBER}
                        key={"PHONENUMBER"}

                    />
                    {
                        ISVIEWEDITHISTORY == 1 &&
                        <TouchableOpacity
                            onPress={() => onClickItem(item, index)}
                            style={{
                                backgroundColor: COLORS.bg00AAFF,
                                width: constants.width - 40,
                                height: 30,
                                alignItems: 'center',
                                justifyContent:'center',
                                borderTopLeftRadius: 5,
                                borderTopRightRadius: 5,
                                borderBottomRightRadius: item.selected ? 0 : 5,
                                borderBottomLeftRadius: item.selected ? 0 : 5,
                                
                            }}
                        >
                            <MyText
                                text={"Xem nội dung chỉnh sửa"}
                                style={{
                                    color: COLORS.bgFFFFFF,
                                    textAlign:'center',
                                    marginTop: 5
                                }}
                            />
                            <View style={{ flex: 1 }} />
                        </TouchableOpacity>
                    }
                    {
                        !helper.IsEmptyObject(dataInfo) && item.selected ?
                            <View style={{
                                marginLeft: 5,
                                marginBottom: 5
                            }}>
                                <MyText
                                    text={'Giao dịch tạo:'}
                                    style={{
                                        color: COLORS.bg00AAFF,
                                        marginTop: 10,
                                        fontWeight: 'bold'
                                    }}
                                />
                                <TextViewEditEmei
                                    name={'Imei: '}
                                    value={dataViewHTREditEmei?.RootTransaction?.Imei}
                                    key={"Imei"}
                                    extraStyle={{ color: COLORS.bg000000, }}
                                />
                                <TextViewEditEmei
                                    name={"Thời gian tạo: "}
                                    value={dataViewHTREditEmei?.RootTransaction?.InputTime}
                                    key={"TIME"}
                                    extraStyle={{ color: COLORS.bg000000, }}
                                />
                                <MyText
                                    text={'Giao dịch chỉnh sửa:'}
                                    style={{
                                        color: COLORS.bg00AAFF,
                                        marginTop: 10,
                                        fontWeight: 'bold'
                                    }}
                                />
                                <TextViewEditEmei
                                    name={'Imei: '}
                                    value={dataViewHTREditEmei?.EditedTransaction?.Imei}
                                    key={"Imei"}
                                    extraStyle={{ color: COLORS.bg000000, }}
                                />
                                <TextViewEditEmei
                                    name={"Thời gian tạo: "}
                                    value={dataViewHTREditEmei?.EditedTransaction?.InputTime}
                                    key={"TIME"}
                                    extraStyle={{ color: COLORS.bg000000, }}
                                />
                            </View>
                            :
                            null
                    }
                </View>
            </View>
        );
    }


    return (
        <View
            style={{
                flex: 1,
                backgroundColor: "white",
            }}
        >
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView
                    style={{
                        flex: 1,
                        alignItems: "center",
                        justifyContent: 'center'
                    }}
                >
                    <Animated.View style={{
                        transform: [{ translateY: translateY }],
                        backgroundColor: COLORS.bgF5F5F5,
                        position: 'relative',
                        top: 0, left: 0, right: 0, zIndex: 1,
                    }}>
                        <View
                            style={{
                                width: constants.width - 20,
                                marginTop: 5
                            }}
                        >
                            <TouchableOpacity style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 5,
                                width: '100%',
                                paddingHorizontal: 5,
                                borderWidth: 1,
                                borderColor: COLORS.bdDDDDDD,
                                height: 44,
                                alignSelf: 'center'
                            }}
                                onPress={() => setIsShowCalendar(true)}
                            >
                                <MyText
                                    style={{
                                        width: '87%',
                                        paddingHorizontal: 5
                                    }}
                                    text={`${moment(fromDate).format(
                                        'DD/MM/YYYY'
                                    )
                                        } - ${moment(toDate).format(
                                            'DD/MM/YYYY'
                                        )
                                        } `}
                                />
                                <Icon
                                    iconSet="Feather"
                                    name="calendar"
                                    style={{
                                        fontSize: 30,
                                        color: COLORS.ic2C8BD7
                                    }}
                                />
                            </TouchableOpacity>
                            <PickerSearch
                                label={"AirTimeTransactionTypeName"}
                                value={"AirTimeTransactionTypeID"}
                                valueSelected={providerInsurance}
                                data={dataProvider}
                                onChange={(item) => {
                                    setProviderInsurance(item.AirTimeTransactionTypeID)
                                }}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 40,
                                    borderRadius: 4,
                                    borderWidth: 1,
                                    borderColor: COLORS.bdE4E4E4,
                                    width: constants.width - 20,
                                    backgroundColor: COLORS.btnFFFFFF,
                                    marginTop: 5
                                }}
                                defaultLabel={"Nhà cung cấp"}
                            />
                            <PickerSearch
                                label={"Label"}
                                value={"SearchTypeID"}
                                valueSelected={searchByInsurance}
                                data={dataInsurance}
                                onChange={(item) => {
                                    setSearchByInsurance(item.SearchTypeID)
                                }}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 40,
                                    borderRadius: 4,
                                    borderWidth: 1,
                                    borderColor: COLORS.bdE4E4E4,
                                    width: constants.width - 20,
                                    backgroundColor: COLORS.btnFFFFFF,
                                    marginBottom: 5,
                                    marginTop: 5
                                }}
                                defaultLabel={"Tìm theo"}
                            />
                        </View>
                    </Animated.View>
                    <SearchInput
                        onSubmit={() => onSubmit(keyword)}
                        inputText={keyword}
                        onChangeText={(text) => {
                            setkeyWork(text)
                        }}
                        onClearText={() => {
                            setkeyWork('');
                        }}
                        placeholder={'Từ khoá'}
                    />
                    <BaseLoading
                        isLoading={stateSearchListHistoryEditEmei.isFetching}
                        isError={stateSearchListHistoryEditEmei.isError}
                        isEmpty={stateSearchListHistoryEditEmei.isEmpty}
                        textLoadingError={stateSearchListHistoryEditEmei.description}
                        onPressTryAgains={() => {
                            getSearchHistoryEditEmei()
                        }}
                        content={
                            <FlatList
                                style={{ marginTop: 5 }}
                                data={updateDataEditEmei}
                                keyExtractor={(item, index) => `${index} `}
                                renderItem={renderItem}
                                stickySectionHeadersEnabled={false}
                                alwaysBounceVertical={false}
                                bounces={false}
                                scrollEventThrottle={16}
                                ListFooterComponent={renderFooter}
                            />
                        }
                    />
                    <ModalCalendar
                        isVisible={isShowCalendar}
                        hideModal={() => {
                            setIsShowCalendar(false);
                        }}
                        startDate={fromDate}
                        endDate={toDate}
                        setDate={(day) => {
                            setFromDate(day.startDate)
                            setToDate(day.endDate)
                        }}
                    />
                </SafeAreaView>
            </KeyboardAwareScrollView>
        </View>
    )
}

const mapStateToProps = function (state) {
    return {
        dataSearchListHistoryEditEmei: state.insurancePVIReducer.dataSearchListHistoryEditEmei,
        stateSearchListHistoryEditEmei: state.insurancePVIReducer.stateSearchListHistoryEditEmei,
        dataInfo: state.insurancePVIReducer.dataInfo,
        itemCatalog: state.collectionReducer.itemCatalog
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsurancePVI: bindActionCreators(actionInsurancePVICreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
        actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
        actionInsuranceBrightside: bindActionCreators(actionInsuranceBrightsideCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(HistoryEditEmei)

const styles = StyleSheet.create({})

const TextField = ({ name, value, extraStyle, isWarning }) => {
    return (
        <MyText
            text={name}
            addSize={-1.5}
            style={{
                color: COLORS.txt8E8E93,
                marginTop: 10
            }}>
            <MyText
                text={value}
                style={[{
                    color: isWarning ? COLORS.txtFF0000 : COLORS.txt333333,
                }, extraStyle]}
            />
        </MyText>
    );
}

const TextViewEditEmei = ({ name, value, extraStyle, isWarning }) => {
    return (
        <MyText
            text={name}
            addSize={-1.5}
            style={{
                color: COLORS.txt8E8E93,
            }}>
            <MyText
                text={value}
                style={[{
                    color: isWarning ? COLORS.txtFF0000 : COLORS.txt333333,
                }, extraStyle]}
            />
        </MyText>
    );
}