import React, { useState, useEffect } from 'react';
import {
    TextInput,
    View
} from 'react-native';
import { MyText } from "@components";
import { helper } from "@common";
import { COLORS } from "@styles";
import  NumberInput  from '../NumberInput/NumberInput';

const TitleInputMoney = ({
    style,
    value,
    onChange,
    placeholder,
    title,
    isRequired,
    percent = 0,
    editable = true,
    length,
    onBlur,
    maxValue
}) => {
    const { width } = style;
    const roundPercent = (percent > 0) ? ` (${percent.toFixed()}%) ` : "";


    return (
        <>
            {title && <MyText
                text={title}
                addSize={-1.5}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: 'bold',
                    fontStyle: 'italic',
                    width: width
                }}>
                {isRequired && (
                    <MyText
                        text={'*'}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txtFF0000
                        }}>
                        <MyText
                            text={roundPercent}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txtFF0000,
                                fontWeight: 'normal'
                            }}
                        />
                    </MyText>
                )}
            </MyText>}
            {
                editable
                    ? <NumberInput
                        style={style}
                        placeholder={placeholder}
                        value={value}
                        onChangeText={onChange}
                        blurOnSubmit={true}
                        onBlur={onBlur}
                        maxLength={length}
                        maxValue={maxValue}
                    />
                    : <View style={[style, { justifyContent: "center" }]}>
                        <MyText
                            text={helper.convertNum(value, false) || placeholder}
                            style={{
                                color: COLORS.txt333333
                            }}
                        />
                    </View>
            }
        </>
    );
}

export default TitleInputMoney;