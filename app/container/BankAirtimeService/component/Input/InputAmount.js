import React from 'react';
import {
    View,
    TouchableOpacity,
    TextInput,
    StyleSheet,
} from 'react-native';
import { Icon, MyText } from "@components";
import { constants } from '@constants';
import { translate } from '@translate';
import { COLORS } from "@styles";
import TitleInputMoney from './TitleInputMoney';
import { helper } from '@common';

const InputAmount = ({
    value,
    onChange,
    onCheckMount,
    disabled,
    onSubmitEditing,
    title,
    placeholder,
    isRequired,
    onBlur
}) => {
    return (
        <View style={{
            marginBottom: 5
        }} >
            <MyText
                text={title}
                addSize={-1.5}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: "bold",
                    fontStyle: "italic"
                }}>
                {
                    isRequired &&
                    <MyText
                        text={"*"}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txtFF0000
                        }}
                    />
                }
            </MyText>
            <View style={{
                flexDirection: 'row',
                alignItems: 'center'
            }}>
                <View style={{
                    flexDirection: "row",
                    width: constants.width - 20,
                    marginBottom: 4,
                    height: 40,
                    backgroundColor: COLORS.bgFFFFFF,
                    borderRadius: 5,
                    opacity: disabled ? 0.8 : 1,
                    marginTop: 5
                }}>
                    <TitleInputMoney
                        style={{
                            borderWidth: 1,
                            borderRadius: 4,
                            borderColor: COLORS.bdCCCCCC,
                            backgroundColor: COLORS.bgFFFFFF,
                            color: COLORS.txt333333,
                            height: 40,
                            marginBottom: 5,
                            paddingHorizontal: 10,
                            width: constants.width - 20
                        }}
                        placeholder={"0"}
                        value={value}
                        onChange={onChange}
                        length={30}
                        onBlur={onBlur}
                    />

                </View>
            </View>
        </View>
    );
}


export default InputAmount

const styles = StyleSheet.create({})