import React, { PureComponent } from 'react';
import Signature from 'react-native-signature-canvas';
import SafeAreaView from 'react-native-safe-area-view';
import { translate } from '@translate';import { constants } from '../../../../constants';
;

class SignImage extends PureComponent {
  constructor() {
    super();
    this.state = {};
  }

  render() {
    let { takeSignature, closeSignature } = this.props;
    return (
      <SafeAreaView
        style={{
          flex: 1,
          justifyContent: 'center'
        }}>
        <Signature
          onOK={takeSignature}
          onEmpty={closeSignature}
          onBegin={this.props.onBegin}
          onEnd={this.props.onEnd}
          descriptionText={translate('activeSimManager.signature_uppercase')}
          clearText={translate('activeSimManager.delete')}
          confirmText={translate('activeSimManager.done')}
          penColor={'#147efb'}
          minWidth={2.5}
          backgroundColor={'#FFFFFF'}
          webStyle={`
          .m-signature-pad {
              box-shadow: none; 
              border: none;
          }
          .m-signature-pad--body {
            position: relative;
            margin: 20px;
            top: 10px;
            height: ${constants.width}px;
            bottom: 100px;
            border: 1px solid #f4f4f4;
          }
          .m-signature-pad--footer {
              position: absolute;
              left: 20px;
              right: 20px;
              bottom: 0px;
              height: 60px;
          }
          .m-signature-pad--footer
          .button {
            // position: absolute;
            bottom: 10px;
            background-color: #2FB47C;
            width: 100px;
            height: 40px;
            line-height: 30px;
            text-align: center;
            color: #FFF;
            border: none;
            outline: none;
          }
          .m-signature-pad--footer
          .description {
              color: #147efb;
              font-size: small;
              text-align: center;
            }
          `}
          imageType={'image/png'}
        />
      </SafeAreaView>
    );
  }
}

export default SignImage

