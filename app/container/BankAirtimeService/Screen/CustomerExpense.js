import React, { Component } from "react";
import {
  View,
  ScrollView,
  Alert,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import { MyText, Icon, showBlock<PERSON>, hide<PERSON>lock<PERSON> } from "@components";
import {
  helper,
  storageHelper,
  convertHtml2Image,
  convertToBase64,
  printSocket,
} from "@common";
import { constants } from "@constants";
import * as actionBankAirtimeServiceCreator from "../action";
import { translate } from "@translate";
import { COLORS } from "@styles";
import * as actionCollectionCreator from "../../CollectionTransfer/action";
import PaymentTable from "../component/Table/PaymentTable";
import * as actionSaleOrderCreator from "../../SaleOrderPayment/action";
import * as actionManagerSOCreator from "../../SaleOrderManager/action";
import PrintReport from "../../SaleOrderPayment/component/PrintReport";
import Report from "../../SaleOrderPayment/component/PrintReport/Report";
import * as actionCollectInstallmentCreator from "../../CollectInstallmentPayments/action";
const { H_BILL, H_VOUCHER, H_BANK, H_KEY, H_AIRTIME } = constants;
class CustomerExpense extends Component {
  constructor(props) {
    super(props);
    this.state = {
      reportRetail: {},
      reportVAT: {},
      reportCommon: {},
      reportInfo: [],
      isVisible: false,
      base64PDF: "",
      totalRemain: 0,
      isShow: false,
    };
    this.dataPrint = {};
  }

  componentDidMount() {
    this.props.actionSaleOrder.getReportPrinterSocket(100);
  }

  componentDidUpdate(preProps, preState) {
    if (preProps.defaultReport !== this.props.defaultReport) {
      const { defaultReport } = this.props;
      this.setState({
        reportRetail: defaultReport.retail,
        reportVAT: defaultReport.vat,
        reportCommon: defaultReport.common,
      });
    }
    if (preProps.reportContent !== this.props.reportContent) {
      const { reportContent } = this.props;
      if (helper.IsNonEmptyArray(reportContent)) {
        const reportInfo = reportContent.map((ele) => {
          return {
            ReportContent: ele.REPORTCONTENT,
            NumberOfCopy: "1",
            ReportName: ele.REPORTNAME,
          };
        });
        this.setState({ reportInfo });
      }
    }
  }

  getReportPrinter = () => {
    const {
      dataSO: { SaleOrderTypeID },
      actionSaleOrder,
    } = this.props;
    actionSaleOrder.getReportPrinterSocket(SaleOrderTypeID);
  };

  handleOption = () => {
    Alert.alert("", "Bạn có muốn thực hiện chức năng chi tiền không!", [
      {
        text: "BỎ QUA",
        onPress: hideBlockUI,
      },
      {
        text: "TIẾP TỤC",
        onPress: () => this.handleSpendMoney(),
      },
    ]);
  };

  handleSpendMoney = () => {
    const { qrInfo, actionBankAirtimeService } = this.props;
    const { ServiceVoucherID } = qrInfo ?? "";
    const data = { ServiceVoucherID: ServiceVoucherID };
    showBlockUI();
    actionBankAirtimeService
      .getProcessoutVoucher(data)
      .then((reponse) => {
        const inOutVoucherID = reponse?.InOutVoucherBO?.InOutVoucherID ?? "";
        Alert.alert("Thông báo", reponse.Message, [
          {
            text: "OK",
            onPress: () => {
              this.onCheckPrintContent(inOutVoucherID);
            },
          },
        ]);
      })
      .catch((error) => {
        Alert.alert("", error?.msgError, [
          {
            text: "OK",
            onPress: () => {
              hideBlockUI();
            },
          },
        ]);
      });
  };

  onCheckPrintContent = (inOutVoucherID) => {
    const { reportRetail, reportVAT, reportCommon, reportInfo } = this.state;
    const isRetail = !helper.IsEmptyObject(reportRetail);
    const isVat = !helper.IsEmptyObject(reportVAT);
    const isCommon = !helper.IsEmptyObject(reportCommon);
    const isReport = isRetail || isVat || isCommon;
    const reportContents = reportInfo.filter((ele) =>
      helper.IsNonEmptyString(ele.NumberOfCopy)
    );
    if (!isReport) {
      Alert.alert("", translate("saleOrderPayment.please_choose_printer"));
    } else {
      const data = {
        saleOrderID: inOutVoucherID,
        reportContents: [
          {
            ReportContent: "AirtimeTransferContent",
            NumberOfCopy: "1",
            ReportName: "Biên nhận nạp tiền ngân hàng",
          },
        ],
      };
      const isReceipt = reportContents.some(
        (ele) =>
          ele?.ReportContent == "OutputReceiptContent" ||
          ele?.ReportContent == "KeySoftwareContent"
      );
      data.isFitContent = true;
      data.isGetContentHTML = true;
      if (isReceipt) {
        this.getReceiptContentHtml(data);
      } else {
        this.getContentHtml(data);
      }
      this.dataPrint = data;
    }
  };

  getContentBase64PDF = (info) => {
    showBlockUI();
    this.props.actionSaleOrder
      .getReportContentBase64(info)
      .then((data) => {
        this.onPrintBillPDF(data, info.reportContents);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification_uppercase"), msgError, [
          {
            text: translate("saleOrderPayment.btn_skip_uppercase"),
            style: "default",
            onPress: hideBlockUI,
          },
          {
            text: translate("saleOrderPayment.btn_retry_uppercase"),
            style: "default",
            onPress: () => this.getContentBase64PDF(info),
          },
        ]);
      });
  };

  onPrintBillPDF = (data, reportContents) => {
    const requestAPI = this.getPrintPDFRequestAPI(data, reportContents);
    if (helper.IsNonEmptyArray(requestAPI)) {
      this.printAllRequest(requestAPI);
    } else {
      hideBlockUI();
    }
  };

  getPrintPDFRequestAPI = (data, reportContents) => {
    const {
      dataSO: { SaleOrderID },
    } = this.props;
    const requestAPI = [];
    const {
      EBillContent,
      eBillContentPrinterTypeID,

      GiftVCIssueContentPrint,
      giftVCIssueContentPrinterTypeID,

      EBillContentIncome,
      eBillContentIncomePrinterTypeID,

      OutTransContent,
      outTransContentPrinterTypeID,

      BankAccountContent,
      bankAccountContentPrinterTypeID,

      PrepareProductsForSaleContent,
      prepareProductsForSaleContentPrinterTypeID,

      DosageContent,
      dosageContentPrinterTypeID,

      infoBatchNOContent,
      infoBatchNOContentPrinterTypeID,

      OutputReceiptContent,
      outputReceiptContentPrinterTypeID,

      AirtimeTransferContent,
      airtimeTransferContentPrinterTypeID,
    } = data;
    reportContents.forEach((ele) => {
      const { ReportContent, NumberOfCopy } = ele;
      switch (ReportContent) {
        case "EBillContent":
          if (helper.IsNonEmptyString(EBillContent)) {
            const report = this.getReport(eBillContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  EBillContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "EBillContentIncome":
          if (helper.IsNonEmptyString(EBillContentIncome)) {
            const report = this.getReport(eBillContentIncomePrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  EBillContentIncome
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "GiftVCIssueContentPrint":
          if (helper.IsNonEmptyString(GiftVCIssueContentPrint)) {
            const report = this.getReport(giftVCIssueContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  GiftVCIssueContentPrint
                );
                requestAPI.push(printService);
              }
              actionSaleOrderCreator.insertLogPrintGift(SaleOrderID);
            }
          }
          break;
        case "KeySoftwareContent":
          if (helper.IsNonEmptyString(OutTransContent)) {
            const report = this.getReport(outTransContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              const printService = this.getPrintServicePDF(
                report,
                OutTransContent
              );
              requestAPI.push(printService);
            }
          }
          break;
        case "BankAccountContent":
          if (helper.IsNonEmptyString(BankAccountContent)) {
            const report = this.getReport(bankAccountContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  BankAccountContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "PrepareProductsForSaleContent":
          if (helper.IsNonEmptyString(PrepareProductsForSaleContent)) {
            const report = this.getReport(
              prepareProductsForSaleContentPrinterTypeID
            );
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  PrepareProductsForSaleContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "DosageContent":
          if (helper.IsNonEmptyString(DosageContent)) {
            const report = this.getReport(dosageContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  DosageContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "InfoBatchNOContent":
          if (helper.IsNonEmptyString(infoBatchNOContent)) {
            const report = this.getReport(infoBatchNOContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  infoBatchNOContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "OutputReceiptContent":
          if (helper.IsNonEmptyString(OutputReceiptContent)) {
            const report = this.getReport(outputReceiptContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  OutputReceiptContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "AirtimeTransferContent":
          if (helper.IsNonEmptyString(AirtimeTransferContent)) {
            const report = this.getReport(airtimeTransferContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  AirtimeTransferContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        default:
          console.log(ele);
          break;
      }
    });
    return requestAPI;
  };

  getPrintServicePDF = (report, content) => {
    let printerConfig = {
      strPrinterName: report.PRINTERNAME,
      strPaperSize: report.PAPERSIZE,
      paperwidth: report.PAPERWIDTH,
      parperheight: report.PARPERHEIGHT,
      intCopyCount: 1,
      bolIsDuplex: false,
      bolShrinkToMargin: false,
      strBase64: content,
    };
    if (report.REPORTID == 2820) {
      printerConfig.strPaperSize = "A4 210 x 297 mm";
    }
    let formBody = [];
    for (const property in printerConfig) {
      const encodedKey = encodeURIComponent(property);
      const encodedValue = encodeURIComponent(printerConfig[property]);
      formBody.push(encodedKey + "=" + encodedValue);
    }
    formBody = formBody.join("&");
    return new Promise((resolve, reject) => {
      actionSaleOrderCreator
        .printBillVoucher(formBody)
        .then((result) => {
          resolve(result);
        })
        .catch((msgError) => {
          reject(msgError);
        });
    });
  };

  printAllRequest = (allPromise) => {
    Promise.all(allPromise)
      .then((result) => {
        console.log("PRINT RSULT", result);
        Alert.alert("", translate("saleOrderPayment.print_successfully"), [
          {
            text: "OK",
            style: "default",
            onPress: hideBlockUI,
          },
        ]);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification"), msgError, [
          {
            text: "OK",
            style: "default",
            onPress: hideBlockUI,
          },
        ]);
      });
  };

  getReport = (type) => {
    const { reportRetail, reportVAT, reportCommon } = this.state;
    switch (type) {
      case "InvoiceRetailPrinter":
        return reportRetail;
      case "VATContentPrint":
        return reportVAT;
      default:
        // CommonPrinter
        return reportCommon;
    }
  };

  getContentBase64View = () => {
    const { base64PDF } = this.state;
    if (helper.IsNonEmptyString(base64PDF)) {
      this.setState({ isVisible: true });
    } else {
      showBlockUI();
      const { actionManagerSO, dataSO } = this.props;
      actionManagerSO
        .getContentBase64View({
          reportContent: "BankAccountContent",
          saleOrderID: dataSO.SaleOrderID,
        })
        .then((base64) => {
          this.setState({
            isVisible: true,
            base64PDF: base64,
          });
          hideBlockUI();
        })
        .catch((msgError) => {
          Alert.alert(translate("common.notification"), msgError, [
            {
              text: "OK",
              onPress: hideBlockUI,
            },
          ]);
        });
    }
  };

  /*  */
  printAllRequestFW = async (allPromise) => {
    try {
      for (const body of allPromise) {
        await actionSaleOrderCreator.printBillVoucherBit(body);
        await helper.sleep(1000);
      }
      Alert.alert("", translate("saleOrderPayment.print_successfully"), [
        {
          text: "OK",
          style: "default",
          onPress: hideBlockUI,
        },
      ]);
    } catch (msgError) {
      Alert.alert(translate("common.notification"), msgError, [
        {
          text: "OK",
          style: "default",
          onPress: hideBlockUI,
        },
      ]);
    }
  };

  handlePrintCouppon = async () => {
    const {
      actionManagerSO,
      actionSaleOrder,
      actionCollectInstallment,
      navigation,
      dataQueryStatus,
      actionBankAirtimeService,
      qrInfo,
      actionCollection,
      dataPromotionGiftVoucher,
    } = this.props;
    const { PromotionSaleorderID } = dataQueryStatus ?? {};
    const { ServiceVoucherID } = qrInfo ?? {};
    const isPromotionGift = helper.IsNonEmptyArray(
      dataPromotionGiftVoucher?.PromotionGift
    );
    if (helper.IsNonEmptyString(PromotionSaleorderID)) {
      const { reportRetail, reportVAT, reportCommon } = this.state;
      const success = await actionSaleOrder.setDataSO({
        SaleOrderID: PromotionSaleorderID,
        SaleOrderTypeID: 100,
      });
      if (success) {
        const data = {
          retail: reportRetail,
          vat: reportVAT,
          common: reportCommon,
        };
        await actionManagerSO.getContentTypeReport(PromotionSaleorderID);
        actionSaleOrder.getReportPrinterSocket(100);
        actionCollectInstallment.updateItemSelectedPrint(data);
        navigation.replace("PrintCoupon");
      }
    } else if (isPromotionGift) {
      navigation.navigate("ScanQrCodeVoucher");
    } else {
      Alert.alert("", translate("saleOrderPayment.print_successfully"), [
        {
          text: "Về màn hình quản lý",
          onPress: () => {
            hideBlockUI();
            actionBankAirtimeService.clear_data_customer();
            navigation.navigate("HistorySell", {
              SaleOrderID: ServiceVoucherID,
            });
          },
        },
        {
          text: "Tiếp tục thực hiện dịch vụ",
          onPress: () => {
            hideBlockUI();
            const item = {
              AirtimeServiceGroupID: 35,
              AirtimeServiceGroupName: "Rút tiền ngân hàng",
              ServiceCategoryID: 12,
            };
            actionBankAirtimeService.clear_data_customer();
            actionCollection.updateItemCatalog(item);
            navigation.navigate("BankAirtimeService");
          },
        },
      ]);
    }
  };

  handleNavigationScanqQrCodeCoupon = () => {
    const {
      updateHeaderAirtime,
      itemCatalog,
      actionCollectInstallment,
      dataPromotionGiftVoucher,
      navigation,
      qrInfo
    } = this.props;
    const {
      ServiceCategoryID,
      AirtimeServiceGroupID
    } = itemCatalog ?? {};
    const { AirTimeTransactionTypeID } = updateHeaderAirtime;
    const { ServiceVoucherID } = qrInfo ?? {};
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimeTransactionTypeID: AirTimeTransactionTypeID,
      "productID": "*************",
      phoneNumber: '',
      serviceVoucherID: ServiceVoucherID
    };
    showBlockUI();
    actionCollectInstallment
      .getPromotionService(data)
      .then((response) => {
        hideBlockUI();
        const isPromotionGift = helper.IsNonEmptyArray(response?.PromotionGift);
        if (isPromotionGift) {
          actionCollectInstallment.getDataPromotionGiftVoucher({
            ...data,
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": AirTimeTransactionTypeID,
            "productID": "*************",
            "phoneNumber": '',
            "serviceVoucherID": ServiceVoucherID,
            "PromotionGift": response?.PromotionGift,
          })
          navigation.navigate("ScanQrCodeVoucher");
        };
      })
      .catch((error) => {
        Alert.alert("", error?.msgError, [
          {
            text: translate("common.btn_skip"),
            onPress: () => {
              hideBlockUI()
            }
          },
          {
            text: translate("common.btn_notify_try_again"),
            onPress: () => this.handleNavigationScanqQrCodeCoupon(),
          },
        ]);
      });

  }

  printAllRequestSocket = async (allPromise) => {
    try {
      for (const { data, ip, delay } of allPromise) {
        await printSocket(data, ip);
        if (delay > 0) {
          await helper.sleep(delay);
        }
      }
      // this.handlePrintCouppon();
      this.handleNavigationScanqQrCodeCoupon();
    } catch (msgError) {
      Alert.alert(translate("common.notification"), msgError, [
        {
          text: "OK",
          style: "default",
          onPress: () => {
            hideBlockUI();
            // this.handlePrintCouppon();
            this.handleNavigationScanqQrCodeCoupon();
          },
        },
      ]);
    }
  };

  getContentHtml = (info) => {
    showBlockUI();
    this.props.actionSaleOrder
      .getReportContentBase64(info)
      .then((data) => {
        this.onConvertHTML(data, info.reportContents);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification_uppercase"), msgError, [
          {
            text: translate("saleOrderPayment.btn_skip_uppercase"),
            style: "default",
            onPress: hideBlockUI,
          },
          {
            text: translate("saleOrderPayment.btn_retry_uppercase"),
            style: "default",
            onPress: () => this.getContentHtml(info),
          },
        ]);
      });
  };

  getReceiptContentHtml = (info) => {
    showBlockUI();
    this.props.actionSaleOrder
      .getReportContentBase64New(info)
      .then((data) => {
        this.onConvertHTML(data, info.reportContents);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification_uppercase"), msgError, [
          {
            text: translate("saleOrderPayment.btn_skip_uppercase"),
            style: "default",
            onPress: hideBlockUI,
          },
          {
            text: translate("saleOrderPayment.btn_retry_uppercase"),
            style: "default",
            onPress: () => this.getReceiptContentHtml(info),
          },
        ]);
      });
  };

  requestConvertHtml = async (data, reportContents) => {
    try {
      const requestConvert = [];
      const {
        EBillContentHTML,
        GiftVCIssueContentPrintHTML,
        EBillContentIncomeHTML,
        OutTransContentHTML,
        BankAccountContentHTML,
        PrepareProductsForSaleContentHTML,
        DosageContentHTML,
        InfoBatchNOContentHTML,
        OutputReceiptContentHTML,
        AirtimeTransferContentHTML,
      } = data;
      for (const ele of reportContents) {
        const { ReportContent } = ele;
        switch (ReportContent) {
          case "EBillContent":
            if (helper.IsNonEmptyString(EBillContentHTML)) {
              const eBillContent = await convertHtml2Image(
                EBillContentHTML,
                H_BILL
              );
              requestConvert.push([eBillContent]);
            }
            break;
          case "EBillContentIncome":
            if (helper.IsNonEmptyString(EBillContentIncomeHTML)) {
              const eBillContentIncome = await convertHtml2Image(
                EBillContentIncomeHTML,
                H_BILL
              );
              requestConvert.push([eBillContentIncome]);
            }
            break;
          case "GiftVCIssueContentPrint":
            if (helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)) {
              const giftData =
                GiftVCIssueContentPrintHTML.split(`<br /><br />`);
              const giftVCIssueContentPrint = [];
              for (const giftHtml of giftData) {
                const giftContent = await convertHtml2Image(
                  giftHtml,
                  H_VOUCHER
                );
                giftVCIssueContentPrint.push(giftContent);
              }
              requestConvert.push(giftVCIssueContentPrint);
            }
            break;
          case "KeySoftwareContent":
            if (helper.IsNonEmptyString(OutTransContentHTML)) {
              const keySoftwareContent = await convertHtml2Image(
                OutTransContentHTML,
                H_KEY
              );
              requestConvert.push([keySoftwareContent]);
            }
            break;
          case "BankAccountContent":
            if (helper.IsNonEmptyString(BankAccountContentHTML)) {
              const bankAccountContent = await convertHtml2Image(
                BankAccountContentHTML,
                H_BANK
              );
              requestConvert.push([bankAccountContent]);
            }
            break;
          case "PrepareProductsForSaleContent":
            if (helper.IsNonEmptyString(PrepareProductsForSaleContentHTML)) {
              const prepareContent = await convertHtml2Image(
                PrepareProductsForSaleContentHTML,
                H_BILL
              );
              requestConvert.push([prepareContent]);
            }
            break;
          case "DosageContent":
            if (helper.IsNonEmptyString(DosageContentHTML)) {
              const dosageContent = await convertHtml2Image(
                DosageContentHTML,
                H_VOUCHER
              );
              requestConvert.push([dosageContent]);
            }
            break;
          case "InfoBatchNOContent":
            if (helper.IsNonEmptyString(InfoBatchNOContentHTML)) {
              const batchContent = await convertHtml2Image(
                InfoBatchNOContentHTML,
                H_VOUCHER
              );
              requestConvert.push([batchContent]);
            }
            break;
          case "OutputReceiptContent":
            if (helper.IsNonEmptyString(OutputReceiptContentHTML)) {
              const receiptContent = await convertHtml2Image(
                OutputReceiptContentHTML,
                H_KEY
              );
              requestConvert.push([receiptContent]);
            }
            break;
          case "AirtimeTransferContent":
            if (helper.IsNonEmptyString(AirtimeTransferContentHTML)) {
              const airtimeTransferContent = await convertHtml2Image(
                AirtimeTransferContentHTML,
                H_AIRTIME
              );
              requestConvert.push([airtimeTransferContent]);
            }
            break;
          default:
            console.log(ele);
            break;
        }
      }
      return requestConvert;
    } catch (error) {
      console.log("convertHtml2Image", error);
      Alert.alert(
        "",
        'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.',
        [
          {
            text: "OK",
            style: "default",
            onPress: hideBlockUI,
          },
        ]
      );
    }
  };

  onConvertHTML = async (data, reportContents) => {
    const dataBit = await this.requestConvertHtml(data, reportContents);
    if (helper.IsNonEmptyArray(dataBit)) {
      this.onPrintBillHTML(data, reportContents, dataBit);
    } else {
      hideBlockUI();
    }
  };

  getPrintServiceHTML = (report, info, type) => {
    const {
      userInfo: { userName },
    } = this.props;
    let body = {
      Printer: report.PRINTERSHORTNAME,
      Value: info,
      Type: type,
      User: userName,
      Status: "ReprintPayment",
    };
    return body;
  };

  getPrintServiceSocket = (report, info) => {
    let body = {
      ip: report.IPPRINTER,
      delay: report.DELAY,
      data: info,
    };
    return body;
  };

  getPrintHTMLRequestAPI = (data, reportContents, dataBit) => {
    const {
      dataSO: { SaleOrderID },
    } = this.props;
    const { reportRetail } = this.state;
    const requestAPI = [];
    const {
      eBillContentPrinterTypeID,
      giftVCIssueContentPrinterTypeID,
      eBillContentIncomePrinterTypeID,
      outTransContentPrinterTypeID,
      bankAccountContentPrinterTypeID,
      GiftVCIssueContentPrintHTML,
      dosageContentPrinterTypeID,
      infoBatchNOContentPrinterTypeID,
      outputReceiptContentPrinterTypeID,
      airtimeTransferContentPrinterTypeID,
    } = data;
    reportContents.forEach((ele, index) => {
      const { ReportContent, NumberOfCopy } = ele;
      const dataConvert = dataBit[index];
      let report = {};
      switch (ReportContent) {
        case "EBillContent":
          report = this.getReport(eBillContentPrinterTypeID);
          break;
        case "EBillContentIncome":
          report = this.getReport(eBillContentIncomePrinterTypeID);
          break;
        case "GiftVCIssueContentPrint":
          report = this.getReport(giftVCIssueContentPrinterTypeID);
          break;
        case "KeySoftwareContent":
          report = this.getReport(outTransContentPrinterTypeID);
          break;
        case "BankAccountContent":
          report = this.getReport(bankAccountContentPrinterTypeID);
          break;
        case "DosageContent":
          report = this.getReport(dosageContentPrinterTypeID);
          break;
        case "InfoBatchNOContent":
          report = this.getReport(infoBatchNOContentPrinterTypeID);
          break;
        case "OutputReceiptContent":
          report = this.getReport(outputReceiptContentPrinterTypeID);
          break;
        case "AirtimeTransferContent":
          report = this.getReport(airtimeTransferContentPrinterTypeID);
          break;
        default:
          report = reportRetail;
          break;
      }
      if (helper.IsNonEmptyArray(dataConvert)) {
        for (let i = 0; i < NumberOfCopy; i++) {
          dataConvert.forEach((info) => {
            if (!report.IPPRINTER) {
              report.IPPRINTER = "*************";
              report.DELAY = 500;
            }
            const printService = this.getPrintServiceSocket(
              report,
              info,
              ReportContent
            );
            requestAPI.push(printService);
          });
        }
        const isPMH = ReportContent == "GiftVCIssueContentPrint";
        if (isPMH && helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)) {
          actionSaleOrderCreator.insertLogPrintGift(SaleOrderID);
        }
      }
    });
    return requestAPI;
  };

  onPrintBillHTML = (data, reportContents, dataBit) => {
    const requestAPI = this.getPrintHTMLRequestAPI(
      data,
      reportContents,
      dataBit
    );
    if (helper.IsNonEmptyArray(requestAPI)) {
      this.printAllRequestSocket(requestAPI);
    } else {
      hideBlockUI();
    }
  };

  /*  */
  printAllRequestBHX = async (allPromise) => {
    try {
      for (const body of allPromise) {
        await actionSaleOrderCreator.printBillVoucherBHX(body);
        await helper.sleep(1000);
      }
      Alert.alert("", translate("saleOrderPayment.print_successfully"), [
        {
          text: "OK",
          style: "default",
          onPress: hideBlockUI,
        },
      ]);
    } catch (msgError) {
      Alert.alert(translate("common.notification"), msgError, [
        {
          text: "OK",
          style: "default",
          onPress: hideBlockUI,
        },
      ]);
    }
  };

  getContentHtmlBHX = (info) => {
    showBlockUI();
    this.props.actionSaleOrder
      .getReportContentBase64(info)
      .then((data) => {
        this.onConvertHTMLBHX(data, info.reportContents);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification_uppercase"), msgError, [
          {
            text: translate("saleOrderPayment.btn_skip_uppercase"),
            style: "default",
            onPress: hideBlockUI,
          },
          {
            text: translate("saleOrderPayment.btn_retry_uppercase"),
            style: "default",
            onPress: () => this.getContentHtmlBHX(info),
          },
        ]);
      });
  };

  requestConvertHtmlBHX = (data, reportContents) => {
    try {
      const requestConvert = [];
      const {
        EBillContentHTML,
        GiftVCIssueContentPrintHTML,
        EBillContentIncomeHTML,
        OutTransContentHTML,
        BankAccountContentHTML,
        PrepareProductsForSaleContentHTML,
        DosageContentHTML,
        InfoBatchNOContentHTML,
        OutputReceiptContentHTML,
        AirtimeTransferContentHTML,
      } = data;
      for (const ele of reportContents) {
        const { ReportContent } = ele;
        switch (ReportContent) {
          case "EBillContent":
            if (helper.IsNonEmptyString(EBillContentHTML)) {
              const eBillContent = convertToBase64(EBillContentHTML);
              requestConvert.push(eBillContent);
            }
            break;
          case "EBillContentIncome":
            if (helper.IsNonEmptyString(EBillContentIncomeHTML)) {
              const eBillContentIncome = convertToBase64(
                EBillContentIncomeHTML
              );
              requestConvert.push(eBillContentIncome);
            }
            break;
          case "GiftVCIssueContentPrint":
            if (helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)) {
              const giftContent = convertToBase64(GiftVCIssueContentPrintHTML);
              requestConvert.push(giftContent);
            }
            break;
          case "KeySoftwareContent":
            if (helper.IsNonEmptyString(OutTransContentHTML)) {
              const keySoftwareContent = convertToBase64(OutTransContentHTML);
              requestConvert.push(keySoftwareContent);
            }
            break;
          case "BankAccountContent":
            if (helper.IsNonEmptyString(BankAccountContentHTML)) {
              const bankAccountContent = convertToBase64(
                BankAccountContentHTML
              );
              requestConvert.push(bankAccountContent);
            }
            break;
          case "PrepareProductsForSaleContent":
            if (helper.IsNonEmptyString(PrepareProductsForSaleContentHTML)) {
              const prepareContent = convertToBase64(
                PrepareProductsForSaleContentHTML
              );
              requestConvert.push(prepareContent);
            }
            break;
          case "DosageContent":
            if (helper.IsNonEmptyString(DosageContentHTML)) {
              const dosageContent = convertToBase64(DosageContentHTML);
              requestConvert.push(dosageContent);
            }
            break;
          case "InfoBatchNOContent":
            if (helper.IsNonEmptyString(InfoBatchNOContentHTML)) {
              const batchContent = convertToBase64(InfoBatchNOContentHTML);
              requestConvert.push(batchContent);
            }
            break;
          case "OutputReceiptContent":
            if (helper.IsNonEmptyString(OutputReceiptContentHTML)) {
              const receiptContent = convertToBase64(OutputReceiptContentHTML);
              requestConvert.push(receiptContent);
            }
            break;
          case "AirtimeTransferContent":
            if (helper.IsNonEmptyString(AirtimeTransferContentHTML)) {
              const airtimeTransferContent = convertToBase64(
                AirtimeTransferContentHTML
              );
              requestConvert.push(airtimeTransferContent);
            }
            break;
          default:
            console.log(ele);
            break;
        }
      }
      console.log("convertHtml2Image", requestConvert);
      return requestConvert;
    } catch (error) {
      console.log("convertHtml2Image", error);
      Alert.alert(
        "",
        'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.',
        [
          {
            text: "OK",
            style: "default",
            onPress: hideBlockUI,
          },
        ]
      );
    }
  };

  onConvertHTMLBHX = (data, reportContents) => {
    const dataContent = this.requestConvertHtmlBHX(data, reportContents);
    if (helper.IsNonEmptyArray(dataContent)) {
      this.onPrintBillHTMLBHX(data, reportContents, dataContent);
    } else {
      hideBlockUI();
    }
  };

  getPrintServiceHTMLBHX = (report, info, type) => {
    let body = {
      printerName: report.PRINTERNAME,
      docData: info,
    };
    return body;
  };

  getPrintHTMLRequestAPIBHX = (data, reportContents, dataBit) => {
    const { reportRetail } = this.state;
    const requestAPI = [];
    const {
      eBillContentPrinterTypeID,
      eBillContentIncomePrinterTypeID,
      giftVCIssueContentPrinterTypeID,
      outTransContentPrinterTypeID,
      bankAccountContentPrinterTypeID,
      dosageContentPrinterTypeID,
      infoBatchNOContentPrinterTypeID,
      outputReceiptContentPrinterTypeID,
      airtimeTransferContentPrinterTypeID,
    } = data;
    reportContents.forEach((ele, index) => {
      const { ReportContent } = ele;
      const dataConvert = dataBit[index];
      let report = {};
      switch (ReportContent) {
        case "EBillContent":
          report = this.getReport(eBillContentPrinterTypeID);
          break;
        case "EBillContentIncome":
          report = this.getReport(eBillContentIncomePrinterTypeID);
          break;
        case "GiftVCIssueContentPrint":
          report = this.getReport(giftVCIssueContentPrinterTypeID);
          break;
        case "KeySoftwareContent":
          report = this.getReport(outTransContentPrinterTypeID);
          break;
        case "BankAccountContent":
          report = this.getReport(bankAccountContentPrinterTypeID);
          break;
        case "DosageContent":
          report = this.getReport(dosageContentPrinterTypeID);
          break;
        case "InfoBatchNOContent":
          report = this.getReport(infoBatchNOContentPrinterTypeID);
          break;
        case "OutputReceiptContent":
          report = this.getReport(outputReceiptContentPrinterTypeID);
          break;
        case "AirtimeTransferContent":
          report = this.getReport(airtimeTransferContentPrinterTypeID);
          break;
        default:
          report = reportRetail;
          break;
      }
      if (helper.IsNonEmptyString(dataConvert)) {
        const printService = this.getPrintServiceHTMLBHX(
          report,
          dataConvert,
          ReportContent
        );
        requestAPI.push(printService);
      }
    });
    return requestAPI;
  };

  onPrintBillHTMLBHX = (data, reportContents, dataBit) => {
    const requestAPI = this.getPrintHTMLRequestAPIBHX(
      data,
      reportContents,
      dataBit
    );
    if (helper.IsNonEmptyArray(requestAPI)) {
      this.printAllRequestBHX(requestAPI);
    } else {
      hideBlockUI();
    }
  };

  switchPrintToPDF = () => {
    this.dataPrint.isFitContent = false;
    this.dataPrint.isGetContentHTML = false;
    this.getContentBase64PDF(this.dataPrint);
  };

  render() {
    const { reportRetail, reportVAT, reportCommon } = this.state;
    const { printerRetail, printerVAT, printerCommon, statePrinter, qrInfo } =
      this.props;
    const { ServiceVoucherID, Withdrawer, PriceInfo } = qrInfo ?? {};
    const { SalePrice, Amount } = PriceInfo ?? "";
    return (
      <View
        style={{
          flex: 1,
        }}
      >
        <Header
          title={`${"Thực hiện giao dịch cho yêu cầu SV"}\n${ServiceVoucherID}`}
        />
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            paddingVertical: 10,
          }}
        >
          <SafeAreaView
            style={{
              flex: 1,
              backgroundColor: COLORS.bgFAFAFA,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <PaymentTable
              amount={Amount}
              totalAmount={SalePrice}
              Withdrawer={Withdrawer}
              isShowTable={true}
              isShowFee={false}
            />
            <View
              style={{
                marginTop: 10,
                padding: 5,
                marginLeft: 230,
                alignItems: "flex-end",
              }}
            >
              <MyText
                onPress={() => {
                  this.props.actionBankAirtimeService.clear_data_customer();
                  this.props.navigation.navigate("HistorySell", {
                    SaleOrderID: ServiceVoucherID,
                  });
                }}
                text={"Về quản lý giao dịch"}
                addSize={-1.5}
                style={{
                  color: COLORS.bg1E88E5,
                  fontWeight: "bold",
                  fontStyle: "italic",
                  textDecorationLine: "underline",
                }}
              />
            </View>
            {
              <View
                style={{
                  width: "100%",
                  height: "auto",
                }}
              >
                <PrintReport
                  title={translate("saleOrderPayment.choose_printer")}
                  statePrinter={statePrinter}
                  onTryAgains={this.getReportPrinter}
                  dataRetail={printerRetail}
                  dataVAT={printerVAT}
                  dataCommon={printerCommon}
                  renderItemRetail={({ item, index }) => (
                    <Report
                      key={`ReportRetail`}
                      info={item}
                      report={reportRetail}
                      onCheck={() => {
                        storageHelper.setDefaultPrinter(item.STOREPRINTERID, 0);
                        this.setState({ reportRetail: item });
                      }}
                    />
                  )}
                  renderItemVAT={({ item, index }) => (
                    <Report
                      key={`ReportVAT`}
                      info={item}
                      report={reportVAT}
                      onCheck={() => {
                        storageHelper.setDefaultPrinter(item.STOREPRINTERID, 1);
                        this.setState({ reportVAT: item });
                      }}
                    />
                  )}
                  renderItemCommon={({ item, index }) => (
                    <Report
                      key={`ReportCommon`}
                      info={item}
                      report={reportCommon}
                      onCheck={() => {
                        storageHelper.setDefaultPrinter(item.STOREPRINTERID, 2);
                        this.setState({ reportCommon: item });
                      }}
                    />
                  )}
                />
              </View>
            }
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <View
                style={{
                  width: constants.width - 200,
                  height: 50,
                  flexDirection: "row",
                  marginTop: 10,
                }}
              >
                <TouchableOpacity
                  onPress={() => this.handleOption()}
                  style={{
                    backgroundColor: "pink",
                    flex: 1,
                    height: 50,
                    borderRadius: 18,
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: COLORS.bgF49B0C,
                  }}
                >
                  <MyText
                    text={"CHI TIỀN"}
                    style={{
                      fontWeight: "bold",
                      color: COLORS.bgFFFFFF,
                    }}
                  />
                </TouchableOpacity>
              </View>
            </View>
          </SafeAreaView>
        </ScrollView>
      </View>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    qrInfo: state.bankAirtimeServiceReducer.qrInfo,
    itemCatalog: state.collectionReducer.itemCatalog,
    dataCreateServiceRequest:
      state.bankAirtimeServiceReducer.dataCreateServiceRequest,
    dataSO: state.saleOrderPaymentReducer.dataSO,
    printerRetail: state.saleOrderPaymentReducer.printerRetail,
    printerVAT: state.saleOrderPaymentReducer.printerVAT,
    printerCommon: state.saleOrderPaymentReducer.printerCommon,
    defaultReport: state.saleOrderPaymentReducer.defaultReport,
    statePrinter: state.saleOrderPaymentReducer.statePrinter,
    reportContent: state.managerSOReducer.reportContent,
    stateReportContent: state.managerSOReducer.stateReportContent,
    userInfo: state.userReducer,
    dataQueryStatus: state.bankAirtimeServiceReducer.dataQueryStatus,
    dataPromotionGiftVoucher:
      state.collectInstallmentReducer.dataPromotionGiftVoucher,
    updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    actionBankAirtimeService: bindActionCreators(
      actionBankAirtimeServiceCreator,
      dispatch
    ),
    actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
    actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
    actionCollectInstallment: bindActionCreators(
      actionCollectInstallmentCreator,
      dispatch
    ),
  };
};

const Header = ({ title }) => {
  return (
    <View
      style={{
        width: constants.width,
        height: 50,
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: COLORS.bg2FB47C,
      }}
    >
      <MyText
        text={title}
        addSize={2}
        style={{
          fontWeight: "bold",
          color: COLORS.txtFFF6AD,
          textAlign: "center",
        }}
      />
    </View>
  );
};

export default connect(mapStateToProps, mapDispatchToProps)(CustomerExpense);
