import { Alert, Image, TouchableOpacity, View } from "react-native";
import React, { Component } from "react";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import SafeAreaView from "react-native-safe-area-view";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { dateH<PERSON>per, helper } from "@common";
import { MyText, hideBlockUI, showBlockUI } from "@components";
import * as actionCollectionCreator from "../../../CollectionTransfer/action";
import Accordion from "../../../InsuranceAirtimeService/component/Button/Accordion";
import { COLORS } from "../../../../styles";
import { constants } from "../../../../constants";
import * as actionBankAirtimeServiceCreator from "../../action";

class PaymentTransaction extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() { }

  componentDidUpdate(prevProps, prevState) { }

  handleSpendMoney = () => {
    const { AIRTIMETRANSACTIONID } = this.props?.route?.params ?? '';
    const { dataUploadAttachment, navigation } = this.props;
    const { AirtimeTransactionID, ServiceVoucherID } = dataUploadAttachment ?? '';
    const { actionBankAirtimeService } = this.props;
    const data = {
      "airtimeTransactionID": AirtimeTransactionID || AIRTIMETRANSACTIONID
    }
    showBlockUI();
    actionBankAirtimeService.getProcessoutVoucher(data).then((reponse) => {
      Alert.alert("Thông báo", reponse.Note, [
        {
          text: "Về màn hình quản lý",
          onPress: () => {
            hideBlockUI();
            this.props.navigation.navigate("HistorySell", {
              "SaleOrderID": ServiceVoucherID
            })
          },
        },
        {
          text: "Tiếp tục thực hiện dịch vụ",
          onPress: () => {
            hideBlockUI();
            const item = {
              AirtimeServiceGroupID: 35,
              AirtimeServiceGroupName: "Rút tiền ngân hàng",
              ServiceCategoryID: 12,
            }
            this.props.actionCollection.updateItemCatalog(item)
            this.props.navigation.navigate("BankAirtimeService")
          },
        },
      ]);

    }).catch(error => {
      Alert.alert("", error?.msgError, [{
        text: "OK",
        onPress: () => {
          hideBlockUI()
        }

      }])
    });
  }

  render() {
    const { dataUploadAttachment, navigation } = this.props;
    const { cus_AirTimeTransactionBO, AirtimeTransactionID, ServiceVoucherID } = dataUploadAttachment ?? {};
    const { PhoneNumber, CustomerName, Amount } = cus_AirTimeTransactionBO ?? {};
    const { } = this.state;

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: "white",
        }}
      >
        <KeyboardAwareScrollView
          style={{
            flex: 1,
          }}
          enableResetScrollToCoords={false}
          keyboardShouldPersistTaps="always"
          bounces={false}
          overScrollMode="always"
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          extraScrollHeight={60}
        >
          <SafeAreaView
            style={{
              flex: 1,
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <View
              style={{
                marginTop: 5,
                width: '100%'
              }}
            >
              <Image
                style={{
                  width: '100%',
                  height: 330,
                }}
                resizeMode={"stretch"}
                source={require("../../../../../assets/tpbank_pay.png")}
              />
            </View>
            <Accordion
              status={true}
              width={"100%"}
              title={"THÔNG TIN GIAO DỊCH"}
              Children={
                <View
                  style={{
                    padding: 5,
                    marginBottom: 5,
                    backgroundColor: COLORS.bgF0F0F0,
                    paddingBottom: 10,
                  }}
                >
                  <View
                    style={{
                      borderTopWidth: 2,
                      width: "98%",
                      alignSelf: "center",
                      borderColor: COLORS.bgA7A7A7,
                    }}
                  />
                  <TextField
                    name={"Mã giao dịch:"}
                    value={PhoneNumber}
                    color={COLORS.bg1E88E5}
                    fontWeight={true}
                  />
                  <TextField
                    name={"Tên khách hàng:"}
                    value={CustomerName}
                    fontWeight={false}
                  />
                  <MoneyField
                    title={"Số tiền chi:"}
                    value={helper.formatMoney(Amount)}
                    color={COLORS.bgFF0000}
                  />

                </View>
              }
            />
          </SafeAreaView>
          <View style={{
            flexDirection: "row-reverse",
            marginTop: 10,
            padding: 5
          }}>
            <MyText
              onPress={() =>
                this.props.navigation.navigate("HistorySell", {
                  "SaleOrderID": ServiceVoucherID
                })
              }
              text={"Về quản lý giao dịch"}
              addSize={-1.5}
              style={{
                color: COLORS.bg1E88E5,
                fontWeight: "bold",
                fontStyle: "italic",
                textDecorationLine: "underline"
              }} />
          </View>
          <View style={{
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <View
              style={{
                width: constants.width - 200,
                height: 50,
                flexDirection: "row",
                marginTop: 10,
              }}
            >
              <TouchableOpacity
                onPress={() => this.handleSpendMoney()}
                style={{
                  backgroundColor: "pink",
                  flex: 1,
                  height: 50,
                  borderRadius: 18,
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: COLORS.bgF49B0C,
                }}
              >
                <MyText
                  text={"CHI TIỀN"}
                  style={{
                    fontWeight: "bold",
                    color: COLORS.bgFFFFFF,
                  }}
                />
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAwareScrollView>
      </View>
    );
  }
}

const mapStateToProps = function (state) {
  return {
    itemCatalog: state.collectionReducer.itemCatalog,
    dataValidateServiceRequest: state.bankAirtimeServiceReducer.dataValidateServiceRequest,
    updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
    dataUploadAttachment: state.bankAirtimeServiceReducer.dataUploadAttachment
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(PaymentTransaction);

const TextField = ({
  name,
  value,
  color,
  titleColor = COLORS.txt8E8E93,
  fontWeight,
}) => {
  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 10,
      }}
    >
      <MyText
        text={name}
        addSize={-1.5}
        style={{
          color: titleColor,
          fontWeight: "500",
        }}
      />
      <MyText
        text={value}
        addSize={-1.5}
        style={{
          color: color,
          width: "auto",
          marginLeft: 5,
          flex: 1,
          textAlign: "right",
          fontWeight: "bold",
        }}
      />
    </View>
  );
};

const MoneyField = ({ title, value, color = COLORS.txt333333 }) => {
  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 10,
      }}
    >
      <MyText
        text={`${title}: `}
        addSize={-1.5}
        style={{
          color: COLORS.txt8E8E93,
          fontWeight: "700",
        }}
      />
      <MyText
        text={value}
        addSize={-1.5}
        style={{
          color: color,
          fontWeight: "bold",
        }}
      />
    </View>
  );
};
