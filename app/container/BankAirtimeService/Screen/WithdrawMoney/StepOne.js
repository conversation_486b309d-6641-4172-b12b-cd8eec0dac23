import { View, Animated, TouchableOpacity, Keyboard, Alert } from 'react-native';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SafeAreaView from 'react-native-safe-area-view';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { dateH<PERSON>per, helper } from '@common';
import { translate } from '@translate';
import { API_CONST, constants, } from '@constants';
import { COLORS } from '@styles';
import {
    CameraDOT,
    MyText,
    RadioButton,
    TitleInput,
    CaptureCamera,
    showBlockUI,
    hideBlockUI,
    DatePicker,
    Picker
} from '@components';
import * as actionBankAirtimeServiceCreator from "../../action";
import * as installmentAction from "../../../InstallmentManagerBC/action";
import { getImageCDN } from "../../../ActiveSimManager/action";
import InputAmount from '../../component/Input/InputAmount';
import CheckBoxConform from '../../component/Checkbox/CheckBoxConform';
class StepOne extends Component {
    constructor(props) {
        super(props);
        this.state = {
            scrollY: new Animated.Value(0),
            identificationNumber: '',
            loaderName: '',
            rechargerPhoneNumber: '',
            keyword: '',
            recipientName: '',
            depositAmount: '',
            rechargeFee: '',
            isVisibleCamera: false,
            lstBankAccount: [],
            itemCheckBankAccount: {},
            radioIDCardType: [
                {
                    title: "CMND",
                    sym: translate("collection.sym_id_card"),
                    selected: true,
                    value: 1
                },
                {
                    title: "CCCD",
                    sym: translate("collection.sym_id_card_2"),
                    selected: false,
                    value: 2
                },
                {
                    title: "Thẻ căn cước",
                    sym: "Thẻ căn cước",
                    selected: false,
                    value: 4
                }
            ],
            cus_FilePathFrontOfIDCard: "",
            cus_FilePathBackOfIDCard: "",
            urlImage: "",
            property: "",
            dateIssueIdentificationCar: "",
            receiptCode: "",
            customerNationalityID: 241,
            idCardType: 1,
            iDCardIssuePlaceList: [],
            client_IDCardIssuePlaceID: '',
            isCheck: false,
            amountLimit: {},
            isActiveAmount: true,
            productId: ''
        };
        this.timeOutScroll = null;
        this.isScrolling = false;
        this.keySearch = "";
    }

    componentDidMount() {
        this.getAmountLimitAirtimeService();
    }

    getAmountLimitAirtimeService = () => {
        showBlockUI()
        const {
            actionBankAirtimeService,
            navigation,
            updateHeaderAirtime
        } = this.props;
        const {
            itemCatalog
        } = this.props;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID
        } = itemCatalog ?? {};
        const {
            AirTimeTransactionTypeID
        } = updateHeaderAirtime ?? {};
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": AirTimeTransactionTypeID
        }
        actionBankAirtimeService.getAmountLimitAirtimeService(data).then((reponse) => {
            const filterData = reponse?.[0]?.ConstraintInfos;
            const filterProductId = reponse?.[0]?.ProductID
            this.setState({ amountLimit: filterData })
            this.setState({ productId: filterProductId })
            hideBlockUI();
        }).catch(msgError => {
            Alert.alert("", msgError, [{
                text: "OK",
                onPress: () => {
                    navigation.goBack();
                    hideBlockUI()
                }

            }])
        });
    }

    componentDidUpdate(prevProps, prevState) {
        if (this.props.dataQueryCustomerBank !== prevProps.dataQueryCustomerBank) {
            this.setState({
                recipientName: this.props.dataQueryCustomerBank.CustomerName
            })
        }
        if (prevState.identificationNumber !== this.state.identificationNumber || prevState.idCardType !== this.state.idCardType) {
            // Gọi hàm handleGet khi identificationNumber thay đổi
            this.getDataIssueplace(this.state.idCardType, 241, this.state.identificationNumber);
        }

        if (this.props.dataFeeCashin !== prevProps.dataFeeCashin) {
            this.setState({
                rechargeFee: this.props.dataFeeCashin?.CustFee?.TransFee
            })
        }

    }

    getDataIssueplace = (idType, nationalityID = 241, CardID) => {
        var length = idType == 1 ? 9 : 12;
        if (CardID != null && CardID.length == length) {
            this.props.actionBankAirtimeService.getIssueplace(idType, nationalityID, CardID).then(
                res => {
                    console.log("res res ", res);
                    this.setState({ iDCardIssuePlaceList: res })
                    this.setState({ client_IDCardIssuePlaceID: res[0].idCardIssuePlaceId, })
                }
            ).catch(
                err => {
                    Alert.alert(
                        translate('common.notification'),
                        err.msgError,
                        [
                            {
                                text: translate('common.btn_close'), onPress: () => {

                                }
                            }
                        ],
                        { cancelable: false }
                    )
                }
            )
        }
    }

    takePictureFE = (imageInfo, property, indexImage, client_IDCardType) => {
        helper.resizeImage(imageInfo).then(({ path, uri, size, name }) => {
            console.log("uri " + uri);
            showBlockUI();
            var urlImg = null;
            this.getBodyUpload({
                uri: uri,
                type: 'image/jpeg',
                name: name,
            }).then(res => {
                urlImg = res.url;
                if (indexImage != 2) {
                    var hardUrl = indexImage == 0 ? "https://drive.google.com/file/d/1z8F5IBLQaVGPLZMgo_s6FGeS72dfOyVT/view"
                        : "https://drive.google.com/file/d/1LnC0PnbDg_kQpqpdcQFN7ZlB-LfbTsPY/view"
                    if (client_IDCardType == 2) {
                        hardUrl = indexImage == 0 ? "http://10.1.12.58:50520/get_origin_image/20210621110839S-3XUskEB5e6BcVyFU4hDPC7-EiFjTpKbW4gyWiGQB9vWEQ"
                            : "http://10.1.12.58:50520/get_origin_image/20210621110757S-3XUskEB5e6BcVyFU4hDPC7-GDHjJar5yVSA5b5WoTK6y9"
                    }
                    this.getInfoCustomerByImage(uri, indexImage)
                        .then((response) => {
                            console.log("getInfoCustomerByImage response");
                            response.image_url = urlImg;
                            hideBlockUI();
                            console.log(response, property, indexImage, client_IDCardType, "response, property, indexImage,client_IDCardType")
                            const {
                                customerIDCard,
                                customerName,
                                idCardIssueDate
                            } = response;
                            if (indexImage == 0) {
                                this.setState({ identificationNumber: customerIDCard });
                                this.setState({ loaderName: customerName })
                            } else if (indexImage == 1) {
                                this.setState({ dateIssueIdentificationCar: idCardIssueDate })
                            }
                            this.updateInfoCardToState(response, property, indexImage);
                        })
                        .catch(
                            err => {
                                hideBlockUI();
                                this.setState({
                                    isVisibleCamera: false,
                                    [property]: urlImg
                                });
                            }
                        )
                }
                else {
                    hideBlockUI();
                    setState({
                        isVisibleCamera: false,
                        ...state.EPOSTransactionBO,
                        [property]: urlImg
                    });
                }
            }
            )
        }).catch((error) => {
            console.log("resizeImage", error);
        });
    }

    updateInfoCardToState = (objInfoCard, property, indexImage) => {
        //0: mt
        //1: ms
        //2: chan dung
        console.log("obj:", property);
        console.log(objInfoCard.customerName, "objInfoCard.customerName");
        console.log(objInfoCard);
        if (indexImage == 0) {
            var customerName = '';
            var middleName = '';
            var firstName = null;
            var lastName = null;
            if (objInfoCard.customerName != undefined && objInfoCard.customerName.length > 0) {
                customerName = objInfoCard.customerName.split(' ');
                if (customerName.length > 0) {
                    firstName = customerName[customerName.length - 1];
                    lastName = customerName.length > 1 ? customerName[0] : '';
                    if (customerName.length > 2) {
                        for (var i = 1; i < customerName.length - 1; i++) {
                            middleName += customerName[i] + ' ';
                        }
                        //Nếu têm đệm hơn 15 ký tự thì sẽ không gán vào cả họ, tên, tên đệm 
                        if (middleName.toString().trim().length < 15) {
                            middleName = middleName.toString().trim();
                        }
                        else {
                            firstName = '';
                            middleName = '';
                            lastName = '';
                        }
                    }
                }
            }
            var formatDate = helper.IsEmptyObject(objInfoCard.customerBirthday)
                ? null
                : objInfoCard.customerBirthday;
            // : (new Date(objInfoCard.customerBirthday));
            this.setState({
                isVisibleCamera: false,
            });
        }
        var formatIssueDate = helper.IsEmptyObject(objInfoCard.idCardIssueDate)
            ? null
            : objInfoCard.idCardIssueDate;
        // : (new Date(objInfoCard.idCardIssueDate));
        var formatExpiriedDate = helper.IsEmptyObject(objInfoCard.idCardExpiriedDate)
            ? null
            : objInfoCard.idCardExpiriedDate;
        // : (new Date(objInfoCard.idCardExpiriedDate));
        this.setState({
            isVisibleCamera: false,
            [property]: objInfoCard.image_url
        });
        // }
    }

    getTypeImage = (identificationType, indexImage) => {
        switch (identificationType) {
            case 1:
                switch (indexImage) {
                    case 0:
                        return 1;
                    case 1:
                        return 3;
                    default:
                        return 0;
                }
            case 2:
                switch (indexImage) {
                    case 0:
                        return 2;
                    case 1:
                        return 4;
                    default:
                        return 0;
                }
            case 4:
                switch (indexImage) {
                    case 0:
                        return 11;
                    case 1:
                        return 12;
                    default:
                        return 0;
                }

            default:
                return 0;
        }
    }

    getInfoCustomerByImage = (uriImage, indexImage) => {
        const {
            userInfo
        } = this.props;
        return new Promise((resolve, reject) => {
            var typeImageDetect = this.getTypeImage(1, indexImage)
            console.log(typeImageDetect, "typeImageDetect");
            // if (typeImageDetect != 0) {
            let bodyFromData = new FormData();
            bodyFromData.append('file', {
                uri: uriImage,
                type: 'image/jpg',
                name: "getInfoCustomerByImage" + dateHelper.getTimestamp()
            });
            console.log(bodyFromData, "bodyFromData");
            bodyFromData.append('client_id', `MWGPOS_${userInfo.userName}`);
            bodyFromData.append('chosen_side', typeImageDetect);
            console.log(bodyFromData, "bodyFromData");
            console.log(bodyFromData, "bodyFromData==>")
            installmentAction.getInfoByImage(bodyFromData)
                .then(
                    res => {
                        switch (typeImageDetect) {
                            case 1:
                                resolve({
                                    provinceID: res.cap_1_id,
                                    districtID: res.cap_2_id,
                                    wardID: res.cap_3_id,
                                    provinceName: res.cap1,
                                    districtName: res.cap2,
                                    wardName: res.cap3,
                                    customerBirthday: res.date_of_birth,
                                    customerName: res.full_name,
                                    customerAddress: res.place_of_permanent,
                                    customerIDCard: res.id_no,
                                    image_url: res.image_url,
                                    streetAddress: res.cap4,
                                    idCardExpiriedDate: res.expiration_date
                                }
                                );
                                break;
                            case 2:
                                resolve({
                                    provinceID: res.cap_1_id,
                                    districtID: res.cap_2_id,
                                    wardID: res.cap_3_id,
                                    provinceName: res.cap1,
                                    districtName: res.cap2,
                                    wardName: res.cap3,
                                    customerBirthday: res.date_of_birth,
                                    customerName: res.full_name,
                                    customerAddress: res.place_of_permanent,
                                    customerIDCard: res.id_no,
                                    image_url: res.image_url,
                                    streetAddress: res.cap4,
                                    idCardExpiriedDate: res.expiration_date
                                }
                                );
                                break;
                            case 3:
                                resolve({
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    idCardIssuePlace: res.place_of_issue_id,
                                });
                                break;
                            case 4:
                                resolve({
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    idCardIssuePlace: res.place_of_issue_id,
                                });
                                break;
                            case 11:
                                resolve({
                                    provinceID: res.cap_1_id,
                                    districtID: res.cap_2_id,
                                    wardID: res.cap_3_id,
                                    provinceName: res.cap1,
                                    districtName: res.cap2,
                                    wardName: res.cap3,
                                    customerBirthday: res.date_of_birth,
                                    customerName: res.full_name,
                                    customerBirthday: birthday,
                                    customerIDCard: res.id_no,
                                    image_url: res.image_url,
                                    streetAddress: res.cap4,
                                    idCardExpiriedDate: res.expiration_date
                                }
                                );
                                break;
                            case 12:
                                resolve({
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    idCardIssuePlace: res.place_of_issue_id
                                });
                                break;

                            default:
                                console.log(res)
                                break;
                        }
                    }
                ).catch(
                    err => {
                        console.log("err getInfoCustomerByImage", err);
                        reject(err);
                    }
                );
            // }
        });
    }

    uploadPicture = (fromData) => {
        const { API_GET_IMAGE_CDN } = API_CONST;
        return new Promise((resolve, reject) => {
            getImageCDN(fromData).then(cdnImages => {
                console.log("uploadPicture url", API_GET_IMAGE_CDN + cdnImages[0]);
                resolve({ url: `${API_GET_IMAGE_CDN + cdnImages[0]}` })
            }).catch(error => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('instalmentManager.upload_image_error'),
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: hideBlockUI,
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => this.uploadPicture(fromData),
                            style: "default"
                        },
                    ],
                    { cancelable: false },
                );
            })
        });
    }

    getBodyUpload = (file) => {
        const fromData = new FormData();
        fromData.append('file', file);
        console.log(file, fromData, "fromData")
        return this.uploadPicture(fromData)
    }

    removePicture = (property, property2) => () => {
        this.setState({
            [property]: "",
            [property2]: "",
        });
    }

    selectItemCardType = (index) => {
        const {
            radioIDCardType
        } = this.state;
        const newRadioIDCardType = radioIDCardType.map((item) => ({
            ...item,
            selected: false
        }));
        newRadioIDCardType[index].selected = true;
        this.setState({ radioIDCardType: newRadioIDCardType });
        console.log("giatrivalue", newRadioIDCardType[index].value)
        this.setState({ idCardType: newRadioIDCardType[index].value })
    };

    handleCheckBankAccount = () => {
        const {
            identificationNumber,
            loaderName,
            rechargerPhoneNumber,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            client_IDCardIssuePlaceID,
            receiptCode,
            depositAmount,
            dateIssueIdentificationCar,
            isCheck,
            idCardType,
            amountLimit,
            isActiveAmount,
            productId
        } = this.state;
        const {
            actionBankAirtimeService,
            navigation,
            updateHeaderAirtime
        } = this.props;
        const {
            itemCatalog
        } = this.props;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID
        } = itemCatalog ?? {};
        const {
            AirTimeTransactionTypeID
        } = updateHeaderAirtime ?? {};
        const min = amountLimit?.[0]?.ConstraintValue?.minvalue;
        const max = amountLimit?.[0]?.ConstraintValue?.maxvalue;
        Keyboard.dismiss();
        const regExpIDCard9 = new RegExp(/^\d{9}$/);
        const regExpIDCard12 = new RegExp(/^\d{12}$/);
        const isValidateIDCard9 = regExpIDCard9.test(identificationNumber);
        const isValidateIDCard12 = regExpIDCard12.test(identificationNumber);
        const isValidateIDCard = isValidateIDCard9 || isValidateIDCard12;
        const regExpPhoneNumber10 = new RegExp(/^\d{10}$/);
        const isValidateRechargerPhoneNumber = regExpPhoneNumber10.test(rechargerPhoneNumber);
        if (cus_FilePathFrontOfIDCard == "") {
            Alert.alert("", translate('collection.take_front_id_card'))
            return false;
        }
        if (cus_FilePathBackOfIDCard == "") {
            Alert.alert("", translate("collection.take_back_id_card"))
            return false;
        }
        if (!isValidateIDCard) {
            Alert.alert("", translate("collection.check_id_card"));
            return false;
        }
        if (dateIssueIdentificationCar == "") {
            Alert.alert("", "Vui lòng chọn ngày cấp");
            return false;
        }
        if (client_IDCardIssuePlaceID == "") {
            Alert.alert("", "Vui lòng chọn nơi cấp");
            return false;
        }
        if (loaderName == "") {
            Alert.alert("", "Vui lòng nhập tên người nhận")
            return false;
        }
        if (!isValidateRechargerPhoneNumber) {
            Alert.alert("", translate("collection.check_phone"));
            return false;
        }
        if (identificationNumber == "") {
            Alert.alert("", translate("collection.enter_card_number"))
            return false;
        }
        if (rechargerPhoneNumber == "") {
            Alert.alert("", "Vui lòng nhập số điện thoại người nhận")
            return false;
        }
        if (receiptCode == "") {
            Alert.alert("", "Vui lòng nhập mã nhận tiền")
            return false;
        }
        if (depositAmount == "") {
            Alert.alert("", "Vui lòng nhập số tiền nhận")
            return false;
        }
        if (isActiveAmount) {
            Alert.alert("", `Vui lòng nhập số tiền nhận trong khoảng ${helper.formatMoney(min)} -  ${helper.formatMoney(max)}`)
            return false;
        }
        if (isCheck == false) {
            Alert.alert("", "Xác nhận thông tin nhận tiền đã chính xác trước khi thực hiện thao tác tiếp theo")
            return false;
        }
        else {
            showBlockUI();
            const data = {
                "catalogID": ServiceCategoryID,
                "serviceGroupID": AirtimeServiceGroupID,
                "airtimeTransactionTypeID": AirTimeTransactionTypeID,
                "customerName": loaderName,
                "customerPhone": rechargerPhoneNumber,
                "customerIDCard": identificationNumber,
                "phoneNumber": receiptCode,
                "amount": depositAmount
            }
            actionBankAirtimeService.validateDataServiceRequest(data).then((reponsePromotion) => {
                hideBlockUI();
                navigation.navigate('StepTwo', {
                    depositAmount,
                    loaderName,
                    receiptCode,
                    rechargerPhoneNumber,
                    cus_FilePathFrontOfIDCard,
                    cus_FilePathBackOfIDCard,
                    idCardType,
                    identificationNumber,
                    dateIssueIdentificationCar,
                    client_IDCardIssuePlaceID,
                    productId
                });
            })
                .catch((msgError) => {
                    hideBlockUI();
                    Alert.alert("", msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                hideBlockUI();
                            },
                        },
                    ]);
                });
        }
    }

    render() {
        const {
            identificationNumber,
            loaderName,
            rechargerPhoneNumber,
            depositAmount,
            radioIDCardType,
            isVisibleCamera,
            dateIssueIdentificationCar,
            receiptCode,
            isCheck,
            amountLimit,
            isActiveAmount
        } = this.state;
        const selectedCard = radioIDCardType.find(
            (item) => item.selected
        );
        const client_IDCardType = selectedCard.value;
        const min = amountLimit?.[0]?.ConstraintValue?.minvalue;
        const max = amountLimit?.[0]?.ConstraintValue?.maxvalue;
        console.log(amountLimit, min, max, depositAmount, "reponseAmountLimit")
        return (
            <View style={{
                flex: 1,
                backgroundColor: 'white'
            }}>
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            alignItems: 'center'
                        }}>
                        <View
                            style={{
                                width: constants.width - 20,
                                marginTop: 10
                            }}>
                            <MyText style={{
                                marginTop: 10,
                                marginBottom: 10,
                                fontWeight: 'bold',
                                color: COLORS.bg00AAFF,
                                fontSize: 15,
                                fontStyle: 'italic',
                                textDecorationLine: 'underline',
                            }}
                                text={translate("collection.sender_information")}
                            />
                            <View style={{
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 7,
                                padding: 10,
                                alignItems: 'center',
                                shadowColor: COLORS.bg7F7F7F,
                                shadowOffset: {
                                    width: 0,
                                    height: 0,
                                },
                                shadowOpacity: 0.5,
                                shadowRadius: 1,
                                elevation: 5,
                            }}>
                                <View
                                    style={{ marginVertical: 5, flexDirection: 'row', alignItems: 'center', marginLeft: 10 }}>
                                    <RadioButton
                                        style={{
                                            flexDirection: 'row'
                                        }}
                                        containerStyle={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            width: constants.width / 4
                                        }}
                                        dataItems={this.state.radioIDCardType}
                                        selectItem={this.selectItemCardType}
                                        mainComponent={(item) => {
                                            return (
                                                <MyText
                                                    text={item.title}
                                                    style={{
                                                        color: item.selected
                                                            ? COLORS.txtFF8900
                                                            : COLORS.txt333333,
                                                        marginLeft: 2,
                                                        fontSize: 15
                                                    }}
                                                />
                                            );
                                        }}
                                    />
                                </View>
                                <View
                                    style={{
                                        justifyContent: 'center'
                                    }}>
                                    <MyText style={{
                                        marginLeft: 5,
                                        padding: 10
                                    }}
                                        text={`${translate("collection.take_a_shot")} ${selectedCard.sym} ${translate("collection.front")}`}
                                    />
                                    <View
                                        style={{
                                            marginVertical: 0,
                                            paddingHorizontal: 10,
                                            alignItems: 'center',
                                            width: constants.width
                                        }}>
                                        <CameraDOT
                                            uriImage={this.state.cus_FilePathFrontOfIDCard}
                                            onTakePicture={(response) => {
                                                this.takePictureFE(response, "cus_FilePathFrontOfIDCard", 0, client_IDCardType)

                                            }}
                                            onDelete={this.removePicture("cus_FilePathFrontOfIDCard", "client_ImageFrontOfIDCard")}
                                        />
                                    </View>
                                    <MyText
                                        style={{
                                            marginLeft: 5,
                                            padding: 10
                                        }}
                                        text={`${translate("collection.take_a_shot")} ${selectedCard.sym} ${translate("collection.backside")}`}
                                    />
                                    <View
                                        style={{
                                            marginVertical: 0,
                                            paddingHorizontal: 10,
                                            alignItems: 'center',
                                            width: constants.width
                                        }}>
                                        <CameraDOT
                                            uriImage={this.state.cus_FilePathBackOfIDCard}
                                            onTakePicture={(response) => {
                                                this.takePictureFE(response, "cus_FilePathBackOfIDCard", 1, client_IDCardType)
                                            }}
                                            onDelete={this.removePicture("cus_FilePathBackOfIDCard", "client_ImageBackOfIDCard")}
                                        />
                                    </View>
                                </View>
                            </View>
                            <View style={{
                                marginTop: 20,
                            }}>
                                <TitleInput
                                    title={"Số CMND/CCCD: "}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 10,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8
                                    }}
                                    placeholder={translate("collection.placeholder_ID_card_number")}
                                    value={identificationNumber}
                                    onChangeText={(text) => {
                                        let validate = client_IDCardType == 1 ? new RegExp(/^\d{0,9}$/) : new RegExp(/^\d{0,12}$/);
                                        if (validate.test(text) || text == "") {
                                            this.setState({ identificationNumber: text })
                                        }
                                    }}
                                    keyboardType="numeric"
                                    returnKeyType="done"
                                    onSubmitEditing={Keyboard.dismiss}
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ identificationNumber: '' });
                                    }}
                                    key="identificationNumber"
                                    isRequired={true}
                                />
                                <View style={{
                                    marginVertical: -5
                                }}>
                                    <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                                        text={"Ngày cấp: "}
                                        children={<MyText text={'*'} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                                    />
                                    <DatePicker
                                        date={dateIssueIdentificationCar}
                                        format={'YYYY-MM-DD'}
                                        onDateChange={(dateStr) => {
                                            this.setState({ dateIssueIdentificationCar: dateStr })

                                        }}
                                    />
                                </View>
                                <View style={{
                                    marginBottom: 8,
                                    marginTop: 8
                                }}>
                                    <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                                        text={'Nơi cấp: '}
                                        children={<MyText text={"*"} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                                    />
                                    <View style={{
                                        borderWidth: 1.5,
                                        borderColor: COLORS.bdE4E4E4,
                                        alignItems: "center",
                                        justifyContent: "center",
                                        borderRadius: 5,
                                        height: "auto",
                                        backgroundColor: COLORS.bgFFFFFF,
                                    }}>
                                        <Picker
                                            label={"idCardIssuePlaceName"}
                                            value={"idCardIssuePlaceId"}
                                            data={this.state.iDCardIssuePlaceList}
                                            valueSelected={this.state.client_IDCardIssuePlaceID}
                                            onChange={(item) => {
                                                this.setState({ client_IDCardIssuePlaceID: item.idCardIssuePlaceId, });
                                            }}
                                            defaultLabel={translate('instalmentManager.header_place')}
                                            style={{
                                                flex: 1,
                                                flexDirection: "row",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                height: 30,
                                                marginVertical: 5,
                                                marginLeft: -5,
                                                marginRight: -10,
                                            }}
                                        />
                                    </View>
                                </View>
                                <TitleInput
                                    title={'Họ và tên người nhận: '}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 10,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8,
                                    }}
                                    placeholder={"Nhập họ và tên người nhận"}
                                    value={loaderName}
                                    onChangeText={(text) => {
                                        if (helper.isValidateCharVN(text)) {
                                            this.setState({ loaderName: text });
                                        }
                                    }}
                                    keyboardType={"default"}
                                    returnKeyType={"done"}
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ loaderName: '' });
                                    }}
                                    key="loaderName"
                                    isRequired={true}
                                />
                                <TitleInput
                                    title={"Số điện thoại người nhận: "}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 5,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8
                                    }}
                                    placeholder={"Nhập số điện thoại người nhận"}
                                    value={rechargerPhoneNumber}
                                    onChangeText={(text) => {
                                        const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                        const isValidate = regExpPhone.test(text) || (text == "");
                                        if (isValidate) {
                                            this.setState({ rechargerPhoneNumber: text });
                                        }
                                    }}
                                    keyboardType="numeric"
                                    returnKeyType="done"
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ rechargerPhoneNumber: '' });
                                    }}
                                    key="rechargerPhoneNumber"
                                    isRequired={true}
                                />
                                <TitleInput
                                    title={"Mã nhận tiền: "}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 5,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8
                                    }}
                                    placeholder={"Nhập mã nhận tiền"}
                                    value={receiptCode}
                                    onChangeText={(text) => {
                                        const isValidateCharVN = (text) => {
                                            const regex = /^[a-zA-Z0-9À-ỹ\s]*$/;
                                            return regex.test(text);
                                        }
                                        if (isValidateCharVN(text)) {
                                            this.setState({ receiptCode: text });
                                        }
                                    }}
                                    keyboardType={"default"}
                                    returnKeyType="done"
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ receiptCode: '' });
                                    }}
                                    key="receiptCode"
                                    isRequired={true}
                                />
                                <InputAmount
                                    title={"Số tiền nhận: "}
                                    placeholder={translate("collection.enter_amount_want_to_deposit")}
                                    value={depositAmount}
                                    onChange={(text) => {
                                        if (text >= min && text <= max) {
                                            this.setState({
                                                depositAmount: text,
                                            });
                                            this.setState({ isActiveAmount: false })
                                        } else {
                                            this.setState({ isActiveAmount: true })
                                        }
                                    }}
                                    disabled={false}
                                    isRequired={true}
                                />
                                {isActiveAmount ? (
                                    <MyText
                                        text={`(Số tiền nhận nhập trong khoảng ${helper.formatMoney(min)} - ${helper.formatMoney(max)})`}
                                        style={{ color: COLORS.txtFF0000, paddingVertical: 5, paddingTop: 0, fontSize: 12, alignSelf: 'flex-end', fontStyle: 'italic' }}
                                    />
                                ) : null}
                                <CheckBoxConform
                                    onPress={() => this.setState({ isCheck: !isCheck })}
                                    isCheck={isCheck}
                                    title={"Xác nhận thông tin nhận tiền đã chính xác \nLưu ý: Mã nhận tiền chỉ được truy vấn tối đa 3 lần"}
                                />
                                <View
                                    style={{
                                        width: constants.width - 25,
                                        height: 50,
                                        flexDirection: "row",
                                        marginTop: 10,
                                    }}
                                >
                                    <TouchableOpacity
                                        onPress={() => this.props.navigation.goBack()}
                                        style={{
                                            width: 120,
                                            height: 50,
                                            borderRadius: 18,
                                            borderWidth: 2,
                                            borderColor: COLORS.bg00A98F,
                                            alignItems: "center",
                                            justifyContent: "center",
                                            backgroundColor: "white",
                                        }}
                                    >
                                        <MyText
                                            text={"QUAY LẠI"}
                                            style={{
                                                fontWeight: "bold",
                                                color: COLORS.bg00A98F,
                                            }}
                                        />
                                    </TouchableOpacity>
                                    <View
                                        style={{
                                            width: 10,
                                        }}
                                    />
                                    <TouchableOpacity
                                        onPress={() => this.handleCheckBankAccount()}
                                        style={{
                                            backgroundColor: "pink",
                                            flex: 1,
                                            height: 50,
                                            borderRadius: 18,
                                            alignItems: "center",
                                            justifyContent: "center",
                                            backgroundColor: COLORS.bgF49B0C,
                                        }}
                                    >
                                        <MyText
                                            text={"TIẾP THEO"}
                                            style={{
                                                fontWeight: "bold",
                                                color: COLORS.bgFFFFFF,
                                            }}
                                        />
                                    </TouchableOpacity>
                                </View>
                            </View>

                        </View>
                    </SafeAreaView>
                    <CaptureCamera
                        iisVisibleCamera={isVisibleCamera}
                        disabledUploadImage={true}
                        takePicture={(camera) => {
                            this.takePicture(camera);
                        }}
                        closeCamera={() => {
                            this.setState({ ...state, isVisibleCamera: false })
                        }}
                        selectPicture={() => {
                            selectImage();
                        }}
                    />
                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        dataQueryCustomerBank: state.collectionReducer.dataQueryCustomerBank,
        stateQueryCustomerBank: state.collectionReducer.stateQueryCustomerBank,
        dataFeeCashin: state.collectionReducer.dataFeeCashin,
        userInfo: state.userReducer,
        itemCatalog: state.collectionReducer.itemCatalog,
        updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(StepOne);
