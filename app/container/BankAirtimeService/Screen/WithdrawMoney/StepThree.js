import { Alert, Image, TouchableOpacity, View } from "react-native";
import React, { Component } from "react";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import SafeAreaView from "react-native-safe-area-view";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { date<PERSON><PERSON><PERSON>, helper } from "@common";
import {
    MyText,
    TitleInput,
    showBlockUI,
    hideBlockUI,
    Button
} from '@components';
import * as actionCollectionCreator from "../../../CollectionTransfer/action";
import { COLORS } from "../../../../styles";
import { constants } from "../../../../constants";
import Camera from "../../component/Camera";
import { translate } from "../../../../translations";
import ImageProcess from "../../component/SignImage/ImageProcess";
import SignImage from "../../component/SignImage/SignImage";
import { API_CONST } from '@constants';
import { getImageCDN } from '../../../ActiveSimManager/action.js';
import * as actionGetSimCreator from '../../../ActiveSimManager/action';
import RNFetchBlob from 'rn-fetch-blob';
import * as actionBankAirtimeServiceCreator from "../../action";

class StepThree extends Component {
    constructor(props) {
        super(props);
        this.state = {
            customerPortrait: [],
            linkSignImage: "",
            isVisibleSign: false,
            signature: '',
            signBase64: '',
            isCheck: false,
            scrollEnabled: true,
            uriImages: [],
        };
    }

    componentDidMount() {}

    componentDidUpdate(prevProps, prevState) { }

    closeSignature = () => {
        this.setState({ isVisibleSign: false });
    };

    takeSignature = (signature) => {
        showBlockUI()
        this.setState({
            isVisibleSign: false,
        });
        let timestamp = Date.now()
        const fs = RNFetchBlob.fs
        const filePathSignature = `${fs.dirs.DocumentDir}/signature/${timestamp}.png`
        fs.writeFile(filePathSignature, signature.split(",")[1], 'base64').then((response) => {
            const newURIImages = [...this.state.uriImages];
            const uri_path = Platform.OS == 'ios'
                ? filePathSignature
                : `file://${filePathSignature}`;
            const bodyFromData = new FormData();
            bodyFromData.append('file', {
                uri: uri_path,
                type: 'image/png',
                name: `takeSignature${dateHelper.getTimestamp()}`
            }); actionGetSimCreator.getImageCDN(bodyFromData)
                .then((res) => {
                    hideBlockUI()
                    const remoteSignatureURI =
                        API_CONST.API_GET_IMAGE_CDN + res[0];
                    this.setState({ linkSignImage: remoteSignatureURI })
                    this.setState({ signature: remoteSignatureURI });
                    console.log(remoteSignatureURI, "remoteSignatureURI")
                    hideBlockUI();
                })
                .catch((error) => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        error.msgError,
                        [
                            {
                                text: translate('common.btn_skip'),
                                onPress: hideBlockUI,
                                style: 'cancel'
                            },
                            {
                                text: translate(
                                    'common.btn_notify_try_again'
                                ),
                                onPress: () => {
                                    this.takeSignature(signature);
                                },
                                style: 'default'
                            }
                        ],
                        { cancelable: false }
                    );
                });
        }).catch((error) => {
            hideBlockUI()
            console.log("🚀 ~ SimProcess ~ fs.createFile ~ error:", error)
        });

    };

    handleCreateOrder = () => {
        this.props.navigation.navigate("StepThree")
    }

    handleContinue = () => {
        const {
            customerPortrait,
            signature
        } = this.state;
        const {
            actionBankAirtimeService,
            dataCreateServiceRequest
        } = this.props;
        const { cus_AirtimeTransactionSVMapBO } = dataCreateServiceRequest;
        const { ServiceVoucherID } = cus_AirtimeTransactionSVMapBO ?? '';
        const { SERVICEVOUCHERID } = this.props?.route?.params ?? ''
        const portrait = customerPortrait?.[0]?.UrlFile;
        if (portrait == "") {
            Alert.alert("", "Vui lòng chụp chân dung khách hàng");
            return false;
        }
        if (signature == "") {
            Alert.alert("", "Chứ ký khách hàng không được để trống");
            return false;
        }
        else {
            showBlockUI();
            const data = {
                "serviceVoucherID": ServiceVoucherID || SERVICEVOUCHERID,
                "airTimeTransactionBO": {
                    "cus_AirTimeTransactionAttachBOLst": [
                        {
                            "FilePath": portrait,
                            "cus_ShortName": "ACD"
                        },
                        {
                            "FilePath": signature,
                            "cus_ShortName": "SIGN"
                        }
                    ]
                }
            }
            actionBankAirtimeService.uploadAirtimeAttachment(data).then((reponsePromotion) => {
                hideBlockUI();
                this.props.navigation.navigate("OTPCreOrder", { isCalled: true })
            })
                .catch((msgError) => {
                    hideBlockUI();
                    Alert.alert("", msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                hideBlockUI();
                            },
                        },
                    ]);
                });
        }
    }

    render() {
        const {
            customerPortrait,
            linkSignImage,
            isCheck,
        } = this.state;
        return (
            <View
                style={{
                    flex: 1,
                    backgroundColor: "white",
                }}
            >
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                    scrollEnabled={this.state.scrollEnabled}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <MyText
                            text={"Chân dung khách hàng"}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt333333,
                                fontWeight: "bold",
                                fontStyle: "italic",
                                marginBottom: 10,
                                marginTop: 15
                            }}>
                            {
                                <MyText
                                    text={"*"}
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.txtFF0000
                                    }}
                                />
                            }
                        </MyText>
                        <View
                            style={{
                                flex: 1,
                                borderWidth: 1,
                                margin: 10,
                                width: 350,
                                height: 200,
                                borderColor: COLORS.bg19A796,
                                paddingBottom: 15,
                                paddingHorizontal: 10,
                                borderRadius: 10,
                                justifyContent: "center",
                                alignItems: "center",
                            }}
                        >
                            <Camera
                                lstImage={customerPortrait}
                                handleGetImage={(strImage) => {
                                    this.setState({ customerPortrait: strImage })
                                }}
                            />
                        </View>
                        <MyText
                            text={translate("collection.customer_signature")}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt333333,
                                fontWeight: "bold",
                                fontStyle: "italic",
                                marginBottom: 10
                            }}>
                            {
                                <MyText
                                    text={"*"}
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.txtFF0000
                                    }}
                                />
                            }
                        </MyText>
                        <View style={{
                            width: '100%',
                            height: linkSignImage ? 200 : 400,
                            justifyContent: 'center'
                        }}>
                            {
                                linkSignImage ?
                                    <View
                                        style={{
                                            width: '100%',
                                            height: 220,
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}
                                    >
                                        <View style={{
                                            flex: 1,
                                            borderWidth: 1,
                                            margin: 10,
                                            width: 350,
                                            height: 230,
                                            borderColor: COLORS.bg19A796,
                                            paddingBottom: 15,
                                            paddingHorizontal: 10,
                                            borderRadius: 10,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            marginTop: 10
                                        }}>
                                            <ImageProcess
                                                onCamera={() => {
                                                    this.setState({
                                                        isVisibleSign: true
                                                    });
                                                }}
                                                urlImageLocal={this.state.signature}
                                                urlImageRemote={this.state.signature}
                                                deleteImage={() => {
                                                    this.setState({ signature: '' })
                                                    this.setState({ linkSignImage: '' })
                                                }}
                                                isSignature={true}
                                            />
                                        </View>
                                    </View>
                                    :
                                    <SignImage
                                        onBegin={() => {
                                            this.setState({
                                                scrollEnabled: false
                                            })
                                        }}
                                        onEnd={() => {
                                            this.setState({
                                                scrollEnabled: true
                                            })
                                        }}
                                        isVisibleSign={() => this.state.isVisibleSign}
                                        takeSignature={this.takeSignature}
                                        closeSignature={() => { this.setState({ isVisibleSign: false }) }}
                                    />
                            }
                        </View>
                        <View
                            style={{
                                width: constants.width - 25,
                                height: 50,
                                flexDirection: "row",
                                marginTop: 20,
                            }}
                        >
                            <TouchableOpacity
                                onPress={() => this.props.navigation.goBack()}
                                style={{
                                    width: 120,
                                    height: 50,
                                    borderRadius: 18,
                                    borderWidth: 2,
                                    borderColor: COLORS.bg00A98F,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    backgroundColor: "white",
                                }}
                            >
                                <MyText
                                    text={"QUAY LẠI"}
                                    style={{
                                        fontWeight: "bold",
                                        color: COLORS.bg00A98F,
                                    }}
                                />
                            </TouchableOpacity>
                            <View
                                style={{
                                    width: 10,
                                }}
                            />
                            <TouchableOpacity
                                onPress={() => this.handleContinue()}
                                style={{
                                    backgroundColor: "pink",
                                    flex: 1,
                                    height: 50,
                                    borderRadius: 18,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    backgroundColor: COLORS.bgF49B0C,
                                }}
                            >
                                <MyText
                                    text={"TẠO GIAO DỊCH"}
                                    style={{
                                        fontWeight: "bold",
                                        color: COLORS.bgFFFFFF,
                                    }}
                                />
                            </TouchableOpacity>
                        </View>
                    </SafeAreaView>
                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        dataValidateServiceRequest: state.bankAirtimeServiceReducer.dataValidateServiceRequest,
        dataCreateServiceRequest: state.bankAirtimeServiceReducer.dataCreateServiceRequest,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionGetSim: bindActionCreators(actionGetSimCreator, dispatch),
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(StepThree);

const TextField = ({
    name,
    value,
    color,
    titleColor = COLORS.txt8E8E93,
    fontWeight,
}) => {
    return (
        <View
            style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginTop: 10,
            }}
        >
            <MyText
                text={name}
                addSize={-1.5}
                style={{
                    color: titleColor,
                    fontWeight: "500",
                }}
            />
            <MyText
                text={value}
                addSize={-1.5}
                style={{
                    color: color,
                    width: "auto",
                    marginLeft: 5,
                    flex: 1,
                    textAlign: "right",
                    fontWeight: "bold",
                }}
            />
        </View>
    );
};

const MoneyField = ({ title, value, color = COLORS.txt333333 }) => {
    return (
        <View
            style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginTop: 10,
            }}
        >
            <MyText
                text={`${title}: `}
                addSize={-1.5}
                style={{
                    color: COLORS.txt8E8E93,
                    fontWeight: "700",
                }}
            />
            <MyText
                text={value}
                addSize={-1.5}
                style={{
                    color: color,
                    fontWeight: "bold",
                }}
            />
        </View>
    );
};
