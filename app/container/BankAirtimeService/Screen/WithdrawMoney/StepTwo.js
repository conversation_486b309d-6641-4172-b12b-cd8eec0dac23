import { Alert, Image, TouchableOpacity, View } from "react-native";
import React, { Component } from "react";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import SafeAreaView from "react-native-safe-area-view";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { dateHelper, helper } from "@common";
import { MyText, hideBlockUI, showBlockUI } from "@components";
import * as actionCollectionCreator from "../../../CollectionTransfer/action";
import Accordion from "../../../InsuranceAirtimeService/component/Button/Accordion";
import { COLORS } from "../../../../styles";
import { constants } from "../../../../constants";
import * as actionBankAirtimeServiceCreator from "../../action";
import moment from "moment";

class StepTwo extends Component {
    constructor(props) {
        super(props);
        this.state = {};
    }

    componentDidMount() { }

    componentDidUpdate(prevProps, prevState) { }

    handleCreateOrder = (customerData) => {
        const {
            navigation,
            itemCatalog,
            actionBankAirtimeService,
            updateHeaderAirtime
        } = this.props;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID
        } = itemCatalog ?? {};
        const {
            AirTimeTransactionTypeID
        } = updateHeaderAirtime ?? {};
        const {
            depositAmount,
            loaderName,
            receiptCode,
            rechargerPhoneNumber,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            idCardType,
            identificationNumber,
            dateIssueIdentificationCar,
            client_IDCardIssuePlaceID,
            productId
        } = customerData;
        const inputDate = dateIssueIdentificationCar;
        const formattedDate = moment(inputDate).format("DD/MM/YYYY");
        showBlockUI();
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": AirTimeTransactionTypeID,
            "airTimeTransactionBO": {
                "productid": productId,
                "amount": depositAmount,
                "fee": 0,
                "phoneNumber": receiptCode,
                "inputPrice": depositAmount,
                "salePrice": depositAmount,
                "customerName": loaderName,
                "customerPhone": rechargerPhoneNumber,
                "cus_AirTimeTransactionAttachBOLst": [
                    {
                        // "FilePath": "https://cdn.tgdd.vn/erp/MWGLogos/erablue_logo_20221012.jpg",
                        "FilePath": cus_FilePathFrontOfIDCard,
                        "cus_ShortName": "CMT"
                    },
                    {
                        // "FilePath": "https://cdn.tgdd.vn/erp/MWGLogos/erablue_logo_20221012.jpg",
                        "FilePath": cus_FilePathBackOfIDCard,
                        "cus_ShortName": "CMS"
                    }
                ]
            },
            "ExtraData": {
                "customerCardType": idCardType,
                "customerIDCard": identificationNumber,
                "customerCardIssueDate": formattedDate,
                "customerCardIssuePlaceID": client_IDCardIssuePlaceID
            }
        }
        actionBankAirtimeService.getDataCreateServiceRequest(data).then((reponsePromotion) => {
            hideBlockUI();
            this.props.navigation.navigate("StepThree")
        })
            .catch((msgError) => {
                hideBlockUI();
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }

    render() {
        const { } = this.state;
        const { dataValidateServiceRequest } = this.props;
        const { MWGData, PartnerData, ValidateResult } = dataValidateServiceRequest ?? {};
        const { IsMismatch } = ValidateResult;
        const {
            depositAmount,
            loaderName,
            receiptCode,
            rechargerPhoneNumber,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            idCardType,
            identificationNumber,
            dateIssueIdentificationCar,
            client_IDCardIssuePlaceID,
            productId
        } = this.props.route.params ?? {};
        const customerData = {
            depositAmount,
            loaderName,
            receiptCode,
            rechargerPhoneNumber,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            idCardType,
            identificationNumber,
            dateIssueIdentificationCar,
            client_IDCardIssuePlaceID,
            productId
        }
        return (
            <View
                style={{
                    flex: 1,
                    backgroundColor: "white",
                }}
            >
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <View
                            style={{
                                marginTop: 5,
                            }}
                        >
                            <Image
                                style={{
                                    width: 250,
                                    height: 140,
                                }}
                                resizeMode={"stretch"}
                                source={require("../../../../../assets/vpbank_notify.png")}
                            />
                        </View>
                        <Accordion
                            status={true}
                            width={"100%"}
                            title={"THÔNG TIN BẠN NHẬP"}
                            Children={
                                <View
                                    style={{
                                        padding: 5,
                                        marginBottom: 5,
                                        backgroundColor: COLORS.bgF0F0F0,
                                        paddingBottom: 10,
                                    }}
                                >
                                    <View
                                        style={{
                                            borderTopWidth: 2,
                                            width: "98%",
                                            alignSelf: "center",
                                            borderColor: COLORS.bgA7A7A7,
                                        }}
                                    />
                                    <TextField
                                        name={"Người nhận:"}
                                        value={MWGData?.CustomerName}
                                        color={COLORS.bg1E88E5}
                                        fontWeight={true}
                                    />
                                    <MoneyField
                                        title={"Số tiền chi:"}
                                        value={helper.formatMoney(MWGData?.Amount)}
                                        color={COLORS.bgFF0000}
                                    />
                                    <TextField
                                        name={"CMND/CCCD:"}
                                        value={MWGData?.CustomerIDCard}
                                        fontWeight={false}
                                    />
                                </View>
                            }
                        />
                        <Accordion
                            status={true}
                            width={"100%"}
                            title={"THÔNG TIN ĐỐI TÁC TRẢ VỀ"}
                            Children={
                                <View
                                    style={{
                                        padding: 5,
                                        marginBottom: 5,
                                        backgroundColor: COLORS.bgF0F0F0,
                                        paddingBottom: 10,
                                    }}
                                >
                                    <View
                                        style={{
                                            borderTopWidth: 2,
                                            width: "98%",
                                            alignSelf: "center",
                                            borderColor: COLORS.bgA7A7A7,
                                        }}
                                    />
                                    <TextField
                                        name={"Người nhận:"}
                                        value={PartnerData?.CustomerName}
                                        color={COLORS.bg1E88E5}
                                        fontWeight={true}
                                    />
                                    <MoneyField
                                        title={"Số tiền chi:"}
                                        value={helper.formatMoney(PartnerData?.Amount)}
                                        color={COLORS.bgFF0000}
                                    />
                                    <TextField
                                        name={"CMND/CCCD:"}
                                        value={PartnerData?.CustomerIDCard}
                                        fontWeight={false}
                                    />
                                </View>
                            }
                        />
                        <View
                            style={{
                                width: constants.width - 20,
                                justifyContent: 'center'
                            }}
                        >
                            <MyText
                                text={`Lưu ý: Phải kiểm tra đúng thông tin trước khi "Tạo giao dịch"`}
                                style={{
                                    fontStyle: 'italic',
                                    color: COLORS.bgFF0000
                                }}
                            />
                        </View>
                        <View
                            style={{
                                width: constants.width - 25,
                                height: 50,
                                flexDirection: "row",
                                marginTop: 10,
                            }}
                        >
                            <TouchableOpacity
                                onPress={() => this.props.navigation.goBack()}
                                style={{
                                    width: 120,
                                    height: 50,
                                    borderRadius: 18,
                                    borderWidth: 2,
                                    borderColor: COLORS.bg00A98F,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    backgroundColor: "white",
                                }}
                            >
                                <MyText
                                    text={"QUAY LẠI"}
                                    style={{
                                        fontWeight: "bold",
                                        color: COLORS.bg00A98F,
                                    }}
                                />
                            </TouchableOpacity>
                            <View
                                style={{
                                    width: 10,
                                }}
                            />
                            <TouchableOpacity
                                onPress={() => this.handleCreateOrder(customerData)}
                                style={{
                                    flex: 1,
                                    height: 50,
                                    borderRadius: 18,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    backgroundColor: COLORS.bgF49B0C,
                                    opacity: IsMismatch ? 0.5 : 1
                                }}
                                disabled={IsMismatch}
                            >
                                <MyText
                                    text={"TẠO GIAO DỊCH"}
                                    style={{
                                        fontWeight: "bold",
                                        color: COLORS.bgFFFFFF,
                                    }}
                                />
                            </TouchableOpacity>
                        </View>
                    </SafeAreaView>
                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        dataValidateServiceRequest: state.bankAirtimeServiceReducer.dataValidateServiceRequest,
        updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(StepTwo);

const TextField = ({
    name,
    value,
    color,
    titleColor = COLORS.txt8E8E93,
    fontWeight,
}) => {
    return (
        <View
            style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginTop: 10,
            }}
        >
            <MyText
                text={name}
                addSize={-1.5}
                style={{
                    color: titleColor,
                    fontWeight: "500",
                }}
            />
            <MyText
                text={value}
                addSize={-1.5}
                style={{
                    color: color,
                    width: "auto",
                    marginLeft: 5,
                    flex: 1,
                    textAlign: "right",
                    fontWeight: "bold",
                }}
            />
        </View>
    );
};

const MoneyField = ({ title, value, color = COLORS.txt333333 }) => {
    return (
        <View
            style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginTop: 10,
            }}
        >
            <MyText
                text={`${title}: `}
                addSize={-1.5}
                style={{
                    color: COLORS.txt8E8E93,
                    fontWeight: "700",
                }}
            />
            <MyText
                text={value}
                addSize={-1.5}
                style={{
                    color: color,
                    fontWeight: "bold",
                }}
            />
        </View>
    );
};
