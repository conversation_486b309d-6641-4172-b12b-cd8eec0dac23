import { View, Animated, TouchableOpacity, Alert } from 'react-native';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SafeAreaView from 'react-native-safe-area-view';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { dateH<PERSON>per, helper } from '@common';
import { translate } from '@translate';
import { API_CONST, constants, } from '@constants';
import { COLORS } from '@styles';
import {
    CameraDOT,
    MyText,
    RadioButton,
    CaptureCamera,
    showBlockUI,
    hideBlockUI
} from '@components';
import * as actionBankAirtimeServiceCreator from "../../action";
import * as installmentAction from "../../../InstallmentManagerBC/action";
import Camera from '../../component/Camera';
import { getImageCDN } from '../../../ShoppingCart/action';
class StepOne extends Component {
    constructor(props) {
        super(props);
        this.state = {
            scrollY: new Animated.Value(0),
            customerIDCard: '',
            customerName: '',
            rechargerPhoneNumber: '',
            recipientName: '',
            depositAmount: '',
            isVisibleCamera: false,
            lstBankAccount: [],
            itemCheckBankAccount: {},
            radioIDCardType: [
                // {
                //     title: "CMND",
                //     sym: translate("collection.sym_id_card"),
                //     selected: false,
                //     value: 1
                // },
                {
                    title: "CCCD",
                    sym: translate("collection.sym_id_card_2"),
                    selected: true,
                    value: 2
                },
                {
                    title: "Thẻ căn cước",
                    sym: "Thẻ căn cước",
                    selected: false,
                    value: 4
                }
            ],
            cus_FilePathFrontOfIDCard: "",
            cus_FilePathBackOfIDCard: "",
            indexImage: "",
            property: "",
            dateIssueIdentificationCar: "",
            customerCardType: 2,
            iDCardIssuePlaceList: [],
            client_IDCardIssuePlaceID: '',
            customerPortrait: [],
        };
        this.timeOutScroll = null;
        this.isScrolling = false;
        this.keySearch = "";
    }

    componentDidMount() {
        const { itemCatalog } = this.props;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID
        } = itemCatalog ?? {};
        if (ServiceCategoryID == 12 && AirtimeServiceGroupID == 37) {
            this.getBankAccountList()
        }
        this.updateStateFromProps();
    }

    updateStateFromProps() {
        const { dataUpdateCustomer } = this.props;
        const {
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            dateIssueIdentificationCar,
            customerIDCard,
            customerCardType,
            customerPortrait,
            radioIDCardType,
        } = dataUpdateCustomer || {};
        this.setState({
            cus_FilePathFrontOfIDCard: cus_FilePathFrontOfIDCard || '',
            cus_FilePathBackOfIDCard: cus_FilePathBackOfIDCard || '',
            dateIssueIdentificationCar: dateIssueIdentificationCar || '',
            customerIDCard: customerIDCard || '',
            customerCardType: customerCardType || 2,
            customerPortrait: customerPortrait || [],
            radioIDCardType: radioIDCardType || [
                // {
                //     title: "CMND",
                //     sym: translate("collection.sym_id_card"),
                //     selected: false,
                //     value: 1
                // },
                {
                    title: "CCCD",
                    sym: translate("collection.sym_id_card_2"),
                    selected: true,
                    value: 2
                },
                {
                    title: "Thẻ căn cước",
                    sym: "Thẻ căn cước",
                    selected: false,
                    value: 4
                }
            ]
        });
    }

    getBankAccountList = () => {
        const {
            actionBankAirtimeService,
            navigation,
            itemCatalog,
            dataUpdateCustomer,
            updateHeaderAirtime
        } = this.props;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID
        } = itemCatalog ?? {};
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;
        showBlockUI()
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": AirTimeTransactionTypeID
        }
        actionBankAirtimeService.getBankAccountList(data).then((reponseBankList) => {
            hideBlockUI();
            // actionBankAirtimeService.updateCustomerAirtimeService({
            //     ...dataUpdateCustomer,
            //     lstBankAccount: reponseBankList
            // });
        }).catch(msgError => {
            Alert.alert("", msgError, [{
                text: "OK",
                onPress: () => {
                    navigation.goBack();
                    hideBlockUI()
                }
            }])
        });
    }

    componentDidUpdate(prevProps, prevState) {
        if (this.props.dataQueryCustomerBank !== prevProps.dataQueryCustomerBank) {
            this.setState({
                recipientName: this.props.dataQueryCustomerBank.CustomerName
            })
        }

        if (prevState.customerCardType !== this.state.customerCardType) {
            // Gọi hàm handleGet khi customerIDCard thay đổi
            this.getDataIssueplace(this.state.customerCardType, 241, this.state.customerIDCard);
        }

    }

    getDataIssueplace = (idType, nationalityID = 241, CardID) => {
        const { client_IDCardIssuePlaceID } = this.state;
        const {
            actionBankAirtimeService,
            dataUpdateCustomer
        } = this.props;
        this.props.actionBankAirtimeService.getIssueplace(idType, nationalityID, CardID).then(
            res => {
                actionBankAirtimeService.updateCustomerAirtimeService({
                    ...dataUpdateCustomer,
                    iDCardIssuePlaceList: res,
                    client_IDCardIssuePlaceID: client_IDCardIssuePlaceID || res?.[0]?.idCardIssuePlaceId,
                    customerCardType: this.state.customerCardType
                });
            }
        ).catch(
            err => {
                Alert.alert(
                    translate('common.notification'),
                    err.msgError,
                    [
                        {
                            text: translate('common.btn_close'), onPress: () => {

                            }
                        }
                    ],
                    { cancelable: false }
                )
            }
        )
    }

    takePictureFE = (imageInfo, property, indexImage, client_IDCardType) => {
        const { actionBankAirtimeService, dataUpdateCustomer } = this.props;
        helper.resizeImage(imageInfo).then(({ path, uri, size, name }) => {
            console.log("uri " + uri);
            showBlockUI();
            var urlImg = null;
            this.getBodyUpload({
                uri: uri,
                type: 'image/jpeg',
                name: name,
            }).then(res => {
                urlImg = res.url;
                if (indexImage != 2) {
                    var hardUrl = indexImage == 0 ? "https://drive.google.com/file/d/1z8F5IBLQaVGPLZMgo_s6FGeS72dfOyVT/view"
                        : "https://drive.google.com/file/d/1LnC0PnbDg_kQpqpdcQFN7ZlB-LfbTsPY/view"
                    if (client_IDCardType == 2) {
                        hardUrl = indexImage == 0 ? "http://10.1.12.58:50520/get_origin_image/20210621110839S-3XUskEB5e6BcVyFU4hDPC7-EiFjTpKbW4gyWiGQB9vWEQ"
                            : "http://10.1.12.58:50520/get_origin_image/20210621110757S-3XUskEB5e6BcVyFU4hDPC7-GDHjJar5yVSA5b5WoTK6y9"
                    }
                    this.getInfoCustomerByImage(uri, indexImage)
                        .then((response) => {
                            console.log("getInfoCustomerByImage response");
                            response[property] = urlImg;
                            hideBlockUI();
                            console.log(response, property, indexImage, client_IDCardType, "response, property, indexImage,client_IDCardType")
                            this.updateInfoCardToState(response, property, indexImage);
                        })
                        .catch(
                            err => {
                                hideBlockUI();
                                this.updateInfoCardToState({ [property]: urlImg }, property);
                            }
                        )
                }
                else {
                    hideBlockUI();
                    this.updateInfoCardToState({ [property]: urlImg }, property);
                }
            }
            )
        }).catch((error) => {
            console.log("resizeImage", error);
        });
    }

    updateInfoCardToState = (objInfoCard, property) => {
        console.log(objInfoCard, "Thông tin giấy tờ!");
        const { actionBankAirtimeService, dataUpdateCustomer } = this.props;
        const updatedCustomerData = {
            ...dataUpdateCustomer,
            ...objInfoCard,
            customerCardType: this.state.customerCardType
        };
        actionBankAirtimeService.updateCustomerAirtimeService(updatedCustomerData);
        this.setState({
            customerIDCard: objInfoCard.customerIDCard || this.state.customerIDCard,
            isVisibleCamera: false,
            [property]: objInfoCard[property],
        });
        if (objInfoCard.customerIDCard) {
            this.getDataIssueplace(this.state.customerCardType, 241, objInfoCard.customerIDCard);
        }
    };


    getTypeImage = (identificationType, indexImage) => {
        switch (identificationType) {
            case 1:
                switch (indexImage) {
                    case 0:
                        return 1;
                    case 1:
                        return 3;
                    default:
                        return 0;
                }
            case 2:
                switch (indexImage) {
                    case 0:
                        return 2;
                    case 1:
                        return 4;
                    default:
                        return 0;
                }
            case 4:
                switch (indexImage) {
                    case 0:
                        return 11;
                    case 1:
                        return 12;
                    default:
                        return 0;
                }

            default:
                return 0;
        }
    }

    getInfoCustomerByImage = (uriImage, indexImage) => {
        const { userInfo } = this.props;
        const { customerCardType } = this.state;
        return new Promise((resolve, reject) => {
            var typeImageDetect = helper.getTypeImage(customerCardType, indexImage)
            console.log(typeImageDetect, "typeImageDetect");
            // if (typeImageDetect != 0) {
            let bodyFromData = new FormData();
            bodyFromData.append('file', {
                uri: uriImage,
                type: 'image/jpg',
                name: "getInfoCustomerByImage" + dateHelper.getTimestamp()
            });
            console.log(bodyFromData, "bodyFromData");
            bodyFromData.append('client_id', `MWGPOS_${userInfo.userName}`);
            bodyFromData.append('chosen_side', typeImageDetect);
            console.log(bodyFromData, "bodyFromData");
            console.log(bodyFromData, "bodyFromData==>")
            installmentAction.ocrCCCD(bodyFromData)
                .then(
                    res => {
                        switch (typeImageDetect) {
                            case 1:
                                resolve({
                                    provinceID: res.cap_1_id,
                                    districtID: res.cap_2_id,
                                    wardID: res.cap_3_id,
                                    provinceName: res.cap1,
                                    districtName: res.cap2,
                                    wardName: res.cap3,
                                    customerBirthday: res.date_of_birth,
                                    customerName: res.full_name,
                                    customerAddress: res.place_of_permanent,
                                    customerIDCard: res.id_no,
                                    image_url: res.image_url,
                                    streetAddress: res.cap4,
                                    idCardExpiriedDate: res.expiration_date
                                }
                                );
                                break;
                            case 2:
                                resolve({
                                    provinceID: res.cap_1_id,
                                    districtID: res.cap_2_id,
                                    wardID: res.cap_3_id,
                                    provinceName: res.cap1,
                                    districtName: res.cap2,
                                    wardName: res.cap3,
                                    customerBirthday: res.date_of_birth,
                                    customerName: res.full_name,
                                    customerAddress: res.place_of_permanent,
                                    customerIDCard: res.id_no,
                                    image_url: res.image_url,
                                    streetAddress: res.cap4,
                                    idCardExpiriedDate: res.expiration_date
                                }
                                );
                                break;
                            case 3:
                                resolve({
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    client_IDCardIssuePlaceID: res.erp_place_of_issue_id,
                                });
                                break;
                            case 4:
                                resolve({
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    client_IDCardIssuePlaceID: res.erp_place_of_issue_id,
                                });
                                break;
                            case 11:
                                resolve({
                                    customerBirthday: res.date_of_birth,
                                    customerName: res.full_name,
                                    customerIDCard: res.id_no,
                                    image_url: res.image_url,
                                }
                                );
                                break;
                            case 12:
                                resolve({
                                    provinceID: res.cap_1_id,
                                    districtID: res.cap_2_id,
                                    wardID: res.cap_3_id,
                                    provinceName: res.cap1,
                                    districtName: res.cap2,
                                    wardName: res.cap3,
                                    streetAddress: res.cap4,
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    client_IDCardIssuePlaceID: res.erp_place_of_issue_id,
                                    idCardExpiriedDate: res.expiration_date
                                });
                                break;
                            default:
                                console.log(res)
                                break;
                        }
                    }
                ).catch(
                    err => {
                        console.log("err getInfoCustomerByImage", err);
                        reject(err);
                    }
                );
            // }
        });
    }

    uploadPicture = (fromData) => {
        const { API_GET_IMAGE_CDN } = API_CONST;
        return new Promise((resolve, reject) => {
            getImageCDN(fromData).then(cdnImages => {
                console.log("uploadPicture url", API_GET_IMAGE_CDN + cdnImages[0]);
                resolve({ url: `${API_GET_IMAGE_CDN + cdnImages[0]}` })
            }).catch(error => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('instalmentManager.upload_image_error'),
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: hideBlockUI,
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => this.uploadPicture(fromData),
                            style: "default"
                        },
                    ],
                    { cancelable: false },
                );
            })
        });
    }

    getBodyUpload = (file) => {
        const fromData = new FormData();
        fromData.append('file', file);
        console.log(file, fromData, "fromData")
        return this.uploadPicture(fromData)
    }


    removePicture = (property, property2) => () => {
        this.setState({
            [property]: "",
            [property2]: "",
        });
    }

    selectItemCardType = (index) => {
        const { actionBankAirtimeService, dataUpdateCustomer } = this.props;
        const {
            radioIDCardType
        } = this.state;
        const newRadioIDCardType = radioIDCardType.map((item) => ({
            ...item,
            selected: false
        }));
        newRadioIDCardType[index].selected = true;
        this.setState({ radioIDCardType: newRadioIDCardType });
        // console.log("giatrivalue", newRadioIDCardType[index].value)
        this.setState({ customerCardType: newRadioIDCardType[index].value })
        actionBankAirtimeService.updateCustomerAirtimeService({
            ...dataUpdateCustomer,
            customerCardType: newRadioIDCardType[index].value,
            rechargeFee: "",
            idCardIssueDate: ""
        });
    };


    handleOnNextStep = () => {
        const {
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            customerPortrait,
        } = this.state;
        const { updateHeaderAirtime } = this.props;
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;
        if (cus_FilePathFrontOfIDCard == "") {
            Alert.alert("", translate('collection.take_front_id_card'))
            return false;
        }
        if (cus_FilePathBackOfIDCard == "") {
            Alert.alert("", translate("collection.take_back_id_card"))
            return false;
        }
        if (customerPortrait?.[0]?.UrlFile == '') {
            Alert.alert("", "Vui lòng chụp chân dung khách hàng")
            return false;
        }
        else {
            if (AirTimeTransactionTypeID == 1932 || AirTimeTransactionTypeID == 2092) {
                this.props.navigation.navigate("StepTwoDeposit")
            } else if (AirTimeTransactionTypeID == 1933 || AirTimeTransactionTypeID == 2093) {
                this.props.navigation.navigate("StepTwo")
            }

        }

    }

    render() {
        const {
            radioIDCardType,
            isVisibleCamera,
            customerPortrait
        } = this.state;
        const { itemCatalog } = this.props;
        const { AirtimeServiceGroupID } = itemCatalog ?? {};
        const {
            actionBankAirtimeService,
            dataUpdateCustomer } = this.props;
        const selectedCard = radioIDCardType.find(
            (item) => item.selected
        );
        const client_IDCardType = selectedCard.value;
        return (
            <View style={{
                flex: 1,
                backgroundColor: 'white'
            }}>
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            alignItems: 'center'
                        }}>
                        <View
                            style={{
                                width: constants.width - 20,
                                marginTop: 10
                            }}>
                            <View style={{
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 7,
                                padding: 10,
                                alignItems: 'center',
                                shadowColor: COLORS.bg7F7F7F,
                                shadowOffset: {
                                    width: 0,
                                    height: 0,
                                },
                                shadowOpacity: 0.5,
                                shadowRadius: 1,
                                elevation: 5,
                            }}>
                                <View
                                    style={{ marginVertical: 5, flexDirection: 'row', alignItems: 'center', marginLeft: 10 }}>
                                    <RadioButton
                                        style={{
                                            flexDirection: 'row'
                                        }}
                                        containerStyle={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            width: constants.width / 4
                                        }}
                                        dataItems={helper.IsNonEmptyArray(this.state.radioIDCardType) ? this.state.radioIDCardType : []}
                                        selectItem={this.selectItemCardType}
                                        mainComponent={(item) => {
                                            return (
                                                <MyText
                                                    text={item.title}
                                                    style={{
                                                        color: item.selected
                                                            ? COLORS.txtFF8900
                                                            : COLORS.txt333333,
                                                        marginLeft: 2,
                                                        fontSize: 15
                                                    }}
                                                />
                                            );
                                        }}
                                    />
                                </View>
                                <View
                                    style={{
                                        justifyContent: 'center'
                                    }}>
                                    <MyText style={{
                                        marginLeft: 5,
                                        padding: 10
                                    }}
                                        text={`${translate("collection.take_a_shot")} ${selectedCard.sym} ${translate("collection.front")}`}
                                    />
                                    <View
                                        style={{
                                            marginVertical: 0,
                                            paddingHorizontal: 10,
                                            alignItems: 'center',
                                            width: constants.width
                                        }}>
                                        <CameraDOT
                                            uriImage={this.state.cus_FilePathFrontOfIDCard}
                                            onTakePicture={(response) => {
                                                this.takePictureFE(response, "cus_FilePathFrontOfIDCard", 0, client_IDCardType)

                                            }}
                                            onDelete={this.removePicture("cus_FilePathFrontOfIDCard", "client_ImageFrontOfIDCard")}
                                        />
                                    </View>
                                    <MyText
                                        style={{
                                            marginLeft: 5,
                                            padding: 10
                                        }}
                                        text={`${translate("collection.take_a_shot")} ${selectedCard.sym} ${translate("collection.backside")}`}
                                    />
                                    <View
                                        style={{
                                            marginVertical: 0,
                                            paddingHorizontal: 10,
                                            alignItems: 'center',
                                            width: constants.width
                                        }}>
                                        <CameraDOT
                                            uriImage={this.state.cus_FilePathBackOfIDCard}
                                            onTakePicture={(response) => {
                                                this.takePictureFE(response, "cus_FilePathBackOfIDCard", 1, client_IDCardType)
                                            }}
                                            onDelete={this.removePicture("cus_FilePathBackOfIDCard", "client_ImageBackOfIDCard")}
                                        />
                                    </View>
                                    <MyText
                                        style={{
                                            marginLeft: 5,
                                            padding: 10
                                        }}
                                        text={"Chân dung khách hàng"}
                                    />
                                    <View
                                        style={{
                                            marginVertical: 0,
                                            paddingHorizontal: 10,
                                            alignItems: 'center',
                                            width: constants.width
                                        }}>
                                        <Camera
                                            lstImage={customerPortrait}
                                            handleGetImage={(strImage) => {
                                                actionBankAirtimeService.updateCustomerAirtimeService({
                                                    ...dataUpdateCustomer,
                                                    customerPortrait: strImage
                                                });
                                                this.setState({ customerPortrait: strImage })
                                            }}
                                        />
                                    </View>
                                </View>
                            </View>
                            <View style={{
                                width: '100%',
                                height: 50,
                                flexDirection: 'row',
                                paddingHorizontal: 10,
                                marginTop: 15
                            }}>
                                <TouchableOpacity
                                    onPress={() => this.props.navigation.goBack()}
                                    style={{
                                        width: 120,
                                        height: 50,
                                        borderRadius: 18,
                                        borderWidth: 2,
                                        borderColor: COLORS.bg00A98F,
                                        alignItems: "center",
                                        justifyContent: "center"
                                    }}>
                                    <MyText
                                        text={'QUAY LẠI'}
                                        style={{
                                            fontWeight: 'bold',
                                            color: COLORS.bg00A98F
                                        }}
                                    />
                                </TouchableOpacity>
                                <View style={{
                                    width: 10
                                }} />
                                <TouchableOpacity
                                    onPress={() => this.handleOnNextStep()}
                                    style={{
                                        backgroundColor: 'pink',
                                        flex: 1,
                                        height: 50,
                                        borderRadius: 18,
                                        alignItems: "center",
                                        justifyContent: "center",
                                        backgroundColor: COLORS.bgF49B0C
                                    }}>
                                    <MyText
                                        text={'BƯỚC TIẾP THEO'}
                                        style={{
                                            fontWeight: 'bold',
                                            color: COLORS.bgFFFFFF
                                        }}
                                    />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </SafeAreaView>
                    <CaptureCamera
                        iisVisibleCamera={isVisibleCamera}
                        disabledUploadImage={true}
                        takePicture={(camera) => {
                            this.takePicture(camera);
                        }}
                        closeCamera={() => {
                            this.setState({ ...state, isVisibleCamera: false })
                        }}
                        selectPicture={() => {
                            selectImage();
                        }}
                    />
                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        dataQueryCustomerBank: state.collectionReducer.dataQueryCustomerBank,
        stateQueryCustomerBank: state.collectionReducer.stateQueryCustomerBank,
        dataFeeCashin: state.collectionReducer.dataFeeCashin,
        userInfo: state.userReducer,
        itemCatalog: state.collectionReducer.itemCatalog,
        updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
        dataUpdateCustomer: state.bankAirtimeServiceReducer.dataUpdateCustomer
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(StepOne);
