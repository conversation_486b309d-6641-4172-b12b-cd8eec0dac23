import { Alert, Keyboard, TouchableOpacity, View } from "react-native";
import React, { Component } from "react";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import SafeAreaView from "react-native-safe-area-view";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { helper } from "@common";
import { MyText, TitleInput, DatePicker, Picker, PickerLocation, showBlockUI, hideBlockUI, } from "@components";
import * as actionCollectionCreator from "../../../CollectionTransfer/action";
import { COLORS } from "@styles";
import { constants } from "@constants";
import { translate } from "@translate";
import * as actionGetSimCreator from "../../../ActiveSimManager/action";
import * as actionBankAirtimeServiceCreator from "../../action";
import Promotion from '../../component/Coupon/Promotion';
import Message from "../../component/Text/Message";
import AddressManager from '../../../AddressManager';

class StepTwo extends Component {
    constructor(props) {
        super(props);
        this.state = {
            customerIDCard: "",
            customerName: "",
            rechargerPhoneNumber: "",
            receiptCode: "",
            depositAmount: "",
            idCardIssueDate: "",
            iDCardIssuePlaceList: [],
            client_IDCardIssuePlaceID: "",
            provinceID: 0,
            district: [],
            districtID: 0,
            ward: [],
            wardID: 0,
            indexPager: 0,
            customer: {
                DistrictID: 0,
                WardID: 0,
                ProvinceID: 0,
            },
            provinceName: "",
            districtName: "",
            wardName: "",
            isShowIndicator: false,
            indexPager: 0,
            streetAddress: "",
            depositAmount: '',
        };
        this.pickerRefCustomerLocation = React.createRef();
    }

    componentDidMount() {
        this.updateStateFromProps();
        // this.getProvince();
        this.getDataIssueplace();
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.dataUpdateCustomer !== this.props.dataUpdateCustomer) {
            this.updateStateFromProps();
        }
        // if (prevState.provinceID !== this.state.provinceID) {
        //     if (this.state.provinceID) {
        //         console.log('Fetching districts for provinceID:', this.state.provinceID);
        //         this.getDataDistrict(this.state.provinceID);
        //     }
        // }

        // if (prevState.districtID !== this.state.districtID) {
        //     if (this.state.districtID && this.state.provinceID) {
        //         console.log('Fetching wards for districtID:', this.state.districtID);
        //         this.getDataWard(this.state.provinceID, this.state.districtID);
        //     }
        // }
    }

    getDataIssueplace = () => {
        const { client_IDCardIssuePlaceID } = this.state;
        const {
            actionBankAirtimeService,
            dataUpdateCustomer
        } = this.props;
        const { customerCardType, customerIDCard, iDCardIssuePlaceList } = dataUpdateCustomer ?? "";
        const nationalityID = 241;
        if (!Array.isArray(iDCardIssuePlaceList) || iDCardIssuePlaceList.length === 0) {
            actionBankAirtimeService.getIssueplace(customerCardType, nationalityID, customerIDCard)
                .then(res => {
                    actionBankAirtimeService.updateCustomerAirtimeService({
                        ...dataUpdateCustomer,
                        iDCardIssuePlaceList: res,
                        client_IDCardIssuePlaceID: client_IDCardIssuePlaceID || res?.[0]?.idCardIssuePlaceId,
                    });
                })
                .catch(err => {
                    Alert.alert(
                        translate('common.notification'),
                        err.msgError,
                        [
                            {
                                text: translate('common.btn_close'),
                                onPress: () => { }
                            }
                        ],
                        { cancelable: false }
                    );
                });
        }
    }


    updateStateFromProps() {
        const { dataUpdateCustomer } = this.props;
        const {
            customerIDCard,
            customerName,
            rechargerPhoneNumber,
            receiptCode,
            depositAmount,
            idCardIssueDate,
            iDCardIssuePlaceList,
            client_IDCardIssuePlaceID,
            customer,
            provinceID,
            districtID,
            wardID,
            streetAddress
        } = dataUpdateCustomer;
        this.setState({
            customerIDCard: customerIDCard || "",
            receiptCode: receiptCode || "",
            customerName: customerName || "",
            rechargerPhoneNumber: rechargerPhoneNumber || "",
            depositAmount: depositAmount || "",
            idCardIssueDate: idCardIssueDate || "",
            iDCardIssuePlaceList: iDCardIssuePlaceList || [],
            client_IDCardIssuePlaceID: client_IDCardIssuePlaceID || "",
            customer: customer || '',
            provinceID: provinceID || '',
            districtID: districtID || '',
            wardID: wardID || '',
            streetAddress: streetAddress || '',
        });
    }

    componentWillUnmount() {
        const { dataUpdateCustomer, actionBankAirtimeService } = this.props;
        const { rechargerPhoneNumber, depositAmount } = this.state;
        actionBankAirtimeService.updateCustomerAirtimeService({
            ...dataUpdateCustomer,
            rechargerPhoneNumber,
            depositAmount,
        });
    }

    handleOnNextStep = () => {
        const {
            customerIDCard,
            customerName,
            rechargerPhoneNumber,
            receiptCode,
            depositAmount,
            client_IDCardIssuePlaceID,
            idCardIssueDate,
            streetAddress,
            provinceID,
            districtID,
            wardID
        } = this.state;
        const { dataUpdateCustomer, actionBankAirtimeService } = this.props;
        const regExpIDCard9 = new RegExp(/^\d{9}$/);
        const regExpIDCard12 = new RegExp(/^\d{12}$/);
        const isValidateIDCard9 = regExpIDCard9.test(customerIDCard);
        const isValidateIDCard12 = regExpIDCard12.test(customerIDCard);
        const isValidateIDCard = isValidateIDCard9 || isValidateIDCard12;
        const regExpPhoneNumber10 = new RegExp(/^\d{10}$/);
        const isValidateRechargerPhoneNumber = regExpPhoneNumber10.test(
            rechargerPhoneNumber
        );
        if (!isValidateIDCard) {
            Alert.alert("", translate("collection.check_id_card"));
            return false;
        }
        if (!isValidateRechargerPhoneNumber) {
            Alert.alert("", translate("collection.check_phone"));
            return false;
        }
        if (customerIDCard == "") {
            Alert.alert("", "Vui lòng nhập CMND/CCCD/Thẻ căn cước khách hàng");
            return false;
        }
        if (customerName == "") {
            Alert.alert("", "Vui lòng nhập Họ và tên người nhận");
            return false;
        }
        if (rechargerPhoneNumber == "") {
            Alert.alert("", "Vui lòng nhập số điện thoại người nhận");
            return false;
        }
        if (depositAmount == "") {
            Alert.alert("", "Vui lòng nhập số tiền nhận");
            return false;
        }
        if (client_IDCardIssuePlaceID == "") {
            Alert.alert("", "Vui lòng chọn nơi cấp")
            return false;
        }
        if (!idCardIssueDate || idCardIssueDate.trim() === "") {
            Alert.alert("", "Vui lòng chọn ngày cấp")
            return false;
        }
        if (streetAddress == "") {
            Alert.alert("", "Vui lòng nhập số nhà tên đường")
            return false;
        }
        if (
            districtID == 0 ||
            provinceID == 0 ||
            wardID == 0
        ) {
            Alert.alert("", "Vui lòng chọn Tỉnh/Thành - Quận/Huyện - Phường/Xã");
            return;
        }
        else {
            this.props.navigation.navigate("StepThree");
        }
    };

    // getProvince = () => {
    //     const { actionBankAirtimeService } = this.props;
    //     actionBankAirtimeService.getDataProvince();
    // };

    // getDataDistrict = (provinceID) => {
    //     const { actionBankAirtimeService } = this.props;
    //     this.setState({ isShowIndicator: true });
    //     actionBankAirtimeService
    //         .getDistrict(provinceID)
    //         .then((data) => {
    //             this.setState({ district: data, indexPager: 1, isShowIndicator: false });
    //         })
    //         .catch((msgError) => {
    //             Alert.alert(translate("common.notification_uppercase"), msgError, [
    //                 {
    //                     text: translate("common.btn_skip"),
    //                     style: "cancel",
    //                     onPress: () => this.setState({ isShowIndicator: false })
    //                 },
    //                 {
    //                     text: translate("common.btn_notify_try_again"),
    //                     style: "default",
    //                     onPress: () => getDataDistrict(provinceID),
    //                 },
    //             ]);
    //         });
    // };

    // getDataWard = (provinceID, districtID) => {
    //     const { actionBankAirtimeService } = this.props;
    //     this.setState({ isShowIndicator: true });
    //     actionBankAirtimeService
    //         .getWard(provinceID, districtID)
    //         .then((data) => {
    //             this.setState({ ward: data, indexPager: 2, isShowIndicator: false });
    //         })
    //         .catch((msgError) => {
    //             Alert.alert("Thông báo", msgError, [
    //                 { text: "BỎ QUA", onPress: () => this.setState({ isShowIndicator: false }) },
    //                 { text: "THỬ LẠI", onPress: () => this.getDataWard(provinceID, districtID) },
    //             ]);
    //         });
    // };

    handleGetPromotion = () => {
        const {
            dataUpdateCustomer,
            actionBankAirtimeService,
            updateHeaderAirtime,
            itemCatalog
        } = this.props;
        const {
            rechargerPhoneNumber,
            depositAmount,
        } = this.state;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID
        } = itemCatalog ?? {};
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": AirTimeTransactionTypeID,
            "productID": "*************",
            "amount": depositAmount,
            "extraData": {
                "customerPhone": rechargerPhoneNumber,
            },
        }
        showBlockUI();
        actionBankAirtimeService.getPriceAndFeeService(data).then((reponse) => {
            hideBlockUI();
        })
            .catch((msgError) => {
                hideBlockUI();
                Alert.alert("", msgError?.msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }

    render() {
        const {
            customerIDCard,
            customerName,
            rechargerPhoneNumber,
            depositAmount,
            idCardIssueDate,
            iDCardIssuePlaceList,
            streetAddress
        } = this.state;
        const { dataUpdateCustomer, actionBankAirtimeService, dataFeeCashin } = this.props;
        const { customerCardType, wardID, districtID, provinceID } = dataUpdateCustomer;
        const promotionInfo = dataFeeCashin?.PromotionInfo ?? {};
        const isMessage = promotionInfo?.PromotionMessage;
        const isShowMessage = promotionInfo?.PromotionID === 0 && helper.IsNonEmptyString(isMessage);
        const isShowCoupon = !!promotionInfo?.PromotionID && helper.IsNonEmptyString(isMessage);
        const isConfigAddressNew = helper.checkConfigStoreInsuranceLocationFlowNew();
        return (
            <View
                style={{
                    flex: 1,
                    backgroundColor: "white",
                }}
            >
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            alignItems: "center",
                        }}
                    >
                        <View
                            style={{
                                width: constants.width - 20,
                            }}
                        >
                            <View
                                style={{
                                    marginTop: 20,
                                }}
                            >
                                <TitleInput
                                    title={"Số CMND/CCCD: "}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 10,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8,
                                    }}
                                    placeholder={translate(
                                        "collection.placeholder_ID_card_number"
                                    )}
                                    value={customerIDCard}
                                    onChangeText={(text) => {
                                        let validate =
                                            customerCardType == 1
                                                ? new RegExp(/^\d{0,9}$/)
                                                : new RegExp(/^\d{0,12}$/);
                                        if (validate.test(text) || text === "") {
                                            this.setState({ customerIDCard: text });
                                            if (text !== "") {
                                                actionBankAirtimeService.updateCustomerAirtimeService({
                                                    ...dataUpdateCustomer,
                                                    customerIDCard: text,
                                                });
                                            }
                                        }
                                    }}
                                    keyboardType="numeric"
                                    returnKeyType="done"
                                    onSubmitEditing={Keyboard.dismiss}
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ customerIDCard: "" });
                                    }}
                                    key="customerIDCard"
                                    isRequired={true}
                                />
                                <TitleInput
                                    title={"Họ và tên người nhận: "}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 10,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8,
                                    }}
                                    placeholder={"Nhập họ và tên người nhận"}
                                    value={customerName}
                                    onChangeText={(text) => {
                                        if (helper.isValidateCharVN(text)) {
                                            this.setState({ customerName: text });
                                            if (text !== "") {
                                                actionBankAirtimeService.updateCustomerAirtimeService({
                                                    ...dataUpdateCustomer,
                                                    customerName: text,
                                                });
                                            }
                                        }
                                    }}
                                    keyboardType={"default"}
                                    returnKeyType={"done"}
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ customerName: "" });
                                    }}
                                    key="customerName"
                                    isRequired={true}
                                />

                                <TitleInput
                                    title={"Số điện thoại người nhận: "}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 5,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8,
                                        marginBottom: 10,
                                    }}
                                    placeholder={"Nhập số điện thoại người nhận"}
                                    value={rechargerPhoneNumber}
                                    onChangeText={(text) => {
                                        const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                        const isValidate = regExpPhone.test(text) || text == "";
                                        if (isValidate) {
                                            this.setState({ rechargerPhoneNumber: text });
                                            actionBankAirtimeService.updateCustomerAirtimeService({
                                                ...dataUpdateCustomer,
                                                rechargerPhoneNumber: text,
                                            });
                                        }
                                    }}
                                    onSubmitEditing={Keyboard.dismiss}
                                    onBlur={() => {
                                        // this.handleGetPromotion();
                                    }}
                                    keyboardType="numeric"
                                    returnKeyType="done"
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ rechargerPhoneNumber: "" });
                                    }}
                                    key="rechargerPhoneNumber"
                                    isRequired={true}
                                />

                                <TitleInput
                                    title={"Số tiền rút: "}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 5,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgE0E0E0,
                                        paddingVertical: 8,
                                    }}
                                    placeholder={"Số tiền rút"}
                                    value={helper.formatMoney(depositAmount)}
                                    onChangeText={(text) => { }}
                                    keyboardType="numeric"
                                    returnKeyType="done"
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => { }}
                                    key="depositAmount"
                                    isRequired={true}
                                    editable={false}
                                />
                                <View
                                    style={{
                                        marginVertical: -5,
                                    }}
                                >
                                    <MyText
                                        style={{
                                            fontWeight: "700",
                                            fontStyle: "italic",
                                            fontSize: 13,
                                        }}
                                        text={"Ngày cấp: "}
                                        children={
                                            <MyText
                                                text={"*"}
                                                style={{ color: COLORS.txtFF0000, fontSize: 16 }}
                                            />
                                        }
                                    />
                                    <DatePicker
                                        date={idCardIssueDate}
                                        format={"YYYY-MM-DD"}
                                        onDateChange={(dateStr) => {
                                            this.setState({ idCardIssueDate: dateStr });
                                            actionBankAirtimeService.updateCustomerAirtimeService({
                                                ...dataUpdateCustomer,
                                                idCardIssueDate: `${dateStr}T00:00:00`,
                                            });
                                        }}
                                    />
                                </View>
                                <View
                                    style={{
                                        marginBottom: 8,
                                        marginTop: 8,
                                    }}
                                >
                                    <MyText
                                        style={{
                                            fontWeight: "700",
                                            fontStyle: "italic",
                                            fontSize: 13,
                                        }}
                                        text={"Nơi cấp: "}
                                        children={
                                            <MyText
                                                text={"*"}
                                                style={{ color: COLORS.txtFF0000, fontSize: 16 }}
                                            />
                                        }
                                    />
                                    <View
                                        style={{
                                            borderWidth: 1.5,
                                            borderColor: COLORS.bdE4E4E4,
                                            alignItems: "center",
                                            justifyContent: "center",
                                            borderRadius: 5,
                                            height: "auto",
                                            backgroundColor: COLORS.bgFFFFFF,
                                        }}
                                    >
                                        <Picker
                                            label={"idCardIssuePlaceName"}
                                            value={"idCardIssuePlaceId"}
                                            data={iDCardIssuePlaceList}
                                            valueSelected={this.state.client_IDCardIssuePlaceID}
                                            onChange={(item) => {
                                                this.setState({
                                                    client_IDCardIssuePlaceID: item.idCardIssuePlaceId,
                                                });
                                                actionBankAirtimeService.updateCustomerAirtimeService({
                                                    ...dataUpdateCustomer,
                                                    client_IDCardIssuePlaceID: item.idCardIssuePlaceId,
                                                });
                                            }}
                                            defaultLabel={translate("instalmentManager.header_place")}
                                            style={{
                                                flex: 1,
                                                flexDirection: "row",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                height: 30,
                                                marginVertical: 5,
                                                marginLeft: -5,
                                                marginRight: -10,
                                            }}
                                        />
                                    </View>
                                </View>
                                <TitleInput
                                    title={"Số nhà, tên đường"}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8,
                                    }}
                                    placeholder={"Vui lòng nhập số nhà tên đường"}
                                    value={streetAddress}
                                    onChangeText={(text) => {
                                        if (helper.isValidateCharVN(text)) {
                                            this.setState({
                                                streetAddress: text,
                                                rechargeFee: ""
                                            });
                                            if (text !== "") {
                                                actionBankAirtimeService.updateCustomerAirtimeService({
                                                    ...dataUpdateCustomer,
                                                    streetAddress: text,
                                                });
                                            }
                                        }
                                    }}
                                    keyboardType={"default"}
                                    returnKeyType={"done"}
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ streetAddress: '' });
                                        actionBankAirtimeService.updateCustomerAirtimeService({
                                            ...dataUpdateCustomer,
                                            streetAddress: "",
                                        });
                                    }}
                                    key="streetAddress"
                                    isRequired={true}
                                />
                                <View style={{ marginTop: 5 }}>
                                    <MyText
                                        style={{
                                            fontWeight: "700",
                                            fontStyle: "italic",
                                            fontSize: 13,
                                        }}
                                        text={"Địa chỉ thường trú"}
                                        children={
                                            <MyText
                                                text={"*"}
                                                style={{ color: COLORS.txtFF0000, fontSize: 16 }}
                                            />
                                        }
                                    />
                                    <AddressManager
                                        ref={this.pickerRefPermanent}
                                        wardID={wardID}
                                        districtID={districtID}
                                        provinceID={provinceID}
                                        onChangeProvince={(item) => {
                                            this.setState((prevState) => ({
                                                ...prevState,
                                                provinceName: item.provinceName,
                                                provinceID: item.provinceID,
                                                districtID: 0,
                                                wardID: 0,
                                            }));
                                        }}
                                        onChangeDistrict={(item) => {
                                            this.setState((prevState) => ({
                                                ...prevState,
                                                districtName: item.districtName,
                                                districtID: item.districtID,
                                                wardID: 0,
                                            }));
                                        }}                                        
                                        onChangeWard={(item) => {
                                            if (item.wardID !== this.state.wardID) {
                                                this.setState((prevState) => ({
                                                    ...prevState,
                                                    wardName: item.wardName,
                                                    wardID: item.wardID
                                                }), () => {
                                                    actionBankAirtimeService.updateCustomerAirtimeService({
                                                        ...dataUpdateCustomer,
                                                        provinceID: this.state.provinceID,
                                                        districtID: this.state.districtID,
                                                        wardID: this.state.wardID,
                                                        provinceName: this.state.provinceName,
                                                        districtName: this.state.districtName,
                                                        wardName: this.state.wardName,
                                                    });
                                                });
                                            }
                                        }}
                                        
                                        isShowOldAddress={isConfigAddressNew}
                                        enableDistrictSelection={!isConfigAddressNew}
                                    />
                                </View>
                            </View>
                            {/* {
                                isShowMessage && (
                                    <Message message={isMessage} />
                                )
                            }
                            {
                                isShowCoupon && (
                                    <Promotion message={isMessage} />
                                )
                            } */}
                            <View
                                style={{
                                    width: "100%",
                                    height: 50,
                                    flexDirection: "row",
                                    marginTop: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => this.props.navigation.goBack()}
                                    style={{
                                        width: 120,
                                        height: 50,
                                        borderRadius: 18,
                                        borderWidth: 2,
                                        borderColor: COLORS.bg00A98F,
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                >
                                    <MyText
                                        text={"QUAY LẠI"}
                                        style={{
                                            fontWeight: "bold",
                                            color: COLORS.bg00A98F,
                                        }}
                                    />
                                </TouchableOpacity>
                                <View
                                    style={{
                                        width: 10,
                                    }}
                                />
                                <TouchableOpacity
                                    onPress={() => this.handleOnNextStep()}
                                    style={{
                                        backgroundColor: "pink",
                                        flex: 1,
                                        height: 50,
                                        borderRadius: 18,
                                        alignItems: "center",
                                        justifyContent: "center",
                                        backgroundColor: COLORS.bgF49B0C,
                                    }}
                                >
                                    <MyText
                                        text={"BƯỚC TIẾP THEO"}
                                        style={{
                                            fontWeight: "bold",
                                            color: COLORS.bgFFFFFF,
                                        }}
                                    />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </SafeAreaView>
                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        dataValidateServiceRequest:
            state.bankAirtimeServiceReducer.dataValidateServiceRequest,
        dataCreateServiceRequest:
            state.bankAirtimeServiceReducer.dataCreateServiceRequest,
        dataUpdateCustomer: state.bankAirtimeServiceReducer.dataUpdateCustomer,
        dataProvince: state.insuranceAirtimeServiceReducer.dataProvince,
        updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
        dataFeeCashin: state.bankAirtimeServiceReducer.dataFeeCashin,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionGetSim: bindActionCreators(actionGetSimCreator, dispatch),
        actionBankAirtimeService: bindActionCreators(
            actionBankAirtimeServiceCreator,
            dispatch
        ),
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(StepTwo);
