import { Alert, Image, Keyboard, TouchableOpacity, View } from "react-native";
import React, { Component } from "react";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import SafeAreaView from "react-native-safe-area-view";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { helper } from "@common";
import {
    MyText,
    TitleInput,
    DatePicker,
    Picker,
    showBlockUI,
    hideBlockUI,
    PickerSearch,
    BaseLoading,
    PickerLocation
} from '@components';
import * as actionCollectionCreator from "../../../CollectionTransfer/action";
import { COLORS } from '@styles';
import { constants } from '@constants';
import { translate } from '@translate';
import * as actionGetSimCreator from '../../../ActiveSimManager/action';
import * as actionBankAirtimeServiceCreator from "../../action";
import InputCheckAMount from '../../component/Input/InputCheck/InputCheckAmount';
import CheckAccountNumber from '../../component/Input/CheckAccount/CheckAccountNumber';
import { BarcodeCamera } from "../../../../components";
import Promotion from '../../component/Coupon/Promotion';
import Message from '../../component/Text/Message';
import AddressManager from '../../../AddressManager';

class StepTwoDeposit extends Component {
    constructor(props) {
        super(props);
        this.state = {
            customerIDCard: '',
            customerName: '',
            rechargerPhoneNumber: '',
            receiptCode: '',
            depositAmount: '',
            lstBankAccount: [],
            bankCode: "",
            numberBankAccount: "",
            recipientName: "",
            rechargeFee: 0,
            iDCardIssuePlaceList: [],
            client_IDCardIssuePlaceID: '',
            idCardIssueDate: '',
            provinceID: 0,
            district: [],
            districtID: 0,
            ward: [],
            wardID: 0,
            indexPager: 0,
            customer: {
                DistrictID: 0,
                WardID: 0,
                ProvinceID: 0,
            },
            provinceName: "",
            districtName: "",
            wardName: "",
            isShowIndicator: false,
            indexPager: 0,
            streetAddress: "",
            isCheckCustomer: false,
            isVisibleScan: false,
            bankData: {},
            isActive: true,
            bankName: ""
        };
        this.pickerRefCustomerLocation = React.createRef();
    }

    componentDidMount() {
        this.updateStateFromProps();
        // this.getProvince();
        this.getDataIssueplace();
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.dataUpdateCustomer !== this.props.dataUpdateCustomer) {
            this.updateStateFromProps();
        }
        if (this.props.dataQueryCustomerBank !== prevProps.dataQueryCustomerBank) {
            const { actionBankAirtimeService, dataUpdateCustomer } = this.props;
            const recipientName =
                this.props.dataQueryCustomerBank?.AccountInfo?.data?.accountName ||
                this.state.recipientName;
            const numberBankAccount =
                this.props.dataQueryCustomerBank?.AccountInfo?.data?.accountNo ||
                this.state.numberBankAccount;
            const bankCode =
                this.props.dataQueryCustomerBank?.AccountInfo?.data?.bankCode ||
                this.state.bankCode;
            const isBankCode =
                this.props.dataQueryCustomerBank?.AccountInfo?.data?.bankCode;
            const isListBank = this.props.dataListBank;
            const filterListBank = isListBank?.filter((e) => e.bankCode === isBankCode);
            const bankData = filterListBank?.[0] ||
                this.state.bankData;
            const bankName = bankData?.bankCodeFullName ||
                this.state.bankName;

            this.setState({
                recipientName,
                numberBankAccount,
                bankCode,
                bankData,
                bankName
            });
            actionBankAirtimeService.updateCustomerAirtimeService({
                ...dataUpdateCustomer,
                recipientName,
                numberBankAccount,
                bankCode,
                bankData,
                bankName
            });
        }
        // if (prevState.provinceID !== this.state.provinceID) {
        //     if (this.state.provinceID) {
        //         console.log('Fetching districts for provinceID:', this.state.provinceID);
        //         this.getDataDistrict(this.state.provinceID);
        //     }
        // }

        // if (prevState.districtID !== this.state.districtID) {
        //     if (this.state.districtID && this.state.provinceID) {
        //         console.log('Fetching wards for districtID:', this.state.districtID);
        //         this.getDataWard(this.state.provinceID, this.state.districtID);
        //     }
        // }

    }

    updateStateFromProps() {
        const { dataUpdateCustomer } = this.props;
        const {
            customerIDCard,
            customerName,
            rechargerPhoneNumber,
            receiptCode,
            depositAmount,
            numberBankAccount,
            bankCode,
            recipientName,
            rechargeFee,
            iDCardIssuePlaceList,
            client_IDCardIssuePlaceID,
            idCardIssueDate,
            customer,
            provinceID,
            districtID,
            wardID,
            streetAddress
        } = dataUpdateCustomer;
        this.setState({
            customerIDCard: customerIDCard || '',
            receiptCode: receiptCode || '',
            customerName: customerName || '',
            rechargerPhoneNumber: rechargerPhoneNumber || '',
            depositAmount: depositAmount || '',
            numberBankAccount: numberBankAccount || '',
            bankCode: bankCode || '',
            recipientName: recipientName || '',
            rechargeFee: rechargeFee || 0,
            iDCardIssuePlaceList: iDCardIssuePlaceList || [],
            client_IDCardIssuePlaceID: client_IDCardIssuePlaceID || '',
            idCardIssueDate: idCardIssueDate || '',
            customer: customer || '',
            provinceID: provinceID || '',
            districtID: districtID || '',
            wardID: wardID || '',
            streetAddress: streetAddress || '',
        });
    }

    getDataIssueplace = () => {
        const { client_IDCardIssuePlaceID } = this.state;
        const {
            actionBankAirtimeService,
            dataUpdateCustomer
        } = this.props;
        const { customerCardType, customerIDCard, iDCardIssuePlaceList } = dataUpdateCustomer ?? "";
        const nationalityID = 241;
        if (!Array.isArray(iDCardIssuePlaceList) || iDCardIssuePlaceList.length === 0) {
            actionBankAirtimeService.getIssueplace(customerCardType, nationalityID, customerIDCard)
                .then(res => {
                    actionBankAirtimeService.updateCustomerAirtimeService({
                        ...dataUpdateCustomer,
                        iDCardIssuePlaceList: res,
                        client_IDCardIssuePlaceID: client_IDCardIssuePlaceID || res?.[0]?.idCardIssuePlaceId,
                    });
                })
                .catch(err => {
                    Alert.alert(
                        translate('common.notification'),
                        err.msgError,
                        [
                            {
                                text: translate('common.btn_close'),
                                onPress: () => { }
                            }
                        ],
                        { cancelable: false }
                    );
                });
        }
    }

    componentWillUnmount() {
        const { dataUpdateCustomer, actionBankAirtimeService } = this.props;
        const { rechargerPhoneNumber, depositAmount } = this.state;
        actionBankAirtimeService.updateCustomerAirtimeService({
            ...dataUpdateCustomer,
            rechargerPhoneNumber,
            depositAmount
        });
    }

    handleCheckBankAccount = () => {
        const {
            customerIDCard,
            customerName,
            rechargerPhoneNumber,
            bankCode,
            numberBankAccount,
            idCardIssueDate,
            client_IDCardIssuePlaceID,
            streetAddress,
            provinceID,
            districtID,
            wardID,
            isActive
        } = this.state;
        const {
            actionBankAirtimeService,
            updateHeaderAirtime,
            itemCatalog
        } = this.props;
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID
        } = itemCatalog ?? {};
        const regExpIDCard9 = new RegExp(/^\d{9}$/);
        const regExpIDCard12 = new RegExp(/^\d{12}$/);
        const isValidateIDCard9 = regExpIDCard9.test(customerIDCard);
        const isValidateIDCard12 = regExpIDCard12.test(customerIDCard);
        const isValidateIDCard = isValidateIDCard9 || isValidateIDCard12;
        const regExpPhoneNumber10 = new RegExp(/^\d{10}$/);
        const isValidateRechargerPhoneNumber = regExpPhoneNumber10.test(rechargerPhoneNumber);
        actionBankAirtimeService.clear_data_Fee_Cashin();
        Keyboard.dismiss();
        if (!isValidateIDCard) {
            Alert.alert("", translate("collection.check_id_card"));
            return false;
        }
        if (customerName == "") {
            Alert.alert("", "Vui lòng nhập tên người gửi")
            return false;
        }
        if (!isValidateRechargerPhoneNumber) {
            Alert.alert("", translate("collection.check_phone"));
            return false;
        }
        if (!idCardIssueDate || idCardIssueDate.trim() === "") {
            Alert.alert("", "Vui lòng chọn ngày cấp")
            return false;
        }
        if (client_IDCardIssuePlaceID == "") {
            Alert.alert("", "Vui lòng chọn nơi cấp")
            return false;
        }
        if (streetAddress == "") {
            Alert.alert("", "Vui lòng nhập số nhà tên đường")
            return false;
        }
        if (
            districtID == 0 ||
            provinceID == 0 ||
            wardID == 0
        ) {
            Alert.alert("", "Vui lòng chọn Tỉnh/Thành - Quận/Huyện - Phường/Xã");
            return;
        }
        if (isActive) {
            if (bankCode == "") {
                Alert.alert("", translate("collection.choose_transfer_bank"));
                return false;
            }
            if (numberBankAccount == "") {
                Alert.alert("", translate("collection.enter_account_number"));
                return false;
            }
        }
        if (isActive) {
            const data = {
                "catalogID": ServiceCategoryID,
                "serviceGroupID": AirtimeServiceGroupID,
                "airtimeTransactionTypeID": AirTimeTransactionTypeID,
                "extraData": {
                    "accountType": 2,
                    "accountNo": numberBankAccount,
                    "bankCode": bankCode,
                    "customerName": customerName,
                    "customerPhone": rechargerPhoneNumber,
                    "customerIDCard": customerIDCard
                }
            }
            actionBankAirtimeService.queryCustomerBankAcc(data);
        } else {
            this.setState({ isVisibleScan: true })
        }
    }

    handleGetBankDataQRCodeScan = (barcode) => {
        const {
            actionBankAirtimeService,
            updateHeaderAirtime,
            itemCatalog
        } = this.props;
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID
        } = itemCatalog ?? {};
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": AirTimeTransactionTypeID,
            "extraData": {
                "qrPayload": barcode
            },
        }
        this.setState({ isVisibleScan: false })
        actionBankAirtimeService.getDataBankQRCodeScan(data)
    }

    getFeeCashin = () => {
        const {
            customerIDCard,
            customerName,
            rechargerPhoneNumber,
            bankCode,
            numberBankAccount,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            depositAmount,
        } = this.state;

        const {
            dataUpdateCustomer,
            actionBankAirtimeService,
            updateHeaderAirtime,
            itemCatalog
        } = this.props;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID
        } = itemCatalog ?? {};
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": AirTimeTransactionTypeID,
            "productID": "*************",
            "amount": depositAmount,
            "extraData": {
                "accountType": 2,
                "accountNo": numberBankAccount,
                "bankCode": bankCode,
                "customerName": customerName,
                "customerPhone": rechargerPhoneNumber,
                "customerIDCard": customerIDCard
            },
        }
        showBlockUI();
        actionBankAirtimeService.getPriceAndFeeService(data).then((reponse) => {
            hideBlockUI();
            const feeDeposit = reponse?.PriceInfo?.ExtraData?.fee;
            this.setState({ rechargeFee: feeDeposit })
            actionBankAirtimeService.updateCustomerAirtimeService({
                ...dataUpdateCustomer,
                rechargeFee: feeDeposit
            });
        })
            .catch((msgError) => {
                hideBlockUI();
                Alert.alert("", msgError?.msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }

    // getProvince = () => {
    //     const { actionBankAirtimeService } = this.props;
    //     actionBankAirtimeService.getDataProvince();
    // };

    // getDataDistrict = (provinceID) => {
    //     const { actionBankAirtimeService } = this.props;
    //     this.setState({ isShowIndicator: true });
    //     actionBankAirtimeService
    //         .getDistrict(provinceID)
    //         .then((data) => {
    //             this.setState({ district: data, indexPager: 1, isShowIndicator: false });
    //         })
    //         .catch((msgError) => {
    //             Alert.alert(translate("common.notification_uppercase"), msgError, [
    //                 {
    //                     text: translate("common.btn_skip"),
    //                     style: "cancel",
    //                     onPress: () => this.setState({ isShowIndicator: false })
    //                 },
    //                 {
    //                     text: translate("common.btn_notify_try_again"),
    //                     style: "default",
    //                     onPress: () => getDataDistrict(provinceID),
    //                 },
    //             ]);
    //         });
    // };

    // getDataWard = (provinceID, districtID) => {
    //     const { actionBankAirtimeService } = this.props;
    //     this.setState({ isShowIndicator: true });
    //     actionBankAirtimeService
    //         .getWard(provinceID, districtID)
    //         .then((data) => {
    //             this.setState({ ward: data, indexPager: 2, isShowIndicator: false });
    //         })
    //         .catch((msgError) => {
    //             Alert.alert("Thông báo", msgError, [
    //                 { text: "BỎ QUA", onPress: () => this.setState({ isShowIndicator: false }) },
    //                 { text: "THỬ LẠI", onPress: () => this.getDataWard(provinceID, districtID) },
    //             ]);
    //         });
    // };


    render() {
        const {
            customerIDCard,
            customerName,
            rechargerPhoneNumber,
            depositAmount,
            bankCode,
            numberBankAccount,
            recipientName,
            rechargeFee,
            iDCardIssuePlaceList,
            idCardIssueDate,
            streetAddress,
            isVisibleScan
        } = this.state;
        const { dataUpdateCustomer, actionBankAirtimeService, stateQueryCustomerBank, updateHeaderAirtime, dataListBank, dataFeeCashin, storeID } = this.props;
        const { customerCardType, wardID, districtID, provinceID } = dataUpdateCustomer;
        const promotionInfo = dataFeeCashin?.PromotionInfo ?? {};
        const isMessage = promotionInfo?.PromotionMessage;
        const isShowMessage = promotionInfo?.PromotionID === 0 && helper.IsNonEmptyString(isMessage);
        const isShowCoupon = !!promotionInfo?.PromotionID && helper.IsNonEmptyString(isMessage);
        const min = updateHeaderAirtime?.MinAmount;
        const max = updateHeaderAirtime?.MaxAmount;
        const isConfigAddressNew = helper.checkConfigStoreInsuranceLocationFlowNew();
        return (
            <View style={{
                flex: 1,
                backgroundColor: 'white'
            }}>
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            alignItems: 'center'
                        }}>
                        <View
                            style={{
                                width: constants.width - 20,
                            }}>
                            <View style={{
                                marginTop: 20,
                            }}>
                                <MyText style={{
                                    marginBottom: 10,
                                    fontWeight: 'bold',
                                    color: COLORS.bg00AAFF,
                                    fontSize: 15,
                                    fontStyle: 'italic',
                                    textDecorationLine: 'underline',
                                }}
                                    text={translate("collection.sender_information")}
                                />
                                <TitleInput
                                    title={translate("collection.card_number")}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 10,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8
                                    }}
                                    placeholder={translate("collection.placeholder_ID_card_number")}
                                    value={customerIDCard}
                                    onChangeText={(text) => {
                                        let validate = customerCardType == 1 ? new RegExp(/^\d{0,9}$/) : new RegExp(/^\d{0,12}$/);
                                        if (validate.test(text) || text === "") {
                                            this.setState({
                                                customerIDCard: text,
                                                bankCode: "",
                                                bankName: "",
                                                numberBankAccount: "",
                                                recipientName: "",
                                                rechargeFee: 0
                                            });
                                            if (text !== "") {
                                                actionBankAirtimeService.updateCustomerAirtimeService({
                                                    ...dataUpdateCustomer,
                                                    customerIDCard: text,
                                                    bankCode: "",
                                                    bankName: "",
                                                    numberBankAccount: "",
                                                    recipientName: "",
                                                    rechargeFee: 0
                                                });
                                            }
                                        }
                                    }}
                                    keyboardType="numeric"
                                    returnKeyType="done"
                                    onSubmitEditing={Keyboard.dismiss}
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ customerIDCard: '' });
                                        actionBankAirtimeService.updateCustomerAirtimeService({
                                            ...dataUpdateCustomer,
                                            customerIDCard: "",
                                            bankCode: "",
                                            bankName: "",
                                            numberBankAccount: "",
                                            recipientName: "",
                                            rechargeFee: 0
                                        });
                                    }}
                                    key="customerIDCard"
                                    isRequired={true}
                                />
                                <TitleInput
                                    title={translate("collection.sender_name")}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 10,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8,
                                    }}
                                    placeholder={translate("collection.enter_sender_name")}
                                    value={customerName}
                                    onChangeText={(text) => {
                                        if (helper.isValidateCharVN(text)) {
                                            this.setState({
                                                customerName: text,
                                                bankCode: "",
                                                bankName: "",
                                                numberBankAccount: "",
                                                recipientName: "",
                                                rechargeFee: 0
                                            });
                                            if (text !== "") {
                                                actionBankAirtimeService.updateCustomerAirtimeService({
                                                    ...dataUpdateCustomer,
                                                    customerName: text,
                                                    bankCode: "",
                                                    bankName: "",
                                                    numberBankAccount: "",
                                                    recipientName: "",
                                                    rechargeFee: 0
                                                });
                                            }
                                        }
                                    }}
                                    keyboardType={"default"}
                                    returnKeyType={"done"}
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ customerName: '' });
                                        actionBankAirtimeService.updateCustomerAirtimeService({
                                            ...dataUpdateCustomer,
                                            customerName: "",
                                            bankCode: "",
                                            bankName: "",
                                            numberBankAccount: "",
                                            recipientName: "",
                                            rechargeFee: 0
                                        });
                                    }}
                                    key="customerName"
                                    isRequired={true}
                                />
                                <TitleInput
                                    title={translate("collection.sender_phone")}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 5,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8
                                    }}
                                    placeholder={translate("collection.enter_sender_phone")}
                                    value={rechargerPhoneNumber}
                                    onChangeText={(text) => {
                                        const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                        const isValidate = regExpPhone.test(text) || (text == "");
                                        if (isValidate) {
                                            this.setState({
                                                rechargerPhoneNumber: text,
                                                bankCode: "",
                                                bankName: "",
                                                numberBankAccount: "",
                                                recipientName: "",
                                                rechargeFee: 0
                                            });
                                            actionBankAirtimeService.updateCustomerAirtimeService({
                                                ...dataUpdateCustomer,
                                                rechargerPhoneNumber: text,
                                                bankCode: "",
                                                bankName: "",
                                                numberBankAccount: "",
                                                recipientName: "",
                                                rechargeFee: 0
                                            });
                                        }
                                    }}
                                    keyboardType="numeric"
                                    returnKeyType="done"
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ rechargerPhoneNumber: '' });
                                        actionBankAirtimeService.updateCustomerAirtimeService({
                                            ...dataUpdateCustomer,
                                            rechargerPhoneNumber: "",
                                            bankCode: "",
                                            bankName: "",
                                            numberBankAccount: "",
                                            recipientName: "",
                                            rechargeFee: 0
                                        });
                                    }}
                                    key="rechargerPhoneNumber"
                                    isRequired={true}
                                />
                                <View style={{
                                    marginVertical: -5,
                                }}>
                                    <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                                        text={"Ngày cấp: "}
                                        children={<MyText text={'*'} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                                    />
                                    <DatePicker
                                        date={idCardIssueDate}
                                        format={'YYYY-MM-DD'}
                                        onDateChange={(dateStr) => {
                                            this.setState({ idCardIssueDate: dateStr })
                                            actionBankAirtimeService.updateCustomerAirtimeService({
                                                ...dataUpdateCustomer,
                                                idCardIssueDate: `${dateStr}T00:00:00`
                                            });
                                        }}
                                    />
                                </View>
                                <View style={{
                                    marginBottom: 8,
                                    marginTop: 8
                                }}>
                                    <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                                        text={'Nơi cấp: '}
                                        children={<MyText text={"*"} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                                    />
                                    <View style={{
                                        borderWidth: 1.5,
                                        borderColor: COLORS.bdE4E4E4,
                                        alignItems: "center",
                                        justifyContent: "center",
                                        borderRadius: 5,
                                        height: "auto",
                                        backgroundColor: COLORS.bgFFFFFF,
                                    }}>
                                        <Picker
                                            label={"idCardIssuePlaceName"}
                                            value={"idCardIssuePlaceId"}
                                            data={iDCardIssuePlaceList}
                                            valueSelected={this.state.client_IDCardIssuePlaceID}
                                            onChange={(item) => {
                                                this.setState({ client_IDCardIssuePlaceID: item.idCardIssuePlaceId });
                                                actionBankAirtimeService.updateCustomerAirtimeService({
                                                    ...dataUpdateCustomer,
                                                    client_IDCardIssuePlaceID: item.idCardIssuePlaceId,
                                                });
                                            }}
                                            defaultLabel={translate('instalmentManager.header_place')}
                                            style={{
                                                flex: 1,
                                                flexDirection: "row",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                height: 30,
                                                marginVertical: 5,
                                                marginLeft: -5,
                                                marginRight: -10,
                                            }}
                                        />
                                    </View>
                                </View>
                                <TitleInput
                                    title={"Số nhà, tên đường"}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 10,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8,
                                    }}
                                    placeholder={"Vui lòng nhập số nhà tên đường"}
                                    value={streetAddress}
                                    onChangeText={(text) => {
                                        if (helper.isValidateCharVN(text)) {
                                            this.setState({
                                                streetAddress: text,
                                                rechargeFee: 0
                                            });
                                            if (text !== "") {
                                                actionBankAirtimeService.updateCustomerAirtimeService({
                                                    ...dataUpdateCustomer,
                                                    streetAddress: text,
                                                });
                                            }
                                        }
                                    }}
                                    keyboardType={"default"}
                                    returnKeyType={"done"}
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ streetAddress: '' });
                                        actionBankAirtimeService.updateCustomerAirtimeService({
                                            ...dataUpdateCustomer,
                                            streetAddress: "",
                                        });
                                    }}
                                    key="streetAddress"
                                    isRequired={true}
                                />
                                <View style={{ marginTop: 5 }}>
                                    <MyText
                                        style={{
                                            fontWeight: "700",
                                            fontStyle: "italic",
                                            fontSize: 13,
                                        }}
                                        text={"Địa chỉ thường trú"}
                                        children={
                                            <MyText
                                                text={"*"}
                                                style={{ color: COLORS.txtFF0000, fontSize: 16 }}
                                            />
                                        }
                                    />
                                    <AddressManager
                                        ref={this.pickerRefPermanent}
                                        wardID={wardID}
                                        districtID={districtID}
                                        provinceID={provinceID}
                                        onChangeProvince={(item) => {
                                            this.setState((prevState) => ({
                                                ...prevState,
                                                provinceName: item.provinceName,
                                                provinceID: item.provinceID,
                                                districtID: 0,
                                                wardID: 0,
                                            }));
                                        }}
                                        onChangeDistrict={(item) => {
                                            this.setState((prevState) => ({
                                                ...prevState,
                                                districtName: item.districtName,
                                                districtID: item.districtID,
                                                wardID: 0,
                                            }));
                                        }}
                                        onChangeWard={(item) => {
                                            if (item.wardID !== this.state.wardID) {
                                                this.setState((prevState) => ({
                                                    ...prevState,
                                                    wardName: item.wardName,
                                                    wardID: item.wardID
                                                }), () => {
                                                    actionBankAirtimeService.updateCustomerAirtimeService({
                                                        ...dataUpdateCustomer,
                                                        provinceID: this.state.provinceID,
                                                        districtID: this.state.districtID,
                                                        wardID: this.state.wardID,
                                                        provinceName: this.state.provinceName,
                                                        districtName: this.state.districtName,
                                                        wardName: this.state.wardName,
                                                    });
                                                });
                                            }
                                        }}

                                        isShowOldAddress={isConfigAddressNew}
                                        enableDistrictSelection={!isConfigAddressNew}
                                    />
                                </View>

                                <MyText style={{
                                    marginTop: 10,
                                    marginBottom: 10,
                                    fontWeight: 'bold',
                                    color: COLORS.bg00AAFF,
                                    fontSize: 15,
                                    fontStyle: 'italic',
                                    textDecorationLine: 'underline',
                                }}
                                    text={translate("collection.receiver_information")}
                                />
                                <TouchableOpacity
                                    onPress={() => {
                                        this.setState({ isActive: false }, () => {
                                            this.handleCheckBankAccount();
                                        });
                                    }}
                                    style={{
                                        flexDirection: 'row',
                                        alignSelf: 'flex-end',
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}>
                                    <Image
                                        resizeMode="contain"
                                        source={require("../../../../../assets/camera.png")}
                                        style={{
                                            width: 30,
                                            height: 30,
                                            borderRadius: 10,
                                        }}
                                    />
                                    <MyText style={{
                                        fontWeight: 'bold',
                                        color: COLORS.btn147EFB,
                                        fontSize: 15,
                                        fontStyle: 'italic'
                                    }}
                                        text={"Quét mã QR nhận tiền"}
                                    />
                                </TouchableOpacity>

                                <MyText
                                    text={translate("collection.bank")}
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.txt333333,
                                        fontWeight: "bold",
                                        fontStyle: "italic",
                                        marginBottom: 5
                                    }}>
                                    {
                                        <MyText
                                            text={"*"}
                                            addSize={-1.5}
                                            style={{
                                                color: COLORS.txtFF0000
                                            }}
                                        />
                                    }
                                </MyText>
                                <PickerSearch
                                    label={"bankCodeFullName"}
                                    value={"bankCode"}
                                    valueSelected={bankCode}
                                    data={helper.IsNonEmptyArray(dataListBank) ? dataListBank : []}
                                    onChange={(item) => {
                                        this.setState({
                                            bankCode: item.bankCode,
                                            numberBankAccount: "",
                                            recipientName: "",
                                            rechargeFee: 0,
                                            bankData: item
                                        })
                                        actionBankAirtimeService.clear_data_Fee_Cashin();
                                        actionBankAirtimeService.updateCustomerAirtimeService({
                                            ...dataUpdateCustomer,
                                            bankCode: item.bankCode,
                                            bankName: item.bankCodeFullName,
                                            bankData: item,
                                            numberBankAccount: "",
                                            recipientName: "",
                                            rechargeFee: 0
                                        });
                                    }}
                                    style={{
                                        flex: 1,
                                        flexDirection: "row",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        height: 40,
                                        borderRadius: 4,
                                        borderWidth: 1,
                                        borderColor: COLORS.bdE4E4E4,
                                        width: constants.width - 20,
                                        backgroundColor: COLORS.btnFFFFFF,
                                        marginBottom: 10
                                    }}
                                    defaultLabel={translate("collection.choose_bank")}
                                />
                                <CheckAccountNumber
                                    title={translate("collection.account_number")}
                                    placeholder={translate("collection.account_number_required")}
                                    value={numberBankAccount}
                                    onChangeText={(text) => {
                                        let validate = new RegExp(/^[a-zA-Z0-9\-\%\+\/\$\.\/]{0,30}$/);
                                        if ((validate.test(text) || text == "") && this.state.numberBankAccount !== text) {
                                            console.log(" ?? ", text)
                                            this.setState({
                                                numberBankAccount: text,
                                                recipientName: "",
                                                rechargeFee: 0

                                            });
                                            actionBankAirtimeService.clear_data_Fee_Cashin();
                                            actionBankAirtimeService.updateCustomerAirtimeService({
                                                ...dataUpdateCustomer,
                                                numberBankAccount: text,
                                                recipientName: "",
                                                rechargeFee: 0
                                            });
                                        }
                                    }}
                                    handleCheck={() => {
                                        this.setState({ isActive: true }, () => {
                                            this.handleCheckBankAccount();
                                        });
                                    }}
                                    disabled={false}
                                />
                                {
                                    <BaseLoading
                                        isLoading={stateQueryCustomerBank.isFetching}
                                        isError={stateQueryCustomerBank.isError}
                                        isEmpty={stateQueryCustomerBank.isEmpty}
                                        textLoadingError={stateQueryCustomerBank.description}
                                        onPressTryAgains={() => {
                                            this.setState({ isActive: true }, () => {
                                                this.handleCheckBankAccount();
                                            });
                                        }}
                                        content={
                                            !!recipientName &&
                                            <View>
                                                <TitleInput
                                                    title={translate("collection.recipient_name")}
                                                    styleInput={{
                                                        borderWidth: 1,
                                                        borderRadius: 4,
                                                        borderColor: COLORS.bdCCCCCC,
                                                        marginBottom: 5,
                                                        paddingHorizontal: 10,
                                                        backgroundColor: COLORS.bgF0F0F0,
                                                        paddingVertical: 8,
                                                    }}
                                                    placeholder="0đ"
                                                    value={recipientName}
                                                    onChangeText={(text) => { }}
                                                    keyboardType={"default"}
                                                    returnKeyType={"done"}
                                                    blurOnSubmit
                                                    width={constants.width - 20}
                                                    height={40}
                                                    clearText={() => { }}
                                                    editable={false}
                                                    key="recipientName"
                                                />
                                                <InputCheckAMount
                                                    title={translate("collection.transaction_amount")}
                                                    placeholder={translate("collection.enter_amount_want_to_deposit")}
                                                    value={depositAmount}
                                                    onChange={(text) => {
                                                        this.setState({
                                                            depositAmount: text,
                                                            rechargeFee: 0
                                                        });
                                                        actionBankAirtimeService.updateCustomerAirtimeService({
                                                            ...dataUpdateCustomer,
                                                            depositAmount: text,
                                                            rechargeFee: 0
                                                        });
                                                    }}
                                                    onCheckMount={() => this.getFeeCashin()}
                                                    disabled={false}
                                                    isRequired={true}
                                                    editable={false}
                                                />
                                                {
                                                    !helper.IsEmptyObject(dataFeeCashin) &&
                                                    <View>
                                                        <TitleInput
                                                            title={translate("collection.transaction_fee")}
                                                            styleInput={{
                                                                borderWidth: 1,
                                                                borderRadius: 4,
                                                                borderColor: COLORS.bdCCCCCC,
                                                                marginBottom: 5,
                                                                paddingHorizontal: 10,
                                                                backgroundColor: COLORS.bgF0F0F0,
                                                                paddingVertical: 8,
                                                            }}
                                                            placeholder="0đ"
                                                            value={`${helper.formatMoney(rechargeFee)}`}
                                                            onChangeText={(text) => { }}
                                                            keyboardType="numeric"
                                                            returnKeyType="done"
                                                            blurOnSubmit
                                                            width={constants.width - 20}
                                                            height={40}
                                                            clearText={() => { }}
                                                            editable={!!rechargeFee}
                                                            key="rechargeFee"
                                                        />
                                                        {/* {
                                                            isShowMessage && (
                                                                <Message message={isMessage} />
                                                            )
                                                        }
                                                        {
                                                            isShowCoupon && (
                                                                <Promotion message={isMessage} />
                                                            )
                                                        } */}
                                                        <View style={{
                                                            width: '100%',
                                                            height: 50,
                                                            flexDirection: 'row',
                                                            paddingHorizontal: 10,
                                                            marginTop: 5
                                                        }}>
                                                            <TouchableOpacity
                                                                onPress={() => this.props.navigation.goBack()}
                                                                style={{
                                                                    width: 120,
                                                                    height: 50,
                                                                    borderRadius: 18,
                                                                    borderWidth: 2,
                                                                    borderColor: COLORS.bg00A98F,
                                                                    alignItems: "center",
                                                                    justifyContent: "center"
                                                                }}>
                                                                <MyText
                                                                    text={'QUAY LẠI'}
                                                                    style={{
                                                                        fontWeight: 'bold',
                                                                        color: COLORS.bg00A98F
                                                                    }}
                                                                />
                                                            </TouchableOpacity>
                                                            <View style={{
                                                                width: 10
                                                            }} />
                                                            <TouchableOpacity
                                                                onPress={() => this.props.navigation.navigate('StepThreeDeposit')}
                                                                style={{
                                                                    backgroundColor: 'pink',
                                                                    flex: 1,
                                                                    height: 50,
                                                                    borderRadius: 18,
                                                                    alignItems: "center",
                                                                    justifyContent: "center",
                                                                    backgroundColor: COLORS.bgF49B0C
                                                                }}>
                                                                <MyText
                                                                    text={'BƯỚC TIẾP THEO'}
                                                                    style={{
                                                                        fontWeight: 'bold',
                                                                        color: COLORS.bgFFFFFF
                                                                    }}
                                                                />
                                                            </TouchableOpacity>
                                                        </View>
                                                    </View>
                                                }
                                            </View>

                                        }
                                    />
                                }
                            </View>
                        </View>
                        {isVisibleScan && (
                            <BarcodeCamera
                                isVisible={isVisibleScan}
                                closeCamera={() => this.setState({ isVisibleScan: false })}
                                resultScanBarcode={(barcode) => {
                                    this.handleGetBankDataQRCodeScan(barcode)
                                }}
                            />
                        )}
                    </SafeAreaView>
                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        dataValidateServiceRequest: state.bankAirtimeServiceReducer.dataValidateServiceRequest,
        dataCreateServiceRequest: state.bankAirtimeServiceReducer.dataCreateServiceRequest,
        dataUpdateCustomer: state.bankAirtimeServiceReducer.dataUpdateCustomer,
        updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
        dataQueryCustomerBank: state.bankAirtimeServiceReducer.dataQueryCustomerBank,
        stateQueryCustomerBank: state.bankAirtimeServiceReducer.stateQueryCustomerBank,
        dataListBank: state.bankAirtimeServiceReducer.dataListBank,
        dataProvince: state.insuranceAirtimeServiceReducer.dataProvince,
        dataFeeCashin: state.bankAirtimeServiceReducer.dataFeeCashin,
        storeID: state.userReducer.storeID,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionGetSim: bindActionCreators(actionGetSimCreator, dispatch),
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(StepTwoDeposit);

