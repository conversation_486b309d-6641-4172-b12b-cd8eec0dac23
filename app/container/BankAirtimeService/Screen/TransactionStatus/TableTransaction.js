import { Animated, Image, ScrollView, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { COLORS } from '../../../../styles'
import Table from '../../component/Table/Table'
import { constants } from '../../../../constants'
import { helper } from '../../../../common'

const TableTransaction = () => {

    const [fadeAnim] = useState(new Animated.Value(1));

    useEffect(() => {
        const timer = setTimeout(() => {
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 1000,
                useNativeDriver: true
            }).start();
        }, 3000);
        return () => clearTimeout(timer);

    }, [fadeAnim]);
    const DATA = [
        {
            title: '<PERSON>êu thị',
            value: '340'
        },
        {
            title: 'Siêu thị',
            value: '340'
        },
    ]
    const DATATWO = [
        {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            money: 200000000,
            moneyA: 200000000,
            moneyB: 200000000,
        },
        {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            money: 200000000,
            moneyA: 200000000,
            moneyB: 200000000,
        },
        {
            name: 'Nguyễn Văn C',
            money: 200000000,
            moneyA: 200000000,
            moneyB: 200000000,
        },
        {
            name: 'Nguyễn Văn D',
            money: 200000000,
            moneyA: 200000000,
            moneyB: 200000000,
        },
        {
            name: 'Nguyễn Văn E',
            money: 200000000,
            moneyA: 200000000,
            moneyB: 200000000,
        },
        {
            name: 'Nguyễn Văn F',
            money: 200000000,
            moneyA: 200000000,
            moneyB: 200000000,
        },
        {
            name: 'Nguyễn Văn G',
            money: 200000000,
            moneyA: 200000000,
            moneyB: 200000000,
        },
        {
            name: 'Nguyễn Văn H',
            money: 200000000,
            moneyA: 200000000,
            moneyB: 200000000,
        },

    ]

    return (
        <ScrollView style={styles.container}>
            <View style={{
                paddingHorizontal: 10
            }}>
                <Text style={{
                    fontWeight: 'bold',
                    color: COLORS.bgFF0000
                }}>
                    Hạn mức siêu thị cho dịch vụ nạp tiền và dịch vụ rút tiền tài khoản ngân hàng:
                </Text>
            </View>
            <View style={{
                marginTop: 10
            }}>
                <Table
                    showHeader={false}
                    children={
                        <View>
                            {DATA.map((item, index) => {
                                return (
                                    <View key={index} style={{
                                        flexDirection: 'row',
                                        height: 30,
                                        alignSelf: 'center',
                                        borderColor: COLORS.bg2FB47C,
                                        width: constants.width - 20
                                    }}>
                                        <View style={[styles.itemId, {
                                            borderTopWidth: 1,
                                            borderBottomWidth: index === DATA.length - 1 ? 1 : 0
                                        }]}>
                                            <Text>{item.title}</Text>
                                        </View>
                                        <View style={[styles.itemTitle, {
                                            borderTopWidth: 1,
                                            borderBottomWidth: index === DATA.length - 1 ? 1 : 0
                                        }]}>
                                            <Text>{item.value}</Text>
                                        </View>
                                    </View>
                                )
                            })}
                        </View>
                    }
                />
            </View>
            <View style={{
                width: constants.width - 20,
                alignSelf: 'center',
                marginTop: 10
            }}>
                <View style={{
                }}>
                    <Text style={{
                        fontWeight: 'bold',
                        color: COLORS.bgFF0000
                    }}>
                        Hạn mức nhân viên cho dịch vụ nạp tiền vào tài khoản:
                    </Text>
                </View>
                <ScrollView
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{
                        width: 710,
                    }}
                    horizontal>
                    <Table
                        fuorColumsHeader={true}
                        fuorColumsDraw={true}
                        columsOne={'Nhân viên'}
                        columsTwo={'Hạn mức nhân viên'}
                        columsThree={'Số tiền giao dịch đã thực hiện'}
                        columsFour={'Hạn mức còn lại trong ngày'}
                        children={
                            <View>
                                {DATATWO.map((item, index) => {
                                    return (
                                        <View key={index} style={styles.containerTable}>
                                            <View style={[styles.widthTable, {
                                                borderTopWidth: 1,
                                                borderBottomWidth: index === DATATWO.length - 1 ? 1 : 0,
                                                width: 150,
                                            }]}>
                                                <Text>{item.name}</Text>
                                            </View>
                                            <View style={[styles.widthTable, {
                                                borderTopWidth: 1,
                                                borderBottomWidth: index === DATATWO.length - 1 ? 1 : 0,
                                                width: 150,
                                                alignItems: 'flex-end',
                                                paddingHorizontal: 10
                                            }]}>
                                                <Text>{helper.formatMoney(item.moneyA)}</Text>
                                            </View>
                                            <View style={[styles.widthTable, {
                                                borderTopWidth: 1,
                                                borderBottomWidth: index === DATATWO.length - 1 ? 1 : 0,
                                                width: 210,
                                                alignItems: 'flex-end',
                                                paddingHorizontal: 10
                                            }]}>
                                                <Text>{helper.formatMoney(item.moneyA)}</Text>
                                            </View>
                                            <View style={[styles.widthTable, {
                                                borderTopWidth: 1,
                                                borderBottomWidth: index === DATATWO.length - 1 ? 1 : 0,
                                                borderRightWidth: 1,
                                                width: 200,
                                                alignItems: 'flex-end',
                                                paddingHorizontal: 10
                                            }]}>
                                                <Text>{helper.formatMoney(item.moneyA)}</Text>
                                            </View>
                                        </View>
                                    )
                                })}
                            </View>
                        }
                    />
                </ScrollView>
            </View>
            <Animated.View style={{
                width: 100,
                height: 70,
                alignSelf: 'flex-end',
                alignItems: 'flex-end',
                right: 5,
                opacity: fadeAnim,
            }}>
                <Image
                    resizeMode='stretch'
                    source={require('../../../../../assets/slide.gif')}
                    style={{
                        width: 30,
                        height: 35,
                        marginTop: -10,
                    }}
                />
                <Text style={{
                    color: COLORS.bg288AD6
                }}>Kéo sang trái</Text>
            </Animated.View>
        </ScrollView>
    )
}

export default TableTransaction

const styles = StyleSheet.create({
    container: {
        backgroundColor: COLORS.bgFFFFFF,
        paddingTop: 20,
    },
    containerTable: {
        flexDirection: 'row',
        height: 30,
        alignSelf: 'center',
        borderColor: COLORS.bg2FB47C,
    },
    itemId: {
        flex: 1,
        alignItems: 'flex-start',
        justifyContent: 'center',
        borderLeftWidth: 1,
        borderColor: COLORS.bg2FB47C,
        paddingHorizontal: 5
    },
    itemTitle: {
        flex: 1,
        borderLeftWidth: 1,
        borderRightWidth: 1,
        borderColor: COLORS.bg2FB47C,
        justifyContent: 'center',
        paddingLeft: 7,
    },
    widthTable: {
        // flex: 1,
        borderLeftWidth: 1,
        borderColor: COLORS.bg2FB47C,
        justifyContent: 'center',
        paddingLeft: 5
    }
})
