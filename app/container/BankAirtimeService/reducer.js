import { bankAirtimeServiceState } from "./state";
import { actionBankAirtimeService } from "./action";

const bankAirtimeServiceReducer = function (state = bankAirtimeServiceState, action) {
    switch (action.type) {
        case actionBankAirtimeService.START_GET_SERVICE_LIST:
            return {
                ...state,
                dataServiceList: {},
                stateServiceList: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }
        case actionBankAirtimeService.STOP_GET_SERVICE_LIST:
            return {
                ...state,
                dataServiceList: action.data,
                stateServiceList: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }
        case actionBankAirtimeService.UPDATE_HEADER_AIRTIME:
            return {
                ...state,
                updateHeaderAirtime: action.data,
            }
        case actionBankAirtimeService.START_VALIDATE_DATA_SERVICE_REQUEST:
            return {
                ...state,
                dataValidateServiceRequest: {},
            }
        case actionBankAirtimeService.STOP_VALIDATE_DATA_SERVICE_REQUEST:
            return {
                ...state,
                dataValidateServiceRequest: action.data,
            }
        case actionBankAirtimeService.START_GET_DATA_CREATE_SERVICE_REQUEST:
            return {
                ...state,
                dataCreateServiceRequest: {},
            }
        case actionBankAirtimeService.STOP_GET_DATA_CREATE_SERVICE_REQUEST:
            return {
                ...state,
                dataCreateServiceRequest: action.data,
            }
        case actionBankAirtimeService.START_GET_SEND_OTP_PROCESS:
            return {
                ...state,
                dataSendOTPProcess: {},
            }

        case actionBankAirtimeService.STOP_GET_SEND_OTP_PROCESS:
            return {
                ...state,
                dataSendOTPProcess: action.data,
            }
        case actionBankAirtimeService.UPDATE_CUSTOMER_AIRTIME_SERCICE:
            return {
                ...state,
                dataUpdateCustomer: action.data,
            };
        case actionBankAirtimeService.CLEAR_DATA_CUSTOMER:
            return {
                ...state,
                dataUpdateCustomer: {},
            };
        case actionBankAirtimeService.SET_QR_INFO:
            return {
                ...state,
                qrInfo: action.qrInfo,
            };
        case actionBankAirtimeService.START_QUERY_CUSTOMER_BANK_ACCOUNT:
            return {
                ...state,
                dataQueryCustomerBank: {},
                stateQueryCustomerBank: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }
        case actionBankAirtimeService.STOP_QUERY_CUSTOMER_BANK_ACCOUNT:
            return {
                ...state,
                dataQueryCustomerBank: action.data,
                stateQueryCustomerBank: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }
        case actionBankAirtimeService.START_GET_FEE_CASHIN:
            return {
                ...state,
                dataFeeCashin: {},
            }
        case actionBankAirtimeService.STOP_GET_FEE_CASHIN:
            return {
                ...state,
                dataFeeCashin: action.data,
            }
        case actionBankAirtimeService.STOP_ADD_TO_SALE_ORDER_CART:
            return {
                ...state,
                dataSaleOrderCart: action.dataSaleOrderCart,
            };
        case actionBankAirtimeService.START_GET_BANK_LIST:
            return {
                ...state,
                dataListBank: {},
            }
        case actionBankAirtimeService.STOP_GET_BANK_LIST:
            return {
                ...state,
                dataListBank: action.data,
            }
        case actionBankAirtimeService.STOP_INSERT_AND_CREATE_TICKET:
            return {
                ...state,
                dataInserAndCreateTicket: action.data,
            }
        case actionBankAirtimeService.UPDATE_DATA_TICKET:
            return {
                ...state,
                dataCreateServiceRequest: action.data,
            }
        case actionBankAirtimeService.START_PREPARE_DATA_BEFORE_CREATE_ORDER:
            return {
                ...state,
                dataPrepareCreateOrder: {},
            }
        case actionBankAirtimeService.STOP_PREPARE_DATA_BEFORE_CREATE_ORDER:
            return {
                ...state,
                dataPrepareCreateOrder: action.data,
            }
        case actionBankAirtimeService.CLEAR_DATA_PREAPRE_CREATE_ORDER:
            return {
                ...state,
                dataPrepareCreateOrder: {},
            }
        case actionBankAirtimeService.START_GET_PROVINCE:
            return {
                ...state,
                stateProvince: {
                    isFetching: true,
                    isEmpty: false,
                    description: "",
                    isError: false,
                },
            };
        case actionBankAirtimeService.STOP_GET_PROVINCE:
            return {
                ...state,
                dataProvince: action.dataProvince,
                stateProvince: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError,
                },
            };
        case actionBankAirtimeService.START_GET_DISTRICT:
            return {
                ...state,
                stateDistrict: {
                    isFetching: true,
                    isEmpty: false,
                    description: "",
                    isError: false,
                },
            };
        case actionBankAirtimeService.STOP_GET_DISTRICT:
            return {
                ...state,
                dataDistrict: action.dataDistrict,
                stateDistrict: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError,
                },
            };
        case actionBankAirtimeService.CLEAR_DATA_FEE_CASHIN:
            return {
                ...state,
                dataFeeCashin: {},
            }
        case actionBankAirtimeService.UPDATE_CUSTOMER_PHONE_NUMBER:
            return {
                ...state,
                customerPhone: action.data,
            }
        case actionBankAirtimeService.CLEAR_DATA_FEE_CASHIN:
            return {
                ...state,
                dataFeeCashin: {},
            };
        case actionBankAirtimeService.START_GET_DATA_QUERY_STATUS:
            return {
                ...state,
                dataQueryStatus: {}
            };
        case actionBankAirtimeService.STOP_GET_DATA_QUERY_STATUS:
            return {
                ...state,
                dataQueryStatus: action.data,
            };
        case actionBankAirtimeService.START_GET_DATA_QUERY_STATUS:
            return {
                ...state,
                dataQueryStatus: {}
            };
        case actionBankAirtimeService.STOP_GET_DATA_QUERY_STATUS:
            return {
                ...state,
                dataQueryStatus: action.data,
            };
        default:
            return state;
    }
};

export { bankAirtimeServiceReducer };
