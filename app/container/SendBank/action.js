import { API_CONST } from '@constants';
import { helper } from '@common';
import { METHOD, apiBase, EMPTY, ERROR } from '@config';
import { hideB<PERSON><PERSON>, showB<PERSON><PERSON> } from '../../components';
const {
    API_GET_STORE_INFO,
    API_SEARCH_LIST_SENDBANK,
    API_CHANGE_APPROVE_USER_SENDBANK,
    API_GET_USER,
    API_GET_APPROVAL_STATUS,
    API_GET_CANCEL_REASON,
    API_GET_PAYMENT_DETAILS,
    API_GET_FILE_ATTACH,
    API_APPROVE_REQ_MPOS,
    API_CANCEL_APPROVE_REQ,
    API_GET_LIST_BANK
} = API_CONST;

const START_SEARCH_LIST_SENDBANK = 'START_SEARCH_LIST_SENDBANK';
const STOP_SEARCH_LIST_SENDBANK = 'STOP_SEARCH_LIST_SENDBANK';
const RESET_SEARCH_LIST_SENDBANK = 'RESET_SEARCH_LIST_SENDBANK';
const START_CHANGE_STORE = 'START_CHANGE_STORE';
const STOP_CHANGE_STORE = 'STOP_CHANGE_STORE';
const RESET_CHANGE_STORE = 'RESET_CHANGE_STORE';
const START_SEARCH_USER = 'START_SEARCH_USER';
const STOP_SEARCH_USER = 'STOP_SEARCH_USER';
const RESET_SEARCH_USER = 'RESET_SEARCH_USER';
const START_CHANGE_APPROVE_USER = 'STARTCHANGE_APPROVE_USER';
const STOP_CHANGE_APPROVE_USER = 'STOP_CHANGE_APPROVE_USER';
const START_GET_CANCEL_REASON = 'START_GET_CANCEL_REASON';
const STOP_GET_CANCEL_REASON = 'STOP_GET_CANCEL_REASON';
const START_GET_APPROVAL_STARTS = 'START_GET_APPROVAL_STARTS';
const STOP_GET_APPROVAL_STARTS = 'STOP_GET_APPROVAL_STARTS';
const START_GET_PAYMENT_DETAILS = 'START_GET_PAYMENT_DETAILS';
const STOP_GET_PAYMENT_DETAILS = 'STOP_GET_PAYMENT_DETAILS';
const START_GET_BANKS = 'START_GET_BANKS';
const STOP_GET_BANKS = 'STOP_GET_BANKS';

export const sendBankAction = {
    START_SEARCH_LIST_SENDBANK,
    STOP_SEARCH_LIST_SENDBANK,
    RESET_SEARCH_LIST_SENDBANK,
    START_CHANGE_STORE,
    STOP_CHANGE_STORE,
    RESET_CHANGE_STORE,
    START_SEARCH_USER,
    STOP_SEARCH_USER,
    RESET_SEARCH_USER,
    START_CHANGE_APPROVE_USER,
    STOP_CHANGE_APPROVE_USER,
    START_GET_APPROVAL_STARTS,
    STOP_GET_APPROVAL_STARTS,
    START_GET_PAYMENT_DETAILS,
    STOP_GET_PAYMENT_DETAILS,
    START_GET_CANCEL_REASON,
    STOP_GET_CANCEL_REASON,
    START_GET_BANKS,
    STOP_GET_BANKS
};

export const getListSendBank = function (data = {}) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                fromDate: data.fromDate || new Date(),
                toDate: data.toDate || new Date(),
                keyWordType: '',
                keyWordCharacter: '',
                companyId: 1,
                lstStoreId: [getState().userReducer.storeID],
                status: data.statusCode,
                pageIndex: data.pageIndex || 1,
                pageSize: 10,
                approveRequestId: data.userTextVoucher,
                createdUser: data.userText
            };
            if (body.pageIndex < 2) {
                dispatch(start_list_search_send_bank());
            }
            apiBase(API_SEARCH_LIST_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (
                        helper.IsNonEmptyArray(
                            object.lstTotalPaymentInformation
                        )
                    ) {
                        dispatch(
                            stop_list_search_send_bank(
                                object.lstTotalPaymentInformation
                            )
                        );
                        resolve(object.lstTotalPaymentInformation);
                    } else {
                        dispatch(
                            stop_list_search_send_bank(
                                [],
                                !EMPTY,
                                'Không tìm thấy giao dịch nào',
                                true
                            )
                        );
                        resolve([]);
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_list_search_send_bank(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error.msgError);
                });
        });
    };
};

export const getStoreList = function (keyword = '') {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                keyword: keyword,
                storeIDList: ''
            };
            dispatch(start_get_store_list());
            apiBase(API_GET_STORE_INFO, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        dispatch(stop_get_store_list(object));
                        resolve(object);
                    } else {
                        dispatch(
                            stop_get_store_list(
                                [],
                                !EMPTY,
                                'Không lấy được dữ liệu'
                            )
                        );
                        resolve([]);
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_get_service_receipt(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error);
                });
        });
    };
};

export const searchUser = function (keyword) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                userName: keyword,
                storeIdList: [getState().userReducer.storeID]
            };
            dispatch(start_search_user());
            apiBase(API_GET_USER, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        dispatch(stop_search_user(object));
                        resolve(object);
                    } else {
                        dispatch(
                            stop_search_user(
                                [],
                                !EMPTY,
                                'Không lấy được dữ liệu',
                                !ERROR
                            )
                        );
                        resolve([], EMPTY, 'Không lấy được dữ liệu');
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_search_user([], !EMPTY, error.msgError, ERROR)
                    );
                    reject(error);
                });
        });
    };
};

// Đã bỏ
// export const checkPremissionSendBank = function (userName) {
//     return function (dispatch, getState) {
//         return new Promise((resolve, reject) => {
//             let body = {
//                 loginStoreId: getState().userReducer.storeID,
//                 moduleID: getState().userReducer.moduleID,
//                 languageID: getState().userReducer.languageID,
//                 saleScenarioTypeID: 0,
//                 userName: userName,
//                 permissionKey: 'ACC_SENDBANK_APPROVEREQUEST_MPOS'
//             };
//             apiBase(API_CHECK_PERMISSION_SENDBANK, METHOD.POST, body)
//                 .then((response) => {
//                     const { object } = response;
//                     if (helper.IsNonEmptyArray(object)) {
//                         resolve(object);
//                     } else {
//                         resolve(false);
//                     }
//                 })
//                 .catch((error) => {
//                     reject(error);
//                 });
//         });
//     };
// };

export const getChangeApproveUserSendbank = function (
    item,
    userId,
    userFullName
) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                lstChangeApprove: [
                    {
                        totalRow: item.totalRow,
                        realSendDate: item.realSendDate,
                        approveRequestId: item.approveRequestId,
                        updatedUserFullName: item.updatedUserFullName,
                        totalMoney: item.totalMoney,
                        bankName: item.bankName,
                        updatedDate: item.updatedDate,
                        storeId: item.storeId,
                        approveUserId: userId,
                        approveUserName: userFullName,
                        createdDate: item.createdDate,
                        createdUserName: item.createdUserName,
                        approveRQDate: item.approveRQDate,
                        voucherId: item.voucherId,
                        attached: item.attached,
                        approveRequestUserVS: item.approveRequestUserVS,
                        storeName: item.storeName,
                        approveRequestUserId: item.approveRequestUserId,
                        cancelReason: item.cancelReason,
                        status: item.status
                    }
                ]
            };
            dispatch(start_change_approve_user());
            apiBase(API_CHANGE_APPROVE_USER_SENDBANK, METHOD.POST, body)
                .then((response) => {
                    if (!response.error) {
                        dispatch(stop_change_approve_user(response));
                        resolve('Cập nhật thành công');
                    } else {
                        dispatch(
                            stop_change_approve_user(
                                [],
                                !EMPTY,
                                'Không lấy được dữ liệu'
                            )
                        );
                        resolve('Không lấy được dữ liệu');
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_change_approve_user(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error);
                });
        });
    };
};

// API Lấy trạng thái xử lý
export const getApprovalStatus = () => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            dispatch(start_get_approval_status());
            apiBase(API_GET_APPROVAL_STATUS, METHOD.POST, {})
                .then((res) => {
                    const object = res.object;
                    if (helper.IsNonEmptyArray(object.lstApproveReqStatus)) {
                        dispatch(
                            stop_get_approval_status(object.lstApproveReqStatus)
                        );
                        resolve(object);
                    } else {
                        dispatch(
                            stop_get_approval_status(
                                [],
                                !EMPTY,
                                'Không lấy được dữ liệu',
                                !ERROR
                            )
                        );
                        resolve('Không lấy được dữ liệu');
                    }
                })
                .catch((error) => {
                    dispatch(
                        stop_get_approval_status(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error);
                });
        });
    };
};

// API lấy lý do yêu cầu điều chỉnh
export const getCancelReason = () => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0
            };
            dispatch(start_get_cancel_reason());
            apiBase(API_GET_CANCEL_REASON, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object.lstCancelReason)) {
                        dispatch(
                            stop_get_cancel_reason(object.lstCancelReason)
                        );
                        resolve(object);
                        hideBlockUI();
                    } else {
                        dispatch(
                            stop_get_cancel_reason(
                                [],
                                !EMPTY,
                                'Không tìm thấy dữ liệu',
                                !ERROR
                            )
                        );
                        resolve('Không lấy được dữ liệu');
                    }
                })
                .catch((error) => {
                    console.log(
                        '🤜 ******* returnnewPromise ******* error:',
                        error
                    );
                    dispatch(
                        stop_get_cancel_reason(
                            [],
                            !EMPTY,
                            error.msgError,
                            ERROR
                        )
                    );
                    reject(error);
                });
        });
    };
};

// API Lấy thông tin mã phiếu nộp tiền/chi
export const getPaymentDetail = (approveRequestId) => {
    return (dispatch, getState) => {
        let body = {
            loginStoreId: getState().userReducer.storeID,
            moduleID: getState().userReducer.moduleID,
            languageID: getState().userReducer.languageID,
            saleScenarioTypeID: 0,
            approveRequestId: approveRequestId
        };
        dispatch(start_get_payment_details());
        apiBase(API_GET_PAYMENT_DETAILS, METHOD.POST, body)
            .then((response) => {
                const { object } = response;
                console.log('🤜 ******* .then ******* object:', object);
                if (helper.IsNonEmptyArray(object)) {
                    dispatch(stop_get_payment_details(object));
                } else {
                    dispatch(
                        stop_get_payment_details(
                            [],
                            !EMPTY,
                            'Không tìm thấy dữ liệu'
                        )
                    );
                }
            })
            .catch((error) => {
                dispatch(
                    stop_get_payment_details([], !EMPTY, error.msgError, ERROR)
                );
            });
    };
};

// API lấy danh sách file đính kèm
export const getFileAttach = (approveRequestId) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                approveRequestId: approveRequestId
            };
            apiBase(API_GET_FILE_ATTACH, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        resolve(object);
                    } else {
                        reject({ msgError: 'Không có dữ liệu file đính kèm' });
                    }
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

// API duyệt yêu cầu
export const getApproveRequests = (approveRequestId) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                userName: getState().userReducer.userName,
                approveRequestId: approveRequestId
            };
            apiBase(API_APPROVE_REQ_MPOS, METHOD.POST, body)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

// API Huỷ yêu cầu
export const getCancelApprove = (dataChangeApprove) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                saleScenarioTypeID: 0,
                lstChangeApprove: [dataChangeApprove]
            };
            apiBase(API_CANCEL_APPROVE_REQ, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

// Lấy danh sách ngân hàng
export const getBankList = () => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                storeId: getState().userReducer.storeID
            };
            dispatch(start_get_bank_list());
            apiBase(API_GET_LIST_BANK, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object.lstBank)) {
                        dispatch(stop_get_bank_list(object.lstBank));
                        resolve(object);
                    } else {
                        dispatch(stop_get_bank_list());
                        reject({ msgError: 'Không có dữ liệu ngân hàng' });
                    }
                })
                .catch((error) => {
                    dispatch(stop_get_bank_list(object));
                    reject(error);
                });
        });
    };
};

// API Cập nhật yêu cầu điều chỉnh (Khác WRONGBANK)
export const getOtherWrongbank = (cancelReason, approveRequestId) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                cancelReason: cancelReason,
                approveRequestId: approveRequestId,
                userName: getState().userReducer.userName
            };
            apiBase(API_APPROVE_REQ_MPOS, METHOD.POST, body)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

// API Cập nhật yêu cầu điều chỉnh (WRONGBANK)
export const getWrongbank = (
    cancelReason,
    approveRequestId,
    bankId,
    receiveBankAccount
) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: 0,
                cancelReason: cancelReason,
                approveRequestId: approveRequestId,
                userName: getState().userReducer.userName,
                bankId: bankId,
                receiveBankAccount: receiveBankAccount
            };
            apiBase(API_APPROVE_REQ_MPOS, METHOD.POST, body)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
};

export const resetListSendBank = () => {
    return (dispatch, getState) => {
        dispatch(reset_search_list_send_bank());
    };
};
export const resetChangeStore = () => {
    return (dispatch, getState) => {
        dispatch(reset_change_store());
    };
};

export const resetSearchUser = () => {
    return (dispatch, getState) => {
        dispatch(reset_search_user());
    };
};

const start_list_search_send_bank = () => {
    return {
        type: START_SEARCH_LIST_SENDBANK
    };
};

const stop_list_search_send_bank = (
    dataSearchList,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_SEARCH_LIST_SENDBANK,
        dataSearchList,
        isEmpty,
        description,
        isError
    };
};

const start_get_store_list = () => {
    return {
        type: START_CHANGE_STORE
    };
};

const stop_get_store_list = (
    dataSearch,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_CHANGE_STORE,
        dataSearch,
        isEmpty,
        description,
        isError
    };
};

const reset_search_list_send_bank = () => {
    return {
        type: RESET_SEARCH_LIST_SENDBANK
    };
};

const reset_change_store = () => {
    return {
        type: RESET_CHANGE_STORE
    };
};

const start_search_user = () => {
    return {
        type: START_SEARCH_USER
    };
};

const stop_search_user = (
    dataSearchUser,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_SEARCH_USER,
        dataSearchUser,
        isEmpty,
        description,
        isError
    };
};

const reset_search_user = () => {
    return {
        type: RESET_SEARCH_USER
    };
};

const start_change_approve_user = () => {
    return {
        type: START_CHANGE_APPROVE_USER
    };
};

const stop_change_approve_user = (
    dataApproveUser,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_CHANGE_APPROVE_USER,
        dataApproveUser,
        isEmpty,
        description,
        isError
    };
};

const start_get_cancel_reason = () => {
    return {
        type: START_GET_CANCEL_REASON
    };
};

const stop_get_cancel_reason = (
    dataCancelReason,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_CANCEL_REASON,
        dataCancelReason,
        isEmpty,
        description,
        isError
    };
};

const start_get_approval_status = () => {
    return {
        type: START_GET_APPROVAL_STARTS
    };
};

const stop_get_approval_status = (
    dataApprovalStatus,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_APPROVAL_STARTS,
        dataApprovalStatus,
        isEmpty,
        description,
        isError
    };
};

const start_get_payment_details = () => {
    return {
        type: START_GET_PAYMENT_DETAILS
    };
};

const stop_get_payment_details = (
    dataPaymentDetails,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_PAYMENT_DETAILS,
        dataPaymentDetails,
        isEmpty,
        description,
        isError
    };
};

const start_get_bank_list = () => {
    return {
        type: START_GET_BANKS
    };
};

const stop_get_bank_list = (
    dataBankList,
    isEmpty = false,
    description = '',
    isError = false
) => {
    return {
        type: STOP_GET_BANKS,
        dataBankList,
        isEmpty,
        description,
        isError
    };
};
