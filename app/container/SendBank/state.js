export const sendBankState = {
    dataSearchList: [],
    dataSearch: [],
    dataSearchUser: [],
    dataApproveUser: [],
    dataCancelReason: [],
    dataApprovalStatus: [],
    dataBankList: [],
    stateSearchList: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateSearchStore: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateSearchUser: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateApproveUser: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateCancelReason: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateApprovalStatus: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateBanks: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    }
};
