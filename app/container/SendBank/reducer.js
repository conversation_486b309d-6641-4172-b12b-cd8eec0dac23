import { sendBankState } from './state';
import { sendBankAction } from './action';

const sendBankReducer = (state = sendBankState, action) => {
    switch (action.type) {
        case sendBankAction.START_SEARCH_LIST_SENDBANK:
            return {
                ...state,
                dataSearchList: [],
                stateSearchList: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_SEARCH_LIST_SENDBANK:
            return {
                ...state,
                dataSearchList: action.dataSearchList,
                stateSearchList: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.RESET_SEARCH_LIST_SENDBANK:
            return {
                ...state,
                dataSearchList: [],
                stateSearchList: {
                    isFetching: false,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.START_CHANGE_STORE:
            return {
                ...state,
                dataSearch: [],
                stateSearchStore: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_CHANGE_STORE:
            return {
                ...state,
                dataSearch: action.dataSearch,
                stateSearchStore: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.RESET_CHANGE_STORE:
            return {
                ...state,
                dataSearch: [],
                stateSearchStore: {
                    isFetching: false,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.START_SEARCH_USER:
            return {
                ...state,
                dataSearchUser: [],
                stateSearchUser: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_SEARCH_USER:
            return {
                ...state,
                dataSearchUser: action.dataSearchUser,
                stateSearchUser: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.RESET_SEARCH_USER:
            return {
                ...state,
                dataSearchUser: [],
                stateSearchStore: {
                    isFetching: false,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.START_CHANGE_APPROVE_USER:
            return {
                ...state,
                dataApproveUser: [],
                stateApproveUser: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_CHANGE_APPROVE_USER:
            return {
                ...state,
                dataApproveUser: action.dataApproveUser,
                stateApproveUser: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_GET_CANCEL_REASON:
            return {
                ...state,
                dataCancelReason: [],
                stateCancelReason: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_GET_CANCEL_REASON:
            return {
                ...state,
                dataCancelReason: action.dataCancelReason,
                stateCancelReason: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_GET_APPROVAL_STARTS:
            return {
                ...state,
                dataApprovalStatus: [],
                stateCancelReason: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_GET_APPROVAL_STARTS:
            return {
                ...state,
                dataApprovalStatus: action.dataApprovalStatus,
                stateCancelReason: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_GET_PAYMENT_DETAILS:
            return {
                ...state,
                dataPaymentDetails: [],
                statePaymentDetails: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_GET_PAYMENT_DETAILS:
            return {
                ...state,
                dataPaymentDetails: action.dataPaymentDetails,
                statePaymentDetails: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_GET_BANKS: {
            return {
                ...state,
                dataBankList: [],
                stateBanks: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        }
        case sendBankAction.STOP_GET_BANKS: {
            return {
                ...state,
                dataBankList: action.dataBankList,
                stateBanks: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        }
        default:
            return state;
    }
};

export { sendBankReducer };
