import React from 'react';
import { COLORS } from '@styles';
import { Image, TouchableOpacity } from 'react-native';
import { MyText, Text } from '@components';
import { constants } from '@constants';
import { helper } from '@common';
import { CatalogCollectionAssets } from '@components';

const PinnedItem = ({ item, onPress }) => {

    const { source, screen, AirtimeServiceGroupName, AirtimeServiceGroupID } = item;

    return (
        <TouchableOpacity
            onPress={onPress}
            style={{
                alignItems: 'center',
                width: (constants.width - 40) / 4,
                margin: 5
            }}
            activeOpacity={0.6}
        >
            <Image
                style={{
                    width: 35,
                    height: 35,
                }}
                source={CatalogCollectionAssets[`${AirtimeServiceGroupID}`]}
            />
            {
                helper.IsNonEmptyString(screen) ?
                    <MyText style={{
                        marginTop: 10,
                        textAlign: 'center',
                        width: 60,
                        color: helper.IsNonEmptyString(source) ? COLORS.bgE0E0E0 : COLORS.bg000000

                    }}
                        text={AirtimeServiceGroupName}
                        addSize={-2}
                    />

                    :
                    <Text style={{
                        marginTop: 10,
                        textAlign: 'center',
                        width: 60,
                        color: COLORS.bgE0E0E0
                    }}>
                        {AirtimeServiceGroupName} (Bảo trì)
                    </Text>
            }
        </TouchableOpacity>
    );
};

export default PinnedItem;
