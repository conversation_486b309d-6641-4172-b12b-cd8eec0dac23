import { View, TouchableOpacity } from 'react-native';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SafeAreaView from 'react-native-safe-area-view';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { helper, storageHelper } from '@common';
import { translate } from '@translate';
import { constants, ENUM, STORAGE_CONST } from "@constants";
import { COLORS } from '@styles';
import { MyText, Icon } from '@components';
import InputSearch from './components/InputSearch';
import * as specialSaleProgramActionCreator from '../SpecialSaleProgram/action';
import * as staffPromotionActionCreator from '../StaffPromotion/action';
import * as actionShoppingCartCreator from "../ShoppingCart/action";
import * as actionPouchCreator from "../PouchRedux/action";
import * as actionDetailCreator from "../Detail/action";
import * as actionCollectionCreator from "../CollectionTransfer/action";
import PinnedItem from './components/PinnedItem';

const {
  AN_KHANG_HOME_SCREEN_DATA,
  DEFAULT_HOME_SCREEN_DATA,
  CAM_HOME_SCREEN_DATA,
  LIST_PINNED_CATALOG_MENU_ITEM
} = STORAGE_CONST;

class HomeScreen extends Component {

  constructor(props) {
    super(props);
    const {
      userInfo: { brandID, storeID, userName, companyID, isShowWeb }, USERADJUSTFEE } = props;
    const isBrandAnKhang = brandID == ENUM.BRAND_ID.AN_KHANG;
    const isAllowUserAdjustFee = USERADJUSTFEE == -1 || `,${USERADJUSTFEE},`.includes(`,${userName.toString()},`);
    const keyPermissions = new Set(props.userInfo.permissions);
    this.defaultHomeScreenItem = [
      {
        title: translate("menu.sale"),
        iconName: "currency-usd",
        iconSet: "MaterialCommunityIcons",
        screen: "Sale",
        color: COLORS.icF0C014,
        isNewFeature: false,
        storeIDList: ENUM.ALLOW_STORE_ID.AVA.toString()
      },
      {
        title: translate("menu.management_order"),
        iconName: "file-document-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "OrderManagement",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.installment"),
        iconName: "ios-file-tray-full-outline",
        iconSet: "Ionicons",
        screen: "Installment",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.management_sim"),
        iconName: "sim",
        iconSet: "MaterialCommunityIcons",
        screen: "ActiveSimManager",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.additional_promotion"),
        iconName: "wallet-giftcard",
        iconSet: "MaterialIcons",
        screen: "AdditionalPromotion",
        color: COLORS.ic3BB8C3,
        isNewFeature: false
      },
      {
        title: translate("menu.screen_sticker"),
        iconName: "phone-iphone",
        iconSet: "MaterialIcons",
        screen: "StickerProtector",
        color: COLORS.ic3BB8C3,
        isNewFeature: false
      },
      {
        title: translate("menu.change_watch_battery"),
        iconName: 'watch-outline',
        iconSet: 'Ionicons',
        screen: 'Oclock',
        color: COLORS.ic3BB8C3,
        isNewFeature: false
      },
      {
        title: translate(
          'collection.managerment_multicat_industry_transactions_nfp'
        ),
        iconName: 'comment-bank',
        iconSet: 'MaterialIcons',
        screen: 'CollectionManager',
        color: COLORS.icF0C014,
        isNewFeature: true,
        companyIDList: "1"
      },
      {
        title: "Thông báo",
        iconName: 'notifications-outline',
        iconSet: 'Ionicons',
        screen: 'Notification',
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: "Chuyển đổi hàng không IMEI",
        iconName: "file-sync-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "SwitchIMEI",
        color: COLORS.ic3BB8C3,
        isNewFeature: false
      },
      {
        title: translate("menu.cod_pay"),
        iconName: "file-restore-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "CodPay",
        color: COLORS.ic3BB8C3,
        isNewFeature: false
      },
      {
        title: translate("menu.complaint_refund"),
        iconName: "account-cash-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "CMMoneyComplaint",
        color: COLORS.ic3BB8C3,
        isNewFeature: false
      },
      {
        title: translate("menu.search_imei_history"),
        iconName: "file-clock-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "SearchImei",
        color: COLORS.ic3BB8C3,
        isNewFeature: false
      },
      {
        title: translate("menu.print_config_product"),
        iconName: 'print-outline',
        iconSet: 'Ionicons',
        screen: 'PrintPriceList',
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.scan_qrcode_id"),
        iconName: 'qrcode',
        iconSet: 'MaterialCommunityIcons',
        screen: 'ScanQRCodeID',
        color: COLORS.icF0C014,
        isNewFeature: false,
        storeIDList: "298"
      },
      {
        title: translate("menu.view_promotion_info"),
        iconName: 'file-eye-outline',
        iconSet: 'MaterialCommunityIcons',
        screen: 'ViewPromotion',
        color: COLORS.icF0C014,
        isNewFeature: false,
        userNameList: "18011,137484,44456,44186,154227,33862,32563,44868,118917,95026,142168,162836,156475,165128,20889,19794,6571,2415,4229,53470,57730,117101,12774,16333,26048,3904,40228,41336,53103,14498,19128,75972,2058,16356,76921,9584,37046,18291,118917,6077,39930,94174,31718,121418,53248,134924,179591,167882,189446,200385,175729,19719,27465,55689,3310,28883,21570,29567,3169,54145,29773,33305,2317,21939,28023,11498,81759,1044,2061,27325,55503,65482,62882,86705,107608,103174,142168,35425,14203,16901,87328,85790,162879,162850,165103,165082,120595,165478,165077,165078,165111,165087,165059,2172,22988,25251"
      },
      {
        title: translate("menu.product_returns"),
        iconName: "file-swap-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "ProductReturns",
        color: COLORS.icF19F9D,
        isNewFeature: true
      },
      {
        title: translate("menu.search_inout_voucher"),
        iconName: 'file-percent-outline',
        iconSet: 'MaterialCommunityIcons',
        screen: 'InOutVoucher',
        color: COLORS.icF19F9D,
        isNewFeature: true
      },
      {
        title: translate("oldProduct.upload_oldproduct"),
        iconName: 'cloud-upload-outline',
        iconSet: 'MaterialCommunityIcons',
        screen: 'UploadPictureOldProduct',
        color: COLORS.icF19F9D,
        isNewFeature: true,
      },
      {
        title: 'Ưu đãi nhân viên',
        iconName: 'percent-outline',
        iconSet: 'MaterialCommunityIcons',
        screen: 'StaffPromotion',
        color: COLORS.icF19F9D,
        isNewFeature: true,
      },
      {
        title: "Quản lý phiếu bán hàng",
        iconName: "file-document-edit-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "ReceiptManagement",
        color: COLORS.icF19F9D,
        isNewFeature: true,
      },
      {
        title: 'Thẩm định máy cũ',
        iconName: 'file-replace-outline',
        iconSet: 'MaterialCommunityIcons',
        screen: 'ProductEvaluation',
        color: COLORS.icF19F9D,
        isNewFeature: true,
      },
      {
        title: "Bán hàng Pre-Order",
        iconName: "credit-card-clock-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "Sale",
        color: COLORS.icF19F9D,
        isNewFeature: true,
        isPreOrder: true
      },
      {
        title: translate('menu.sendbank'),
        iconName: 'cash-multiple',
        iconSet: 'MaterialCommunityIcons',
        screen: 'SendBank',
        color: COLORS.icF0C014,
        isNewFeature: true,
      },
      {
        title: translate("menu.sim_price_report"),
        iconName: 'print-outline',
        iconSet: 'Ionicons',
        screen: 'SimPriceReport',
        color: COLORS.icF0C014,
      },
      {
        title: 'Quản lý giao dịch chuyển khoản',
        iconName: "payment",
        iconSet: "MaterialIcons",
        screen: "TransactionScreens",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: 'Siêu thị làm giá',
        iconName: "payments",
        iconSet: "MaterialIcons",
        screen: "PriceMaking",
        color: COLORS.icF0C014,
        isNewFeature: true
      },
      {
        title: 'Huỷ hợp đồng trả góp',
        iconName: "cancel-presentation",
        iconSet: "MaterialIcons",
        screen: "CancelInstallmentContract",
        color: COLORS.icF0C014,
        isNewFeature: true,
      },
      {
        title: 'Chứng nhận dịch vụ',
        iconName: 'file-replace-outline',
        iconSet: 'MaterialCommunityIcons',
        screen: 'Certificate',
        color: COLORS.icF19F9D,
        isNewFeature: true,
      },
    ]
    if (`${storeID}` != '3755' && isShowWeb) {
      this.defaultHomeScreenItem = [
        ...this.defaultHomeScreenItem,
        {
          title: "In đơn hàng Pre ngày DDAY",
          iconName: "print-outline",
          iconSet: "Ionicons",
          screen: "PrintSaleOrderDDay",
          color: COLORS.txt5BB180,
          isNewFeature: true
        },
        {
          title: translate("collection.multicat_industry_service_nfp"),
          iconName: "bank-transfer-out",
          iconSet: "MaterialCommunityIcons",
          screen: "MenuCollection",
          color: COLORS.icF0C014,
          isNewFeature: true,
          companyIDList: "1"
        },
        {
          title: translate("collection.managerment_multicat_industry_transactions_nfp"),
          iconName: "comment-bank",
          iconSet: "MaterialIcons",
          screen: "CollectionManager",
          color: COLORS.icF0C014,
          isNewFeature: true,
          companyIDList: "1"
        },
        {
          title: translate("menu.sale"),
          title: "Bán thẻ cào tại siêu thị",
          iconName: "card-outline",
          iconSet: "Ionicons",
          screen: "SellCard",
          color: COLORS.icF0C014,
          isNewFeature: false
        },
        {
          title: translate("menu.output_receipt_management"),
          iconName: "file-search-outline",
          iconSet: "MaterialCommunityIcons",
          screen: "SearchOutputReceipt",
          color: COLORS.icF0C014,
          isNewFeature: false
        },
      ]
    }
    if (isAllowUserAdjustFee) {
      this.defaultHomeScreenItem = [
        ...this.defaultHomeScreenItem,
        {
          title: "Hỗ trợ DH đổi trả",
          iconName: 'adjust',
          iconSet: 'MaterialCommunityIcons',
          screen: 'RequestReturn',
          color: COLORS.icF0C014,
          isNewFeature: false
        },
      ];
    }
    if (helper.configUserAndStorePre(storeID, userName)) {
      this.defaultHomeScreenItem = [
        ...this.defaultHomeScreenItem,
        {
          title: 'Ghi nhận thông tin PreOrder',
          iconName: "note-text-outline",
          iconSet: "MaterialCommunityIcons",
          screen: "PreInformation",
          color: COLORS.icF0C014,
          isNewFeature: true,
        },
      ]

    }
    if (helper.configUserAndStorePre(storeID, userName)) {
      this.defaultHomeScreenItem = [
        ...this.defaultHomeScreenItem,
        {
          title: 'Ghi nhận thông tin PreOrder',
          iconName: "note-text-outline",
          iconSet: "MaterialCommunityIcons",
          screen: "PreInformation",
          color: COLORS.icF0C014,
          isNewFeature: true,
          brandIDList: '16,1,2'
        },
      ]

    }
    if (helper.configUserAdjustFee(userName)) {
      this.defaultHomeScreenItem = [
        ...this.defaultHomeScreenItem,
        {
          title: "Hỗ trợ DH đổi trả",
          iconName: 'adjust',
          iconSet: 'MaterialCommunityIcons',
          screen: 'RequestReturn',
          color: COLORS.icF0C014,
          isNewFeature: false
        },
      ]
    }
    this.anKhangHomeScreenItem = [
      {
        title: "Bán hàng không tư vấn",
        iconName: "magnify-scan",
        iconSet: "MaterialCommunityIcons",
        screen: 'Pharmacy',
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.management_order"),
        iconName: "file-document-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "OrderManagement",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.additional_promotion"),
        iconName: "wallet-giftcard",
        iconSet: "MaterialIcons",
        screen: "AdditionalPromotion",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.sale"),
        title: "Bán thẻ cào tại siêu thị",
        iconName: "card-outline",
        iconSet: "Ionicons",
        screen: "SellCard",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.output_receipt_management"),
        iconName: "file-search-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "SearchOutputReceipt",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: "Thông báo",
        iconName: 'notifications-outline',
        iconSet: 'Ionicons',
        screen: 'Notification',
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.print_config_product"),
        iconName: 'print-outline',
        iconSet: 'Ionicons',
        screen: 'PrintPriceList',
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.restock"),
        iconName: "database-import",
        iconSet: "MaterialCommunityIcons",
        screen: "Restock",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.use_guide"),
        iconName: "lightbulb-on-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "Tips",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.view_promotion_info"),
        iconName: 'file-eye-outline',
        iconSet: 'MaterialCommunityIcons',
        screen: 'ViewPromotion',
        color: COLORS.icF0C014,
        isNewFeature: false,
        userNameList: ""
      },
      {
        title: translate(
          'collection.managerment_multicat_industry_transactions_nfp'
        ),
        iconName: 'comment-bank',
        iconSet: 'MaterialIcons',
        screen: 'CollectionManager',
        color: COLORS.icF0C014,
        isNewFeature: true,
      },
      {
        title: 'Quản lý giao dịch chuyển khoản',
        iconName: "payment",
        iconSet: "MaterialIcons",
        screen: "TransactionScreens",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: 'Cẩm nang chăm sóc khách hàng',
        iconName: "book-outline",
        iconSet: "Ionicons",
        screen: "HealthGuide",
        color: COLORS.bgFF0000,
        isNewFeature: true,
      },

    ];
    if (isShowWeb) {
      this.anKhangHomeScreenItem = [
        ... this.anKhangHomeScreenItem,
        {
          title: translate("collection.multicat_industry_service_nfp"),
          iconName: "bank-transfer-out",
          iconSet: "MaterialCommunityIcons",
          screen: "MenuCollection",
          color: COLORS.icF0C014,
          isNewFeature: true,
        },
      ];
    }
    if (keyPermissions.has('POS_VIEWDISPLAY') || true) {
      this.defaultHomeScreenItem = [
        ...this.defaultHomeScreenItem,
        {
          title: translate("menu.product_display_management"),
          iconName: 'laptop',
          iconSet: 'MaterialCommunityIcons',
          screen: 'ProductDisplayManager',
          color: COLORS.icF0C014,
          isNewFeature: false
        },
      ];
      this.anKhangHomeScreenItem = [
        ... this.anKhangHomeScreenItem,
        {
          title: translate("menu.product_display_management"),
          iconName: 'laptop',
          iconSet: 'MaterialCommunityIcons',
          screen: 'ProductDisplayManager',
          color: COLORS.icF0C014,
          isNewFeature: false,
        }
      ];
    }
    if (helper.checkConfigStoreBHX(companyID)) {
      this.defaultHomeScreenItem = [
        {
          title: translate("collection.multicat_industry_service_nfp"),
          iconName: "bank-transfer-out",
          iconSet: "MaterialCommunityIcons",
          screen: "MenuCollection",
          color: COLORS.icF0C014,
          isNewFeature: true,
        },
        {
          title: translate("collection.managerment_multicat_industry_transactions_nfp"),
          iconName: "comment-bank",
          iconSet: "MaterialIcons",
          screen: "CollectionManager",
          color: COLORS.icF0C014,
          isNewFeature: true,
        },
        {
          title: translate("menu.sale"),
          title: "Bán thẻ cào tại siêu thị",
          iconName: "card-outline",
          iconSet: "Ionicons",
          screen: "SellCard",
          color: COLORS.icF0C014,
          isNewFeature: false
        },
        {
          title: translate("menu.output_receipt_management"),
          iconName: "file-search-outline",
          iconSet: "MaterialCommunityIcons",
          screen: "SearchOutputReceipt",
          color: COLORS.icF0C014,
          isNewFeature: false
        },
        {
          title: translate('menu.sendbank'),
          iconName: 'cash-multiple',
          iconSet: 'MaterialCommunityIcons',
          screen: 'SendBank',
          color: COLORS.icF0C014,
          isNewFeature: true,
        },
      ]
    }

    this.camHomeScreenItem = [
      {
        title: translate("menu.sale"),
        iconName: "currency-usd",
        iconSet: "MaterialCommunityIcons",
        screen: "Sale",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("menu.management_order"),
        iconName: "file-document-outline",
        iconSet: "MaterialCommunityIcons",
        screen: "OrderManagement",
        color: COLORS.icF0C014,
        isNewFeature: false
      },
      {
        title: translate("oldProduct.upload_oldproduct"),
        iconName: 'cloud-upload-outline',
        iconSet: 'MaterialCommunityIcons',
        screen: 'UploadPictureOldProduct',
        color: COLORS.icF19F9D,
        isNewFeature: true,
        storeIDList: "3010"
      }
    ];

    this.state = {
      keyword: "",
      isFocus: false,
      listMenu: global.isVN ? (isBrandAnKhang ? this.anKhangHomeScreenItem : this.defaultHomeScreenItem) : this.camHomeScreenItem,
      suggestedFeatureState: 1,
      isViewAllFeature: false,
      listPinnedCatalogItem: []
    };
  }

  async componentDidMount() {
    const { userInfo: { brandID } } = this.props;
    const { listMenu } = this.state;
    const isBrandAnKhang = brandID == ENUM.BRAND_ID.AN_KHANG;
    const itemStorageKey = global.isVN ? (isBrandAnKhang ? AN_KHANG_HOME_SCREEN_DATA : DEFAULT_HOME_SCREEN_DATA) : CAM_HOME_SCREEN_DATA;
    const result = await storageHelper.getItem(itemStorageKey);
    if (helper.IsNonEmptyString(result)) {
      const parseResult = JSON.parse(result);
      const isMatchLength = listMenu.length == parseResult.length;
      const isMatchEveryItem = listMenu.filter(item => parseResult.findIndex(ele => ele.screen == item.screen) != -1).length == listMenu.length;
      if (isMatchLength && isMatchEveryItem) {
        this.setState({ listMenu: parseResult });
      }
    }
    const pinnedCatalogItems = await storageHelper.getItem(LIST_PINNED_CATALOG_MENU_ITEM);
    if (helper.IsNonEmptyString(pinnedCatalogItems)) {
      this.setState({ listPinnedCatalogItem: JSON.parse(pinnedCatalogItems) });
    }
  }

  onFocus = () => {
    this.setState({ isFocus: true });
  };

  onBlur = () => {
    this.setState({ isFocus: false });
  };

  clearKeyword = () => {
    this.setState({ keyword: "" });
  };

  onPressMenuItem = (item) => {
    const { screen, isPreOrder } = item;
    const { listMenu } = this.state;
    const updatedListMenu = [item, ...listMenu.filter(element => element.screen != screen)];
    this.setState({
      keyword: "",
      listMenu: updatedListMenu
    });
    const { userInfo: { brandID } } = this.props;
    const isBrandAnKhang = brandID == ENUM.BRAND_ID.AN_KHANG;
    const itemStorageKey = global.isVN ? (isBrandAnKhang ? AN_KHANG_HOME_SCREEN_DATA : DEFAULT_HOME_SCREEN_DATA) : CAM_HOME_SCREEN_DATA;
    storageHelper.setItem(itemStorageKey, JSON.stringify(updatedListMenu));
    this.onNavigate(screen, isPreOrder);
  };

  onNavigate = (screenNam, isPreOrder) => {
    const {
      scenarioSaleState,
      specialSaleProgramAction,
      staffPromotionAction,
      dataShoppingCart,
      actionShoppingCart,
      actionPouch,
      actionDetail
    } = this.props;
    if (isPreOrder) {
      if (!helper.IsEmptyObject(dataShoppingCart)) {
        actionShoppingCart.deleteShoppingCart();
        actionPouch.setDataCartApply();
      }
      specialSaleProgramAction.setScenarioSaleType(
        ENUM.SALE_SCENARIO_TYPE.PRE_ORDER
      );
    }
    else if (scenarioSaleState.saleScenarioTypeID !== ENUM.SALE_SCENARIO_TYPE.SALE) {
      specialSaleProgramAction.setScenarioSaleType(
        ENUM.SALE_SCENARIO_TYPE.SALE
      );
    }
    staffPromotionAction.reset_staff_info();
    actionDetail.reset_map_content_promotion_input();
    this.props.navigation.reset({
      index: 0,
      routes: [{ name: screenNam }]
    });
  };

  renderItemMenu = (item, index) => {
    const { title, iconName, iconSet, color } = item;
    return (
      <TouchableOpacity style={{
        width: (constants.width - 40) / 4,
        margin: 5,
        alignItems: "center",
        height: constants.getSize(65)
      }}
        onPress={() => { this.onPressMenuItem(item); }}
        activeOpacity={0.8}
        key={index.toString()}
      >
        <Icon
          iconSet={iconSet}
          name={iconName}
          color={color}
          size={28}
        />
        <MyText
          style={{
            color: COLORS.txt434343,
            marginTop: 4,
            textAlign: 'center',
          }}
          text={title}
          addSize={-2}
        />
      </TouchableOpacity>
    );
  };

  renderPinnedItem = (item, index) => {
    const { actionCollection, navigation } = this.props;
    return (
      <PinnedItem
        key={`${index}`}
        item={item}
        onPress={() => {
          actionCollection.updateItemCatalog(item);
          if (item.screen) {
            navigation.navigate(item.screen, {
              screen: item.screen, params: {
                item
              }
            });
          }
        }}
      />
    );
  };

  isMatchedProvince = (provinceID, provinceIDList) => {
    if (helper.IsNonEmptyString(provinceIDList)) {
      return `,${provinceIDList},`.includes(`,${provinceID},`);
    } else {
      return true;
    }
  };

  isMatchedBrand = (brandID, brandIDList) => {
    if (helper.IsNonEmptyString(brandIDList)) {
      return `,${brandIDList},`.includes(`,${brandID},`);
    } else {
      return true;
    }
  };

  isMatchedCompany = (companyID, companyIDList) => {
    if (helper.IsNonEmptyString(companyIDList)) {
      return `,${companyIDList},`.includes(`,${companyID},`);
    } else {
      return true;
    }
  };

  isMatchedStore = (storeID, storeIDList, screen, brandID) => {
    const isAva = ENUM.BRAND_ID.AVA.some((brand) => brandID == brand);
    if (helper.IsNonEmptyString(storeIDList)) {
      if (screen == ENUM.SCREENS.SALE) {
        const hidenMenuSale = isAva && (storeIDList == -1 || `,${storeIDList},`.includes(`,${storeID},`));
        return !hidenMenuSale;
      }
      else {
        return `,${storeIDList},`.includes(`,${storeID},`);
      }
    } else {
      return true;
    }
  };

  isMatchedUser = (userName, userNameList) => {
    if (helper.IsNonEmptyString(userNameList)) {
      return `,${userNameList},`.includes(`,${userName},`);
    } else {
      return true;
    }
  };

  isShowItem = (menuItem) => {
    const { userInfo: { brandID, storeID, userName, provinceID, companyID } } = this.props;
    const { storeIDList, brandIDList, userNameList, provinceIDList, screen, companyIDList } = menuItem;
    const isHasProvince = this.isMatchedProvince(provinceID, provinceIDList);
    const isHasBrand = this.isMatchedBrand(brandID, brandIDList);
    const isHasStore = this.isMatchedStore(storeID, storeIDList, screen, brandID);
    const isHasUser = this.isMatchedUser(userName, userNameList);
    const isHasCompany = this.isMatchedCompany(companyID, companyIDList);
    return isHasProvince && isHasBrand && isHasStore && isHasUser && isHasCompany;
  };

  render() {
    const { keyword, isFocus, listMenu, isViewAllFeature, listPinnedCatalogItem } = this.state;

    const searchItems = helper.IsNonEmptyString(keyword) ? listMenu.filter(item => {
      const isResult = helper.removeAccent(item.title).toLowerCase().includes(helper.removeAccent(keyword).toLowerCase());
      return isResult && this.isShowItem(item);
    }).slice(0, 12) : [];

    const isShowButtonClose = isFocus && helper.IsNonEmptyString(keyword);

    const yourFeature = listMenu.filter(item => {
      const { isNewFeature } = item;
      return !isNewFeature && this.isShowItem(item);
    }).slice(0, 8);

    const newFeature = listMenu.filter(item => {
      const { isNewFeature } = item;
      return isNewFeature && this.isShowItem(item);
    }).slice(0, 8);

    const allFeature = listMenu.filter(item => this.isShowItem(item));

    return (
      <View style={{ flex: 1 }}>
        <SafeAreaView
          style={{
            flex: 1,
            backgroundColor: COLORS.bgF0F0F0,
          }}
        >
          <InputSearch
            value={keyword}
            onChangeText={(text) => {
              if (helper.isValidateCharVN(text)) {
                this.setState({ keyword: text });
              }
            }}
            clearText={this.clearKeyword}
            isShowButtonClose={isShowButtonClose}
            onFocus={this.onFocus}
            onBlur={this.onBlur}
          />
          <KeyboardAwareScrollView
            style={{
              flex: 1,
              backgroundColor: COLORS.bgFFFFFF,
            }}
            enableResetScrollToCoords={false}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={60}
          >
            {helper.IsEmptyString(keyword) ?
              <View style={{
                width: constants.width
              }}>{
                  isViewAllFeature ?
                    <View>
                      <View style={{
                        width: constants.width,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        paddingHorizontal: 20,
                      }}>
                        <MyText
                          text={translate('menu.all_features')}
                          style={{
                            fontWeight: 'bold',
                            color: COLORS.txt555555,
                            paddingTop: 10
                          }}
                          addSize={2}
                        />
                        <TouchableOpacity
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginTop: 10,
                            backgroundColor: COLORS.btnFFFFFF,
                            flexDirection: 'row',
                            borderColor: COLORS.txtC800A1,
                            borderRadius: 5,
                            borderWidth: 1
                          }}
                          activeOpacity={0.7}
                          onPress={() => {
                            this.setState({ isViewAllFeature: false });
                          }}
                        >
                          <MyText
                            text={translate('menu.button_your_features')}
                            style={{
                              color: COLORS.txtC800A1,
                              paddingLeft: 10,
                            }}
                          />
                          <Icon
                            iconSet={'MaterialIcons'}
                            name={'arrow-drop-down'}
                            color={COLORS.txtC800A1}
                            size={22}
                          />
                        </TouchableOpacity>
                      </View>
                      <View style={{
                        width: constants.width,
                        alignItems: "center",
                        flexDirection: "row",
                        flexWrap: "wrap"
                      }}>
                        {allFeature.map(this.renderItemMenu)}
                      </View>
                    </View> :
                    <View>
                      <View style={{
                        width: constants.width,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        paddingHorizontal: 20,
                      }}>
                        <MyText
                          text={translate('menu.your_features')}
                          style={{
                            fontWeight: 'bold',
                            color: COLORS.txt555555,
                            paddingTop: 10
                          }}
                          addSize={2}
                        />
                        {(allFeature.length > 8) && <TouchableOpacity
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginTop: 10,
                            backgroundColor: COLORS.btnFFFFFF,
                            flexDirection: 'row',
                            borderColor: COLORS.txtC800A1,
                            borderRadius: 5,
                            borderWidth: 1
                          }}
                          activeOpacity={0.7}
                          onPress={() => {
                            this.setState({ isViewAllFeature: true });
                          }}
                        >
                          <MyText
                            text={translate('menu.button_view_all')}
                            style={{
                              color: COLORS.txtC800A1,
                              paddingLeft: 10,
                            }}
                          />
                          <Icon
                            iconSet={'MaterialIcons'}
                            name={'arrow-drop-down'}
                            color={COLORS.txtC800A1}
                            size={22}
                          />
                        </TouchableOpacity>}
                      </View>
                      <View style={{
                        width: constants.width,
                        alignItems: "center",
                        flexDirection: "row",
                        flexWrap: "wrap"
                      }}>
                        {yourFeature.map(this.renderItemMenu)}
                      </View>
                      {helper.IsNonEmptyArray(listPinnedCatalogItem) && <View>
                        <MyText
                          text={'Tính năng đã pin:'}
                          style={{
                            fontWeight: 'bold',
                            color: COLORS.txt555555,
                            paddingHorizontal: 20,
                            paddingTop: 10
                          }}
                          addSize={2}
                        />
                        <View style={{
                          width: constants.width,
                          alignItems: "center",
                          flexDirection: "row",
                          flexWrap: "wrap"
                        }}>
                          {listPinnedCatalogItem.map(this.renderPinnedItem)}
                        </View>
                      </View>

                      }
                      {helper.IsNonEmptyArray(newFeature) && <MyText
                        text={translate('menu.new_features')}
                        style={{
                          fontWeight: 'bold',
                          color: COLORS.txt555555,
                          paddingHorizontal: 20,
                          paddingTop: 10
                        }}
                        addSize={2}
                      />}
                      <View style={{
                        width: constants.width,
                        alignItems: "center",
                        flexDirection: "row",
                        flexWrap: "wrap"
                      }}>
                        {newFeature.map(this.renderItemMenu)}
                      </View>
                    </View>
                }
              </View> :
              <View style={{
                width: constants.width
              }}>
                <MyText
                  text={translate('menu.search_result')}
                  style={{
                    fontWeight: 'bold',
                    color: COLORS.txt555555,
                    paddingHorizontal: 20,
                    paddingTop: 10
                  }}
                  addSize={2}
                />
                <View style={{
                  width: constants.width,
                  alignItems: "center",
                  flexDirection: "row",
                  flexWrap: "wrap"
                }}>
                  {searchItems.map(this.renderItemMenu)}
                </View>
              </View>}
          </KeyboardAwareScrollView>
        </SafeAreaView>
      </View >
    );
  }
}

const mapStateToProps = (state) => ({
  userInfo: state.userReducer,
  scenarioSaleState: state.specialSaleProgramReducer,
  dataShoppingCart: state.shoppingCartReducer.dataShoppingCart,
  USERADJUSTFEE: state.appSettingReducer.USERADJUSTFEE,
});
const mapDispatchToProps = (dispatch) => ({
  specialSaleProgramAction: bindActionCreators(specialSaleProgramActionCreator, dispatch),
  staffPromotionAction: bindActionCreators(staffPromotionActionCreator, dispatch),
  actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
  actionPouch: bindActionCreators(actionPouchCreator, dispatch),
  actionDetail: bindActionCreators(actionDetailCreator, dispatch),
  actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(HomeScreen);
