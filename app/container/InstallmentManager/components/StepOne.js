import React, { useEffect } from "react";
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Alert
} from 'react-native';
import {
    MyText,
    RadioButton,
    showBlockUI,
    hideBlockUI,
    CaptureCamera,
    FaceCamera,
    DocCamera,
    CameraDOT
} from "@components";
import { translate } from "@translate";
import { helper, dateHelper } from "@common";
import { constants, API_CONST, ENUM } from "@constants";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import ImageProcess from "./ImageProcess";
import ImageResizer from 'react-native-image-resizer';
import { launchImageLibrary } from 'react-native-image-picker';
import * as installmentHelper from "../common/installmentHelper";
import * as installmentAction from "./../action";
import * as installmentBCAction from "../../InstallmentManagerBC/action";

import { useDispatch, useSelector } from 'react-redux';
import { COLORS } from "@styles";
import { getImageCDN } from "../../ShoppingCart/action";

const { API_GET_IMAGE_CDN, API_GET_IMAGE_CDN_NEW } = API_CONST;
const { HYPER_VERGE } = constants;
const { FILE_PATH: { STEP_ONE } } = ENUM;

const StepOne = function ({ parent, onNext, onPrev, validateObject, updatestate, updateprevstate, updateparent }) {
    const [state, setState] = React.useState({
        isVisibleCamera: false,
        EPOSTransactionBO: parent.EPOSTransactionBO,
    })
    const refScroll1 = React.useRef(null);

    const dispatch = useDispatch();
    const userInfo = useSelector(state => state.userReducer);
    useEffect(() => {
        const { client_IDCardType } = state.EPOSTransactionBO;
        let newDataRadioIDCardType = radioIDCardType.map(el => (
            el.value === client_IDCardType ? { ...el, selected: true } : { ...el, selected: false }
        ))
        let findDataItem = newDataRadioIDCardType.find(x => x.value === client_IDCardType);
        if (findDataItem) {
            UpdateInitObject(findDataItem.value);
            setRadioIDCardType(newDataRadioIDCardType);
        }
        else {
            UpdateInitObject(1);
            setRadioIDCardType([
                { title: translate("installmentsteps.stepone_client_idcardtypecmnd"), selected: true, value: 1 },
                { title: translate("installmentsteps.stepone_client_idcardtypecccd"), selected: false, value: 2 },
                { title: "Thẻ căn cước", selected: false, value: 4 },

            ]);
        }
    }, []);
    useEffect(() => {
        setState({
            ...state,
            EPOSTransactionBO: parent.EPOSTransactionBO,
        })
        installmentHelper.initObjectFields = parent.FieldObjects;
        if (refScroll1) {
            refScroll1.current.scrollToPosition(0, 0, false);
        }
    }, [parent.currentPosition]);

    const [indexImage, setIndexImage] = React.useState(0);
    const [property, setProperty] = React.useState("");
    const [radioIDCardType, setRadioIDCardType] = React.useState([
        { title: translate("installmentsteps.stepone_client_idcardtypecmnd"), selected: true, value: 1 },
        { title: translate("installmentsteps.stepone_client_idcardtypecccd"), selected: false, value: 2 },
        { title: "Thẻ căn cước", selected: false, value: 4 },

    ]);
    const selectItemCardType = (data, index) => {
        data.forEach((item) => {
            item.selected = false;
        });
        data[index].selected = true;
        setState({
            ...state,
            EPOSTransactionBO: {
                ...state.EPOSTransactionBO,
                // cus_FilePathFrontOfIDCard: "",
                // cus_FilePathBackOfIDCard: "",
                // cus_FilePathPortrait: "",
                client_IDCardType: data[index].value
            }
        });
        UpdateInitObject(data[index].value);
        setRadioIDCardType(data);
    }

    const UpdateInitObject = (IDCardType) => {
        if (parent.FieldObjects) {
            var idx = parent.FieldObjects.findIndex(p => p.FieldName === 'client_IDCardExpiriedDate');
            if (idx >= 0) {
                if (IDCardType == 1) {
                    parent.FieldObjects[idx].IsRequired = false;
                } else {
                    parent.FieldObjects[idx].IsRequired = true;
                }
            }

            idx = parent.FieldObjects.findIndex(p => p.FieldName === 'IDCard');
            if (idx >= 0) {
                if (IDCardType == 1) {
                    if (parent.FieldObjects[idx].objFieldLength) {
                        parent.FieldObjects[idx].objFieldLength.EqualLength = 9
                    } else {
                        parent.FieldObjects[idx].objFieldLength = {};
                        parent.FieldObjects[idx].objFieldLength.EqualLength = 9;
                    }
                } else {
                    if (parent.FieldObjects[idx].objFieldLength) {
                        parent.FieldObjects[idx].objFieldLength.EqualLength = 12
                    } else {
                        parent.FieldObjects[idx].objFieldLength = {};
                        parent.FieldObjects[idx].objFieldLength.EqualLength = 12;
                    }
                }
            }
            updateparent(parent);
        }
    }

    const updateInfoCardToState = (objInfoCard, property, indexImage) => {
        //0: mt
        //1: ms
        //2: chan dung
        console.log("obj:", property);
        console.log(objInfoCard);
        console.log(objInfoCard);
        if (indexImage == 0) {
            var customerName = '';
            var middleName = '';
            var firstName = null;
            var lastName = null;
            if (objInfoCard.customerName != undefined && objInfoCard.customerName.length > 0) {
                customerName = objInfoCard.customerName.split(' ');
                if (customerName.length > 0) {
                    firstName = customerName[customerName.length - 1];
                    lastName = customerName.length > 1 ? customerName[0] : '';
                    if (customerName.length > 2) {
                        for (var i = 1; i < customerName.length - 1; i++) {
                            middleName += customerName[i] + ' ';
                        }
                        //Nếu têm đệm hơn 15 ký tự thì sẽ không gán vào cả họ, tên, tên đệm 
                        if (middleName.toString().trim().length < 15) {
                            middleName = middleName.toString().trim();
                        }
                        else {
                            firstName = '';
                            middleName = '';
                            lastName = '';
                        }
                    }
                }
            }
            var formatDate = helper.IsEmptyObject(objInfoCard.customerBirthday)
                ? null
                : objInfoCard.customerBirthday;
            // : (new Date(objInfoCard.customerBirthday));
            setState({
                ...state,
                isVisibleCamera: false,
                EPOSTransactionBO: {
                    ...state.EPOSTransactionBO,
                    IDCard: objInfoCard.customerIDCard,
                    ProvinceIDPermanent: parseInt(objInfoCard.provinceID ?? -1),
                    DistrictIDPermanent: parseInt(objInfoCard.districtID ?? -1),
                    WardIDPermanent: parseInt(objInfoCard.wardID ?? -1),
                    StreetPermanent: objInfoCard.streetAddress ?? null,
                    Birthday: formatDate,
                    LastName: lastName,
                    FirstName: firstName,
                    MiddleName: middleName,
                    [property]: objInfoCard.image_url,
                }
            });
            getDataIssueplace(state.EPOSTransactionBO.client_IDCardType, 241, objInfoCard.customerIDCard);
        }
        else
            if (indexImage == 1) {
                //alert(objInfoCard.idCardIssueDate.toString("yyyy-mm-dd"))
                var formatIssueDate = helper.IsEmptyObject(objInfoCard.idCardIssueDate)
                    ? null
                    : objInfoCard.idCardIssueDate;
                // : (new Date(objInfoCard.idCardIssueDate));
                var formatExpiriedDate = helper.IsEmptyObject(objInfoCard.idCardExpiriedDate)
                    ? null
                    : objInfoCard.idCardExpiriedDate;
                // : (new Date(objInfoCard.idCardExpiriedDate));
                setState({
                    ...state,
                    isVisibleCamera: false,
                    EPOSTransactionBO: {
                        ...state.EPOSTransactionBO,
                        client_IDCardIssueDate: formatIssueDate,
                        client_IDCardIssuePlaceID: parseInt(objInfoCard.idCardIssuePlace ?? -1),
                        client_IDCardExpiriedDate: formatExpiriedDate,
                        [property]: objInfoCard.image_url
                    }
                });
            }
    }

    const getDataIssueplace = (idType = 1, nationalityID = 241, CardID) => {
        var length = idType == 1 ? 9 : 12;
        if (CardID != null && CardID.length == length) {
            dispatch(installmentAction.getIssueplace(idType, nationalityID, CardID)).then(
                res => {
                    console.log("respone getDataIssueplace: ", res);
                }
            ).catch(
                err => {
                    console.log("err getDataIssueplace: ", err);
                }
            )
        }
    }

    const showCamera = (property, indexImage) => () => {
        setState({
            ...state,
            isVisibleCamera: true,
        })
        setProperty(property);
        setIndexImage(indexImage);
    }

    const removePicture = (property, property2) => () => {
        setState({
            ...state,
            EPOSTransactionBO: {
                ...state.EPOSTransactionBO,
                [property]: "",
                [property2]: "",
            }
        });
    }

    const selectImage = () => {
        let options = {
            base64: false,
            forceUpOrientation: true,
            fixOrientation: true,
            pauseAfterCapture: true,
            skipProcessing: true,
            writeExif: true
        };
        launchImageLibrary(options = {
            mediaType: 'photo',
            noData: true
        }, (response) => {
            console.log("response: ", response);
            if (helper.hasProperty(response, 'uri')) {
                resizeImage(response).then(
                    ({ path, uri, size, name }) => {
                        console.log("uri " + uri);
                        showBlockUI();
                        setState({
                            ...state,
                            isVisibleCamera: false,
                        });
                        var urlImg = null;
                        getBodyUpload({
                            uri: uri,
                            type: 'image/jpeg',
                            name: name,
                        }).then(res => {
                            urlImg = res.url;
                            if (indexImage != 2) {
                                var hardUrl = indexImage == 0 ? "https://drive.google.com/file/d/1z8F5IBLQaVGPLZMgo_s6FGeS72dfOyVT/view"
                                    : "https://drive.google.com/file/d/1LnC0PnbDg_kQpqpdcQFN7ZlB-LfbTsPY/view"
                                if (state.EPOSTransactionBO.client_IDCardType == 2) {
                                    hardUrl = indexImage == 0 ? "http://10.1.12.58:50520/get_origin_image/20210621110839S-3XUskEB5e6BcVyFU4hDPC7-EiFjTpKbW4gyWiGQB9vWEQ"
                                        : "http://10.1.12.58:50520/get_origin_image/20210621110757S-3XUskEB5e6BcVyFU4hDPC7-GDHjJar5yVSA5b5WoTK6y9"
                                }
                                getInfoCustomerByImage(uri, indexImage)
                                    .then((response) => {
                                        console.log("getInfoCustomerByImage response");
                                        response.image_url = urlImg;
                                        hideBlockUI();
                                        updateInfoCardToState(response, property, indexImage);
                                    })
                                    .catch(
                                        err => {
                                            hideBlockUI();
                                            setState({
                                                ...state,
                                                isVisibleCamera: false,
                                                EPOSTransactionBO: {
                                                    ...state.EPOSTransactionBO,
                                                    [property]: urlImg
                                                }
                                            });
                                        }
                                    )
                            }
                            else {
                                hideBlockUI();
                                setState({
                                    ...state,
                                    isVisibleCamera: false,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        [property]: urlImg
                                    }
                                });
                            }
                        })
                    }
                ).catch(
                    (error) => {
                        console.log("resizeImage", error);
                    }
                );
            }
        });
    }

    const resizeImage = (e) => {
        return new Promise((resolve, reject) => {
            let max = 1024;
            let w = e.width;
            let h = e.height;

            if (e.width > e.height) {
                // orientation = 'landscape';
                if (w > max) {
                    w = max;
                    h = max * e.height / e.width
                }
            } else if (e.width < e.height) {
                //orientation = 'portrait';
                if (h > max) {
                    h = max;
                    w = max * e.width / e.height
                }
            } else { //width == height
                //orientation = 'event';
                if (w > max) {
                    w = max;
                    h = max * e.height / e.width
                }
            }
            ImageResizer.createResizedImage(
                e.uri,
                w,
                h,
                "JPEG",
                100,
                0
            ).then(
                ({ path, uri, size, name }) => {
                    resolve({ path, uri, size, name })
                }
            ).catch(
                (error) => {
                    reject(error)
                }
            );
        });
    }

    const getInfoCustomerByImage = (uriImage, indexImage) => {
        return new Promise((resolve, reject) => {
            var typeImageDetect = helper.getTypeImage(state.EPOSTransactionBO.client_IDCardType, indexImage)
            console.log(typeImageDetect);
            if (typeImageDetect != 0) {
                let bodyFromData = new FormData();
                bodyFromData.append('file', {
                    uri: uriImage,
                    type: 'image/jpg',
                    name: "getInfoCustomerByImage" + dateHelper.getTimestamp()
                });
                bodyFromData.append('client_id', `MWGPOS_${userInfo.userName}`);
                bodyFromData.append('chosen_side', typeImageDetect);
                installmentBCAction.ocrCCCD(bodyFromData)
                    .then(
                        res => {
                            //resolve(res)
                            switch (typeImageDetect) {
                                case 1:
                                    resolve({
                                        provinceID: res.cap_1_id,
                                        districtID: res.cap_2_id,
                                        wardID: res.cap_3_id,
                                        provinceName: res.cap1,
                                        districtName: res.cap2,
                                        wardName: res.cap3,
                                        customerBirthday: res.date_of_birth,
                                        customerName: res.full_name,
                                        customerAddress: res.place_of_permanent,
                                        customerIDCard: res.id_no,
                                        image_url: res.image_url,
                                        streetAddress: res.cap4,
                                        idCardExpiriedDate: res.expiration_date
                                    }
                                    );
                                    break;
                                case 2:
                                    resolve({
                                        provinceID: res.cap_1_id,
                                        districtID: res.cap_2_id,
                                        wardID: res.cap_3_id,
                                        provinceName: res.cap1,
                                        districtName: res.cap2,
                                        wardName: res.cap3,
                                        customerBirthday: res.date_of_birth,
                                        customerName: res.full_name,
                                        customerAddress: res.place_of_permanent,
                                        customerIDCard: res.id_no,
                                        image_url: res.image_url,
                                        streetAddress: res.cap4,
                                        idCardExpiriedDate: res.expiration_date
                                    }
                                    );
                                    break;
                                case 3:
                                    resolve({
                                        image_url: res.image_url,
                                        idCardIssueDate: res.date_of_issue,
                                        idCardIssuePlace: res.erp_place_of_issue_id,
                                    });
                                    break;
                                case 4:
                                    resolve({
                                        image_url: res.image_url,
                                        idCardIssueDate: res.date_of_issue,
                                        idCardIssuePlace: res.erp_place_of_issue_id,
                                    });
                                    break;
                                case 11:
                                    resolve({
                                        customerBirthday: res.date_of_birth,
                                        customerName: res.full_name,
                                        customerIDCard: res.id_no,
                                        image_url: res.image_url,
                                    }
                                    );
                                    break;
                                case 12:
                                    resolve({
                                        provinceID: res.cap_1_id,
                                        districtID: res.cap_2_id,
                                        wardID: res.cap_3_id,
                                        provinceName: res.cap1,
                                        districtName: res.cap2,
                                        wardName: res.cap3,
                                        streetAddress: res.cap4,
                                        image_url: res.image_url,
                                        idCardIssueDate: res.date_of_issue,
                                        idCardIssuePlace: res.erp_place_of_issue_id,
                                        idCardExpiriedDate: res.expiration_date
                                    });
                                    break;
                                default:
                                    console.log(res)
                                    break;
                            }
                        }
                    ).catch(
                        err => {
                            console.log("err getInfoCustomerByImage", err);
                            reject(err);
                        }
                    );
            }
        });
    }

    const takePicture = (photo) => {
        if (helper.hasProperty(photo, 'uri')) {
            helper.resizeImage(photo).then(({ path, uri, size, name }) => {
                console.log("uri " + uri);
                showBlockUI();
                setState({
                    ...state,
                    isVisibleCamera: false,
                });
                var urlImg = null;
                getBodyUpload({
                    uri: uri,
                    type: 'image/jpeg',
                    name: name,
                }).then(res => {
                    urlImg = res.url;
                    if (indexImage != 2) {
                        var hardUrl = indexImage == 0 ? "https://drive.google.com/file/d/1z8F5IBLQaVGPLZMgo_s6FGeS72dfOyVT/view"
                            : "https://drive.google.com/file/d/1LnC0PnbDg_kQpqpdcQFN7ZlB-LfbTsPY/view"
                        if (state.EPOSTransactionBO.client_IDCardType == 2) {
                            hardUrl = indexImage == 0 ? "http://10.1.12.58:50520/get_origin_image/20210621110839S-3XUskEB5e6BcVyFU4hDPC7-EiFjTpKbW4gyWiGQB9vWEQ"
                                : "http://10.1.12.58:50520/get_origin_image/20210621110757S-3XUskEB5e6BcVyFU4hDPC7-GDHjJar5yVSA5b5WoTK6y9"
                        }
                        getInfoCustomerByImage(uri, indexImage)
                            .then((response) => {
                                console.log("getInfoCustomerByImage response");
                                response.image_url = urlImg;
                                hideBlockUI();
                                updateInfoCardToState(response, property, indexImage);
                            })
                            .catch(
                                err => {
                                    console.log("getInfoCustomerByImage err");
                                    hideBlockUI();
                                    setState({
                                        ...state,
                                        isVisibleCamera: false,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            [property]: urlImg
                                        }
                                    });
                                }
                            )
                    }
                    else {
                        hideBlockUI();
                        setState({
                            ...state,
                            isVisibleCamera: false,
                            EPOSTransactionBO: {
                                ...state.EPOSTransactionBO,
                                [property]: urlImg
                            }
                        });
                    }
                })
            }).catch((error) => {
                console.log("resizeImage", error);
            });
        }
    }

    const takePictureFE = (imageInfo, property, indexImage) => {
        helper.resizeImage(imageInfo).then(({ path, uri, size, name }) => {
            console.log("uri " + uri);
            showBlockUI();
            var urlImg = null;
            getBodyUpload({
                uri: uri,
                type: 'image/jpeg',
                name: name,
            }).then(res => {
                urlImg = res.url;
                if (indexImage != 2) {
                    var hardUrl = indexImage == 0 ? "https://drive.google.com/file/d/1z8F5IBLQaVGPLZMgo_s6FGeS72dfOyVT/view"
                        : "https://drive.google.com/file/d/1LnC0PnbDg_kQpqpdcQFN7ZlB-LfbTsPY/view"
                    if (state.EPOSTransactionBO.client_IDCardType == 2) {
                        hardUrl = indexImage == 0 ? "http://10.1.12.58:50520/get_origin_image/20210621110839S-3XUskEB5e6BcVyFU4hDPC7-EiFjTpKbW4gyWiGQB9vWEQ"
                            : "http://10.1.12.58:50520/get_origin_image/20210621110757S-3XUskEB5e6BcVyFU4hDPC7-GDHjJar5yVSA5b5WoTK6y9"
                    }
                    getInfoCustomerByImage(uri, indexImage)
                        .then((response) => {
                            console.log("getInfoCustomerByImage response");
                            response.image_url = urlImg;
                            hideBlockUI();
                            updateInfoCardToState(response, property, indexImage);
                        })
                        .catch(
                            err => {
                                hideBlockUI();
                                setState({
                                    ...state,
                                    isVisibleCamera: false,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        [property]: urlImg
                                    }
                                });
                            }
                        )
                }
                else {
                    hideBlockUI();
                    setState({
                        ...state,
                        isVisibleCamera: false,
                        EPOSTransactionBO: {
                            ...state.EPOSTransactionBO,
                            [property]: urlImg
                        }
                    });
                }
            })
        }).catch((error) => {
            console.log("resizeImage", error);
        });
    }

    const checkValidateData = () => {
        var FieldObjectsByStep = installmentHelper.initObjectFields.filter(item => item.Step == parent.currentPosition);
        for (var i = 0; i < FieldObjectsByStep.length; i++) {
            var item = FieldObjectsByStep[i];
            if (!validateObject(item, state.EPOSTransactionBO)) {
                return false;
            }
        };
        return true;
    }

    const uploadPicture = (fromData) => {
        return new Promise((resolve, reject) => {
            getImageCDN(fromData).then(cdnImages => {
                console.log("uploadPicture url", API_GET_IMAGE_CDN_NEW + cdnImages[0]);
                resolve({ url: API_GET_IMAGE_CDN_NEW + cdnImages[0] })
            }).catch(error => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('instalmentManager.upload_image_error'),
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: hideBlockUI,
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => uploadPicture(fromData),
                            style: "default"
                        },
                    ],
                    { cancelable: false },
                );
            })
        });
    }

    const getBodyUpload = (file) => {
        const body = helper.createFormData({ uri: file.uri, type: file.type, name: file.name, path: STEP_ONE });
        return new Promise((resolve, reject) => {
            uploadPicture(body).then(res => {
                resolve(res)
            });
        });
    }

    return (
        <KeyboardAwareScrollView
            ref={refScroll1}
            enableResetScrollToCoords={false}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={60}>
            <View style={{ marginVertical: 5, flexDirection: 'row' }}>
                <RadioButton
                    style={{
                        flexDirection: 'row',
                    }}
                    containerStyle={{
                        //paddingLeft: constants.getSize(10),
                        alignItems: 'center',
                        justifyContent: "center",
                        width: constants.width / 2
                    }}
                    dataItems={radioIDCardType}
                    selectItem={
                        (index) => { selectItemCardType(radioIDCardType, index) }
                    }
                    mainComponent={(item) => {
                        return (
                            <MyText
                                text={item.title}
                                style={{
                                    color: item.selected ? COLORS.txtFF8900 : COLORS.txt333333,
                                    marginLeft: 2,
                                    fontSize: 15
                                }} />
                        )
                    }}
                />
            </View>
            {state.EPOSTransactionBO.PartnerInstallmentID != 3 ? (
                <>
                    <View style={{
                        marginVertical: 0,
                        paddingHorizontal: 10,
                    }}
                    >
                        <MyText
                            text={`${translate('instalmentManager.capture')} ${state.EPOSTransactionBO.client_IDCardType == 1 ? translate('instalmentManager.id_card_old') : translate('instalmentManager.id_card_new')} ${translate('instalmentManager.front')}`}
                            style={{ fontSize: 15, marginVertical: 5 }}
                        />
                        <CameraDOT
                            uriImage={state.EPOSTransactionBO.cus_FilePathFrontOfIDCard?.includes("http") ? state.EPOSTransactionBO.cus_FilePathFrontOfIDCard : `${state.EPOSTransactionBO.client_ImageFrontOfIDCard ?? ""}`}
                            onTakePicture={(response) => {
                                takePictureFE(response, "cus_FilePathFrontOfIDCard", 0)
                            }}
                            onDelete={removePicture("cus_FilePathFrontOfIDCard", "client_ImageFrontOfIDCard")}
                        />
                    </View>
                    <View style={{
                        marginVertical: 0,
                        paddingHorizontal: 10,
                    }}
                    >
                        <MyText
                            text={`${translate('instalmentManager.capture')} ${state.EPOSTransactionBO.client_IDCardType == 1 ? translate('instalmentManager.id_card_old') : translate('instalmentManager.id_card_new')} ${translate('instalmentManager.back_2')}`}
                            style={{ fontSize: 15, marginVertical: 5 }}
                        />
                        <CameraDOT
                            uriImage={state.EPOSTransactionBO.cus_FilePathBackOfIDCard?.includes("http") ? state.EPOSTransactionBO.cus_FilePathBackOfIDCard : `${state.EPOSTransactionBO.client_ImageBackOfIDCard ?? ""}`}
                            onTakePicture={(response) => {
                                takePictureFE(response, "cus_FilePathBackOfIDCard", 1)
                            }}
                            onDelete={removePicture("cus_FilePathBackOfIDCard", "client_ImageBackOfIDCard")}
                        />
                    </View>
                    <View style={{
                        marginVertical: 0,
                        paddingHorizontal: 10,
                    }}
                    >
                        <MyText
                            text={translate('instalmentManager.portrait')}
                            style={{ fontSize: 15, marginVertical: 5 }}
                        />
                        <ImageProcess
                            onCamera={showCamera("cus_FilePathPortrait", 2)}
                            urlImageLocal={state.EPOSTransactionBO.cus_FilePathPortrait?.includes("http") ? state.EPOSTransactionBO.cus_FilePathPortrait : `${state.EPOSTransactionBO.client_ImagePortrait ?? ""}`}
                            urlImageRemote={state.EPOSTransactionBO.cus_FilePathPortrait?.includes("http") ? state.EPOSTransactionBO.cus_FilePathPortrait : `${state.EPOSTransactionBO.client_ImagePortrait ?? ""}`}
                            deleteImage={removePicture("cus_FilePathPortrait", "client_ImagePortrait")}
                        // uploadImage={this.uploadPicture(0)}
                        />
                    </View>
                </>
            ) : (
                <>
                    <View style={{
                        marginVertical: 0,
                        paddingHorizontal: 10,
                        alignItems: "center",
                        width: constants.width
                    }}
                    >
                        <MyText
                            text={`${translate('instalmentManager.capture')} ${state.EPOSTransactionBO.client_IDCardType == 1 ? translate('instalmentManager.id_card_old') : translate('instalmentManager.id_card_new')} ${translate('instalmentManager.front')}`}
                            style={{ fontSize: 15, marginVertical: 5, width: constants.width - 20 }}
                        />
                        <DocCamera
                            uriImage={state.EPOSTransactionBO.cus_FilePathFrontOfIDCard?.includes("http") ? state.EPOSTransactionBO.cus_FilePathFrontOfIDCard : `${state.EPOSTransactionBO.client_ImageFrontOfIDCard ?? ""}`}
                            onDelete={removePicture("cus_FilePathFrontOfIDCard", "client_ImageFrontOfIDCard")}
                            onTakePicture={(response) => {
                                takePictureFE(response, "cus_FilePathFrontOfIDCard", 0)
                            }}
                            docSide={HYPER_VERGE.DocumentFront}
                            docType={HYPER_VERGE.DocumentCARD}
                            content={HYPER_VERGE.ContentCARD_FRONT}
                        />
                    </View>
                    <View style={{
                        marginVertical: 0,
                        paddingHorizontal: 10,
                        alignItems: "center",
                        width: constants.width
                    }}
                    >
                        <MyText
                            text={`${translate('instalmentManager.capture')} ${state.EPOSTransactionBO.client_IDCardType == 1 ? translate('instalmentManager.id_card_old') : translate('instalmentManager.id_card_new')} ${translate('instalmentManager.back_2')}`}
                            style={{ fontSize: 15, marginVertical: 5, width: constants.width - 20 }}
                        />
                        <DocCamera
                            uriImage={state.EPOSTransactionBO.cus_FilePathBackOfIDCard?.includes("http") ? state.EPOSTransactionBO.cus_FilePathBackOfIDCard : `${state.EPOSTransactionBO.client_ImageBackOfIDCard ?? ""}`}
                            onDelete={removePicture("cus_FilePathBackOfIDCard", "client_ImageBackOfIDCard")}
                            onTakePicture={(response) => {
                                takePictureFE(response, "cus_FilePathBackOfIDCard", 1)
                            }}
                            docSide={HYPER_VERGE.DocumentBack}
                            docType={HYPER_VERGE.DocumentCARD}
                            content={HYPER_VERGE.ContentCARD_BACK}
                        />
                    </View>
                    <View style={{
                        marginVertical: 0,
                        paddingHorizontal: 10,
                        alignItems: "center",
                        width: constants.width
                    }}
                    >
                        <MyText
                            text={translate('instalmentManager.portrait')}
                            style={{ fontSize: 15, marginVertical: 5, width: constants.width - 20 }}
                        />
                        <FaceCamera
                            uriImage={state.EPOSTransactionBO.cus_FilePathPortrait?.includes("http") ? state.EPOSTransactionBO.cus_FilePathPortrait : `${state.EPOSTransactionBO.client_ImagePortrait ?? ""}`}
                            onDelete={removePicture("cus_FilePathPortrait", "client_ImagePortrait")}
                            onTakePicture={(response) => {
                                takePictureFE(response, "cus_FilePathPortrait", 2);
                            }}
                            key={"FaceCamera"}
                        />
                    </View>
                </>
            )}
            <View style={{
                padding: 10,
                width: constants.width
            }}
                activeOpacity={0.7}
            >
                <View
                    style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        //paddingHorizontal: 20,
                        marginVertical: 10,
                    }}
                >
                    <TouchableOpacity
                        style={[styles.btn, { backgroundColor: COLORS.btnFFFFFF, }]}
                        activeOpacity={0.7}
                        onPress={() => {
                            updateprevstate(state.EPOSTransactionBO);
                            onPrev();
                        }}
                    >
                        <MyText text={translate('common.btn_back')} style={{ fontWeight: "bold", backgroundColor: COLORS.btnFFFFFF, color: COLORS.txt4DA6FF }} />
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.btn, { backgroundColor: COLORS.btn4DA6FF }]}
                        activeOpacity={0.7}
                        onPress={() => {
                            if (checkValidateData()) {
                                updatestate(state.EPOSTransactionBO);
                                onNext();
                            }
                        }}
                    >
                        <MyText text={translate('common.btn_continue')} style={styles.txtBtn} />
                    </TouchableOpacity>
                </View>
            </View>
            {state.EPOSTransactionBO.PartnerInstallmentID != 3 && (
                <CaptureCamera
                    isVisibleCamera={state.isVisibleCamera}
                    disabledUploadImage={true}
                    takePicture={takePicture}
                    closeCamera={() => {
                        setState({ ...state, isVisibleCamera: false })
                    }}
                    selectPicture={() => {
                        selectImage();
                    }}
                />
            )}
        </KeyboardAwareScrollView>
    );
}

export default StepOne;
const styles = StyleSheet.create({
    fieldSet: {
        flexDirection: "row",
        marginVertical: 5,
        justifyContent: "space-between",
    },
    view_picker: {
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,
        height: "auto",
        backgroundColor: COLORS.bgFFFFFF,
        shadowColor: COLORS.sd000000,
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.20,
        elevation: 3,
    },
    picker: {
        height: 30,
        //width: constants.width/2 + 20,  
        margin: 5,
    },
    btn: {
        padding: 10,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        width: "47%",
        borderWidth: 1,
        borderColor: COLORS.bd4DA6FF,
        //shadowColor: COLORS.sd000000,
        // shadowOffset: {
        //     width: 0,
        //     height: 1,
        // },
        // shadowOpacity: 0.15,
        // elevation: 3,
        height: "auto",
        //margin: 10
    },
    txtBtn: {
        fontWeight: "bold",
        color: COLORS.txtFFFFFF,
    },
});
