import {
    API_CONST,
    STORAGE_CONST,
} from '@constants';
import { helper, storageHelper } from '@common';
import { METHOD, apiBase, EMPTY, ERROR } from '@config';
import { translate } from "@translate";
const {
    API_GET_LIST_NOTIFICATION,
    API_SEEN_NOTIFICATION,
    API_REMOVE_NOTIFICATION,
    API_REMOVE_ALL_NOTIFICATION
} = API_CONST;

const START_GET_LIST_NOTIFICATION = "START_GET_LIST_NOTIFICATION";
const STOP_GET_LIST_NOTIFICATION = "STOP_GET_LIST_NOTIFICATION";
const START_LOAD_MORE_NOTIFICATION = "START_LOAD_MORE_NOTIFICATION";
const STOP_LOAD_MORE_NOTIFICATION = "STOP_LOAD_MORE_NOTIFICATION";
const SEEN_NOTIFICATION = "SEEN_NOTIFICATION";
const REMOVE_NOTIFICATION = "REMOVE_NOTIFICATION";
const REMOVE_ALL_NOTIFICATION = "REMOVE_ALL_NOTIFICATION";

export const notifyAction = {
    START_GET_LIST_NOTIFICATION,
    STOP_GET_LIST_NOTIFICATION,
    START_LOAD_MORE_NOTIFICATION,
    STOP_LOAD_MORE_NOTIFICATION,
    SEEN_NOTIFICATION,
    REMOVE_NOTIFICATION,
    REMOVE_ALL_NOTIFICATION
}

export const getDataNotification = function (userName) {
    return function (dispatch, getState) {
        const body = {
            "currentUsertoken": userName,
            "filters": [{
                "property": "lastId",
                "value": -1
            }],
            "pageReq": {
                "pageNumber": 0,
                "pageSize": 10
            }
        }
        let isLoadMore = true;
        dispatch(start_get_list_notification());
        apiBase(API_GET_LIST_NOTIFICATION, METHOD.POST, body).then((response) => {
            console.log("getDataNotification success: ", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object?.data)) {
                const { data } = object;
                isLoadMore = (data.length == 10);
                dispatch(stop_get_list_notification(data, isLoadMore));
            }
            else {
                dispatch(stop_get_list_notification([], isLoadMore, EMPTY, "Không có thông báo"));
            }
        }).catch(error => {
            console.log("getDataNotification error: ", error);
            dispatch(stop_get_list_notification([], isLoadMore, !EMPTY, error.msgError, ERROR));
        })
    }
}

export const getMoreDataNotify = function (data) {
    return function (dispatch, getState) {
        const body = {
            "currentUsertoken": data.userName,
            "filters": [{
                "property": "lastId",
                "value": data.lastId
            }],
            "pageReq": {
                "pageNumber": 0,
                "pageSize": 10
            }
        }
        const newData = getState().notifyReducer.dataNotify;
        let isLoadMore = true;
        dispatch(start_load_more_notification());
        apiBase(API_GET_LIST_NOTIFICATION, METHOD.POST, body).then((response) => {
            console.log("getMoreDataNotify success: ", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object?.data)) {
                const { data } = object;
                isLoadMore = (data.length <= 10);
                dispatch(stop_load_more_notification([...newData, ...data], isLoadMore));
            }
            else {
                dispatch(stop_load_more_notification(newData, isLoadMore));
            }
        }).catch(error => {
            console.log("getMoreDataNotify error: ", error);
            isLoadMore = false;
            dispatch(stop_load_more_notification(newData, isLoadMore, ERROR));
        })
    }
}

export const seenNotification = function (notificationId) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            const body = {
                "arrayNotificationIds": [notificationId],
                "currentUsertoken": getState().userReducer.userName
            }
            apiBase(API_SEEN_NOTIFICATION, METHOD.POST, body).then((response) => {
                console.log("seen notification success: ", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object) && !helper.IsEmptyObject(object[0].message)) {
                    const { id } = object[0].message;
                    dispatch(seen_notification(id));
                    resolve(object[0]);
                }
                else {
                    reject("Thông báo đã xem rồi")
                }
            }).catch(error => {
                console.log("seen notification error: ", error);
                reject(error.msgError);
            })
        })
    }
}

export const removeNotification = function (notificationId) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            const body = {
                "arrayNotificationIds": [notificationId],
                "currentUsertoken": getState().userReducer.userName
            }
            apiBase(API_REMOVE_NOTIFICATION, METHOD.POST, body).then((response) => {
                console.log("remove notification success: ", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    const id = object[0];
                    dispatch(remove_notification(id));
                    resolve(id);
                }
            }).catch(error => {
                console.log("remove notification error: ", error);
                reject(error.msgError);
            })
        })
    }
}

export const removeAllNotification = function () {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            const body = {
                "currentUsertoken": getState().userReducer.userName
            }
            apiBase(API_REMOVE_ALL_NOTIFICATION, METHOD.POST, body).then((response) => {
                console.log("remove all notification success: ", response);
                const { object } = response;
                if (object === "DELETED") {
                    dispatch(remove_all_notification());
                    resolve();
                }
            }).catch(error => {
                console.log("remove all notification error: ", error);
                reject(error.msgError);
            })
        })
    }
}

const start_get_list_notification = () => {
    return ({ type: START_GET_LIST_NOTIFICATION });
}

const stop_get_list_notification = (
    dataNotify,
    isLoadMore,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return {
        type: STOP_GET_LIST_NOTIFICATION,
        dataNotify,
        isLoadMore,
        isEmpty,
        description,
        isError,
    };
}

const start_load_more_notification = () => {
    return ({ type: START_LOAD_MORE_NOTIFICATION });
}

const stop_load_more_notification = (
    dataNotify,
    isLoadMore,
    isError = false,
) => {
    return {
        type: STOP_LOAD_MORE_NOTIFICATION,
        dataNotify,
        isLoadMore,
        isError,
    };
}

const seen_notification = (seenNotificationId) => {
    return {
        type: SEEN_NOTIFICATION,
        seenNotificationId
    };
}

const remove_notification = (removeNotificationId) => {
    return {
        type: REMOVE_NOTIFICATION,
        removeNotificationId
    };
}

const remove_all_notification = () => {
    return {
        type: REMOVE_ALL_NOTIFICATION
    };
}