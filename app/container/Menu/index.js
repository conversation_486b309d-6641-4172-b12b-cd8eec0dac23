import React, { useMemo } from 'react';
import {
    StyleSheet,
    View,
    FlatList,
    TouchableOpacity,
    ImageBackground,
    TouchableWithoutFeedback,
    Switch,
    Alert
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import CodePush from 'react-native-code-push';
import { db<PERSON><PERSON><PERSON> } from "@commonDB";
import {
    MyText,
    Icon,
    showBlockUI,
    hideBlockUI
} from '@components';
import { helper, storageHelper, unSubscribeNotify } from '@common';
import { COLORS } from '@styles';
import { translate } from '@translate';
import { STORAGE_CONST, constants, DEVICE, ENUM } from '@constants';
import * as appSwitchActionCreator from "../AppSwitch/action";
import * as specialSaleProgramActionCreator from '../SpecialSaleProgram/action';
import * as staffPromotionActionCreator from '../StaffPromotion/action';
import * as actionShoppingCartCreator from "../ShoppingCart/action";
import * as actionPouchCreator from "../PouchRedux/action";
import * as actionPharmacyCreator from "../AnKhangNew/action";
import * as actionDetailCreator from "../Detail/action";
import * as actionSaleCreator from "../Sale/action";

import {
    usePermissionStore,
    useBrandCheck,
    useLoyaltyStore
} from '../AnKhangNew/hooks';
import { TYPE_DELIVERY } from '../AnKhangNew/constants';

const MenuScreen = ({
    navigation,
    width,
    userInfo,
    appSwitchAction,
    scenarioSaleState,
    specialSaleProgramAction,
    staffPromotionAction,
    shoppingCartState,
    actionShoppingCart,
    actionPouch,
    actionPharmacy,
    actionDetail,
    CUSTOMER_VERIFY,
    electricalPrescriptionBO,
    actionSale
}) => {
    const isHidden = useMemo(() => {
        const id = parseInt(userInfo.brandID);
        return id === ENUM.BRAND_ID.AN_KHANG;
    }, [userInfo.brandID]);
    const isAva = useBrandCheck(ENUM.BRAND_ID.AVA);
    const hasPermission = usePermissionStore();
    const hasPermissionLoyalty = useLoyaltyStore();

    const isShowQuickSale = (hasPermission && isAva) || isHidden;
    const storeAva = useMemo(() => {
        const id = parseInt(userInfo.storeID);
        return isAva && ENUM.ALLOW_STORE_ID.AVA.some((storeId) => storeId == -1 || id == storeId);
    }, [userInfo.storeID]);
    const hiddenMenuSale = isHidden || storeAva;
    const isAllowUserCheckIn = CUSTOMER_VERIFY == -1 || `,${CUSTOMER_VERIFY},`.includes(`,${userInfo.userName.toString()},`);
    let MENU = [
        {
            title: translate("menu.store"),
            iconName: "storefront-outline",
            iconSet: "MaterialCommunityIcons",
            screen: "Store",
        },
        {
            title: translate("menu.sale"),
            iconName: "currency-usd",
            iconSet: "MaterialCommunityIcons",
            screen: "Sale",
            // isHidden: hiddenMenuSale
        },
        {
            title: translate("menu.sale"),
            iconName: "currency-usd",
            iconSet: "MaterialCommunityIcons",
            screen: 'AnKhangPharmacy',
            isHidden: !isHidden
        },
        {
            title: "Bán hàng không tư vấn",
            iconName: "magnify-scan",
            iconSet: "MaterialCommunityIcons",
            screen: 'Pharmacy',
            isHidden: !isShowQuickSale
        },
        {
            title: translate("menu.management_order"),
            iconName: "file-document-outline",
            iconSet: "MaterialCommunityIcons",
            screen: "OrderManagement",
        },
        {
            title: translate("menu.installment"),
            iconName: "ios-file-tray-full-outline",
            iconSet: "Ionicons",
            screen: "Installment",
            isHidden
        },
        {
            title: translate("menu.management_sim"),
            iconName: "sim",
            iconSet: "MaterialCommunityIcons",
            screen: "ActiveSimManager",
            isHidden
        },
        {
            title: translate("menu.additional_promotion"),
            iconName: "wallet-giftcard",
            iconSet: "MaterialIcons",
            screen: "AdditionalPromotion",
        },
        {
            title: translate("menu.screen_sticker"),
            iconName: "phone-iphone",
            iconSet: "MaterialIcons",
            screen: "StickerProtector",
            isHidden
        },
        {
            title: translate("menu.change_watch_battery"),
            iconName: 'watch-outline',
            iconSet: 'Ionicons',
            screen: 'Oclock',
            isHidden
        },
        {
            title: `${translate("menu.inventory_stock")} (Mới)`,
            iconName: "file-table-outline",
            iconSet: "MaterialCommunityIcons",
            screen: "InventoryNew",
            isHidden
        },
        {
            title: 'Tư vấn khách hàng',
            iconName: 'supervised-user-circle',
            iconSet: 'MaterialIcons',
            screen: 'CustomerConsultant',
            isHidden: !hasPermissionLoyalty
        },
        // {
        //     title: "Lịch sử tư vấn cài app QTV khi Thu hộ",
        //     iconName: "people-alt",
        //     iconSet: "MaterialIcons",
        //     screen: "ClientConsultant",
        // },
        {
            title: 'Quản lý giao dịch chuyển khoản',
            iconName: "payment",
            iconSet: "MaterialIcons",
            screen: "TransactionScreens"
        },
        {
            title: 'Pre 2025',
            iconName: "phone-portrait-outline",
            iconSet: "Ionicons",
            screen: "PreIphone"
        },
    ];
    if (global.isVN) {
        if (`${userInfo.storeID}` != '3755' && MULTICAT_COMPANYID.has(`${userInfo.companyID}`) && userInfo.isShowWeb) {
            MENU = [
                ...MENU,
                {
                    title: "Bán thẻ cào tại siêu thị",
                    iconName: "card-outline",
                    iconSet: "Ionicons",
                    screen: "SellCard",
                },
                {
                    title: translate("menu.output_receipt_management"),
                    iconName: "file-search-outline",
                    iconSet: "MaterialCommunityIcons",
                    screen: "SearchOutputReceipt",
                },
                {
                    title: translate("collection.multicat_industry_service_nfp"),
                    iconName: "bank-transfer-out",
                    iconSet: "MaterialCommunityIcons",
                    screen: "MenuCollection",
                },
                {
                    title: translate("collection.managerment_multicat_industry_transactions_nfp"),
                    iconName: "comment-bank",
                    iconSet: "MaterialIcons",
                    screen: "CollectionManager",
                }
            ];
        }
        if (isAllowUserCheckIn) {
            MENU.push({
                title: "Định danh khách hàng",
                iconName: 'qrcode',
                iconSet: 'MaterialCommunityIcons',
                screen: 'CheckInLoyalty',
            });
        }
        MENU = [
            ...MENU,
            {
                title: "Chuyển đổi hàng không IMEI",
                iconName: "file-sync-outline",
                iconSet: "MaterialCommunityIcons",
                screen: "SwitchIMEI",
                isHidden
            },
            // {
            //     title: "Quản lý phiếu bán hàng",
            //     iconName: "file-document-edit-outline",
            //     iconSet: "MaterialCommunityIcons",
            //     screen: "ReceiptManagement"
            // },
            {
                title: translate("menu.restock"),
                iconName: "database-import",
                iconSet: "MaterialCommunityIcons",
                screen: 'Restock',
                isHidden: !isHidden
            },
            {
                title: 'Thu tiền và đóng phiếu sửa chữa',
                iconName: 'newspaper',
                iconSet: 'Ionicons',
                screen: 'CollectAndCloseRepair',
            }
        ];
    }
    if (helper.checkConfigStoreBHX(userInfo.companyID)) {
        MENU = [
            {
                title: translate("menu.store"),
                iconName: "storefront-outline",
                iconSet: "MaterialCommunityIcons",
                screen: "Store",
            },
            {
                title: translate("collection.multicat_industry_service_nfp"),
                iconName: "bank-transfer-out",
                iconSet: "MaterialCommunityIcons",
                screen: "MenuCollection",
            },
            {
                title: translate("collection.managerment_multicat_industry_transactions_nfp"),
                iconName: "comment-bank",
                iconSet: "MaterialIcons",
                screen: "CollectionManager",
            },
            {
                title: "Bán thẻ cào tại siêu thị",
                iconName: "card-outline",
                iconSet: "Ionicons",
                screen: "SellCard",
            },
            {
                title: translate("menu.output_receipt_management"),
                iconName: "file-search-outline",
                iconSet: "MaterialCommunityIcons",
                screen: "SearchOutputReceipt",
            }
        ];
    }

    const onNavigate = (item) => () => {
        // RESET SCENARIO TYPE
        // const cleaningCartScreens = ['AnKhangPharmacy'];
        if (scenarioSaleState.saleScenarioTypeID !== ENUM.SALE_SCENARIO_TYPE.SALE) {
            if (!helper.IsEmptyObject(shoppingCartState.dataShoppingCart)) {
                actionShoppingCart.deleteShoppingCart();
                actionPouch.setDataCartApply();
            }
            specialSaleProgramAction.setScenarioSaleType(
                ENUM.SALE_SCENARIO_TYPE.SALE
            );
        }
        staffPromotionAction.reset_staff_info();
        actionPharmacy.setTypeDelivery(TYPE_DELIVERY.STORE);
        // if (cleaningCartScreens.includes(item.screen)) {
        //     handleCleanShoppingCart(navigation, item.screen);
        // }
        if (!helper.IsEmptyObject(electricalPrescriptionBO)) {
            actionPharmacy.reset_map_prescriptions();
        }
        actionDetail.reset_map_content_promotion_input();
        actionShoppingCart.reset_map_customer_confirm_policy();
        navigation.reset({
            index: 0,
            routes: [{ name: item.screen }]
        });
        actionSale.setStateFilter({})

    };

    const onLogout = async () => {
        showBlockUI();
        unSubscribeNotify({
            "userToken": userInfo.userName,
            "session": DEVICE.uniqueId,
        });
        try {
            await storageHelper.removeKeysByIgnore([STORAGE_CONST.CURRENT_LANGUAGE]);
            await dbHelper.deleteDB();
        } catch (error) {
            console.log("onLogout error", error);
        }
        navigation.closeDrawer();
        await helper.sleep(100);
        hideBlockUI();
        // appSwitchAction.switchToSpalshScreen();
        CodePush.restartApp();
    };

    const onChangeMode = async () => {
        const isOffline = await appSwitchAction.checkActiveOffline();
        if (isOffline) {
            navigation.closeDrawer();
            await helper.sleep(100);
            appSwitchAction.switchToSpalshScreen();
        }
        else {
            navigation.closeDrawer();
            Alert.alert("", "Chế độ OFFLINE chưa được kích hoạt.\nVui lòng liên hệ IT để kiểm tra.");
        }
    };

    return (
        <TouchableWithoutFeedback onPress={navigation.closeDrawer}>
            <View style={{
                flex: 1,
                width: constants.width,
            }}>
                <ImageBackground style={{
                    flex: 1,
                    width: width,
                    backgroundColor: COLORS.bg00A98F,
                    paddingTop: constants.heightTopSafe
                }}
                    source={{ uri: "background_menu" }}
                // source={require('../../../assets/background_menu.png')}
                >
                    <Avatar
                        width={width}
                        info={userInfo}
                        onLogout={onLogout}
                        onChangeMode={onChangeMode}
                    />
                    <View style={{
                        flex: 1,
                        width: width,
                    }}>
                        <FlatList
                            data={MENU}
                            renderItem={({ item, index }) => {
                                return !item.isHidden && (
                                    <Item
                                        info={item}
                                        width={width}
                                        onPress={onNavigate(item)}
                                    />
                                );
                            }}
                            keyExtractor={(item, index) => `${index}`}
                        />
                    </View>
                </ImageBackground>
            </View>
        </TouchableWithoutFeedback>
    );
};

const mapStateToProps = (state) => ({
    userInfo: state.userReducer,
    scenarioSaleState: state.specialSaleProgramReducer,
    shoppingCartState: state.shoppingCartReducer,
    CUSTOMER_VERIFY: state.appSettingReducer.CUSTOMER_VERIFY,
    electricalPrescriptionBO: state._pharmacyReducer.electricalPrescriptionBO
});

const mapDispatchToProps = (dispatch) => ({
    appSwitchAction: bindActionCreators(appSwitchActionCreator, dispatch),
    specialSaleProgramAction: bindActionCreators(specialSaleProgramActionCreator, dispatch),
    staffPromotionAction: bindActionCreators(staffPromotionActionCreator, dispatch),
    actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
    actionPouch: bindActionCreators(actionPouchCreator, dispatch),
    actionPharmacy: bindActionCreators(actionPharmacyCreator, dispatch),
    actionDetail: bindActionCreators(actionDetailCreator, dispatch),
    actionSale: bindActionCreators(actionSaleCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(MenuScreen);

const Avatar = ({ width, info, onLogout, onChangeMode }) => {
    const {
        userName,
        fullName,
        storeName,
        storeID,
        isDefaultStore
    } = info;
    return (
        <View style={{
            flexDirection: "row",
            alignItems: "center",
            paddingVertical: 10,
            width: width,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: COLORS.bdFFFF00
        }}>
            <View style={{
                width: 68,
                height: 68,
                justifyContent: "center",
                alignItems: "center"
            }}>
                <Icon
                    iconSet={"Ionicons"}
                    name={"person-circle"}
                    color={COLORS.icFFFFFF}
                    size={60}
                />
            </View>
            <View style={{
                width: width - 68,
                paddingRight: 4
            }}>
                <MyText
                    style={{ color: COLORS.txtFFFFFF }}
                    text={`${userName} - ${fullName}`}
                    addSize={-1}
                />
                <MyText
                    style={{ color: COLORS.txtF4F7B9, marginTop: 2 }}
                    text={`${storeID}  - ${storeName}`}
                    addSize={-1}
                />
                <View style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between'
                }}>
                    <TouchableOpacity style={{
                        backgroundColor: COLORS.btnFDD700,
                        paddingVertical: 4,
                        paddingHorizontal: 8,
                        borderRadius: 4,
                        justifyContent: "center",
                        alignItems: "center",
                        alignSelf: "flex-start",
                        marginTop: 4,
                        flexDirection: "row",
                    }}
                        onPress={onLogout}
                        activeOpacity={0.8}
                    >
                        <MyText
                            text={translate("menu.log_out")}
                            style={{
                                color: COLORS.txt000000,
                                marginRight: 4
                            }}
                        />
                        <Icon
                            iconSet={"Octicons"}
                            name={"sign-out"}
                            color={COLORS.ic000000}
                            size={14}
                        />
                    </TouchableOpacity>
                    {
                        isDefaultStore &&
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                            <MyText
                                text={"Online"}
                                addSize={2}
                                style={{
                                    color: COLORS.txtFFFFFF,
                                    marginRight: constants.getSize(3)
                                }}
                            />
                            <Switch
                                trackColor={{ false: COLORS.bg7F7F7F, true: COLORS.icFF3C41 }}
                                style={{ transform: [{ scaleX: .8 }, { scaleY: .8 }] }}
                                thumbColor={COLORS.bgFFFFFF}
                                onValueChange={onChangeMode}
                                value={true}
                            />
                        </View>
                    }
                </View>
            </View>
        </View>
    );
};

const Item = ({ info, width, onPress }) => {
    const { iconName, iconSet, title } = info;
    return (
        <TouchableOpacity style={{
            width: width,
            height: 40,
            alignItems: "center",
            flexDirection: "row",
            marginVertical: 4
        }}
            onPress={onPress}
            activeOpacity={0.8}
        >
            <View style={{
                width: 68,
                alignItems: "center",
                justifyContent: "center",
            }}>
                <View style={{
                    width: 28,
                    height: 28,
                    backgroundColor: COLORS.bgFFFFFF,
                    borderRadius: 14,
                    justifyContent: "center",
                    alignItems: "center"
                }}>
                    <Icon
                        iconSet={iconSet}
                        name={iconName}
                        color={COLORS.ic00A98f}
                        size={16}
                    />
                </View>
            </View>
            <MyText
                style={{
                    color: COLORS.txtFFFFFF,
                    width: width - 68,
                    paddingRight: 4
                }}
                text={title}
            />
        </TouchableOpacity>
    );
};

const MULTICAT_COMPANYID = new Set(["1", "8"]);
const JOBCARD_USER = new Set(["85790", "165616", "197025", "98138", "26442"]);
