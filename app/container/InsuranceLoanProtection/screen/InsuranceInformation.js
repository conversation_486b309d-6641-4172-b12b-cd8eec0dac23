import { Animated, FlatList, SafeAreaView, StyleSheet, View } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import StepIndicator from 'react-native-step-indicator';
import { COLORS } from '@styles';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { constants } from '@constants';
import * as actionInsuranceLoanProtectionCreator from '../action';
import StepOneInsurance from './StepOneInsurance';
import StepTwoInsurance from './StepTwoInsurance';
import StepThreeInsurance from './StepThreeInsurance';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

const InsuranceInformation = ({
    route,
    navigation
}) => {
    const { item } = route?.params ?? {};

    const client_steps = [0, 1, 2];

    const [state, setState] = useState({
        currentPosition: client_steps[0],
        scrollY: new Animated.Value(0),
        indexStep: null,
        client_IsHasRegistrationBook: false,
    });

    const flatListSteps = useRef(null);

    const HEADER_MAX_HEIGHT = 1;

    const diffClamp = Animated.diffClamp(
        state.scrollY,
        0,
        HEADER_MAX_HEIGHT
    );

    const translateY = diffClamp.interpolate({
        inputRange: [0, HEADER_MAX_HEIGHT],
        outputRange: [0, -HEADER_MAX_HEIGHT],
    });

    useEffect(() => {
        if (state.indexStep === null) {
            setState(prevState => ({
                ...prevState,
                indexStep: client_steps[0],
            }));
        }
    }, []);

    const onPageChange = (position, valueindexStep) => {
        setState(prevState => ({
            ...prevState,
            indexStep: valueindexStep,
            currentPosition: position,
        }));
    };

    const goBack = () => {
        if (state.indexStep > 0) {
            const newIndex = state.indexStep - 1;
            onPageChange(client_steps[newIndex], newIndex);
        } else {
            console.log("Reached the first step");
        }
    };

    const renderPage = (stepIndex, position) => {
        if (state.indexStep === stepIndex) {
            switch (stepIndex) {
                case 0:
                    return <StepOneInsurance
                        onNext={() => {
                            var step = state.indexStep + 1;
                            onPageChange(client_steps[step], step);

                        }}
                        dataSO={item}
                        onBack={() => {
                            var step = state.indexStep - 1
                            if (step < 0) goBack();
                            else onPageChange(client_steps[step], step);
                        }}
                        onNextStep={() => onPageChange(client_steps[state.indexStep + 1], state.indexStep + 1)}
                    />;
                case 1:
                    return <StepTwoInsurance
                        onBack={() => {
                            var step = state.indexStep - 1
                            if (step < 0) goBack();
                            else onPageChange(client_steps[step], step);
                        }}
                        dataSO={item}
                        onNextStep={() => onPageChange(client_steps[state.indexStep + 1], state.indexStep + 1)}
                    />;
                case 2:
                    return <StepThreeInsurance
                        onBack={() => {
                            var step = state.indexStep - 1
                            if (step < 0) goBack();
                            else onPageChange(client_steps[step], step);
                        }}
                        onNextStep={() => onPageChange(client_steps[state.indexStep + 1], state.indexStep + 1)}
                        navigation={navigation}
                    />;
                default:
                    console.warn(`Invalid step index: ${stepIndex}`);
                    return null;
            }
        } else {
            return null;
        }
    }

    return (
        <View style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
            <KeyboardAwareScrollView
                contentContainerStyle={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView style={{ alignItems: "center", justifyContent: 'center' }}>
                    <View style={{ paddingVertical: 10, alignSelf: "center", width: constants.width - constants.getSize(60), justifyContent: "center" }}>
                        <StepIndicator
                            customStyles={customStyles}
                            currentPosition={state.indexStep !== null ? state.indexStep : 0}
                            stepCount={client_steps.length}
                            labels={labels}
                        />

                    </View>
                </SafeAreaView>
                <Animated.View style={{
                    width: constants.width,
                    flex: 1,
                    height: "auto",
                    justifyContent: 'center',
                    alignItems: 'center',
                    transform: [{ translateY: translateY }, { perspective: 1000 }],
                }}>
                    <SafeAreaView>
                        <FlatList
                            ref={flatListSteps}
                            data={client_steps}
                            renderItem={({ item, index }) => renderPage(item, index)}
                            keyExtractor={(item, index) => index.toString()}
                            horizontal={true}
                            scrollEnabled={false}
                            keyboardShouldPersistTaps={"always"}
                        />
                    </SafeAreaView>
                </Animated.View>
            </KeyboardAwareScrollView>
        </View>
    );
};

const mapStateToProps = function (state) {
    return {
        dataServiceList: state.insuranceLoanProtectionReducer.dataServiceList,
        stateServiceList: state.insuranceLoanProtectionReducer.stateServiceList,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsuranceLoanProtection: bindActionCreators(actionInsuranceLoanProtectionCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(InsuranceInformation)
const styles = StyleSheet.create({});

const labels = [
    "Thông tin bảo hiểm",
    "Thông tin khách hàng",
    "Xác nhận"
];

const customStyles = {
    separatorStrokeWidth: 2,
    stepStrokeWidth: 3,
    stepStrokeFinishedColor: COLORS.txt37B37D,
    stepStrokeUnFinishedColor: COLORS.txtAAAAAA,
    separatorFinishedColor: COLORS.txt37B37D,
    separatorUnFinishedColor: COLORS.txtAAAAAA,
    stepIndicatorFinishedColor: COLORS.txt37B37D,
    stepIndicatorUnFinishedColor: COLORS.txtFFFFFF,
    stepIndicatorCurrentColor: COLORS.txtFFFFFF,
    stepIndicatorLabelFontSize: 13,
    currentStepIndicatorLabelFontSize: 13,
    stepIndicatorLabelCurrentColor: COLORS.txt37B37D,
    stepIndicatorLabelFinishedColor: COLORS.txtFFFFFF,
    stepIndicatorLabelUnFinishedColor: COLORS.txtAAAAAA,
    labelColor: COLORS.txt999999,
    labelSize: 13,
    currentStepLabelColor: COLORS.txt37B37D
};
