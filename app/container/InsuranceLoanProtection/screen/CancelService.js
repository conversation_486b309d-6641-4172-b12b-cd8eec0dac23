import { StyleSheet, View, SafeAreaView, ScrollView, Alert, TextInput, Keyboard } from 'react-native';
import React, { useState, useEffect, useCallback } from 'react';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { Button, Icon, MyText, PickerSearch, TitleInput, hideBlockUI, showBlockUI } from '@components';
import { Color, helper } from '@common';
import { translate } from '@translate';
import ButtonCollection from '../component/ButtonCollection';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionCollectionCreator from '../../CollectionTransfer/action';
import AcceptCancel from '../../CollectionTransferManager/component/AcceptCancel';
import { useFocusEffect } from '@react-navigation/native';
import * as actionManagerSOCreator from "../../SaleOrderManager/action";
import * as actionSaleOrderCreator from "../../SaleOrderPayment/action";
import * as actionInsuranceLoanProtectionCreator from "../../InsuranceBrightside/action";
import * as actionCollectionManagerCreator from '../../CollectionTransferManager/action'
import CollectionStatusModal from '../component/Modal/CollectionStatusModal';

const CancelService = ({ route, actionCollection, dataInserAndCreateTicket, actionInsuranceLoanProtection, navigation, itemCatalog }) => {
    const [requestType, setRequestType] = useState('');
    const [reason, setReason] = useState('');
    const [statusTicket, setStatusTicket] = useState({});
    const [note, setNote] = useState("");
    const [dataRefund, setDataRefund] = useState({});
    const [airTimetransactionRFID, setAirTimetransactionRFID] = useState('');
    const [isVisibleModalCollection, setIsVisibleModalCollection] = useState(false);
    const [defaultRequestType, setDefaultRequestType] = useState({});
    const [defaultRequestReason, setDefaultRequestReason] = useState({});
    const { data } = route.params;
    const getTicketStatus = statusTicket?.STATUSID;
    const getStatusMess = statusTicket?.STATUSMESS;
    const { LISTUSERAPPROVE } = dataInserAndCreateTicket ?? '';
    const { SERVICEVOUCHERID, PRODUCTNAME, CUSTOMERNAME, TOTALAMOUNT, CUSTOMERPHONE, PHONENUMBER, AIRTIMETRANSACTIONID, PRODUCTID, INPUTTIME, AIRTIMETRANSACTIONTYPEID, PROCESSUSER, SALEORDERID, TICKETID } = data;
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? {};

    useEffect(() => {
        getDataInfoRefund();
    }, [actionInsuranceLoanProtection])

    useFocusEffect(
        useCallback(() => {
            if (dataInserAndCreateTicket) {
                actionCollection.cleardDataTicket();
            }
        }, [dataInserAndCreateTicket.STATUSID, actionCollection])
    );

    const getDataInfoRefund = () => {
        showBlockUI();
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
            productID: PRODUCTID,
            inputTime: INPUTTIME
        }
        actionInsuranceLoanProtection.getInfoRefund(data).then((reponse) => {
            hideBlockUI();
            setDataRefund(reponse);
            setDefaultRequestType(reponse?.RefundRequestType?.[0]);
            setDefaultRequestReason(reponse?.RefundRequestReason?.[0]);
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        navigation.goBack()
                        hideBlockUI();
                    }
                },
            ])
        });
    }


    const handleReplyTicket = (AIRTIMETRANSACTIONID) => {
        showBlockUI();
        actionCollection.createTicletServiceRequest(AIRTIMETRANSACTIONID).then((reponseDataTicket) => {
            hideBlockUI();
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI,
                },
                {
                    text: "Thử lại",
                    onPress: () => handleReplyTicket(AIRTIMETRANSACTIONID)
                }
            ])
        });
    }

    const handleQueryStatus = () => {
        showBlockUI();
        const { TICKETID, AIRTIMETRANSACTIONID } = dataInserAndCreateTicket ?? {};
        const data = {
            TICKETID: TICKETID,
            AIRTIMETRANSACTIONID: AIRTIMETRANSACTIONID,
        }
        actionInsuranceLoanProtection.checksSatusTicketService(data).then((reponseStatus) => {
            hideBlockUI();
            setStatusTicket(reponseStatus);

        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI,
                },
                {
                    text: "Thử lại",
                    onPress: () => { handleQueryStatus() }
                }
            ])
        });
    }

    const onCheckSaleOrder = () => {
        if (requestType == "" && dataRefund?.RefundRequestType?.length > 1) {
            Alert.alert("", "Vui lòng chọn loại yêu cầu huỷ");
            return false;
        }
        if (reason == "" && dataRefund?.RefundRequestReason?.length > 1) {
            Alert.alert("", "Vui lòng chọn loại lý do huỷ");
            return false;
        }
        return true;;
    }

    const onCancelSOAndCreateCM = () => {
        const isValidate = onCheckSaleOrder();
        if (isValidate) {
            Alert.alert("", "Bạn có chắc muốn huỷ đơn hàng này", [
                {
                    text: translate('saleOrderManager.btn_skip_uppercase'),
                    style: "cancel"
                },
                {
                    text: translate('saleOrderManager.btn_continue_uppercase'),
                    style: "default",
                    onPress: () => completeAndCreateCM()
                }
            ])
        }
    }

    const completeAndCreateCM = () => {
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
            airtimeTransactionID: AIRTIMETRANSACTIONID,
            totalRefundAmount: TOTALAMOUNT,
            rqRefundTypeID: dataRefund?.RefundRequestReason?.length == 1 ? defaultRequestType?.RefundRequestTypeID : requestType,
            reasonID: dataRefund?.RefundRequestReason?.length == 1 ? defaultRequestReason?.ReasonID : reason,
            refundNote: note,
            approvedUser: PROCESSUSER,
            approvedDate: INPUTTIME
        }
        actionInsuranceLoanProtection.createAirtimeRefund(data).then((reponse) => {
            hideBlockUI();
            setAirTimetransactionRFID(reponse?.AirTimetransactionRFID)
            setIsVisibleModalCollection(true);

        })
            .catch((msgError) => {
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }

    const onNavigationHistoryRefundMoney = () => {
        setIsVisibleModalCollection(false);
        navigation.navigate("RefundHistory", { SERVICEVOUCHERID })
    }

    const handleOrderStatusSuccess = () => {
        setIsVisibleModalCollection(false);
        navigation.navigate("RefundHistory", { SERVICEVOUCHERID })
    }

    return (
        <SafeAreaView style={styles.container}>
            <ScrollView>
                <View style={styles.itemCollect}>
                    <MyText
                        text={SERVICEVOUCHERID}
                        style={{
                            fontWeight: 'bold',
                            fontSize: 16,
                            color: COLORS.bg1E88E5
                        }}
                    />
                    <TextField
                        title={'Tên sản phẩm :'}
                        value={PRODUCTNAME}
                    />
                    <TextField
                        title={'IMEI :'}
                        value={PHONENUMBER}
                    />
                    <TextField
                        title={'Số điện thoại :'}
                        value={CUSTOMERPHONE}
                    />
                    <TextField
                        title={'Tên khách hàng :'}
                        value={CUSTOMERNAME}
                    />
                    <TextField
                        title={'Số tiền :'}
                        value={helper.formatMoney(TOTALAMOUNT)}
                    />
                    <TextField
                        title={'Ngày giao dịch :'}
                        value={INPUTTIME}
                    />
                </View>
                <View style={{
                    backgroundColor: COLORS.bg57a7ff,
                    height: 35,
                    color: COLORS.bg2FB47C,
                    justifyContent: 'center',
                    marginTop: 10
                }}>
                    <MyText
                        text={'Yêu cầu hoàn tiền'}
                        style={{
                            fontWeight: 'bold',
                            fontSize: 16,
                            color: COLORS.bgFFFFFF,
                            marginLeft: 5
                        }}
                    />
                </View>
                <View style={styles.requestRefund}>
                    <TitleInput
                        title={"Số tiền yêu cầu"}
                        styleInput={{
                            borderWidth: 1,
                            borderRadius: 4,
                            borderColor: COLORS.bdCCCCCC,
                            marginBottom: 5,
                            paddingHorizontal: 10,
                            backgroundColor: COLORS.bgFFFFFF,
                            paddingVertical: 8
                        }}
                        placeholder={translate("collection.enter_sender_phone")}
                        value={`${helper.formatMoney(TOTALAMOUNT)}`}
                        onChangeText={(text) => { }}
                        keyboardType="numeric"
                        returnKeyType="done"
                        blurOnSubmit
                        width={constants.width - 20}
                        height={40}
                        clearText={() => { }}
                        key="rechargerPhoneNumber"
                        isRequired={true}
                        editable={false}
                    />
                    <View style={{
                        marginTop: 10
                    }}>
                        <PickerSearch
                            title={"Loại yêu cầu"}
                            label={"RefundRequestTypeName"}
                            value={"RefundRequestTypeID"}
                            isRequired={true}
                            valueSelected={requestType}
                            data={(helper.IsNonEmptyArray(dataRefund?.RefundRequestType)) ? dataRefund?.RefundRequestType : []}
                            onChange={(item) => {
                                setRequestType(item?.RefundRequestTypeID);
                            }}
                            style={{
                                flex: 1,
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                height: 40,
                                borderRadius: 4,
                                borderWidth: 1,
                                borderColor: COLORS.bdE4E4E4,
                                width: constants.width - 20,
                                backgroundColor: COLORS.btnFFFFFF,
                                marginBottom: 10
                            }}
                            disabled={dataRefund?.RefundRequestType?.length == 1 ? true : false}
                            defaultLabel={dataRefund?.RefundRequestType?.length == 1 ? defaultRequestType.RefundRequestTypeName : 'Chọn loại yêu cầu'}
                        />
                    </View>
                    <PickerSearch
                        title={"Lý do"}
                        label={"ReasonName"}
                        value={"ReasonID"}
                        data={(helper.IsNonEmptyArray(dataRefund?.RefundRequestReason)) ? dataRefund?.RefundRequestReason : []}
                        valueSelected={reason}
                        onChange={(item) => {
                            setReason(item?.ReasonID);
                        }}
                        isRequired={true}

                        style={{
                            flex: 1,
                            flexDirection: "row",
                            justifyContent: "center",
                            alignItems: "center",
                            height: 40,
                            borderRadius: 4,
                            borderWidth: 1,
                            borderColor: COLORS.bdE4E4E4,
                            width: constants.width - 20,
                            backgroundColor: COLORS.btnFFFFFF,
                            marginBottom: 10
                        }}
                        disabled={dataRefund?.RefundRequestReason?.length == 1 ? true : false}
                        defaultLabel={dataRefund?.RefundRequestReason?.length == 1 ? defaultRequestReason?.ReasonName : translate('saleOrderManager.select_cancel_reason')} defaultRequestReason
                    />
                    <View style={styles.viewReason} >
                        <MyText text={"Ghi chú"} style={styles.textReason} />
                        <TextInput
                            style={styles.content}
                            value={String(note)}
                            onChangeText={(text) => {
                                setNote(text);
                            }}
                            placeholder={"Không bắt buộc nhập:"}
                            numberOfLines={6}
                            multiline={true}
                            textAlignVertical="top"
                            returnKeyType="done"
                            onSubmitEditing={Keyboard.dismiss}
                        />
                    </View>
                </View>
                <View>
                    {
                        helper.IsEmptyObject(dataInserAndCreateTicket) ?
                            <View>
                                <AcceptCancel
                                    title="QUẢN LÝ SIÊU THỊ XÁC NHẬN"
                                    onPress={() => handleReplyTicket(AIRTIMETRANSACTIONID)}
                                />
                            </View>
                            :
                            <View>
                                <View style={{
                                    padding: 10
                                }}>
                                    <View style={{
                                        backgroundColor: COLORS.bgFFFFFF,
                                        borderRadius: 7,
                                        padding: 10,
                                        shadowColor: COLORS.bg7F7F7F,
                                        shadowOffset: {
                                            width: 0,
                                            height: 0,
                                        },
                                        shadowOpacity: 0.5,
                                        shadowRadius: 1,
                                        elevation: 5,
                                    }}>
                                        <MyText
                                            text={"Yêu cầu hoàn tiền của giao dịch "}
                                            addSize={-1.5}
                                            style={{
                                                color: COLORS.txt333333,
                                                marginBottom: 10,
                                                fontSize: 15,
                                                marginTop: 10,
                                                marginLeft: 5
                                            }} >
                                            {
                                                <MyText
                                                    text={`[${SERVICEVOUCHERID}]`}
                                                    addSize={-1.5}
                                                    style={{
                                                        color: COLORS.txtD0021B,
                                                        fontSize: 15
                                                    }}
                                                >
                                                    {
                                                        <MyText
                                                            text={` của bạn đã được gửi thông báo đến app X-Work của quản lý siêu thị có chấm công trong ca gồm: ${LISTUSERAPPROVE}. Vui lòng chờ quản lý siêu thị xác nhận trên App X-Work!`}
                                                            addSize={-1.5}
                                                            style={{
                                                                color: COLORS.txt333333,
                                                                fontSize: 15
                                                            }}
                                                        />
                                                    }
                                                </MyText>
                                            }
                                        </MyText>
                                        <View style={{
                                            flexDirection: 'row'
                                        }}>
                                            <Icon
                                                iconSet={'MaterialIcons'}
                                                name={'info-outline'}
                                                color={COLORS.bgFF0000}
                                                size={18}
                                            />
                                            <MyText
                                                text={"Ticket có hiệu lực trong 10 phút"}
                                                addSize={-1.5}
                                                style={{
                                                    color: COLORS.bgFF0000,
                                                    fontSize: 15,
                                                    marginLeft: 5,
                                                    fontStyle: 'italic'
                                                }}
                                            />
                                        </View>
                                    </View>
                                    <View style={{
                                        flexDirection: 'row',
                                        backgroundColor: COLORS.bgFFFFFF,
                                        borderRadius: 7,
                                        padding: 10,
                                        alignItems: 'center',
                                        shadowColor: COLORS.bg7F7F7F,
                                        shadowOffset: {
                                            width: 0,
                                            height: 0,
                                        },
                                        shadowOpacity: 0.5,
                                        shadowRadius: 1,
                                        elevation: 5,
                                        marginTop: 10
                                    }}>
                                        <MyText
                                            text={"Trạng thái ticket:"}
                                            addSize={-1.5}
                                            style={{
                                                color: COLORS.txt333333,
                                                marginBottom: 10,
                                                fontSize: 15,
                                                marginTop: 10,
                                                fontWeight: 'bold'
                                            }} />
                                        <MyText
                                            text={getTicketStatus != null ? getStatusMess : "Đã gửi cho QLST"}
                                            addSize={-1.5}
                                            style={{
                                                color: (getTicketStatus != "APPROVE" && getTicketStatus != null) ? COLORS.bgFF0000 : COLORS.bg00AAFF,
                                                fontSize: 15,
                                                fontWeight: 'bold',
                                                marginLeft: 5
                                            }}
                                        />
                                    </View>
                                </View>
                                <View style={{
                                    padding: 10
                                }}>
                                    {

                                        getTicketStatus == "APPROVE" ?
                                            <ButtonAction
                                                onPress={onCancelSOAndCreateCM}
                                                disabled={false}
                                            />
                                            :
                                            <View style={{
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                flexDirection: 'row'
                                            }}>
                                                <ButtonCollection
                                                    onPress={() => handleReplyTicket(AIRTIMETRANSACTIONID)}
                                                    disabled={helper.IsEmptyObject(statusTicket) || getTicketStatus == undefined || getTicketStatus == "WAITING" ? true : false}
                                                    title={"GỬI LẠI TICKET"}
                                                    iconSet={"Ionicons"}
                                                    nameIcon={"reload"}
                                                    style={{
                                                        backgroundColor: COLORS.bg00A98F
                                                    }}
                                                    opacity={helper.IsEmptyObject(statusTicket) || getTicketStatus == undefined || getTicketStatus == "WAITING" ? 0.5 : 1}
                                                />
                                                <View style={{ flex: 1 }} />
                                                <ButtonCollection
                                                    onPress={() => handleQueryStatus()}
                                                    title={"KIỂM TRA KẾT QUẢ"}
                                                    iconSet={"Ionicons"}
                                                    nameIcon={"search"}
                                                    style={{
                                                        backgroundColor: COLORS.bg1E88E5,
                                                    }}
                                                />
                                            </View>
                                    }
                                </View>
                            </View>
                    }
                </View>
                {
                    isVisibleModalCollection && <CollectionStatusModal
                        isVisible={isVisibleModalCollection}
                        SaleOrderID={SALEORDERID}
                        onClose={() => onNavigationHistoryRefundMoney()}
                        onSuccess={() => handleOrderStatusSuccess()}
                        airTimetransactionRFID={airTimetransactionRFID}
                    />
                }
            </ScrollView>
        </SafeAreaView>
    )
}

const mapStateToProps = (state) => ({
    saleOrder: state.managerSOReducer.infoSODelete,
    dataInserAndCreateTicket: state.collectionReducer.dataInserAndCreateTicket,
    itemCatalog: state.collectionReducer.itemCatalog
});

const mapDispatchToProps = (dispatch) => ({
    actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
    actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
    actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
    actionInsuranceLoanProtection: bindActionCreators(actionInsuranceLoanProtectionCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(CancelService);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    itemCollect: {
        width: constants.width - 10,
        backgroundColor: COLORS.bgF0F0F0,
        marginTop: 10,
        alignSelf: 'center',
        borderRadius: 10,
        paddingVertical: 10,
        paddingHorizontal: 10
    },
    content: {
        borderWidth: 1,
        height: 100,
        borderColor: Color.hiddenGray,
        padding: 10,
    },
    requestRefund: {
        width: constants.width,
        alignSelf: 'center',
        paddingVertical: 10,
        paddingHorizontal: 10,
    }
})

const TextField = ({ title, value }) => {
    return (
        <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            paddingVertical: 10
        }}>
            <MyText
                text={title}
                style={{
                    flex: 1,
                    color: COLORS.bg8E8E93
                }}
            />
            <MyText
                text={value}

                style={{
                    flex: 1,
                    color: COLORS.bg000000,
                    textAlign: 'right'
                }}
            />
        </View>
    )
}

const ButtonAction = ({ disabled, onPress }) => {
    return (
        <View style={{
            alignItems: "center",
            width: constants.width,
            paddingVertical: 10
        }}>
            <Button
                text={translate('saleOrderManager.btn_cancel_export_request')}
                styleContainer={{
                    backgroundColor: COLORS.btn288AD6,
                    borderColor: COLORS.bd288AD6,
                    borderWidth: 1,
                    borderRadius: 4,
                    paddingVertical: 10,
                    paddingHorizontal: 20,
                    opacity: disabled ? 0.5 : 1
                }}
                styleText={{
                    color: COLORS.txtFFFFFF,
                }}
                onPress={onPress}
                disabled={disabled}
            />
        </View>
    );
}

