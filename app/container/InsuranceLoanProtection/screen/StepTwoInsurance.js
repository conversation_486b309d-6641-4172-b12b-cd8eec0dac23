import { StyleSheet, Text, View, TouchableOpacity, SafeAreaView, Alert } from 'react-native';
import React, { useEffect, useState } from 'react';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { MyText, PickerLocation, TitleInput } from '@components';
import * as actionInsuranceLoanProtectionCreator from '../action';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { helper } from '@common';
import { translate } from '@translate';

const StepTwoInsurance = ({ dataUpdateCustomer, actionInsuranceLoanProtection, dataDetailSO, dataSO, dataProvince, onNextStep, onBack, route }) => {
    const [customerName, setCustomerName] = useState('');
    const [customerPhone, setCustomerPhone] = useState('');
    const [customerHouseNumber, setCustomerHouseNumber] = useState('')
    const [provinceID, setProvinceID] = useState(0);
    const [district, setDistrict] = useState([]);
    const [districtID, setDistrictID] = useState(0);
    const [ward, setWard] = useState([]);
    const [wardID, setWardID] = useState(0);
    const [isShowIndicator, setIsShowIndicator] = useState(false);
    const [indexPager, setIndexPager] = useState(0);
    const [customer, setCustomer] = useState({
        DistrictID: 0,
        WardID: 0,
        ProvinceID: 0
    });
    const [provinceName, setProvinceName] = useState('');
    const [districName, setDistricName] = useState();
    const [wardName, setWardName] = useState('');
    const getProvince = () => {
        actionInsuranceLoanProtection.getDataProvince();
    }

    const { CUSTOMERNAME, CUSTOMERPHONE } = dataSO ?? {};

    useEffect(() => {
        if (!!CUSTOMERNAME || !!CUSTOMERPHONE) {
            setCustomerName(CUSTOMERNAME)
            setCustomerPhone(CUSTOMERPHONE)
        }
    }, [CUSTOMERNAME, CUSTOMERPHONE])

    const getDataDistrict = (provinceID) => {
        setIsShowIndicator(true);
        actionInsuranceLoanProtection.getDistrict(provinceID).then(data => {
            setDistrict(data);
            setIndexPager(1);
            setIsShowIndicator(false);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: () => setIsShowIndicator(false)
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => getDataDistrict(provinceID)
                    }
                ]
            )
        });
    }

    const getDataWard = (provinceID, districtID) => {
        setIsShowIndicator(true);
        actionInsuranceLoanProtection.getWard(provinceID, districtID).then(data => {
            setWard(data);
            setIndexPager(2);
            setIsShowIndicator(false);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: () => setIsShowIndicator(false)
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => getDataWard(provinceID, districtID)
                    }
                ]
            )
        });
    }

    useEffect(
        getProvince,
        []
    )
    const effectChangeProvince = () => {
        if (provinceID > 0) {
            getDataDistrict(provinceID);
        }
    }

    const effectChangeDistrict = () => {
        if (districtID > 0) {
            getDataWard(provinceID, districtID);
        }
    }

    useEffect(
        effectChangeProvince,
        [provinceID]
    )

    useEffect(
        effectChangeDistrict,
        [districtID]
    )

    const handleOnNextStep = () => {
        if (customerName == "") {
            Alert.alert("", "Vui lòng nhập tên khách hàng!")
            return;
        }
        else if (customerPhone == "") {
            Alert.alert("", "Vui lòng nhập số điện thoại khách hàng!")
            return;
        }
        else if (customerPhone.length < 10) {
            Alert.alert("", "Vui lòng nhập số nhà!")
            return;
        }
        else if (customerHouseNumber == "") {
            Alert.alert("", "Vui lòng nhập số nhà !")
            return;
        }
        else if (helper.IsEmptyObject(customer) || customer.DistrictID == 0 || customer.ProvinceID == 0 || customer.WardID == 0) {
            Alert.alert("", "Vui lòng chọn Tỉnh/Thành - Quận/Huyện - Phường/Xã")
            return;
        }
        else {
            actionInsuranceLoanProtection.updateCustomerAirtimeService({
                ...dataUpdateCustomer,
                customerPhone,
                customerName,
                customerHouseNumber,
                customer,
                provinceName,
                districName,
                wardName
            })
            onNextStep();
        }
    }
    return (
        <View style={{
            flex: 1,
            backgroundColor: COLORS.bgFFFFFF,
            width: constants.width
        }}>
            <SafeAreaView style={{ flex: 1, }}>
                <KeyboardAwareScrollView
                    contentContainerStyle={{
                        flex: 1,
                        paddingHorizontal: 10,
                        paddingTop: 30
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                >
                    <MyText
                        text={'Thông tin khách hàng'}
                        style={{
                            fontWeight: 'bold',
                        }}
                    />
                    <View style={{
                        paddingTop: 10
                    }}>
                        <TitleInput
                            title={"Họ tên khách hàng"}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                backgroundColor: COLORS.bgFFFFFF,
                                paddingVertical: 8
                            }}
                            placeholder={"Vui lòng nhập họ tên khách hàng: "}
                            value={customerName}
                            onChangeText={(text) => {
                                setCustomerName(text)
                            }}
                            keyboardType="default"
                            returnKeyType="done"
                            blurOnSubmit
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setCustomerName('')
                            }}
                            key="customerName"
                            isRequired={true}
                        />
                        <TitleInput
                            title={"Số điện thoại khách hàng"}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                backgroundColor: COLORS.bgFFFFFF,
                                paddingVertical: 8
                            }}
                            placeholder={"Vui lòng nhập số điện thoại khách hàng: "}
                            value={customerPhone}
                            onChangeText={(text) => {
                                const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                const isValidate = regExpPhone.test(text) || (text == "");
                                if (isValidate) {
                                    setCustomerPhone(text)
                                }
                            }}
                            keyboardType="numeric"
                            returnKeyType="done"
                            blurOnSubmit
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setCustomerPhone('')
                            }}
                            key="customerPhoneNumber"
                            isRequired={true}
                        />
                    </View>
                    <View style={{
                        paddingTop: 10
                    }}>
                        <TitleInput
                            title={"Số nhà"}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 10,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                backgroundColor: COLORS.bgFFFFFF,
                                paddingVertical: 8,
                                width: '100%',
                                marginTop: 7

                            }}
                            placeholder={"Vui lòng nhập số nhà: "}
                            value={customerHouseNumber}
                            onChangeText={(text) => {
                                setCustomerHouseNumber(text)
                            }}
                            keyboardType="default"
                            returnKeyType="done"
                            blurOnSubmit
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setCustomerHouseNumber('')
                            }}
                            key="customerName"
                            isRequired={true}
                        >

                        </TitleInput>
                    </View>
                    <View style={{ marginTop: 5 }}>
                        <PickerLocation
                            dataProvince={{
                                data: dataProvince,
                                id: "provinceID",
                                value: "provinceName"
                            }}
                            dataDistrict={{
                                data: district,
                                id: "districtID",
                                value: "districtName"
                            }}
                            dataWard={{
                                data: ward,
                                id: "wardID",
                                value: "wardName"
                            }}
                            wardID={wardID}
                            districtID={districtID}
                            provinceID={provinceID}
                            onSelectProvince={(item) => {
                                setProvinceName(item.provinceName)
                                setProvinceID(item.provinceID);
                                setDistrictID(0);
                                setWardID(0);
                            }}
                            onSelectDistrict={(item) => {
                                setDistricName(item.districtName)
                                setDistrictID(item.districtID);
                                setWardID(0);
                            }}
                            onSelectWard={(item) => {
                                if (item.wardID != wardID) {
                                    setWardName(item.wardName)
                                    setWardID(item.wardID);
                                    setCustomer({
                                        ...customer,
                                        ProvinceID: provinceID,
                                        DistrictID: districtID,
                                        WardID: item.wardID
                                    })
                                }
                            }}
                            indexPager={indexPager}
                            onShowPicker={(index) => {
                                setIndexPager(index);
                            }}
                            updatePager={(index) => {
                                setIndexPager(index);
                            }}
                            isShowIndicator={isShowIndicator}
                        />
                    </View>
                    <View style={{
                        width: '100%',
                        height: 50,
                        flexDirection: 'row',
                        paddingHorizontal: 10
                    }}>
                        <TouchableOpacity
                            onPress={onBack}
                            style={{
                                width: 120,
                                height: 50,
                                borderRadius: 18,
                                borderWidth: 2,
                                borderColor: COLORS.bg00A98F,
                                alignItems: "center",
                                justifyContent: "center"
                            }}>
                            <MyText
                                text={'QUAY LẠI'}
                                style={{
                                    fontWeight: 'bold',
                                    color: COLORS.bg00A98F
                                }}
                            />
                        </TouchableOpacity>
                        <View style={{
                            width: 10
                        }} />
                        <TouchableOpacity
                            onPress={() => handleOnNextStep()}
                            style={{
                                backgroundColor: 'pink',
                                flex: 1,
                                height: 50,
                                borderRadius: 18,
                                alignItems: "center",
                                justifyContent: "center",
                                backgroundColor: COLORS.bgF49B0C
                            }}>
                            <MyText
                                text={'BƯỚC TIẾP THEO'}
                                style={{
                                    fontWeight: 'bold',
                                    color: COLORS.bgFFFFFF
                                }}
                            />
                        </TouchableOpacity>
                    </View>
                </KeyboardAwareScrollView>

            </SafeAreaView>
        </View >
    )
}

const mapStateToProps = function (state) {
    return {
        itemAirtimeCode: state.insuranceLoanProtectionReducer.itemAirtimeCode,
        dataProvince: state.insuranceLoanProtectionReducer.dataProvince,
        dataDetailSO: state.insuranceLoanProtectionReducer.dataDetailSO,
        dataUpdateCustomer: state.insuranceLoanProtectionReducer.dataUpdateCustomer,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsuranceLoanProtection: bindActionCreators(actionInsuranceLoanProtectionCreator, dispatch),
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(StepTwoInsurance);


const styles = StyleSheet.create({})