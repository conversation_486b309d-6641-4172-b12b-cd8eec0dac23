import { StyleSheet, View, TouchableOpacity, ScrollView, Alert } from 'react-native'
import React, { useState } from 'react'
import Accordion from '../component/Accordion'
import { COLORS } from '@styles'
import { constants } from '@constants'
import { MyText, hideB<PERSON>UI, showBlockUI } from '@components'
import { helper } from '@common'
import * as actionPaymentOrderCreator from "../../SaleOrderPayment/action";
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import * as actionInsuranceLoanProtectionCreator from '../action';
import { translate } from '@translate'

const StepThreeInsurance = ({
    onBack,
    itemCatalog,
    onNextStep,
    dataUpdateCustomer,
    dataValidateService,
    itemAirtimeCode,
    dataGetPrice,
    dataGetPromotion,
    actionInsuranceLoanProtection,
    dataSO,
    actionPaymentOrder,
    navigation
}) => {
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? '';
    const { customerHouseNumber, customerName, customerPhone, InsuranceMonth, InsuranceProductID, customer, wardName, districName, provinceName } = dataUpdateCustomer;
    const {
        Imei,
        ProfileID,
        StartDateInsLoan,
        EndDateInsLoan,
        ListInsProgram,
        InsCustomerID,
        MainProductID,
        PaymentAmountMonthly,
        TermLoan,
        TotalPrePaid,
        CustomerIDCard,
        CustomerBirthday,
        LoanAmount
    } = dataValidateService ?? {};
    const { AirTimeTransactionTypeName, AirTimeTransactionTypeID } = itemAirtimeCode ?? '';
    const { FeeInfo, PriceInfo, TotalAmount } = dataGetPrice ?? {};
    const { SaleOrderID } = dataSO ?? {};
    const { ExtraData } = dataGetPromotion ?? {};
    const { PriceVAT, Amount, SalePrice, InputPrice } = PriceInfo ?? '';
    const { Fee } = FeeInfo ?? '';
    const { ProductPromotion } = ExtraData ?? {};
    const { ListInsProgramDetail, InsProgramName } = ListInsProgram?.[0] ?? {};
    const { InsuranceID, InsProgramID, InsuranceApplyID } = ListInsProgramDetail?.[0]
    const { DistrictID, ProvinceID, WardID } = customer
    const { partnerInstallment, setPartnerInstallment } = useState([])
    console.log(dataValidateService, 'log aaaaa');
    const handleAddCart = () => {
        showBlockUI();
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AirTimeTransactionTypeID,
            airTimeTransactionBO: {
                "productid": InsuranceProductID,
                "amount": Amount,
                "fee": Fee,
                "phonenumber": Imei,
                "inputPrice": InputPrice,
                "salePrice": SalePrice,
                "customerName": customerName,
                "customerAddress": customerHouseNumber,
                "customerPhone": customerPhone,
                "districtID": DistrictID,
                "provinceID": ProvinceID,
                "wardID": WardID,
                "apartmentNumberAddress": customerHouseNumber
            },
            addToSaleOrderCart: PriceVAT,
            insuranceID: InsuranceID,
            insCustomerID: InsCustomerID,
            insProgramID: InsProgramID,
            insuranceMonth: InsuranceMonth,
            insuranceApplyID: InsuranceApplyID,
            mainSaleOrderID: SaleOrderID,
            mainProductID: MainProductID,
            productPromotion: ProductPromotion,

            voucherConcern: "",
            paymentAmountMonthly: PaymentAmountMonthly,
            profileID: ProfileID,
            termLoan: TermLoan,
            totalPrePaid: TotalPrePaid,
            customerIDCard: CustomerIDCard,
            customerBirthday: CustomerBirthday,
            startDate: StartDateInsLoan,
            endDate: EndDateInsLoan

        }
        actionInsuranceLoanProtection.addToSaleOrderCart(data).then((reponse) => {
            hideBlockUI()
            goToPaymentSO(reponse);
        }).catch((error) => {
            Alert.alert(translate("common.notification_uppercase"), error, [
                {
                    text: translate("common.btn_close"),
                    onPress: hideBlockUI,
                },
            ]);
        });
    }

    const goToPaymentSO = (rpSaleOrderCart) => {
        const dataSaleOrderCart = rpSaleOrderCart.object;
        const SaleOrders = dataSaleOrderCart.SaleOrders[0];
        const { SaleOrderID } = SaleOrders;
        actionPaymentOrder
            .setDataSO({
                SaleOrderID: SaleOrderID,
                SaleOrderTypeID: 1000,
            })
            .then((success) => {
                hideBlockUI();
                navigation.navigate("SaleOrderPayment");
                actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
                actionPaymentOrder.getReportPrinterSocket(1000);
                actionPaymentOrder.getDataQRTransaction(SaleOrderID);
            });
    };

    return (
        <View style={{
            flex: 1
        }}>
            <ScrollView contentContainerStyle={{
                alignItems: 'center',
            }}>
                <Accordion
                    status={true}
                    title={'Thông tin người mua bảo hiểm'}
                    Children={
                        <View style={{
                            backgroundColor: COLORS.bgF0F0F0,
                            width: constants.width - 25,
                            paddingHorizontal: 10,
                            paddingBottom: 10
                        }}>
                            <View style={{
                                width: '100%',
                                borderWidth: 1,
                                alignSelf: 'center',
                                borderColor: 'gray'
                            }} />
                            <TextField
                                title={'Họ và Tên'}
                                value={customerName}
                            />
                            <TextField
                                title={'Số điện thoại'}
                                value={customerPhone}
                            />
                            <TextField
                                title={'Địa chỉ'}
                                value={`${customerHouseNumber}, ${wardName}, ${districName}, ${provinceName}`}
                            />
                        </View>
                    }
                />
                <Accordion
                    status={true}

                    title={'Thông tin gói bảo hiểm'}
                    Children={
                        <View style={{
                            backgroundColor: COLORS.bgF0F0F0,
                            width: constants.width - 25,
                            paddingHorizontal: 10,
                            paddingBottom: 10
                        }}>
                            <View style={{
                                width: '100%',
                                borderWidth: 1,
                                alignSelf: 'center',
                                borderColor: 'gray'
                            }} />
                            <TextField
                                title={'Gói bảo hiểm'}
                                value={InsProgramName}
                            />
                            <TextField
                                title={'Thời gian bảo hiểm'}
                                value={`${TermLoan} tháng`}
                            />
                            <TextField
                                title={'Ngày bắt đầu hiệu lực'}
                                value={StartDateInsLoan}
                            />
                            <TextField
                                title={'Ngày hết hạn'}
                                value={EndDateInsLoan}
                            />
                        </View>
                    }
                />
                <Accordion
                    status={true}

                    title={'Thông tin hợp đồng bảo hiểm'}
                    Children={
                        <View style={{
                            backgroundColor: COLORS.bgF0F0F0,
                            width: constants.width - 25,
                            paddingHorizontal: 10,
                            paddingBottom: 10
                        }}>
                            <View style={{
                                width: '100%',
                                borderWidth: 1,
                                alignSelf: 'center',
                                borderColor: 'gray'
                            }} />
                            <TextField
                                title={'Hợp đồng bảo hiểm'}
                                value={ProfileID}
                            />
                            <TextField
                                title={'Số tiền bảo hiểm'}
                                value={helper.formatMoney(LoanAmount)}
                            />
                        </View>
                    }
                />
                <Accordion
                    status={true}
                    titleColor='white'
                    iconColor='white'
                    title={'Thông tin thanh toán bảo hiểm'}
                    backgroundColor={COLORS.bg2FB47C}
                    Children={
                        <View style={{
                            backgroundColor: COLORS.bg2FB47C,
                            width: constants.width - 25,
                            paddingHorizontal: 10,
                            paddingBottom: 10
                        }}>
                            <View style={{
                                width: '100%',
                                borderWidth: 1,
                                alignSelf: 'center',
                                borderColor: 'white'
                            }} />
                            <TextField
                                title={'Phí bảo hiểm'}
                                value={helper.formatMoney(TotalAmount)}
                                color={'white'}
                            />
                            <TextField
                                title={'Tổng cộng'}
                                color={'white'}
                                value={helper.formatMoney(TotalAmount)}
                            />
                        </View>
                    }
                />
                <View style={{
                    width: constants.width,
                    height: 50,
                    flexDirection: 'row',
                    paddingHorizontal: 10,
                    marginTop: 10
                }}>
                    <TouchableOpacity
                        onPress={onBack}
                        style={{
                            width: 120,
                            height: 50,
                            borderRadius: 18,
                            borderWidth: 2,
                            borderColor: COLORS.bg00A98F,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: 'white'

                        }}>
                        <MyText
                            text={'QUAY LẠI'}
                            style={{
                                fontWeight: 'bold',
                                color: COLORS.bg00A98F
                            }}
                        />
                    </TouchableOpacity>
                    <View style={{
                        width: 10
                    }} />
                    <TouchableOpacity
                        onPress={() => handleAddCart()}
                        style={{
                            backgroundColor: 'pink',
                            flex: 1,
                            height: 50,
                            borderRadius: 18,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: COLORS.bgF49B0C
                        }}>
                        <MyText
                            text={'BƯỚC TIẾP THEO'}
                            style={{
                                fontWeight: 'bold',
                                color: COLORS.bgFFFFFF
                            }}
                        />
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </View>
    )
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        dataUpdateCustomer: state.insuranceLoanProtectionReducer.dataUpdateCustomer,
        dataValidateService: state.insuranceLoanProtectionReducer.dataValidateService,
        itemAirtimeCode: state.insuranceLoanProtectionReducer.itemAirtimeCode,
        dataGetPrice: state.insuranceLoanProtectionReducer.dataGetPrice,
        dataGetPromotion: state.insuranceLoanProtectionReducer.dataGetPromotion,
        dataSO: state.saleOrderPaymentReducer.dataSO,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsuranceLoanProtection: bindActionCreators(actionInsuranceLoanProtectionCreator, dispatch),
        actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(StepThreeInsurance);

const styles = StyleSheet.create({})

const TextField = ({ title, value, color }) => {
    return (
        <View style={{
            width: '100%',
            flexDirection: 'row',
            justifyContent: 'space-between',
            paddingVertical: 7
        }}>
            <MyText
                text={`${title}: `}
                style={{
                    color: color
                }}
            />
            <MyText
                text={value}
                style={{
                    color: color
                }}
            />
        </View>
    )
}