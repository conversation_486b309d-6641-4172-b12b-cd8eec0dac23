import { SafeAreaView, StyleSheet, View, FlatList } from 'react-native'
import React, { useEffect, useState } from 'react'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { TouchableOpacity } from 'react-native';
import { Image } from 'react-native';
import { BaseLoading, Icon, MyText } from '@components';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionInsuranceExtendedCreator from "../action";
import * as actionPaymentOrderCreator from "../../SaleOrderPayment/action";
import InsuranceBenefits from '../component/Modal/InsuranceBenefits';

const ReplacementService = ({
    navigation,
    dataServiceList,
    stateServiceList,
    actionInsuranceLoanProtection,
    itemCatalog
}) => {
    console.log('dataServiceList', dataServiceList);
    const [isVisibleModal, setisVisibleModal] = useState(false);
    const [updateItem, setUpdateItem] = useState({});
    const {
        ServiceCategoryID,
        AirtimeServiceGroupID
    } = itemCatalog ?? {};
    useEffect(() => {
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID
        };
        actionInsuranceLoanProtection.getServiceList(data);
    }, [actionInsuranceLoanProtection])

    const renderItem = ({ item, index }) => {
        setUpdateItem(item)
        return (
            <View style={{
                padding: 10
            }}>
                <View style={{
                    width: '100%',
                    height: 90,
                    backgroundColor: COLORS.txt0099E5,
                    borderRadius: 10,
                    shadowColor: '#000',
                    shadowOffset: {
                        width: 0,
                        height: 2
                    },
                    shadowOpacity: 0.25,
                    shadowRadius: 4,
                    elevation: 5,
                }}>
                    <TouchableOpacity
                        onPress={() => {
                            // actionInsuranceLoanProtection.clear_data_validate_service_request()
                            actionInsuranceLoanProtection.updateItemAirtimeCode(item)
                            navigation.navigate("SaleOrderInsuraneLoanProtection", { ServiceCategoryID, AirtimeServiceGroupID, item })
                        }
                        }
                        style={{
                            width: '100%',
                            height: 90,
                            borderRadius: 10,
                            backgroundColor: COLORS.bgFFFFFF,
                            flexDirection: 'row',
                            alignItems: 'center',
                            activeOpacity: 0.8
                        }}>
                        <Image
                            resizeMode='contain'
                            style={{
                                width: 90,
                                height: 50,
                            }}
                            source={{
                                uri: item.Logo
                            }}
                        />
                        <MyText
                            style={{
                                color: COLORS.txt000000,
                                width: 180,
                                fontSize: 14,
                                fontWeight: 'bold',
                            }}
                            text={item.AirTimeTransactionTypeName}
                        />
                    </TouchableOpacity>
                </View>
            </View>

        );
    };
    const preGetServiceList = (ServiceCategoryID, AirtimeServiceGroupID) => {
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID
        };
        actionInsuranceLoanProtection.getServiceList(data);
    }
    const DATA_LIST_MENU = [
        {
            title: 'Quyền lợi bảo hiểm',
            type: 'FontAwesome',
            name: 'info',
            size: 17,
            onAction: () => setisVisibleModal(true)
        },
        {
            title: 'Biểu phí',
            type: 'FontAwesome',
            name: 'dollar-sign',
            size: 18
        },
        {
            title: 'Lịch sử hoàn tiền',
            type: 'MaterialCommunityIcons',
            name: 'cash-refund',
            size: 22
        },
        {
            title: 'Chỉnh sửa bảo hiểm',
            type: 'FontAwesome',
            name: 'edit',
            size: 17
        },
        {
            title: 'Lịch sử đơn hàng',
            type: 'MaterialCommunityIcons',
            name: 'history',
            size: 25
        },

    ]
    return (
        <View
            style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF,
            }}
        >
            <KeyboardAwareScrollView
                contentContainerStyle={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView
                    style={{
                        alignItems: "center",
                        flex: 1,
                    }}
                >
                    <BaseLoading
                        isLoading={stateServiceList.isFetching}
                        isError={stateServiceList.isError}
                        isEmpty={stateServiceList.isEmpty}
                        textLoadingError={stateServiceList.description}
                        onPressTryAgains={() => {
                            preGetServiceList(ServiceCategoryID, AirtimeServiceGroupID)
                        }}
                        content={
                            <View style={{
                                justifyContent: 'space-between',
                                height: '100%'
                            }}>
                                <FlatList
                                    style={{
                                        backgroundColor: COLORS.bgFFFFFF,
                                        padding: 5,
                                        flex: 1,
                                    }}
                                    data={dataServiceList}
                                    keyExtractor={(item, index) => `${index}`}
                                    renderItem={(item) => renderItem(item)}
                                />
                                <View style={{
                                    flex: 1,
                                    backgroundColor: COLORS.bgFFFFFF,
                                    shadowColor: '#000',
                                }}>
                                    <MyText
                                        text={'Thông tin hỗ trợ'}
                                        style={{
                                            padding: 10,
                                            fontSize: 18,
                                            fontWeight: '600'
                                        }}
                                    />
                                    <View style={{
                                        flex: 1,
                                        flexDirection: 'row',
                                        flexWrap: 'wrap',
                                        justifyContent: 'space-between',
                                        paddingHorizontal: 10
                                    }}>
                                        {DATA_LIST_MENU.map((item, index) => {
                                            return (
                                                <TouchableOpacity
                                                    onPress={item.onAction}
                                                    style={{
                                                        width: constants.width * 0.8 / 4,
                                                        height: 80,
                                                        backgroundColor: 'pink',
                                                        marginTop: 10,
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        borderRadius: 15,
                                                        shadowColor: "#000",
                                                        shadowOffset: {
                                                            width: 0,
                                                            height: 2,
                                                        },
                                                        shadowOpacity: 0.25,
                                                        shadowRadius: 3.84,

                                                        elevation: 5,
                                                        backgroundColor: COLORS.bgFFFFFF,
                                                        paddingHorizontal: 5
                                                    }}>
                                                    <View style={{
                                                        height: '50%',
                                                        width: '100%',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                    }}>
                                                        <Icon
                                                            iconSet={item.type}
                                                            name={item.name}
                                                            size={item.size}
                                                            color={'gray'}
                                                        />
                                                    </View>
                                                    <View style={{
                                                        height: '50%',
                                                        width: '100%',
                                                    }}>
                                                        <MyText
                                                            text={item.title}
                                                            style={{
                                                                textAlign: 'center',
                                                                color: 'gray'
                                                            }}
                                                        />
                                                    </View>
                                                </TouchableOpacity>
                                            )
                                        })}
                                    </View>
                                </View>
                            </View>
                        }
                    />
                </SafeAreaView>
                {
                    isVisibleModal && <InsuranceBenefits
                        imageURL={updateItem?.InsBenefits}
                        isVisible={isVisibleModal}
                        hideModal={() => {
                            setisVisibleModal(false)

                        }}

                    />}
            </KeyboardAwareScrollView>

        </View>
    )
}

const mapStateToProps = function (state) {
    return {
        dataServiceList: state.insuranceLoanProtectionReducer.dataServiceList,
        stateServiceList: state.insuranceLoanProtectionReducer.stateServiceList,
        itemCatalog: state.collectionReducer.itemCatalog,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsuranceLoanProtection: bindActionCreators(actionInsuranceExtendedCreator, dispatch),
        actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(ReplacementService)

const styles = StyleSheet.create({})