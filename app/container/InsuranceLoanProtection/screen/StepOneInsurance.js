import { Alert, FlatList, Pressable, SafeAreaView, StyleSheet, Text, View, TouchableOpacity } from 'react-native'
import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionInsuranceLoanProtectionCreator from '../action';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Accordion from '../component/Accordion';
import TextView from '../component/TextView';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { MyText, PickerSearch, hideBlockUI, showBlockUI } from '@components';
import { dateHelper, helper } from '@common';

const StepOneInsurance = ({
    actionInsuranceLoanProtection,
    itemCatalog,
    dataValidateService,
    dataSO,
    itemAirtimeCode,
    onNextStep,
    dataGetPrice
}) => {
    const [chooseInsurance, setChooseInsurance] = useState('');
    const [listInsProgramDetail, setListInsProgramDetail] = useState('');
    const [updateItem, setUpdateItem] = useState({});
    const { PROFILEID, TERMLOAN, SALEORDERID, LOANAMOUNT } = dataSO ?? {};
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? '';
    const { AirTimeTransactionTypeID } = itemAirtimeCode ?? '';
    const { StartDateInsLoan, EndDateInsLoan, ProductID } = dataValidateService ?? {};
    const [updateMonth, setUpdateMonth] = useState([]);
    const { TotalAmount } = dataGetPrice ?? {};
    const [InsuranceProductID, setUpdateInsuranceProductID] = useState('')
    const [showInfoInsurance, setShowInfoInsurance] = useState(false);
    useEffect(() => {
        setShowInfoInsurance(false)
    }, [])
    useEffect(() => {
        if (listInsProgramDetail.length > 0) {
            setUpdateMonth(listInsProgramDetail)
        }
    }, [listInsProgramDetail]);
    const { InsuranceMonth } = updateItem;

    const renderItemMonth = ({ item, index }) => {
        console.log(item, 'log itemmmmm');
        return (
            <Pressable
                onPress={() => onClickItem(item, index)}
                key={item.id}
                disabled={listInsProgramDetail?.length == 1}
                style={{
                    margin: 5,
                    backgroundColor: item.selected || listInsProgramDetail?.length == 1 ? COLORS.bg2FB47C : COLORS.bgFFFFFF,
                    borderRadius: 20,
                    padding: 10,
                    width: "30%",
                    alignItems: 'center',
                    shadowColor: '#000',
                    shadowOffset: {
                        width: 0,
                        height: 2,
                    },
                    shadowOpacity: 0.25,
                    shadowRadius: 4,
                    elevation: 5,
                }}
            >
                <View
                    style={{
                        flexDirection: "row",
                        alignItems: "center",
                    }}
                >
                    <MyText
                        style={{
                            color: item.selected ? COLORS.bgFFFFFF : COLORS.bgD1D3D8,
                            fontSize: 15,
                            fontWeight: item.selected ? "bold" : null
                        }}
                        text={`${item.TermLoan} tháng`}
                    />
                </View>
            </Pressable>
        )
    }

    const onClickItem = (item) => {
        const newDataMonth = listInsProgramDetail?.map((r) => ({
            ...r,
            selected: r.InsuranceProductID === item.InsuranceProductID,
        }));
        setUpdateMonth(newDataMonth);
        getDataPromotionService(item);
        setUpdateItem(item);
    };

    const getDataPromotionService = (item) => {
        showBlockUI();
        const { InsProgramID, InsuranceProductID } = item;
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AirTimeTransactionTypeID,
            productID: InsuranceProductID,
            phoneNumber: PROFILEID,
            insProgramID: InsProgramID,
            mainProductID: ProductID
        }
        actionInsuranceLoanProtection.getPromotionService(data).then((reponsePromotion) => {
            handleGetPriceService(reponsePromotion, item)

        })
            .catch((msgError) => {
                hideBlockUI();
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }

    const handleGetPriceService = (reponsePromotion, item) => {
        showBlockUI();
        const { InsuranceProductID, InsuranceMonth } = item ?? {};
        const { ProductPromotion } = reponsePromotion?.ExtraData
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AirTimeTransactionTypeID,
            productID: InsuranceProductID,
            retailPriceVAT: LOANAMOUNT,
            mainSaleOrderID: SALEORDERID,
            productPromotion: ProductPromotion,
            termLoan: TERMLOAN
        }
        actionInsuranceLoanProtection.getPriceService(data).then((reponse) => {
            hideBlockUI();
            setUpdateInsuranceProductID(InsuranceProductID);
            setShowInfoInsurance(true)
        })
            .catch((msgError) => {
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }
    return (
        <SafeAreaView style={{
            flex: 1,
        }}>
            <KeyboardAwareScrollView
                style={{
                    flex: 1
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps={"always"}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView
                    style={{
                        justifyContent: 'center',
                    }}
                >
                    <View
                        style={{
                            marginTop: 10,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                        <Accordion
                            status={true}
                            title={`MÃ HỢP ĐỒNG: ${PROFILEID}`}
                            Children={
                                <View style={{
                                    backgroundColor: COLORS.bgF0F0F0,
                                    width: constants.width - 25
                                }}>
                                    <View style={{
                                        borderTopWidth: 2,
                                        borderColor: COLORS.bgA7A7A7,
                                        width: '95 %',
                                        alignSelf: 'center',
                                    }} />
                                    <View style={{
                                        padding: 5,
                                        marginBottom: 5,
                                        paddingHorizontal: 10,
                                    }}>
                                        <TextView
                                            name={"Thời gian tham gia bảo hiểm: "}
                                            value={`${TERMLOAN} tháng`} />
                                    </View>
                                </View>
                            }
                        />
                    </View>
                    <View style={{
                        paddingTop: 10
                    }}>
                        <MyText
                            style={{
                                color: COLORS.bg000000,
                                fontSize: 15,
                                fontWeight: "bold",
                                paddingHorizontal: 5
                            }}
                            text={'Thông tin bảo hiểm'}
                        />
                        <PickerSearch
                            label={"InsProgramName"}
                            value={"InsProgramID"}
                            valueSelected={chooseInsurance}
                            data={helper.IsNonEmptyArray(dataValidateService?.ListInsProgram) ? dataValidateService?.ListInsProgram : []}
                            onChange={(item) => {
                                setChooseInsurance(item?.InsProgramID);
                                if (item.ListInsProgramDetail?.length > 1) {
                                    setListInsProgramDetail(item?.ListInsProgramDetail);
                                } else {
                                    setListInsProgramDetail(item?.ListInsProgramDetail);
                                    getDataPromotionService(item.ListInsProgramDetail?.[0]);
                                    setUpdateItem(item.ListInsProgramDetail?.[0]);
                                }
                            }}
                            style={{
                                alignSelf: 'center',
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                height: 40,
                                borderRadius: 4,
                                borderWidth: 1,
                                borderColor: COLORS.bdE4E4E4,
                                width: constants.width - 20,
                                backgroundColor: COLORS.btnFFFFFF,
                                marginBottom: 10,
                                marginTop: 5
                            }}
                            defaultLabel={"Chọn gói bảo hành"}
                        />
                        <FlatList
                            data={updateMonth}
                            keyExtractor={(item, index) => `${index}`}
                            renderItem={renderItemMonth}

                            stickySectionHeadersEnabled={true}
                            alwaysBounceVertical={false}
                            bounces={false}
                            numColumns={3}
                            scrollEventThrottle={16}
                        />
                        {showInfoInsurance && !helper.IsEmptyObject(dataGetPrice) &&
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                                <Accordion
                                    status={true}
                                    title={'Thông tin gói bảo hiểm'}
                                    Children={
                                        <View style={{
                                            backgroundColor: COLORS.bgF0F0F0,
                                            width: constants.width - 25,
                                            paddingHorizontal: 5,
                                            paddingBottom: 5
                                        }}>
                                            <View style={{
                                                borderTopWidth: 2,
                                                borderColor: COLORS.bgA7A7A7,
                                                width: '95 %',
                                                alignSelf: 'center',
                                            }} />
                                            <View style={{
                                                padding: 5,
                                                marginBottom: 5,
                                                paddingHorizontal: 10
                                            }}>
                                                <TextView
                                                    name={"Số tiền bảo hiểm"}
                                                    value={helper.formatMoney(LOANAMOUNT)} />
                                                <TextView
                                                    name={"Ngày bắt đầu hiệu lực"}
                                                    value={dateHelper.formatDateDDMMYYYY(dateHelper.convert_string_to_date(StartDateInsLoan))} />
                                                <TextView
                                                    name={"Ngày hết hạn"}
                                                    value={dateHelper.formatDateDDMMYYYY(dateHelper.convert_string_to_date(EndDateInsLoan))} />
                                            </View>
                                        </View>
                                    }
                                />
                                <Accordion
                                    status={true}
                                    title={'Thông tin thanh toán bảo hiểm'}
                                    Children={
                                        <View style={{
                                            backgroundColor: COLORS.bgF0F0F0,
                                            width: constants.width - 25,
                                            paddingHorizontal: 5,
                                            paddingBottom: 5
                                        }}>
                                            <View style={{
                                                borderTopWidth: 2,
                                                borderColor: COLORS.bgA7A7A7,
                                                width: '95 %',
                                                alignSelf: 'center',
                                            }} />
                                            <View style={{
                                                padding: 5,
                                                marginBottom: 5,
                                                paddingHorizontal: 10
                                            }}>
                                                <TextView
                                                    name={"Giá bán gói bảo hiểm"}
                                                    value={helper.formatMoney(TotalAmount)} />
                                            </View>
                                        </View>
                                    }
                                />
                            </View>
                        }
                        {
                            showInfoInsurance && !helper.IsEmptyObject(dataGetPrice) && (
                                <View style={{
                                    width: '100%',
                                    height: 50,
                                    flexDirection: 'row',
                                    paddingHorizontal: 10,
                                    marginTop: 10
                                }}>
                                    <TouchableOpacity
                                        onPress={() => {
                                            actionInsuranceLoanProtection.updateCustomerAirtimeService({
                                                InsuranceMonth,
                                                InsuranceProductID
                                            })
                                            onNextStep();
                                        }}
                                        style={{
                                            backgroundColor: 'pink',
                                            flex: 1,
                                            height: 50,
                                            borderRadius: 18,
                                            alignItems: "center",
                                            justifyContent: "center",
                                            backgroundColor: COLORS.bgF49B0C
                                        }}>
                                        <MyText
                                            text={'BƯỚC TIẾP THEO'}
                                            style={{
                                                fontWeight: 'bold',
                                                color: COLORS.bgFFFFFF
                                            }}
                                        />
                                    </TouchableOpacity>
                                </View>
                            )
                        }

                    </View>
                </SafeAreaView>
            </KeyboardAwareScrollView>

        </SafeAreaView>
    )
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        dataValidateService: state.insuranceLoanProtectionReducer.dataValidateService,
        itemAirtimeCode: state.insuranceLoanProtectionReducer.itemAirtimeCode,
        dataGetPrice: state.insuranceLoanProtectionReducer.dataGetPrice
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsuranceLoanProtection: bindActionCreators(actionInsuranceLoanProtectionCreator, dispatch),
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(StepOneInsurance);


const styles = StyleSheet.create({})