import {
    BarcodeCamera,
    BaseLoading,
    Icon,
    <PERSON><PERSON><PERSON>t,
    <PERSON>erSearch,
    hide<PERSON><PERSON><PERSON>,
    showBlock<PERSON>
} from '@components';
import { constants } from '@constants';
import { COLORS } from '@styles';
import moment from 'moment';
import React, { useCallback, useState } from 'react';
import {
    StyleSheet,
    TouchableOpacity,
    Animated,
    View,
    FlatList,
    Alert
} from 'react-native';
import SafeAreaView from 'react-native-safe-area-view';
import ModalCalendar from '../component/ModalCalendar';

import SearchBar from '../component/SearchBar';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionInsuranceLoanProtectionCreator from '../action';
import * as actionCollectionCreator from '../../CollectionTransfer/action';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import * as actionSaleOrderCreator from "../../SaleOrderPayment/action";
import { translate } from '@translate';
import ItemLoanInsurance from '../component/ItemLoanInsurance';
import { useFocusEffect } from '@react-navigation/core';

const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;

const SaleOrderInsuraneExtended = ({
    navigation,
    dataSearchListSO,
    stateSearchListSO,
    actionInsuranceLoanProtection,
    itemCatalog,
    dataDetailSO,
    actionSaleOrder,
    itemAirtimeCode
}) => {
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog;
    const [filter, setFilter] = useState({
        scrollY: new Animated.Value(0),
        isIncome: 0,
        isCompleteInfo: 0,
        isDelivery: 0
    });
    const diffClamp = Animated.diffClamp(filter.scrollY, 0, DISTANCE);
    const translateY = diffClamp.interpolate({
        inputRange: [0, DISTANCE],
        outputRange: [0, -DISTANCE]
    });
    const [isShowCalendar, setIsShowCalendar] = useState(false);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [keyword, setkeyWork] = useState('');
    const [isVisibleBarcodeScanner, setIsVisibleBarcodeScanner] = useState(false);
    const [validateType, setValidateType] = useState("SO");
    const [showDetail, setShowDetail] = useState(false);
    const { AirTimeTransactionTypeID } = itemAirtimeCode ?? '';
    const [SearchByList, setSearchByList] = useState([]);
    const [PartnerInstallment, setPartnerInstallment] = useState([])
    const [searchByInsurance, setSearchByInsurance] = useState('');
    const [partnerInsurance, setPartnerInsurance] = useState('')
    console.log(SearchByList, PartnerInstallment, 'log hihih');
    useFocusEffect(
        React.useCallback(() => {
            actionInsuranceLoanProtection.clear_data_search_so()
        }, [actionInsuranceLoanProtection])
    );
    useFocusEffect(
        useCallback(() => {
            const data = {
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                isLoadSearchBy: true
            };
            actionInsuranceLoanProtection.getServiceListHistory(data).then((response) => {
                console.log(response, 'log response');
                setSearchByList(response[0].SearchByList)
                setPartnerInstallment(response[2].PartnerInstallment)
            }).catch((err) => console.log(err));
        }, [actionInsuranceLoanProtection])
    );
    const getSearchListSO = ({
        catalogID: ServiceCategoryID,
        serviceGroupID: AirtimeServiceGroupID,
        airtimeTransactionTypeID: AirTimeTransactionTypeID,
        validateType: validateType,
        searchType: searchByInsurance,
        PartnerInstallID: partnerInsurance,
        keyword: keyword,
        fromDate: fromDate,
        toDate: toDate,
        extraData: extraData
    }) => {
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AirTimeTransactionTypeID,
            validateType: validateType,
            searchType: searchByInsurance,
            PartnerInstallID: partnerInsurance,
            keyword: keyword,
            fromDate: fromDate,
            toDate: toDate,
            extraData: {
                "PartnerInstallID": partnerInsurance,
            }
        };
        actionInsuranceLoanProtection.getDataSearchSO(data);
    };

    const onGetDetailSO = (item) => {
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AirTimeTransactionTypeID,
            keyword: keyword,
            fromDate: new Date(fromDate),
            toDate: new Date(toDate),
            extraData: {
                "PartnerInstallID": -1,
                "SOINFO": item
            }
        }
        showBlockUI()
        actionInsuranceLoanProtection.geDataBuyInsurance(data).then((dataSaleOrderDetail) => {
            hideBlockUI();
            actionSaleOrder.setDataSO({ SaleOrderID: item.SALEORDERID, SaleOrderTypeID: 100 })
            navigation.navigate("InsuranceInformation", { item })
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () =>
                        onGetDetailSO(item),
                }
            ])
        });
    }

    const renderItem = ({ item, index }) => {
        return (
            <View style={styles.item}>
                <ItemLoanInsurance
                    onShowDetail={() => onGetDetailSO(item)}
                    item={item}
                    onClose={() => setShowDetail(false)}
                    showDetail={showDetail}
                    dataSaleOrder={dataDetailSO}
                />
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <KeyboardAwareScrollView
                style={{
                    flex: 1
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}>
                <SafeAreaView style={styles.safeareaview}>
                    <Animated.View
                        style={{
                            transform: [{ translateY: translateY }],
                            position: 'relative',
                            top: 0,
                            left: 0,
                            right: 0,
                            zIndex: 1,
                            backgroundColor: COLORS.bgF5F5F5
                        }}>
                        <View style={styles.childrenAnimated}>
                            <TouchableOpacity
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    backgroundColor: COLORS.bgFFFFFF,
                                    borderRadius: 5,
                                    width: constants.width - 20,
                                    paddingHorizontal: 5,
                                    borderWidth: 1,
                                    borderColor: COLORS.bdDDDDDD,
                                    height: 44,
                                    alignSelf: 'center'
                                }}
                                onPress={() => setIsShowCalendar(true)}>
                                <MyText
                                    style={{
                                        width: '87%',
                                        paddingHorizontal: 5
                                    }}
                                    text={`${moment(fromDate).format(
                                        'DD/MM/YYYY'
                                    )} - ${moment(toDate).format(
                                        'DD/MM/YYYY'
                                    )} `}
                                />
                                <Icon
                                    iconSet="Feather"
                                    name="calendar"
                                    style={{
                                        fontSize: 30,
                                        color: COLORS.ic2C8BD7
                                    }}
                                />
                            </TouchableOpacity>
                        </View>
                        <PickerSearch
                            label={"Label"}
                            value={"SearchTypeID"}
                            valueSelected={searchByInsurance}
                            data={SearchByList}
                            onChange={(item) => {
                                setSearchByInsurance(item.SearchTypeID)
                            }}
                            style={{
                                alignSelf: 'center',
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                height: 40,
                                borderRadius: 4,
                                borderWidth: 1,
                                borderColor: COLORS.bdE4E4E4,
                                width: constants.width - 20,
                                backgroundColor: COLORS.btnFFFFFF,
                                marginBottom: 10,
                                marginTop: 5
                            }}
                            defaultLabel={"Nhập mã yêu cầu xuất"}
                        />
                        <PickerSearch
                            label={"PartnerInstallName"}
                            value={"PartnerInstallmentID"}
                            valueSelected={partnerInsurance}
                            data={PartnerInstallment}
                            onChange={(item) => {
                                setPartnerInsurance(item.PartnerInstallmentID)
                            }}
                            style={{
                                alignSelf: 'center',
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                height: 40,
                                borderRadius: 4,
                                borderWidth: 1,
                                borderColor: COLORS.bdE4E4E4,
                                width: constants.width - 20,
                                backgroundColor: COLORS.btnFFFFFF,
                                marginBottom: 10,
                                marginTop: 5
                            }}
                            defaultLabel={"Chọn đối tác trả góp"}
                        />
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                paddingHorizontal: 10
                            }}>
                            <SearchBar
                                hasBarcodeScanner
                                onOpenBarcodeScanner={() =>
                                    setIsVisibleBarcodeScanner(true)
                                }
                                placeholder="Nhập mã hồ sơ/ Số điện thoại/ Yêu cầu xuất"
                                value={keyword}
                                onChangeText={(text) => {
                                    setkeyWork(text);
                                }}
                                onSearch={() => getSearchListSO({
                                    catalogID: ServiceCategoryID,
                                    serviceGroupID: AirtimeServiceGroupID,
                                    airtimeTransactionTypeID: AirTimeTransactionTypeID,
                                    validateType: validateType,
                                    searchType: searchByInsurance,
                                    PartnerInstallID: partnerInsurance,

                                    keyword: keyword,
                                    fromDate: new Date(fromDate),
                                    toDate: new Date(toDate),
                                    extraData: {
                                        "PartnerInstallID": partnerInsurance,
                                    }
                                })}
                                onDelete={() => {
                                    setkeyWork('');
                                }}
                            />
                        </View>
                    </Animated.View>

                    <View style={styles.containerListView}>
                        <BaseLoading
                            isLoading={stateSearchListSO.isFetching}
                            isError={stateSearchListSO.isError}
                            isEmpty={stateSearchListSO.isEmpty}
                            textLoadingError={stateSearchListSO.description}
                            onPressTryAgains={() => {
                                getSearchListSO({
                                    catalogID: ServiceCategoryID,
                                    serviceGroupID: AirtimeServiceGroupID,
                                    airtimeTransactionTypeID: AirTimeTransactionTypeID,
                                    validateType: validateType,
                                    searchType: searchByInsurance,
                                    PartnerInstallID: partnerInsurance,

                                    keyword: keyword,
                                    fromDate: fromDate,
                                    toDate: toDate,
                                    extraData: {
                                        "PartnerInstallID": partnerInsurance,
                                    }
                                })
                            }}
                            content={
                                <View
                                    style={{
                                        width: constants.width,
                                        height: '100%',
                                    }}>
                                    <FlatList
                                        data={dataSearchListSO}
                                        keyExtractor={(item, index) =>
                                            `${index}`
                                        }
                                        renderItem={renderItem}
                                    />
                                </View>
                            }
                        />
                    </View>
                </SafeAreaView>
            </KeyboardAwareScrollView>
            <ModalCalendar
                isVisible={isShowCalendar}
                hideModal={() => {
                    setIsShowCalendar(false);
                }}
                startDate={fromDate}
                endDate={toDate}
                setDate={(day) => {
                    setFromDate(day.startDate)
                    setToDate(day.endDate)
                    getSearchListSO({
                        catalogID: ServiceCategoryID,
                        serviceGroupID: AirtimeServiceGroupID,
                        airtimeTransactionTypeID: AirTimeTransactionTypeID,
                        validateType: validateType,
                        searchType: searchByInsurance,
                        PartnerInstallID: partnerInsurance,

                        keyword: keyword,
                        fromDate: new Date(day.startDate),
                        toDate: new Date(day.endDate),
                        extraData: {
                            "PartnerInstallID": partnerInsurance,
                        }
                    })
                }}
            />
            {isVisibleBarcodeScanner && (
                <BarcodeCamera
                    isVisible={isVisibleBarcodeScanner}
                    closeCamera={() => {
                        setIsVisibleBarcodeScanner(false);
                    }}
                    resultScanBarcode={(text) => {
                        setIsVisibleBarcodeScanner(false);
                        setIMEI(text);
                        validateIMEI(text);
                    }}
                />
            )}

        </View>
    );
};

const mapStateToProps = function (state) {
    return {
        dataSearchListSO: state.insuranceLoanProtectionReducer.dataSearchListSO,
        stateSearchListSO: state.insuranceLoanProtectionReducer.stateSearchListSO,
        itemCatalog: state.collectionReducer.itemCatalog,
        dataDetailSO: state.insuranceLoanProtectionReducer.dataDetailSO,
        sateDetailSO: state.insuranceLoanProtectionReducer.sateDetailSO,
        itemAirtimeCode: state.insuranceLoanProtectionReducer.itemAirtimeCode
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsuranceLoanProtection: bindActionCreators(actionInsuranceLoanProtectionCreator, dispatch),
        actionCollectionCreator: bindActionCreators(actionCollectionCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(SaleOrderInsuraneExtended);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    childrenAnimated: {
        width: constants.width,
        marginTop: 5
    },
    safeareaview: {
        flex: 1,
        alignItems: 'center'
    },
    containerListView: {
        width: '100%',
        height: '100%',
    },
    item: {
        width: constants.width - 20,
        paddingVertical: 5,
        alignSelf: 'center',
        backgroundColor: COLORS.bgFFFFFF,
        marginTop: 10,
        borderRadius: 10,
        paddingHorizontal: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3
        },
        shadowOpacity: 0.27,
        shadowRadius: 4.65,

        elevation: 6
    },
    twoPicker: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        height: 38,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        width: constants.width - 20,
        backgroundColor: COLORS.btnFFFFFF,
        alignSelf: 'center',
        bottom: 5,
        borderRadius: 19
    }
});
