import {
    StyleSheet,
    View,
    SafeAreaView,
    Animated,
    TouchableOpacity,
    FlatList,
    Alert
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { COLORS } from '@styles';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { constants } from '@constants';
import { BaseLoading, Icon, MyText, PickerSearch, hideBlockUI } from '@components';
import SearchInput from '../component/SearchInput'
import ModalCalendar from '../component/ModalCalendar';
import moment from 'moment';
import ItemRefund from '../component/ItemRefund';
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux';
import * as actionInsuranceLoanProtectionCreator from "../action";
import { helper } from '@common';
import * as actionCollectionCreator from "../../CollectionTransfer/action";

const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;

const RefundHistory = ({
    dataSearchListHistory,
    stateSearchListHistory,
    actionInsuranceLoanProtection,
    itemCatalog,
    route
}) => {
    const {
        ServiceCategoryID,
        AirtimeServiceGroupID
    } = itemCatalog ?? {};

    const { SERVICEVOUCHERID } = route?.params ?? "";

    const [filter, setFilter] = useState({
        scrollY: new Animated.Value(0),
        isIncome: 0,
        isCompleteInfo: 0,
        isDelivery: 0
    });
    const [isShowCalendar, setIsShowCalendar] = useState(false);
    const [keyword, setkeyWork] = useState('');
    const [fromDate, setFromDate] = useState(new Date())
    const [toDate, setToDate] = useState(new Date())
    const [dataHistoryRefund, setDataHistoryRefund] = useState()
    const [searchByRefund, setSearchByRefund] = useState('');
    const [providerRefund, setProviderRefund] = useState('');
    const [searchByList, setSearchByList] = useState([]);
    const [updateSearchByList, setUpdateSearchByList] = useState('')
    const [providerList, setProviderList] = useState([]);
    const [updateProviderList, setUpdateProviderList] = useState('');
    const [approvalStatusList, setApprovalStatusList] = useState([]);
    const [updateApprovalStatusList, setupdateApprovalStatusList] = useState('');
    const [firtApprovalStatusLis, setFirtApprovalStatusLis] = useState('')

    const diffClamp = Animated.diffClamp(filter.scrollY, 0, DISTANCE);
    const translateY = diffClamp.interpolate({
        inputRange: [0, DISTANCE],
        outputRange: [0, -DISTANCE]
    });


    useEffect(() => {
        //nếu vị trí của data thay đổi backend phải tự chịu lỗi
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            isLoadSearchBy: true
        };
        actionInsuranceLoanProtection.getServiceListRefund(data).then((response) => {
            setSearchByList(response[0].SearchByList)
            setProviderList(response[1].ProviderList)
            setApprovalStatusList(response[2].ApprovalStatusList)
        }).catch((err) => console.log(err));
    }, [actionInsuranceLoanProtection])

    const getServiceListHistoryRefund = () => {
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            fromDate: new Date(fromDate),
            toDate: new Date(toDate),
            keyword: keyword,
            searchType: updateSearchByList,
            airtimetrsTypeIDList: updateProviderList,
            approvalStatus: firtApprovalStatusLis
        }
        actionInsuranceLoanProtection.getServiceListHistoryRefund(data)
        setDataHistoryRefund(dataSearchListHistory)
    }

    useEffect(() => {
        if (!!SERVICEVOUCHERID) {
            setkeyWork(SERVICEVOUCHERID)
        } else {
            console.log()
            setkeyWork("")
        }
        getServiceListHistoryRefund()
    }, [actionInsuranceLoanProtection, fromDate, toDate, searchByRefund, providerRefund, updateProviderList, updateSearchByList, updateApprovalStatusList, updateApprovalStatusList, firtApprovalStatusLis, SERVICEVOUCHERID])

    useEffect(() => {
        setDataHistoryRefund(dataSearchListHistory)
    }, [dataSearchListHistory])

    const onSubmit = (keyword) => {
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            keyword: keyword,
            fromDate:
                new Date(fromDate),
            toDate:
                new Date(toDate),
            searchType: updateSearchByList,
            airtimetrsTypeIDList: updateProviderList,
            approvalStatus: firtApprovalStatusLis
        }
        actionInsuranceLoanProtection.getServiceListHistoryRefund(data)
    }

    const actionSpendCash = (item) => {
        Alert.alert('Thông báo', `Bạn có chắc chắn muốn chi tiền cho giao dịch ${item.OUTPUTRECEIPTID} này?`, [
            {
                text: 'Cancel',
                onPress: () => console.log('Cancel Pressed'),
                style: 'cancel',
            },
            { text: 'OK', onPress: () => handleRefresh(item) },
        ]);
    }
    const handleRefresh = (item) => {
        const { AIRTIMETRANSACTIONRFID, AIRTIMETRANSACTIONID } = item
        const data = {
            airtimeTransactionID: AIRTIMETRANSACTIONID,
            airtimeTransactionRFID: AIRTIMETRANSACTIONRFID
        }
        actionInsuranceLoanProtection.getStatusHistory(data).then((response) => {
            hideBlockUI();
            Alert.alert('Thông báo', 'Chi tiền thành công', [
                { text: 'OK', onPress: () => getServiceListHistoryRefund() },
            ]);

        }).catch(error => {
            Alert.alert('Thông báo', error.msgError, [
                {
                    text: 'Cancel',
                    onPress: () => console.log('Cancel Pressed'),
                    style: 'cancel',
                },
                { text: 'OK', onPress: () => getServiceListHistoryRefund() },
            ]);
        });
    }

    return (
        <View style={styles.container}>
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}>
                <SafeAreaView style={styles.safeareaview}>
                    <Animated.View
                        style={{
                            transform: [{ translateY: translateY }],
                            position: 'relative',
                            top: 0,
                            left: 0,
                            right: 0,
                            zIndex: 1,
                            backgroundColor: COLORS.bgF5F5F5,
                        }}>
                        <View style={styles.childrenAnimated}>
                            <TouchableOpacity style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 5,
                                width: constants.width - 20,
                                paddingHorizontal: 5,
                                borderWidth: 1,
                                borderColor: COLORS.bdDDDDDD,
                                height: 44,
                                alignSelf: 'center'
                            }}
                                onPress={() => setIsShowCalendar(true)}
                            >
                                <MyText
                                    style={{
                                        width: '87%',
                                        paddingHorizontal: 5
                                    }}
                                    text={`${moment(fromDate).format(
                                        'DD/MM/YYYY'
                                    )
                                        } - ${moment(toDate).format(
                                            'DD/MM/YYYY'
                                        )
                                        } `}
                                />
                                <Icon
                                    iconSet="Feather"
                                    name="calendar"
                                    style={{
                                        fontSize: 30,
                                        color: COLORS.ic2C8BD7
                                    }}
                                />
                            </TouchableOpacity>
                            <PickerSearch
                                label={'AirTimeTransactionTypeName'}
                                value={'AirTimeTransactionTypeID'}
                                valueSelected={updateProviderList}
                                data={helper.IsEmptyArray(providerList) ? providerList : []}
                                onChange={(item) => {
                                    setUpdateProviderList(item.AirTimeTransactionTypeID)
                                }}
                                style={styles.twoPicker}
                                defaultLabel={'Nhà cung cấp'}
                            />
                            <PickerSearch
                                label={'Label'}
                                value={'ID'}
                                valueSelected={updateApprovalStatusList}
                                data={helper.IsEmptyArray(approvalStatusList) ? approvalStatusList : []}
                                onChange={(item) => {
                                    setupdateApprovalStatusList(item.ID)
                                    setFirtApprovalStatusLis(item.ApprovalStatus)
                                }}
                                style={styles.pickerSearch}
                                defaultLabel={'Kết quả huỷ/chuyển'}
                            />
                            <PickerSearch
                                label={'Label'}
                                value={'SearchTypeID'}
                                valueSelected={updateSearchByList}
                                data={helper.IsEmptyArray(searchByList) ? searchByList : []}
                                onChange={(item) => {
                                    setUpdateSearchByList(item.SearchTypeID)
                                }}
                                style={styles.twoPicker}
                                defaultLabel={'Tìm theo'}
                            />
                        </View>
                    </Animated.View>
                    <SearchInput
                        onSubmit={() => onSubmit(keyword)}
                        inputText={keyword}
                        onChangeText={(text) => {
                            setkeyWork(text)
                        }}
                        onClearText={() => {
                            setkeyWork('');
                        }}
                        placeholder={'Từ khoá'}
                    />

                    <BaseLoading
                        isLoading={stateSearchListHistory.isFetching}
                        isError={stateSearchListHistory.isError}
                        isEmpty={stateSearchListHistory.isEmpty}
                        textLoadingError={stateSearchListHistory.description}
                        onPressTryAgains={() => {
                            getServiceListHistoryRefund()
                        }}
                        content={
                            <View style={{
                                width: constants.width,
                            }}>
                                <FlatList
                                    style={{ marginTop: 5 }}
                                    data={helper.IsNonEmptyArray(dataHistoryRefund) ? dataHistoryRefund : []}
                                    keyExtractor={(item, index) => `${index} `}
                                    renderItem={({ item, index }) => (
                                        <ItemRefund
                                            ServiceCategoryID={ServiceCategoryID}
                                            AirtimeServiceGroupID={AirtimeServiceGroupID}
                                            info={item}
                                            // onRefresh={() => handleRefresh(item)}
                                            onRefresh={() => actionSpendCash(item)}
                                        />
                                    )
                                    }
                                />
                            </View>
                        }
                    />
                </SafeAreaView>
            </KeyboardAwareScrollView>
            <ModalCalendar
                isVisible={isShowCalendar}
                hideModal={() => {
                    setIsShowCalendar(false);
                }}
                startDate={fromDate}
                endDate={toDate}
                setDate={(day) => {
                    setFromDate(day.startDate)
                    setToDate(day.endDate)
                }}
            />
        </View>
    );
};

const mapStateToProps = function (state) {
    return {
        dataSearchListHistory: state.insuranceLoanProtectionReducer.dataSearchListHistory,
        stateSearchListHistory: state.insuranceLoanProtectionReducer.stateSearchListHistory,
        itemCatalog: state.collectionReducer.itemCatalog
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsuranceLoanProtection: bindActionCreators(actionInsuranceLoanProtectionCreator, dispatch),
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(RefundHistory);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    safeareaview: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    childrenAnimated: {
        width: constants.width,
        marginTop: 5
    },
    showCalendarButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 5,
        width: constants.width - 10,
        paddingHorizontal: 5,
        borderWidth: 1,
        borderColor: COLORS.bdDDDDDD,
        height: 50,
        alignSelf: 'center'
    },
    textCalendar: {
        width: '87%',
        paddingHorizontal: 5
    },
    horizontalPicker: {
        width: constants.width - 10,
        height: 50,
        marginTop: 13,
        alignSelf: 'center',
        flexDirection: 'row'
    },
    childrenPicker: {
        flex: 1,
        paddingRight: 3
    },
    childrenPickerRight: {
        flex: 1,
        paddingLeft: 3
    },
    twoPicker: {
        flex: 1,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        height: 40,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        width: constants.width - 20,
        backgroundColor: COLORS.btnFFFFFF,
        marginTop: 5,
        alignSelf: "center",
    },
    pickerSearch: {
        flex: 1,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        height: 40,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        width: constants.width - 20,
        backgroundColor: COLORS.btnFFFFFF,
        marginTop: 5,
        alignSelf: "center",
    },
    input: {
        width: constants.width - 10,
        borderRadius: 10,
        height: 50,
        marginTop: 10
    }
});
