import React, { Component } from 'react';
import {
    View,
    SafeAreaView,
    Alert,
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { constants } from '@constants';
import {
    MyText,
    showBlock<PERSON>,
    hideBlock<PERSON>,
    TitleInput
} from '@components';
import { COLORS } from "@styles"
import ButtonOTP from './compnents/ButtonOTP';
import * as actionInsuranceLoanProtectionCreator from '../action';
import * as actionCollectionCreator from "../../CollectionTransfer/action";

class OTPAirtimeService extends Component {
    constructor(props) {
        super(props);
        this.state = {
            reportRetail: {},
            reportVAT: {},
            reportCommon: {},
            reportInfo: [],
            isVisible: false,
            base64PDF: "",
            inputOTP: "",
            second: 0,
            running: false
        };
        this.timer = null;
        this.focusListener = null;
    }
    //data test Viettel 0326793893, 0123456789
    onFocus = () => {
        const { isCalled } = this.props.route.params ?? {};
        this.setState({ second: 0 })
        if (isCalled == true) {
            this.handleSendOTPProcessServicePrequest();
        } else {
            this.handleGetTransactionDetail();
        }
    }

    componentDidMount() {
        this.focusListener = this.props.navigation.addListener('focus', () => {
            this.onFocus();
        });
        this.blurListener = this.props.navigation.addListener('blur', () => {
            this.onBlur();
        });
    }

    componentWillUnmount() {
        if (this.focusListener.remove) {
            this.focusListener.remove();
        }
        if (this.blurListener.remove) {
            this.blurListener.remove();
            this.resetTimer();
        }

    }

    startTimer = () => {
        this.timer = setInterval(() => {
            if (this.state.second > 0) {
                this.setState((prevState) => ({
                    second: prevState.second - 1,
                }));
            } else {
                clearInterval(this.timer);
            }
        }, 1000);
    };

    onBlur() {
        this.resetTimer();
    }

    resetTimer = () => {
        clearInterval(this.timer);
        this.setState({ second: 0, running: false });
    };

    componentDidUpdate(preProps, preState) {
    }

    handleSendOTPProcessServicePrequest = () => {
        showBlockUI();
        const {
            dataSO,
            actionInsuranceLoanProtection
        } = this.props;
        const { SaleOrderID } = dataSO ?? {};
        const data = {
            saleOrderID: SaleOrderID
        }
        actionInsuranceLoanProtection.getSendOTPProcessServicePrequest(data).then((reponse) => {
            hideBlockUI();
            const { dataSendOTPProcess } = this.props;
            const { TimeCountDown } = dataSendOTPProcess;
            this.setState({ second: TimeCountDown })
            this.startTimer();
            this.setState({ running: true });
        }).catch((msgError) => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        });
    }

    handleGetTransactionDetail = () => {
        showBlockUI();
        const {
            dataSO,
            actionInsuranceLoanProtection
        } = this.props;
        const { SaleOrderID } = dataSO ?? {};
        actionInsuranceLoanProtection.getGetTransactionDetail(SaleOrderID).then((reponse) => {
            hideBlockUI();
            const { dataSendOTPProcess } = this.props;
            const { TimeCountDown } = dataSendOTPProcess;
            this.setState({ second: TimeCountDown })
            this.startTimer();
            this.setState({ running: true });
        }).catch((msgError) => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        });
    }

    handlePreSendOTPProcessServicePrequest = () => {
        showBlockUI();
        const {
            dataSO,
            actionInsuranceLoanProtection
        } = this.props;
        const { SaleOrderID } = dataSO ?? {};
        const data = {
            saleOrderID: SaleOrderID
        }
        this.setState({ second: 0 })
        actionInsuranceLoanProtection.getSendOTPProcessServicePrequest(data).then((reponse) => {
            hideBlockUI();
            const { dataSendOTPProcess } = this.props;
            const { TimeCountDown } = dataSendOTPProcess;
            this.setState({ second: TimeCountDown })
            this.startTimer();
            this.setState({ running: true });
            //  if (this.state.second == 0) {
            //     this.resetTimer();
            // }
        }).catch((msgError) => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        });
    }


    render() {
        const {
            inputOTP,
            second,
        } = this.state;
        const {
            dataSendOTPProcess
        } = this.props;
        const { IsReSendOTP, IsVerifyOTP, TimeCountDown, TimeCountDownFix } = dataSendOTPProcess ?? {};
        console.log("Dạ con log này xin anh Đa đừng xoá ạ !!!", IsReSendOTP, IsVerifyOTP, TimeCountDown, dataSendOTPProcess)
        return (
            <SafeAreaView style={{
                flex: 1,
                backgroundColor: COLORS.bgFAFAFA
            }}>
                <View style={{
                    width: constants.width
                }}>
                    <View
                        style={{
                            marginTop: 10,
                            padding: 10
                        }}
                    >
                        <MyText
                            text={"Xác nhận đăng ký gói cước:"}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt333333,
                                fontWeight: "bold",
                                fontStyle: "italic"
                            }} />
                        <MyText
                            // text={`Hệ thống đã gửi mã OTP tới số thuê bao của khách hàng .Bạn hãy nhập vào OTP (OTP có hiệu lực trong ${TimeCountDownFix} phút)`}
                            text={`Hệ thống đã gửi mã OTP tới số thuê bao của khách hàng .Bạn hãy nhập vào OTP (OTP có hiệu lực trong ${5} phút)`}
                            addSize={-1.5}
                            style={{
                                color: COLORS.bg000000,
                                marginTop: 10,
                                marginBottom: 10
                            }}
                        />
                        <TitleInput
                            title={"Mã OTP"}
                            isRequired={true}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bg2C8BD7,
                                marginBottom: 10,
                                paddingHorizontal: 10,
                                backgroundColor: COLORS.bgFFFFFF,
                                paddingVertical: 8,
                            }}
                            placeholder={"Vui lòng nhập mã OTP"}
                            value={inputOTP}
                            onChangeText={(text) => {
                                const regExpOTP = new RegExp(/^\d{0,6}$/);
                                const isValidate = regExpOTP.test(text) || text === "";
                                if (isValidate) {
                                    this.setState({ inputOTP: text })
                                }
                            }}
                            keyboardType={"numeric"}
                            returnKeyType={"done"}
                            onBlur={() => { }}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                this.setState({ inputOTP: "" })
                            }}
                            key="phoneNumber"
                        />
                        <View style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            textAlign: 'center',
                            flexDirection: 'row',
                            marginTop: 20
                        }}>
                            <ButtonOTP
                                onPress={() => this.handlePreSendOTPProcessServicePrequest()}
                                disabled={IsReSendOTP == true && second == 0 ? false : true}
                                title={`Gửi lại OTP (${second} Giây)`}
                                iconSet={"Ionicons"}
                                nameIcon={"reload"}
                                style={{
                                    backgroundColor: COLORS.bg00A98F
                                }}
                                opacity={IsReSendOTP == true && second == 0 ? 1 : 0.5}
                            />
                            <View style={{ flex: 1 }} />
                            <ButtonOTP
                                onPress={() => this.handleConfirmOTP()}
                                title={"Xác nhận OTP"}
                                disabled={second != 0 ? false : true}
                                iconSet={"Ionicons"}
                                nameIcon={"search"}
                                style={{
                                    backgroundColor: COLORS.bg1E88E5,
                                }}
                                opacity={second != 0 ? 1 : 0.5}
                            />
                        </View>
                    </View>
                </View>
            </SafeAreaView>
        );
    }

    handleConfirmOTP = () => {
        showBlockUI();
        const {
            inputOTP,
        } = this.state;
        const {
            dataSO
        } = this.props;
        const { SaleOrderID } = dataSO ?? {};
        const data = {
            saleOrderID: SaleOrderID,
            otp: inputOTP
        }
        if (inputOTP == '' || inputOTP.length !== 6) {
            Alert.alert("", "Vui lòng nhập mã OTP đúng 6 chữ số")
            return hideBlockUI();
        } else {
            this.props.actionInsuranceLoanProtection.getConfirmOTPProcessServicePrequest(data).then((reponse) => {
                Alert.alert("Thông báo", reponse.Message, [
                    {
                        text: "OK",
                        onPress: () => {
                            if (!reponse.IsVerifyOTPSuccess) {
                                hideBlockUI();
                            } else {
                                this.goBack(reponse);
                            }
                        },
                    },
                ]);
            }).catch((msgError) => {
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
        }
    }

    goBack = (reponse) => {
        hideBlockUI();
        const {
            navigation,
            dataSO: { SaleOrderID },
            actionCollection
        } = this.props;
        const { ServiceCategoryID, AirtimeServiceGroupID } = reponse ?? "";
        const item = { ServiceCategoryID, AirtimeServiceGroupID }
        this.setState({ inputOTP: '' });
        actionCollection.updateItemCatalog(item)
        navigation.navigate("HistorySellInsuranceLoan", {
            SaleOrderID
        });
    }
}

const mapStateToProps = (state) => ({
    dataSO: state.saleOrderPaymentReducer.dataSO,
    userInfo: state.userReducer,
    dataSendOTPProcess: state.insuranceLoanProtectionReducer.dataSendOTPProcess
});

const mapDispatchToProps = (dispatch) => ({
    actionInsuranceLoanProtection: bindActionCreators(actionInsuranceLoanProtectionCreator, dispatch),
    actionCollection: bindActionCreators(actionCollectionCreator, dispatch)
});

export default connect(mapStateToProps, mapDispatchToProps)(OTPAirtimeService);
