import React from 'react';
import {
    View,
    Alert,
    ActivityIndicator
} from 'react-native';
import KModal from "react-native-modal";
import { MyText, Icon, Button, hideBlockUI } from "@components";
import { constants } from "@constants";
import { COLORS } from "@styles";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionCollectionManagerCreator from '../../../CollectionTransferManager/action';
import * as actionInsuranceBrightsideCreator from "../../../InsuranceBrightside/action";
import { translate } from '@translate';
import { helper } from '@common';

const CollectionStatusModal = ({
    isVisible,
    onClose,
    onSuccess,
    actionInsuranceBrightside,
    dataCreateAirtimeRefund
}) => {
    const intervalId = React.useRef(0);
    const [dataStatus, setDataStatus] = React.useState({});
    const [iconsName, setIconName] = React.useState("");
    const [colorStatus, setColorStatus] = React.useState("#F49B0C")
    const status = dataStatus?.cus_AirtimeStatusCacheBO?.Status;
    const checkStatus = (status == "SUCCESS" || status == "FAIL");
    const checkStatusSuccess = status == "SUCCESS";
    const checkStatusFainal = (status == "SUCCESS" || status == "FAIL")
    const statusNameTransaction = dataStatus?.cus_AirtimeStatusCacheBO?.Description;
    const description = dataStatus?.cus_AirtimeStatusCacheBO?.Description;
    React.useEffect(() => {
        intervalId.current = setInterval(() => {
            getQueryTransactionPartner();
        }, 5000)
        return () => {
            if (intervalId.current) {
                clearInterval(intervalId.current);
            }
        }
    }, [])

    const getQueryTransactionPartner = () => {
        const { AirTimetransactionRFID, AirTimeTransactionID } = dataCreateAirtimeRefund ?? {};
        const data = {
            airtimeTransactionRFID: AirTimetransactionRFID,
            airtimeTransactionID: AirTimeTransactionID
        }
        actionInsuranceBrightside.getQuerysTatusServiceRequest(data).then((transaction) => {
            setDataStatus(transaction);
            const { Status } = transaction.cus_AirtimeStatusCacheBO;
            hideBlockUI();
            switch (Status) {
                case 'PENDING': {
                    setIconName("");
                    setColorStatus("#F49B0C");
                    break;
                }
                case 'SUCCESS': {
                    setIconName("md-checkmark-circle-outline");
                    setColorStatus("#1E88E5");
                    break;
                }
                case 'FAIL':
                    setIconName("ios-close-circle-outline");
                    setColorStatus("#EA1D5D");
                    break;
                default:
                    break;
            }
        }).catch(msgError => {
            console.log(msgError)
        });
    }


    return (
        <KModal
            isVisible={isVisible}
            style={{
                margin: 0,
                justifyContent: 'center',
                flexDirection: 'column',
                alignItems: 'center'
            }}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}>
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    width: '95%',
                    height: '30%',
                    borderRadius: 5,
                    alignItems: 'center'
                }}>
                <View>
                    <View style={{
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: "white",
                        borderRadius: 15,
                        marginTop: 55,
                    }}>
                        <View style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                            <View style={{
                                width: 50,
                                height: 50,
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 25,
                                alignItems: 'center',
                                justifyContent: 'center',
                                shadowColor: COLORS.bg000000,
                                shadowOffset: {
                                    width: 0,
                                    height: 10,
                                },
                                shadowOpacity: 0.25,
                                shadowRadius: 4,
                                elevation: 5,
                            }}>
                                <Icon
                                    iconSet={"Ionicons"}
                                    name={iconsName}
                                    style={{ marginLeft: 3 }}
                                    size={30}
                                    color={colorStatus}
                                />
                                {
                                    !checkStatusFainal &&
                                    <ActivityIndicator
                                        color={COLORS.bg00A98F}
                                        style={{
                                            position: 'absolute',
                                        }}
                                    />
                                }
                            </View>
                            <MyText
                                style={{
                                    color: colorStatus,
                                    fontSize: 25,
                                    textAlign: 'center',
                                    fontWeight: 'bold'
                                }}
                                text={checkStatus ? statusNameTransaction : translate("collection.transactions_processed")} />
                        </View>
                        <MyText
                            style={{
                                color: COLORS.bg000000,
                                fontSize: 15,
                                textAlign: 'center',
                                marginTop: 10
                            }}
                            text={checkStatus ? description : ''} />
                        {
                            checkStatusSuccess ?
                                <Button
                                    onPress={onSuccess}
                                    text={translate("collection.completed")}
                                    styleContainer={{
                                        borderRadius: 7,
                                        backgroundColor: COLORS.bgF49B0C,
                                        marginLeft: 10,
                                        height: 40,
                                        width: constants.getSize(130),
                                        marginTop: 40
                                    }}
                                    styleText={{
                                        color: COLORS.txtFFFFFF,
                                        fontSize: 14,
                                        fontWeight: 'bold'
                                    }}
                                />
                                :
                                <Button
                                    onPress={onClose}
                                    disabled={!helper.IsEmptyObject(dataStatus) ? false : true}
                                    text={translate("collection.close")}
                                    styleContainer={{
                                        borderRadius: 7,
                                        backgroundColor: COLORS.bg1E88E5,
                                        marginLeft: 10,
                                        height: 40,
                                        width: constants.getSize(130),
                                        marginTop: 40,
                                        opacity: !helper.IsEmptyObject(dataStatus) ? 1 : 0.5
                                    }}
                                    styleText={{
                                        color: COLORS.txtFFFFFF,
                                        fontSize: 14,
                                        fontWeight: 'bold'
                                    }}
                                />
                        }
                    </View>
                </View>
            </View >
        </KModal >

    );
}


const mapStateToProps = (state) => ({
    userInfo: state.userReducer,
    dataCreateAirtimeRefund: state.insuranceBrightsideReducer.dataCreateAirtimeRefund,
});

const mapDispatchToProps = (dispatch) => ({
    actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
    actionInsuranceBrightside: bindActionCreators(actionInsuranceBrightsideCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(CollectionStatusModal);


