import { SafeAreaView, StyleSheet, TouchableOpacity, View } from 'react-native';
import KModal from "react-native-modal";
import React from 'react';
import { connect } from 'react-redux';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { Icon, ImageURI } from '@components';
import ImageZoom from 'react-native-image-pan-zoom';

const width = constants.width;
const height = width * 1.6;

const InsuranceBenefits = ({
    isVisible,
    hideModal,
    imageURL
}) => {
    return (
        <KModal
            isVisible={isVisible}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}
            style={{ margin: 0 }}
        >
            <View style={{
                flex: 1,
                backgroundColor: COLORS.bg000000,
                paddingTop: constants.heightTopSafe,
            }}>
                <SafeAreaView style={{
                    flex: 1,
                    justifyContent: "center",
                    alignItems: 'center',
                }}>
                    <ImageZoom cropWidth={constants.width}
                        cropHeight={constants.height}
                        imageWidth={width}
                        imageHeight={height}>
                        <ImageURI
                            uri={imageURL}
                            resizeMode="contain"
                            style={{
                                width: width,
                                height: height,
                            }}
                        />
                    </ImageZoom>
                    <TouchableOpacity style={{
                        width: 50,
                        height: 50,
                        position: "absolute",
                        top: 10,
                        right: 0,
                        justifyContent: "center",
                        alignItems: "center",
                        alignSelf: "flex-end",
                    }}
                        activeOpacity={0.8}
                        onPress={hideModal}
                    >
                        <Icon
                            iconSet={"Ionicons"}
                            name={"close"}
                            size={30}
                            color={COLORS.icFFFFFF}
                        />
                    </TouchableOpacity>
                </SafeAreaView>
            </View>
        </KModal>

    )
}

const mapStateToProps = (state) => ({
    userInfo: state.userReducer
});

const mapDispatchToProps = (dispatch) => ({
});

export default connect(mapStateToProps, mapDispatchToProps)(InsuranceBenefits);

const styles = StyleSheet.create({})

