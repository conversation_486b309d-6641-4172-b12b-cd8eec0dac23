import { Modal, StyleSheet, View, TouchableOpacity } from 'react-native';
import React from 'react';

const ModalDetail = ({ visible = false, width, height, children, backgroundColor = 'rgba(0, 0, 0, 0.6)', animationType = 'slide', onClose }) => {
    return (
        <Modal animationType={animationType} transparent={true} visible={visible}>
            <TouchableOpacity
                onPress={onClose}
                activeOpacity={0.95}
                style={{
                    width: '100%',
                    height: '100%',
                    backgroundColor: backgroundColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                }}>
                <View
                    style={[
                        {
                            borderRadius: 10,
                            backgroundColor: 'white',
                            postion: 'absolute',
                        },
                        {
                            width: width,
                            height: height,
                        },
                    ]}>
                    {children}
                </View>
            </TouchableOpacity>
        </Modal>
    );
};

export default ModalDetail;

const styles = StyleSheet.create({});
