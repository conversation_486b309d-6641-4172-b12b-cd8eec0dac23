import { View, Text, TouchableOpacity } from 'react-native'
import React from 'react'
import { MyText } from '@components'

const ButtonAction = ({ title, style, onPress, disabled, opacity, styleText }) => {
    return (
        <View style={{
        }}>
            <TouchableOpacity
                onPress={onPress}
                disabled={disabled}
                style={[style, {
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: 160,
                    height: 45,
                    borderRadius: 10,
                    flexDirection: 'row',
                    opacity: opacity
                }]}
            >
                <MyText
                    text={title}
                    addSize={-1.5}
                    style={[styleText, {
                        fontSize: 15,
                        fontWeight: 'bold',
                        marginLeft: 5,
                        width: 90,
                        textAlign: 'center'
                    }]}
                />
            </TouchableOpacity>
        </View>
    )
}

export default ButtonAction
