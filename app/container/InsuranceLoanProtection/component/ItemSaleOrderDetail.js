import { StyleSheet, View, TouchableOpacity } from 'react-native';
import React from 'react';
import { COLORS } from '@styles';
import { Icon, MyText } from '@components';

const ItemSaleOrderDetail = ({ item, onBuyInsurance }) => {
    const {
        IMEI,
        PRODUCTNAME
    } = item ?? '';

    return (
        <View style={{
            padding: 10
        }}>
            <View style={{
                width: '100%',
                backgroundColor: 'white',
                borderRadius: 7,
                padding: 5,
                shadowColor: '#000',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 5,
            }}>
                <View style={{
                    flexDirection: 'row',
                }}>
                    <View style={{
                        flex: 1,
                        justifyContent: 'center',
                    }}>
                        <View style={{
                            width: '100%',
                            backgroundColor: COLORS.bg00A896,
                            height: 40,
                            justifyContent: 'center',
                            alignItems: 'center',
                            borderRadius: 4
                        }}>
                            <TextHeder
                                name={"IMEI: "}
                                value={IMEI}
                                key={"IMEI"}
                                extraStyle={{ fontWeight: 'bold' }}
                            />
                        </View>
                        <TextField
                            name={"Sản phẩm: "}
                            value={PRODUCTNAME}
                            key={"PRODUCTNAME"}
                        />
                    </View>
                </View>
                <View style={{
                    marginTop: 5
                }}>
                    <TouchableOpacity
                        onPress={onBuyInsurance}
                        style={{
                            width: '40%',
                            height: 40,
                            backgroundColor: '#2FB47B',
                            marginTop: 10,
                            borderRadius: 5,
                            paddingHorizontal: 10,
                            alignItems: 'center',
                            alignSelf: 'center',
                            justifyContent: 'center',
                            flexDirection: 'row',
                            alignSelf: 'flex-end',
                        }}>
                        <View style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: 30,
                            height: 30,
                            borderRadius: 30 / 2,
                            borderColor: COLORS.bgFFFFFF,
                            borderWidth: 1
                        }}>
                            <Icon
                                iconSet={"Ionicons"}
                                name={"cart-outline"}
                                color={COLORS.bgFFFFFF}
                                size={20}
                                style={{

                                }}
                            />
                        </View>
                        <MyText
                            text={'Mua BHMR'}
                            style={{
                                fontWeight: 'bold',
                                color: COLORS.bgFFFFFF,
                                marginLeft: 5
                            }}
                        />
                    </TouchableOpacity>
                </View>
            </View>

        </View>
    )
}

export default ItemSaleOrderDetail

const styles = StyleSheet.create({})

const TextField = ({ name, value, extraStyle, isWarning }) => {
    return (
        <MyText
            text={name}
            addSize={-1.5}
            style={{
                color: COLORS.txt8E8E93,
                marginTop: 10,
                marginLeft: 5
            }}>
            <MyText
                text={value}
                style={[{
                    color: COLORS.bg000000, flexWrap: 'wrap'
                }, extraStyle]}
            />
        </MyText>
    );
}

const TextHeder = ({ name, value, extraStyle, isWarning }) => {
    return (
        <MyText
            text={name}
            addSize={-1.5}
            style={{
                color: COLORS.bgFFFFFF,
                marginLeft: 5,
                fontWeight: 'bold'
            }}>
            <MyText
                text={value}
                style={[{
                    color: COLORS.bgFFFFFF, flexWrap: 'wrap'
                }, extraStyle]}
            />
        </MyText>
    );
}