import { StyleSheet, Text, View, TouchableOpacity, LayoutAnimation, Dimensions, Animated } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { Icon, MyText } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';

const Accordion = ({ style, Children, title,
  iconColor = COLORS.bg2FB47C, status = false, width = Dimensions.get('window').width - 80, backgroundColor = COLORS.bgF0F0F0, titleColor = COLORS.bg2C8BD7 }) => {
  const [isOpen, setIsOpen] = useState(status);

  const toggleOpen = () => {
    setIsOpen(value => !value);
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  }

  const rotateValue = useRef(new Animated.Value(0)).current;

  const rotateIcon = () => {
    Animated.timing(rotateValue, {
      toValue: isOpen ? 1 : 0,
      duration: 300,
      useNativeDriver: true
    }).start();
  };
  useEffect(() => {
    rotateIcon();
  }, [isOpen]);

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg']
  });
  return (
    <View style={{
      width: constants.width,
      alignItems: 'center'
    }}>
      <TouchableOpacity
        style={[styles.list, !isOpen ? styles.hidden : undefined, {
          // borderWidth: isOpen ? 1 : 0
        }]}
        onPress={toggleOpen} activeOpacity={0.8}>
        <View style={{
          width: constants.width - 25,
          height: 40,
          backgroundColor: backgroundColor,
          flexDirection: 'row',
        }}>
          <View style={{
            flex: 1,
            justifyContent: 'center',
            top: 1.5,
            paddingHorizontal: 10
          }}>
            <MyText
              text={title}
              ShowFW={true}
              fontSize={17}
              style={{
                fontWeight: 'bold',
                color: titleColor
              }}
            />
          </View>
          <View style={{
            width: 80,
            height: 40,
            alignItems: 'center',
            justifyContent: 'flex-end',
            flexDirection: 'row',
            paddingRight: 15

          }}>
            <Animated.View
              style={[{ transform: [{ rotate }] }]}
            >
              <Icon
                iconSet='Entypo'
                name={
                  isOpen
                    ? 'arrow-with-circle-up'
                    : 'arrow-with-circle-down'
                }
                size={20}
                color={iconColor}
              />
            </Animated.View>
          </View>
        </View>
        <View style={[{
          width: constants.width - 25,
          backgroundColor: 'yellow',
        }, { ...style }]}>
          {Children}
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    width: Dimensions.get('window').width - 25,
    height: 40,
  },
  hidden: {
    height: 40,
    width: constants.width - 25,
  },
  list: {
    overflow: 'hidden',
    width: constants.width - 25,
    backgroundColor: 'white',
    borderRadius: 10,
    // borderWidth: 2,
    borderColor: 'gray',
    marginTop: 10,
  },

});
export default Accordion
