import { StyleSheet, View, TouchableOpacity } from 'react-native'
import React from 'react'
import { COLORS } from '@styles'
import { Icon, MyText } from '@components'
import { helper } from '@common'


const ItemLoanInsurance = ({ item, onShowDetail, dataSaleOrder }) => {
    return (
        <View>
            <View style={{
                width: '100%',
            }}>
                <View style={{
                    flexDirection: 'row',
                    height: 40,
                }}>
                    <View style={{
                        flex: 1,
                        justifyContent: 'center',
                    }}>
                        <MyText
                            text={item.SALEORDERID}
                            addSize={2}
                            style={{
                                fontWeight: 'bold',
                                color: '#437FE6'
                            }}
                        />
                    </View>
                </View>

                <ContractField
                    title={'Số hợp đồng'}
                    contractId={item.PROFILEID}
                    status={item.REVIEWSTATUSNAME}
                />
                <TextField
                    title={'Đối tác trả góp'}
                    value={item.PARTNERINSTALLMENTNAME}
                />
                <TextField
                    title={'Khách hàng'}
                    value={item.CUSTOMERNAME}
                />
                <TextField
                    title={'Số điện thoại'}
                    value={item.CUSTOMERPHONE}
                />
                <MoneyField
                    title={'Khoản vay'}
                    value={`${helper.formatMoney(item.LOANAMOUNT)}`}
                    color={COLORS.bgFF0000}
                />
                <TextField
                    title={'Ngày bắt đầu'}
                    value={item.REVIEWDATE}
                />
            </View>
            <View style={{
                paddingBottom: 20
            }}>
                <TouchableOpacity
                    onPress={onShowDetail}
                    style={{
                        width: '40%',
                        height: 40,
                        backgroundColor: '#F49B0C',
                        marginTop: 17,
                        borderRadius: 20,
                        paddingHorizontal: 10,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'center',
                        flexDirection: 'row',
                        alignSelf: 'center',
                    }}>
                    <View style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: 30,
                        height: 30,
                        borderRadius: 30 / 2,
                        borderColor: COLORS.bgFFFFFF,
                        borderWidth: 1
                    }}>
                        <Icon
                            iconSet={"Ionicons"}
                            name={"cart-outline"}
                            color={COLORS.bgFFFFFF}
                            size={20}
                            style={{

                            }}
                        />
                    </View>
                    <MyText
                        text={'Mua BHKV'}
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.bgFFFFFF,
                            marginLeft: 5
                        }}
                    />
                </TouchableOpacity>
            </View>
        </View>
    )
}

export default ItemLoanInsurance

const styles = StyleSheet.create({})

const TextField = ({ title, value, extraStyle, isWarning }) => {
    return (
        <MyText
            text={`${title}: `}
            addSize={-1.5}
            style={{
                color: COLORS.txt8E8E93,
                marginTop: 10
            }}>
            <MyText
                text={value}
                style={[{
                    color: isWarning ? COLORS.txtFF0000 : COLORS.txt333333,
                }, extraStyle]}
            />
        </MyText>
    );
}
const MoneyField = ({ title, value, color = COLORS.txt333333 }) => {
    return (
        <View style={{
            flexDirection: "row",
            justifyContent: "space-between",
            marginTop: 10
        }}>
            <MyText
                text={`${title}: `}
                addSize={-1.5}
                style={{
                    color: COLORS.txt8E8E93,
                }}
            />
            <MyText
                text={value}
                addSize={-1.5}
                style={{
                    color: color,
                    fontWeight: 'bold'
                }}
            />
        </View>
    );
}

const ContractField = ({ title, contractId, status }) => {
    return (
        <View style={{
            flexDirection: 'row'
        }}>
            <MyText
                text={`${title}: `}
                addSize={-1.5}
                style={{
                    color: COLORS.txt8E8E93,
                }}
            />
            <MyText
                text={`${contractId} `}
                addSize={-1.5}
                style={{
                    fontWeight: 'bold'
                }}
            />
            <MyText
                text={`-  ${status}`}
                addSize={-1.5}
                style={{
                    color: COLORS.txt3369E7,
                    fontWeight: 'bold'
                }}
            />
        </View>
    )
}