import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Icon, MyText } from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import { translate } from '@translate';
import DropdownMenu from './DropdowmnBrightside';
import { COLORS } from "@styles";

const ItemCollection = ({
  item,
  onPressDetail,
  onPrint,
  onPayment,
  onAskStatus,
  onActiveCollection,
  onEdit,
  onDelete,
  navigation,
  ServiceCategoryID,
  AirtimeServiceGroupID,
  onPreEnterOTP
}) => {
  const {
    SERVICEVOUCHERID,
    SALEORDERID,
    AIRTIMETRANSACTIONTYPENAME,
    CUSTOMERALIAS,
    AIRTIMESTATUSNAME,
    CUSTOMERNAME,
    TOTALPAID,
    IsDeletedSO,
    IsQueryStatus,
    IsReActive,
    ISCOLLECTMONEY,
    ISPENDING,
    ISALLOWDELETE,
    DESCRIPTION,
    ISSPRINTPAYBILL,
    ISSO<PERSON>LETED,
    AIRTIMETRANSACTIONTYPEID,
    ISALLOWREFUNDSERVICE,
    ISPRINTINSURANCEFILE,
    ISVERIFYOTP,
  } = item;
  const [isShow, setIsShow] = useState(false)

  return (
    <View style={{
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 5,
      paddingVertical: 5
    }}>
      <View
        style={{
          width: constants.width,
          padding: 8,
          width: '97%',
          padding: 10,
          borderColor: COLORS.bd218DEB,
          backgroundColor: COLORS.bgFFFFFF,
          borderRadius: 10,
          shadowColor: COLORS.bg000000,
          shadowOffset: {
            width: 0,
            height: 2
          },
          shadowOpacity: 0.25,
          shadowRadius: 4,
          elevation: 5,
        }}>
        <View style={{
          flexDirection: "row",
        }}>
          <TouchableOpacity
            disabled={AIRTIMETRANSACTIONTYPEID == 1513 ? false : true}
            onPress={onPressDetail}
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center'
            }}
          >
            <MyText
              text={SERVICEVOUCHERID}
              style={{
                color: COLORS.bg2C8BD7,
                fontWeight: 'bold',
                textAlign: 'center',
              }}
            />
            <MyText
              text={" - "}
              addSize={-1.5}
              style={{
                color: COLORS.bgEA1D5D,
                fontWeight: 'bold'
              }}
            />
            <MyText
              text={CUSTOMERALIAS}
              addSize={-1.5}
              style={{
                color: COLORS.bg000000,
                fontSize: 15,
              }}

            />
            <Icon
              iconSet={'Ionicons'}
              name={"create-outline"}
              color={COLORS.bg2C8BD7}
              size={16}
              style={{
                marginLeft: 4,
                marginBottom: 4
              }}
            />
          </TouchableOpacity>
        </View>
        <TextField
          name={translate("collection.export_request")}
          value={SALEORDERID}
          key={"SALEORDERID"}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        <TextField
          name={translate("collection.transaction_type")}
          value={AIRTIMETRANSACTIONTYPENAME}
          key={"AIRTIMETRANSACTIONTYPENAME"}
          isWarning={AIRTIMETRANSACTIONTYPENAME}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        <TextField
          name={translate("collection.status")}
          value={AIRTIMESTATUSNAME}
          key={"AIRTIMESTATUSNAME"}
          extraStyle={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
        />
        <MyText
          text={`${translate("collection.note")} ${DESCRIPTION}`}
          addSize={-1.5}
          style={{
            color: COLORS.bg8E8E93,
            marginTop: 10,
            fontStyle: 'italic'
          }}
        />
        <TextField
          name={translate("collection.customer_name")}
          value={CUSTOMERNAME}
          key={"CUSTOMERNAME"}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        <MoneyField
          name={translate("collection.amount_of_money")}
          value={`${helper.formatMoney(TOTALPAID, false)} `}
          color={COLORS.txtFF0000}
          key={"Payment"}
        />
        {
          ISSODELETED == 1 &&
          <TextField
            name={translate("collection.refund_status")}
            value={translate("collection.refunds")}
            key={"ISSODELETED"}
            extraStyle={{ color: COLORS.bg000000 }}
          />
        }
        <ButtonAction
          navigation={navigation}
          onPayment={onPayment}
          onPrint={onPrint}
          onActionList={() => setIsShow(!isShow)}
          onAskStatus={onAskStatus}
          onEdit={onEdit}
          onActiveCollection={onActiveCollection}
          ishow={isShow}
          isDeletedSO={IsDeletedSO}
          IsQueryStatus={IsQueryStatus}
          IsReActive={IsReActive}
          ISCOLLECTMONEY={ISCOLLECTMONEY}
          ISPENDING={ISPENDING}
          ISSPRINTPAYBILL={ISSPRINTPAYBILL}
          item={item}
          ServiceCategoryID={ServiceCategoryID}
          AirtimeServiceGroupID={AirtimeServiceGroupID}
          ISALLOWREFUNDSERVICE={ISALLOWREFUNDSERVICE}
          ISPRINTINSURANCEFILE={ISPRINTINSURANCEFILE}
          ISVERIFYOTP={ISVERIFYOTP}
          onPreEnterOTP={onPreEnterOTP}
        />
        {
          ISALLOWDELETE == 1 ?
            <ButtonDelete
              isVisible={true}
              onDelete={onDelete}
              appName={"POS"}
            />
            :
            null
        }
      </View>
    </View>
  );
}

export default ItemCollection;

const MoneyField = ({ name, value, color = COLORS.txt333333 }) => {
  return (
    <View style={{
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 10
    }}>
      <MyText
        text={name}
        addSize={-1.5}
        style={{
          color: COLORS.txt8E8E93,
        }}
      />
      <MyText
        text={value}
        addSize={-1.5}
        style={{
          color: color,
          fontWeight: 'bold'
        }}
      />
    </View>
  );
}

const TextField = ({ name, value, extraStyle, isWarning }) => {
  return (
    <MyText
      text={name}
      addSize={-1.5}
      style={{
        color: COLORS.txt8E8E93,
        marginTop: 10
      }}>
      <MyText
        text={value}
        style={[{
          color: isWarning ? COLORS.txtFF0000 : COLORS.txt333333,
        }, extraStyle]}
      />
    </MyText>
  );
}

const IconField = ({
  title,
  name,
  color,
  textAlign,
  onPress,
  iconSet = 'Ionicons'
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.6}
      onPress={onPress}
    >
      <MyText
        text={title}
        addSize={-1}
        style={{
          color: color,
          textAlign: textAlign,
          paddingVertical: 2
        }}>
        {" "}
        <Icon
          iconSet={iconSet}
          name={name}
          color={color}
          size={14}
        />
      </MyText>
    </TouchableOpacity>
  );
}

const ButtonAction = ({
  onEdit,
  onPrint,
  onAskStatus,
  ISCOLLECTMONEY,
  ISPENDING,
  ISSPRINTPAYBILL,
  item,
  ServiceCategoryID,
  AirtimeServiceGroupID,
  ISALLOWREFUNDSERVICE,
  ISPRINTINSURANCEFILE,
  ISVERIFYOTP,
  onPreEnterOTP
}) => {
  const [visibleMenu, setVisibleMenu] = useState(false);

  let menu = [];
  if (ISPRINTINSURANCEFILE == 1) {
    menu = [
      ...menu, {
        screen: 'PrintCertificate',
        title: 'In GCN bảo hiểm',
        defaultTab: {},
        iconName: 'print',
        iconSet: 'FontAwesome6',
        color: "#5BB180"
      },
    ]
  }
  if (ISALLOWREFUNDSERVICE == 1) {
    menu = [
      ...menu, {
        screen: 'CancelService',
        title: 'Tạo yêu cầu hoàn tiền',
        defaultTab: {},
        iconName: 'rotate-left',
        iconSet: 'FontAwesome6',
        color: "#5BB180"
      }
    ]
  }

  return (
    <View style={{
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 6,
      width: constants.width,
      paddingRight: 15
    }}>
      {
        ISPENDING == 1 ?
          <IconField
            title={translate("collection.click_check_result")}
            name={"reload-outline"}
            color={COLORS.bgF49B0C}
            textAlign={"right"}
            onPress={onAskStatus}
          />
          :
          null
      }
      {
        ISCOLLECTMONEY == 1 ?
          <IconField
            title={translate("collection.collect_money")}
            name={"arrow-redo-outline"}
            color={COLORS.ic008000}
            textAlign={"left"}
            onPress={onEdit}
          />
          :
          null
      }
      {
        ISSPRINTPAYBILL == 1 ?
          <IconField
            title={translate("collection.print_receipt")}
            name={"print-outline"}
            color={COLORS.icC822B0}
            textAlign={"right"}
            onPress={onPrint}
          />
          :
          null
      }
      {
        ISVERIFYOTP == 0 ?
          <IconField
            title={"Xác nhận OTP"}
            name={"reload-outline"}
            color={COLORS.txtFF00BF}
            textAlign={"right"}
            onPress={onPreEnterOTP}
          />
          :
          null
      }
      <View
        style={{ marginRight: 15 }}
      >
        <DropdownMenu
          visible={visibleMenu}
          setVisible={setVisibleMenu}
          menu={menu}
          data={item}
          ServiceCategoryID={ServiceCategoryID}
          AirtimeServiceGroupID={AirtimeServiceGroupID}
        />
      </View>
    </View>
  );
}

const ButtonDelete = ({ onDelete, isVisible }) => {
  return (
    <View style={{
      position: "absolute",
      top: 0,
      right: 0,
      alignItems: "flex-end"
    }}>
      {
        isVisible &&
        <TouchableOpacity style={{
          padding: 6,
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: COLORS.btnDCE1E1,
          borderBottomStartRadius: 14,
        }}
          activeOpacity={0.6}
          onPress={onDelete}
        >
          <Icon
            iconSet={"Ionicons"}
            name={"trash"}
            color={COLORS.icFF0000}
            size={14}
          />
          <MyText
            text={translate('common.btn_cancel')}
            addSize={-1.5}
            style={{
              color: COLORS.txtFF0000
            }}
          />
        </TouchableOpacity>
      }
    </View>
  );
}