import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { MyText } from '@components'
import { COLORS } from '@styles'

const TextView = ({ name, value, color }) => {
    return (
        <View style={{
            flexDirection: "row",
            justifyContent: "space-between",
            marginTop: 10
        }}>
            <MyText
                text={name}
                addSize={-1.5}
                style={{
                    color: COLORS.txt8E8E93,
                }}
            />
            <MyText
                text={value}
                addSize={-1.5}
                style={{
                    color: color,
                    width: 'auto',
                    marginLeft: 5
                }}
            />
        </View>
    )
}

export default TextView

const styles = StyleSheet.create({})