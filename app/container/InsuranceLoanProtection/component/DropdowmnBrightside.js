import { Icon, MyText } from '@components';
import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Menu, MenuItem } from 'react-native-material-menu';
import { useNavigation } from '@react-navigation/native';
import { COLORS } from '@styles';

const DropdowmnBrightside = ({ menu = [], data = {}, ServiceCategoryID = "", AirtimeServiceGroupID = "" }) => {
    const [visible, setVisible] = React.useState(false);
    const navigation = useNavigation();

    return (
        <Menu
            visible={visible}
            animationDuration={visible ? 300 : 30}
            anchor={
                <TouchableOpacity
                    onPress={() => setVisible((currentState) => !currentState)}>
                    <Icon
                        iconSet="MaterialIcons"
                        name="more-horiz"
                        size={30}
                        color={COLORS.btn6CAF52}
                    />
                </TouchableOpacity>
            }
            onRequestClose={() => setVisible(false)}>
            {menu.length > 0 && menu.map((item) => {
                const { title, screen, defaultTab, iconSet, iconName } = item;
                return (
                    <MenuItem
                        key={defaultTab}
                        textStyle={{ color: "#141414" }}
                        onPress={() => {
                            navigation.navigate(screen, { data, ServiceCategoryID, AirtimeServiceGroupID });
                            setVisible(false);
                        }}
                    >
                        <View
                            style={{
                                alignItems: 'center',
                                flexDirection: 'row',
                                paddingLeft: 8,
                                paddingTop: 8
                            }}>
                            <Icon
                                iconSet={iconSet}
                                name={iconName}
                                size={24}
                                style={{ marginRight: 4 }}
                                color={item.color}
                            />
                            <MyText style={{ marginLeft: 4 }} text={title} />
                        </View>
                    </MenuItem>
                );
            })}
        </Menu>
    );
};

export default DropdowmnBrightside;