import React from 'react';
import { View, TouchableOpacity, TextInput, Keyboard } from 'react-native';
import { Icon } from '@components';

import { COLORS } from '@styles';

const SearchBar = ({
    value,
    onChangeText,
    onSearch,
    disabled,
    onDelete,
    placeholder,
    hasBarcodeScanner = false,
    onOpenBarcodeScanner
}) => {
    const handleSearch = () => {
        Keyboard.dismiss();
        onSearch();
    };
    return (
        <View
            style={{
                flexDirection: 'row',
                marginVertical: 10,
                borderWidth: 1,
                borderColor: COLORS.bdE4E4E4,
                height: 38,
                backgroundColor: COLORS.bgFFFFFF,
                borderRadius: 19,
                justifyContent: 'space-between',
                alignItems: 'center',
                alignSelf: 'center',
                opacity: disabled ? 0.4 : 1,
                flex: 1
            }}>
            <TextInput
                value={value}
                onChangeText={onChangeText}
                placeholder={placeholder}
                placeholderTextColor={COLORS.txt808080}
                onSubmitEditing={handleSearch}
                style={{
                    flex: 1,
                    height: 36,
                    paddingLeft: 20,
                    fontSize: 12.5
                }}
                maxLength={30}
                editable={!disabled}
            />
            {!!value && (
                <TouchableOpacity onPress={onDelete}>
                    <Icon
                        iconSet="Entypo"
                        name="circle-with-cross"
                        color={COLORS.bgE0E0E0}
                        size={20}
                    />
                </TouchableOpacity>
            )}
            {!value && hasBarcodeScanner && (
                <TouchableOpacity
                    style={{
                        marginRight: 5,
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginLeft: 10
                    }}
                    onPress={onOpenBarcodeScanner}
                    activeOpacity={0.7}>
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        name="barcode-scan"
                        color={COLORS.icFFD400}
                        size={20}
                    />
                </TouchableOpacity>
            )}
            <TouchableOpacity
                style={{
                    height: 38,
                    width: 48,
                    justifyContent: 'center',
                    alignItems: 'flex-end',
                    paddingRight: 20
                }}
                onPress={handleSearch}
                disabled={disabled}>
                <Icon
                    iconSet="Ionicons"
                    name="search"
                    color={COLORS.ic288AD6}
                    size={22}
                />
            </TouchableOpacity>
        </View>
    );
};

export default SearchBar;
