import { API_CONST } from "@constants";
import { helper, dateHelper } from "@common";
import { apiBase, METHOD, ERROR, EMPTY, SUCCESS } from "@config";
import { translate } from "@translate";

const {
    API_GET_SERVICE_LIST,
    API_VALIDATE_DATA_SERVICE_REQUEST,
    API_GET_PRICE_SERVICE,
    API_GET_CREATE_SERVICE_REQUEST,
    API_GET_DATA_COLLECTION_MANAGER_NEW,
    API_GET_PROMOTION_SERVICE,
    API_GET_INFO_REFUND,
    API_CREATE_AIRTIME_REFUND,
    API_CHECK_STATUS_TICKET_SERVICE,
    API_GET_SERVICE_LIST_HISTORY_REFUND,
    API_GET_PROCESSOUT_VOUCHER,
    API_GET_CANCEL_AND_CREATE_AIRTIME,
    API_GET_QUERY_STATUS,
    API_GET_DATA_INFO,
    API_GET_SEARCH_SO,
    API_GET_DETAIL_SO,
    API_GET_PROVINCE,
    API_GET_DISTRICT,
    API_GET_WARD,
    API_GET_PROCESS_SERVICE_REQUEST,
    API_GET_TRANSACTION_DETAIL
} = API_CONST;

const START_GET_SERVICE_LIST = "START_GET_SERVICE_LIST";
const STOP_GET_SERVICE_LIST = "STOP_GET_SERVICE_LIST";
const START_VALIDATE_DATA_SERVICE_REQUEST = "START_VALIDATE_DATA_SERVICE_REQUEST";
const STOP_VALIDATE_DATA_SERVICE_REQUEST = "STOP_VALIDATE_DATA_SERVICE_REQUEST"
const START_ADD_TO_SALE_ORDER_CART = "START_ADD_TO_SALE_ORDER_CART";
const STOP_ADD_TO_SALE_ORDER_CART = "STOP_ADD_TO_SALE_ORDER_CART";
const CLEAR_DATA_VALIDATE_SERVICE_REQUEST = 'CLEAR_DATA_VALIDATE_SERVICE_REQUEST';
const START_SEARCH_HISTORY_INSURANCE = "START_SEARCH_HISTORY_INSURANCE";
const STOP_SEARCH_HISTORY_INSURANCE = "STOP_SEARCH_HISTORY_INSURANCE";
const START_GET_CREATE_AIRTIME_REFUND = "START_GET_CREATE_AIRTIME_REFUND";
const STOP_GET_CREATE_AIRTIME_REFUND = "STOP_GET_CREATE_AIRTIME_REFUND";
const START_GET_SEARCH_SO = 'START_GET_SEARCH_SO';
const STOP_GET_SEARCH_SO = "STOP_GET_SEARCH_SO";
const CLEAR_DATA_SEARCH_SO = "CLEAR_DATA_SEARCH_SO"
const START_GET_DETAIL_SO = "START_GET_DETAIL_SO";
const STOP_GET_DETAIL_SO = "STOP_GET_DETAIL_SO";
const START_GET_DATA_BUY_INSURANCE = "START_GET_DATA_BUY_INSURANCE";
const STOP_GET_DATA_BUY_INSURANCE = "STOP_GET_DATA_BUY_INSURANCE"
const UPDATE_ITEM_AIRTIME_CODE = "UPDATE_ITEM_AIRTIME_CODE";
const START_GET_PROMOTION_SERVICE = "START_GET_PROMOTION_SERVICE";
const STOP_GET_PROMOTION_SERVICE = "STOP_GET_PROMOTION_SERVICE";
const START_GET_PROVINCE = "START_GET_PROVINCE";
const STOP_GET_PROVINCE = "STOP_GET_PROVINCE";
const START_GET_DISTRICT = "START_GET_DISTRICT";
const STOP_GET_DISTRICT = "STOP_GET_DISTRICT";
const START_GET_PRICE_SERVICE = "START_GET_PRICE_SERVICE";
const STOP_GET_PRICE_SERVICE = "STOP_GET_PRICE_SERVICE";
const UPDATE_CUSTOMER_AIRTIME_SERCICE = "UPDATE_CUSTOMER_AIRTIME_SERCICE";
const CLEAR_DATA_VALIDATE_SERVICE = "CLEAR_DATA_VALIDATE_SERVICE";
const START_GET_SEND_OTP_PROCESS = "START_GET_SEND_OTP_PROCESS";
const STOP_GET_SEND_OTP_PROCESS = "STOP_GET_SEND_OTP_PROCESS";


export const actionInsuranceLoanProtection = {
    START_GET_SERVICE_LIST,
    STOP_GET_SERVICE_LIST,
    START_ADD_TO_SALE_ORDER_CART,
    STOP_ADD_TO_SALE_ORDER_CART,
    START_VALIDATE_DATA_SERVICE_REQUEST,
    STOP_VALIDATE_DATA_SERVICE_REQUEST,
    CLEAR_DATA_VALIDATE_SERVICE_REQUEST,
    START_SEARCH_HISTORY_INSURANCE,
    STOP_SEARCH_HISTORY_INSURANCE,
    START_GET_CREATE_AIRTIME_REFUND,
    STOP_GET_CREATE_AIRTIME_REFUND,
    START_GET_SEARCH_SO,
    STOP_GET_SEARCH_SO,
    CLEAR_DATA_SEARCH_SO,
    START_GET_DETAIL_SO,
    STOP_GET_DETAIL_SO,
    START_GET_DATA_BUY_INSURANCE,
    STOP_GET_DATA_BUY_INSURANCE,
    UPDATE_ITEM_AIRTIME_CODE,
    START_GET_PROVINCE,
    STOP_GET_PROVINCE,
    START_GET_DISTRICT,
    STOP_GET_DISTRICT,
    START_GET_PRICE_SERVICE,
    STOP_GET_PRICE_SERVICE,
    UPDATE_CUSTOMER_AIRTIME_SERCICE,
    START_GET_PROMOTION_SERVICE,
    STOP_GET_PROMOTION_SERVICE,
    CLEAR_DATA_VALIDATE_SERVICE,
    START_GET_SEND_OTP_PROCESS,
    STOP_GET_SEND_OTP_PROCESS,
};

//handle function
export const getServiceList = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: data.catalogID,
            serviceGroupID: data.serviceGroupID
        }
        dispatch(start_get_service_list())
        apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
            .then((response) => {
                console.log("getServiceList success", response);
                if (helper.IsNonEmptyArray(response.object)) {
                    dispatch(stop_get_service_list(response.object, false, '', false));
                } else {
                    dispatch(stop_get_service_list([], true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_get_service_list([], false, error.msgError, true))
                console.log("getServiceList error", error);
            })
    }
};

export const getPromotionService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                productID: data.productID,
                phoneNumber: data.phoneNumber,
                insProgramID: data.insProgramID,
                mainProductID: data.mainProductID
            };
            dispatch(start_get_promotion_service());
            apiBase(API_GET_PROMOTION_SERVICE, METHOD.POST, body)
                .then((response) => {
                    console.log("getPromotionService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_get_promotion_service(response.object));
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPromotionService error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getPriceService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                productID: data.productID,
                loanAmount: data.retailPriceVAT,
                outputDate: data.outputDate,
                insuranceMonth: data.insuranceMonth,
                mainProductID: data.mainProductID,
                productPromotion: data.productPromotion,
                mainSaleOrderID: data.mainSaleOrderID,
                termLoan: data.termLoan
            };
            dispatch(start_get_price_service())
            apiBase(API_GET_PRICE_SERVICE, METHOD.POST, body)
                .then((response) => {
                    console.log("getPriceService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_get_price_service(response.object));
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPriceService error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const addToSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                airTimeTransactionBO: data.airTimeTransactionBO,
                loanAmount: data.retailPriceVAT,
                insuranceID: data.insuranceID,
                insuranceID: data.insuranceID,
                insCustomerID: data.insCustomerID,
                insProgramID: data.insProgramID,
                insuranceMonth: data.insuranceMonth,
                insuranceApplyID: data.insuranceApplyID,
                mainSaleOrderID: data.mainSaleOrderID,
                mainProductID: data.mainProductID,
                productID: data.productID,
                phoneNumber: data.phoneNumber,
                productPromotion: data.productPromotion,
                voucherConcern: data.voucherConcern,
                paymentAmountMonthly: data.paymentAmountMonthly,
                profileID: data.profileID,
                termLoan: data.termLoan,
                totalPrePaid: data.totalPrePaid,
                customerIDCard: data.customerIDCard,
                customerBirthday: data.customerBirthday,
                startDate: data.startDate,
                endDate: data.endDate
            };
            dispatch(start_add_to_sale_order_cart());
            apiBase(API_GET_CREATE_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("addToSaleOrderCart success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_add_to_sale_order_cart(object));
                        resolve(response);
                    } else {
                        dispatch(stop_add_to_sale_order_cart({}));
                        reject({ msgError: translate("saleOrder.error_create_order") });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("addToSaleOrderCart error", error);
                    reject(msgError);
                });
        });
    };
};

export const getSearchHistoryInsurance = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                Keyword: data.keyword.length > 0 ? data.keyword : '',
                ProcessUser: getState().userReducer.userName,
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                IsDeleted: false,
                airtimetrsTypeIDList: data.airtimetrsTypeIDList ? data.airtimetrsTypeIDList : '',
                searchType: data.searchType ? data.searchType : ''
            }
            dispatch(start_search_history_insurance())
            apiBase(API_GET_DATA_COLLECTION_MANAGER_NEW, METHOD.POST, body)
                .then((response) => {
                    console.log("getSearchHistoryInsurance BODY", response.object);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getSearchHistoryInsurance success", response);
                        dispatch(stop_search_history_insurance(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_search_history_insurance([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getSearchHistoryInsurance err', error);
                    dispatch(stop_search_history_insurance([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }
}

export const getServiceListHistory = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                isLoadSearchBy: data.isLoadSearchBy
            }
            apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
                .then((response) => {
                    console.log("getServiceListHistory success", response);
                    if (helper.IsNonEmptyArray(response.object)) {
                        resolve(response.object)
                    } else {
                        reject('Không lấy được dữ liệu')
                    }
                }).catch(error => {
                    reject(error.msgError)
                    console.log("getServiceListHistory error", error);
                })
        }
        )

    }
};

export const getInfoRefund = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                productID: data.productID,
                inputTime: data.inputTime
            };
            apiBase(API_GET_INFO_REFUND, METHOD.POST, body)
                .then((response) => {
                    console.log("getInfoRefund success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getInfoRefund error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const createAirtimeRefund = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                airtimeTransactionID: data.airtimeTransactionID,
                totalRefundAmount: data.totalRefundAmount,
                rqRefundTypeID: data.rqRefundTypeID,
                reasonID: data.reasonID,
                refundNote: data.refundNote,
                approvedUser: data.approvedUser,
                approvedDate: data.approvedDate
            };
            dispatch(start_get_create_airtime_refund());
            apiBase(API_CREATE_AIRTIME_REFUND, METHOD.POST, body)
                .then((response) => {
                    console.log("createAirtimeRefund success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response.object);
                        dispatch(stop_get_create_airtime_refund(response.object, false, '', false));
                    }
                })
                .catch((error) => {
                    console.log("createAirtimeRefund error", error);
                    dispatch(stop_get_create_airtime_refund({}, false, error.msgError, true))
                    reject(error.msgError);
                });
        });
    };
};

export const checksSatusTicketService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                ticketID: data.TICKETID,
                airtimeTransactionID: data.AIRTIMETRANSACTIONID
            };
            apiBase(API_CHECK_STATUS_TICKET_SERVICE, METHOD.POST, body).then((response) => {
                console.log("checksSatusTicketService success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("checksSatusTicketService error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getQuerysTatusServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionRFID: data.airtimeTransactionRFID
            };
            apiBase(API_GET_CANCEL_AND_CREATE_AIRTIME, METHOD.POST, body)
                .then((response) => {
                    console.log("getQuerysTatusServiceRequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getQuerysTatusServiceRequest error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getServiceListHistoryRefund = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                Keyword: data.keyword.length > 0 ? data.keyword : '',
                ProcessUser: getState().userReducer.userName,
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                airtimetrsTypeIDList: data.airtimetrsTypeIDList ? data.airtimetrsTypeIDList : '',
                searchType: data.searchType ? data.searchType : '',
                approvalStatus: data.approvalStatus
            }
            dispatch(start_search_history_insurance())
            apiBase(API_GET_SERVICE_LIST_HISTORY_REFUND, METHOD.POST, body)
                .then((response) => {
                    console.log("getServiceListHistoryRefund BODY", response.object);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getServiceListHistoryRefund success", response);
                        dispatch(stop_search_history_insurance(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_search_history_insurance([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getServiceListHistoryRefund err', error);
                    dispatch(stop_search_history_insurance([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }
}

export const getServiceListRefund = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                isLoadSearchBy: data.isLoadSearchBy,
                "isSearchByRefund": data.isLoadSearchBy,
            }
            dispatch(start_get_service_list())
            apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
                .then((response) => {
                    console.log("getServiceListRefund success", response);
                    if (helper.IsNonEmptyArray(response.object)) {
                        resolve(response.object)
                    } else {
                        reject('Không lấy được dữ liệu')
                    }
                }).catch(error => {
                    reject(error.msgError)
                    console.log("getServiceListRefund error", error);
                })
        }
        )

    }
};
export const getStatusHistory = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionRFID: data.airtimeTransactionRFID
            }
            apiBase(API_GET_PROCESSOUT_VOUCHER, METHOD.POST, body)
                .then((response) => {
                    console.log('getStatusHistory success', response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                }).catch((error) => {
                    console.log('getStatusHistory error', error);
                    reject(error);
                })
        })
    }
}

export const queryTransactionPartner = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                productID: data.productID,
                saleOrderID: data.saleOrderID
            };
            apiBase(API_GET_QUERY_STATUS, METHOD.POST, body).then((response) => {
                console.log("queryTransactionPartner success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("queryTransactionPartner error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getDataInfo = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "serviceVoucherID": data.serviceVoucherID
            };
            apiBase(API_GET_DATA_INFO, METHOD.POST, body).then((response) => {
                console.log("getDataInfo success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            })
                .catch((error) => {
                    console.log("getDataInfo error", error);
                    reject(error)
                });
        });
    };
};

export const getDataSearchSO = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                validateType: data.validateType,
                searchType: data.searchType,
                PartnerInstallID: data.PartnerInstallID,
                keyword: data.keyword.length > 0 ? data.keyword : '',
                fromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                toDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                extraData: data.extraData

            }
            dispatch(start_search_so())
            apiBase(API_GET_SEARCH_SO, METHOD.POST, body)
                .then((response) => {
                    console.log('getDataSearchSO BODY', response?.object?.SOINFO);
                    if (
                        helper.IsNonEmptyArray(response?.object?.SOINFO)
                    ) {
                        console.log('getDataSearchSO success', response);
                        dispatch(stop_search_so(response?.object?.SOINFO));
                        resolve(response?.object?.SOINFO);
                    } else {
                        dispatch(stop_search_so([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu")
                    }
                })
                .catch((error) => {
                    console.log('getDataSearchSO error: ', error);
                    dispatch(stop_search_so([], !EMPTY, error.msgError, ERROR))
                    reject("Lỗi lấy thông tin dữ liệu")
                })
        })
    }
}


export const geDataBuyInsurance = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                validateType: "CHECKBUY",
                searchType: 1,
                keyword: data.keyword,
                fromDate: data.fromDate,
                toDate: data.toDate,
                extraData: data.extraData
            }
            dispatch(start_validate_data_service_request())
            apiBase(API_GET_DETAIL_SO, METHOD.POST, body)
                .then((response) => {
                    console.log("geDataBuyInsurance success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_validate_data_service_request(response.object));
                        resolve(response.object);
                    }
                }).catch(error => {
                    console.log("geDataBuyInsurance error", error.msgError);
                    dispatch(stop_validate_data_service_request({}, !EMPTY, error.msgError, ERROR))
                    reject(error.msgError)
                })
        })
    }
}

export const getDataProvince = function () {
    return function (dispatch, getState) {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "keyWord": "",
            "top": 64
        };
        dispatch(start_get_province());
        apiBase(API_GET_PROVINCE, METHOD.POST, body).then((response) => {
            console.log("getDataProvince success", response);
            const { object } = response;
            if (helper.isArray(object) && object.length > 0) {
                dispatch(stop_get_province(object));
            }
            else {
                dispatch(stop_get_province([], !EMPTY, translate("provincemain.mess_error"), ERROR));
            }
        }).catch(error => {
            console.log("getDataProvince error", error);
            dispatch(stop_get_province([], !EMPTY, error.msgError, ERROR));
        })
    }
}

export const getDataDistrict = function (provinceID) {
    return function (dispatch, getState) {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "keyWord": "",
            "provinceID": provinceID,
            "top": 64
        };
        dispatch(start_get_district());
        apiBase(API_GET_DISTRICT, METHOD.POST, body).then((response) => {
            console.log("getDataDistrict success", response);
            const { object } = response;
            if (helper.isArray(object) && object.length > 0) {
                dispatch(stop_get_district(object));
            }
            else {
                dispatch(stop_get_district([], !EMPTY, translate("provincemain.mess_error1"), ERROR));
            }
        }).catch(error => {
            console.log("getDataDistrict error", error);
            dispatch(stop_get_district([], !EMPTY, error.msgError, ERROR));
        })
    }
}

export const getDistrict = function (provinceID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "keyWord": "",
                "provinceID": provinceID,
                "top": 64
            };
            apiBase(API_GET_DISTRICT, METHOD.POST, body).then((response) => {
                console.log("getDistrict success", response);
                const { object } = response;
                if (helper.isArray(object) && object.length > 0) {
                    resolve(object)
                }
                else {
                    reject(translate("provincemain.mess_error1"));
                }
            }).catch(error => {
                console.log("getDistrict error", error);
                reject(error.msgError);
            })
        })
    };
}

export const getWard = function (provinceID, districtID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "provinceID": provinceID,
                "districtID": districtID,
                "top": 64
            };
            apiBase(API_GET_WARD, METHOD.POST, body).then((response) => {
                console.log("getWard success", response);
                const { object } = response;
                if (helper.isArray(object) && object.length > 0) {
                    resolve(object)
                }
                else {
                    reject(translate("provincemain.mess_error2"));
                }
            }).catch(error => {
                console.log("getWard error", error);
                reject(error.msgError);
            })
        })
    };
}

export const getAllLocation = function (provinceID, districtID) {
    return new Promise((resolve, reject) => {
        console.log("getAllLocation");
        const allPromise = [
            getDistrict(provinceID),
            getWard(provinceID, districtID)
        ];
        Promise.all(allPromise).then(result => {
            console.log("getAllLocation success", result);
            resolve({
                dataDistrict: result[0],
                dataWard: result[1]
            })
        }).catch(msgError => {
            console.log("getAllLocation error", msgError);
            reject(msgError);
        })
    })
}

export const getSendOTPProcessServicePrequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: data.saleOrderID,
                isRequestOTP: true
            };
            dispatch(start_get_send_otp_processs());
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getSendOTPProcessServicePrequest success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_send_otp_processs(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_send_otp_processs({}));
                        reject({ msgError: "Không tìm thấy thông tin gửi lại OTP" });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("getSendOTPProcessServicePrequest error", error);
                    reject(msgError);
                });
        });
    };
};

export const getGetTransactionDetail = function (saleOrderID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: saleOrderID,
            };
            dispatch(start_get_send_otp_processs());
            apiBase(API_GET_TRANSACTION_DETAIL, METHOD.POST, body)
                .then((response) => {
                    console.log("getGetTransactionDetail success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_send_otp_processs(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_send_otp_processs({}));
                        reject({ msgError: "Không tìm thấy thông tin gửi lại OTP" });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("getGetTransactionDetail error", error);
                    reject(msgError);
                });
        });
    };
};

export const getConfirmOTPProcessServicePrequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: data.saleOrderID,
                otp: data.otp
            };
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getConfirmOTPProcessServicePrequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getConfirmOTPProcessServicePrequest error", error);
                    reject(error.msgError);
                });
        });
    };
};

API_GET_PROCESS_SERVICE_REQUEST

const update_item_airtime_code = (
    data
) => ({
    type: UPDATE_ITEM_AIRTIME_CODE,
    data
});

export const updateItemAirtimeCode = (data) => {
    return function (dispatch, getState) {
        dispatch(update_item_airtime_code(data));
    }
}

export const start_add_to_sale_order_cart = () => {
    return {
        type: START_ADD_TO_SALE_ORDER_CART,
    };
};

export const stop_add_to_sale_order_cart = (dataSaleOrderCart = {}) => {
    return {
        type: STOP_ADD_TO_SALE_ORDER_CART,
        dataSaleOrderCart,
    };
};

export const start_get_service_list = () => {
    return {
        type: START_GET_SERVICE_LIST,
    };
};

export const stop_get_service_list = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SERVICE_LIST,
    data,
    isEmpty,
    description,
    isError
});

export const start_get_price_service = () => {
    return {
        type: START_GET_PRICE_SERVICE,
    };
};

export const stop_get_price_service = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_PRICE_SERVICE,
    data,
    isEmpty,
    description,
    isError
});

export const start_validate_data_service_request = () => {
    return {
        type: START_VALIDATE_DATA_SERVICE_REQUEST,
    };
};

export const stop_validate_data_service_request = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_VALIDATE_DATA_SERVICE_REQUEST,
    data,
    isEmpty,
    description,
    isError
});

export const clear_data_validate_service_request = () => ({
    type: CLEAR_DATA_VALIDATE_SERVICE_REQUEST
});

export const start_search_history_insurance = () => {
    return {
        type: START_SEARCH_HISTORY_INSURANCE,
    };
};

export const stop_search_history_insurance = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_SEARCH_HISTORY_INSURANCE,
    data,
    isEmpty,
    description,
    isError
});

export const start_get_create_airtime_refund = () => {
    return {
        type: START_GET_CREATE_AIRTIME_REFUND,
    };
};

export const stop_get_create_airtime_refund = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_CREATE_AIRTIME_REFUND,
    data,
    isEmpty,
    description,
    isError
});
export const start_search_so = () => {
    return {
        type: START_GET_SEARCH_SO,
    };
};
export const stop_search_so = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_GET_SEARCH_SO,
    data,
    isEmpty,
    description,
    isError
});

export const clear_data_search_so = () => ({
    type: CLEAR_DATA_SEARCH_SO
});

export const start_get_detail_so = () => {
    return {
        type: START_GET_DETAIL_SO,
    };
};
export const stop_get_detail_so = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_GET_DETAIL_SO,
    data,
    isEmpty,
    description,
    isError
});

const start_get_province = () => {
    return ({
        type: START_GET_PROVINCE
    });
}

const stop_get_province = (
    dataProvince,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PROVINCE,
        dataProvince,
        isEmpty,
        description,
        isError,
    });
}

const start_get_promotion_service = () => {
    return ({
        type: START_GET_PROMOTION_SERVICE
    });
}

const stop_get_promotion_service = (
    data,
) => {
    return ({
        type: STOP_GET_PROMOTION_SERVICE,
        data,
    });
}

const start_get_district = () => {
    return ({
        type: START_GET_DISTRICT
    });
}

const stop_get_district = (
    dataDistrict,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_DISTRICT,
        dataDistrict,
        isEmpty,
        description,
        isError,
    });
}

const update_customer_airtime_service = (
    data
) => ({
    type: UPDATE_CUSTOMER_AIRTIME_SERCICE,
    data
});

export const updateCustomerAirtimeService = (data) => {
    return function (dispatch, getState) {
        dispatch(update_customer_airtime_service(data));
    }
}

export const clear_data_validate_service = () => ({
    type: CLEAR_DATA_VALIDATE_SERVICE
});

const start_get_send_otp_processs = () => ({
    type: START_GET_SEND_OTP_PROCESS
});
const stop_get_send_otp_processs = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SEND_OTP_PROCESS,
    data,
    isEmpty,
    description,
    isError
});
