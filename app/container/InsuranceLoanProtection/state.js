export const insuranceLoanProtectionState = {
    dataSaleOrderCart: {},
    dataServiceList: {},
    stateServiceList: {
        isFetching: false,
        isError: false,
        isEmpty: false,
        description: ''
    },
    dataValidateService: {},
    stateValidateService: {
        isFetching: false,
        isError: false,
        isEmpty: false,
        description: ''
    },
    dataSearchListHistory: {},
    stateSearchListHistory: {
        isFetching: false,
        isError: false,
        isEmpty: false,
        description: ''
    },
    dataSearchListSO: {},
    stateSearchListSO: {
        isFetching: false,
        isError: false,
        isEmpty: false,
        description: ''
    },
    dataDetailSO: {},
    sateDetailSO: {
        isFetching: false,
        isError: false,
        isEmpty: false,
        description: ''
    },
    dataCreateAirtimeRefund: {},
    itemAirtimeCode: {},
    dataProvince: [],
    stateProvince: {
        isFetching: false,
        isEmpty: false,
        description: "",
        isError: false,
    },
    GetPromotion: {},
    dataDistrict: [],
    stateDistrict: {
        isFetching: false,
        isEmpty: false,
        description: "",
        isError: false,
    },
    dataGetPrice: {},
    dataUpdateCustomer: {},
    dataSendOTPProcess: {}
};