import React, { useEffect, useState } from 'react';
import { View, SafeAreaView, Dimensions, StyleSheet, Text } from 'react-native';
import { COLORS } from '@styles';
import { MyText } from '@components';
import {
    PagerTitleIndicator
} from 'react-native-best-viewpager';
import ReplacementService from './screen/ReplacementService';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionIninsuranceLoanProtectionCreator from "./action";
import ButtonTabs from './component/ButtonTabs';
import RefundHistory from './screen/RefundHistory';
import HistorySellInsuranceLoan from './screen/HistorySellInsuranceLoan';
import { useFocusEffect } from '@react-navigation/native';

const IninsuranceLoanProtection = ({
    navigation,
    route
}) => {
    const { item, tabIndex, SaleOrderID } = route.params ?? {};
    const [saleOrderID, setSaleOderID] = useState(SaleOrderID);
    const [firstTimeHistoryTab, setFirstTimeHistoryTab] = useState(true);//đặt cờ lần đầu vào tab HistorySellInsuranceLoan :D
    const [activeTab, setActiveTab] = useState(tabIndex ?? 0);

    const [data, setData] = useState({
        fromDate: '',
        toDate: ''
    })
    useEffect(() => {
        setData(data)
    }, [data]);

    useFocusEffect(
        React.useCallback(() => {
            if (firstTimeHistoryTab && SaleOrderID !== undefined) {
                setActiveTab(tabIndex)
                setSaleOderID(SaleOrderID); // Giữ nguyên SaleOrderID
                setFirstTimeHistoryTab(false);
            }
        }, [tabIndex, saleOrderID])
    );

    useEffect(() => {
        if (activeTab !== 1) { // Nếu tab hiện tại không phải là tab Xem lịch sử bán
            setSaleOderID(''); // Đặt saleOrderID về ''
        }
    }, [activeTab]);;

    return (
        <SafeAreaView
            style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF,
            }}>
            <ButtonTabs
                TabsOne={'Bán bảo hiểm'}
                TabsTwo={'Xem lịch sử bán'}
                TabsThree={'Xem lịch sử hoàn tiền'}
                activeTab={activeTab}
                setActiveTab={setActiveTab} />
            {activeTab === 0 && (
                <ReplacementService
                    navigation={navigation}
                    item={item}
                />
            )}
            {activeTab === 1 && (
                <HistorySellInsuranceLoan
                    navigation={navigation}
                    data={data}
                    item={item}
                    SaleOrderID={saleOrderID}
                />
            )}
            {activeTab === 2 && (
                <RefundHistory
                    navigation={navigation}
                    item={item}
                />
            )}
        </SafeAreaView>
    )
}

export const renderPagerTitleIndicator = (titles) => {
    const itemWidth = {
        width: Dimensions.get('window').width / titles.length - 30
    };
    return (
        <PagerTitleIndicator
            initialPage={0}
            style={{ backgroundColor: COLORS.bg00A98F }}
            titles={titles}
            trackScroll={true}
            selectedBorderStyle={{
                backgroundColor: COLORS.bgFFF000,
                height: 2,
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
            }}
            renderTitle={(index, title, isSelected) => {
                return (
                    <View style={itemWidth}>
                        <MyText
                            style={{
                                color: isSelected
                                    ? COLORS.txtFFF000
                                    : COLORS.txtFFFFFF,
                                fontWeight: 'bold',
                                textAlign: 'center'
                            }}
                            text={title}
                        />
                    </View>
                );
            }}
        />
    );
};

const mapStateToProps = function (state) {
    return {}
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionIninsuranceLoanProtection: bindActionCreators(actionIninsuranceLoanProtectionCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(IninsuranceLoanProtection)

const styles = StyleSheet.create({})