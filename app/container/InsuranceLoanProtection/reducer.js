import { insuranceLoanProtectionState } from "./state";
import { actionInsuranceLoanProtection } from "./action";

const insuranceLoanProtectionReducer = function (state = insuranceLoanProtectionState, action) {
    switch (action.type) {
        case actionInsuranceLoanProtection.START_GET_SERVICE_LIST:
            return {
                ...state,
                dataServiceList: {},
                stateServiceList: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }

        case actionInsuranceLoanProtection.STOP_GET_SERVICE_LIST:
            return {
                ...state,
                dataServiceList: action.data,
                stateServiceList: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }

        case actionInsuranceLoanProtection.START_SEARCH_HISTORY_INSURANCE:
            return {
                ...state,
                dataSearchListHistory: {},
                stateSearchListHistory: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }
        case actionInsuranceLoanProtection.STOP_SEARCH_HISTORY_INSURANCE:
            return {
                ...state,
                dataSearchListHistory: action.data,
                stateSearchListHistory: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }
        case actionInsuranceLoanProtection.START_GET_SEARCH_SO:
            return {
                ...state,
                dataSearchListSO: {},
                stateSearchListSO: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }
        case actionInsuranceLoanProtection.STOP_GET_SEARCH_SO:
            return {
                ...state,
                dataSearchListSO: action.data,
                stateSearchListSO: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }
        case actionInsuranceLoanProtection.CLEAR_DATA_SEARCH_SO:
            return {
                ...state,
                dataSearchListSO: {},
            };
        case actionInsuranceLoanProtection.START_VALIDATE_DATA_SERVICE_REQUEST:
            return {
                ...state,
                dataValidateService: {},
                stateValidateService: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }
        case actionInsuranceLoanProtection.STOP_VALIDATE_DATA_SERVICE_REQUEST:
            return {
                ...state,
                dataValidateService: action.data,
                stateValidateService: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }
        case actionInsuranceLoanProtection.START_GET_DETAIL_SO:
            return {
                ...state,
                dataDetailSO: {},
                sateDetailSO: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }
        case actionInsuranceLoanProtection.STOP_GET_DETAIL_SO:
            return {
                ...state,
                dataDetailSO: action.data,
                sateDetailSO: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }
        case actionInsuranceLoanProtection.CLEAR_DATA_VALIDATE_SERVICE_REQUEST:
            return {
                ...state,
                dataValidateService: {},
                stateValidateService: {}
            };
        case actionInsuranceLoanProtection.STOP_ADD_TO_SALE_ORDER_CART:
            return {
                ...state,
                dataSaleOrderCart: action.dataSaleOrderCart,
            };
        case actionInsuranceLoanProtection.START_GET_CREATE_AIRTIME_REFUND:
            return {
                ...state,
                dataCreateAirtimeRefund: {},
            }
        case actionInsuranceLoanProtection.STOP_GET_CREATE_AIRTIME_REFUND:
            return {
                ...state,
                dataCreateAirtimeRefund: action.data,
            }
        case actionInsuranceLoanProtection.UPDATE_ITEM_AIRTIME_CODE:
            return {
                ...state,
                itemAirtimeCode: action.data,
            }
        case actionInsuranceLoanProtection.START_GET_PROVINCE:
            return {
                ...state,
                stateProvince: {
                    isFetching: true,
                    isEmpty: false,
                    description: "",
                    isError: false,
                },
            };
        case actionInsuranceLoanProtection.STOP_GET_PROVINCE:
            return {
                ...state,
                dataProvince: action.dataProvince,
                stateProvince: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError,
                },
            };
        case actionInsuranceLoanProtection.START_GET_DISTRICT:
            return {
                ...state,
                stateDistrict: {
                    isFetching: true,
                    isEmpty: false,
                    description: "",
                    isError: false,
                },
            };
        case actionInsuranceLoanProtection.STOP_GET_DISTRICT:
            return {
                ...state,
                dataDistrict: action.dataDistrict,
                stateDistrict: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError,
                },
            };
        case actionInsuranceLoanProtection.START_GET_PRICE_SERVICE:
            return {
                ...state,
                dataGetPrice: {},
            };
        case actionInsuranceLoanProtection.STOP_GET_PRICE_SERVICE:
            return {
                ...state,
                dataGetPrice: action.data,
            };
        case actionInsuranceLoanProtection.UPDATE_CUSTOMER_AIRTIME_SERCICE:
            return {
                ...state,
                dataUpdateCustomer: action.data,
            };
        case actionInsuranceLoanProtection.START_GET_PROMOTION_SERVICE:
            return {
                ...state,
                dataGetPromotion: {},
            };
        case actionInsuranceLoanProtection.STOP_GET_PROMOTION_SERVICE:
            return {
                ...state,
                dataGetPromotion: action.data,
            };
        case actionInsuranceLoanProtection.CLEAR_DATA_VALIDATE_SERVICE:
            return {
                ...state,
                dataValidateService: {},
                stateValidateService: {}
            };
        case actionInsuranceLoanProtection.START_GET_SEND_OTP_PROCESS:
            return {
                ...state,
                dataSendOTPProcess: {},
            }

        case actionInsuranceLoanProtection.STOP_GET_SEND_OTP_PROCESS:
            return {
                ...state,
                dataSendOTPProcess: action.data,
            }
        default:
            return state;
    }
};

export { insuranceLoanProtectionReducer };