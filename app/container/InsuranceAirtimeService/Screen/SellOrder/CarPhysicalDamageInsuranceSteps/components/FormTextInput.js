import { helper } from '@common'
import { Icon, MyText } from '@components'
import { constants } from '@constants'
import { COLORS } from '@styles'
import { translate } from '@translate'
import React, { useEffect, useState } from 'react'
import { Alert, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native'
import TooltipWrapper from './TooltipWrapper'

export default function TextInputField({ textInputData, onSubmit, placeholder, updateHeaderAirtime }) {

    const [textValue, setTextValue] = useState('');

    const { Label, Value, IsObliged, PropertyID } = textInputData;
    const isRequired = IsObliged == 1;

    // LOG  37 Số điện thoại người mua
    // LOG  16 Email
    // LOG  115 Email người được bảo hiểm
    // LOG  10 Số điện thoại

    const isMultipleLine = PropertyID == 106
    const isPhoneNumber = PropertyID == 37 || PropertyID == 10 || PropertyID == 13 || PropertyID == 25;
    const { AirTimeTransactionTypeID } = updateHeaderAirtime ?? '';

    useEffect(() => {
        if (helper.IsNonEmptyString(Value) || (Value !== null && Value !== undefined && Value !== '')) {
            setTextValue(Value)
        } else {
            setTextValue('')
        }
    }, [Value])


    const submitText = ({ nativeEvent: { text } }) => {
        let isValid = true;
        if (PropertyID == 111) {
            const idCardNumberRegex = /^(\d{9}|\d{12})$/;
            isValid = idCardNumberRegex.test(text)
        } else if (PropertyID == 108) {
            const idCardNumberRegex = /^[\d|-]{9,14}$/;
            isValid = idCardNumberRegex.test(text)
        } else if (PropertyID == 16 || PropertyID == 115) {
            const emailRegex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
            isValid = text.length <= 1 || emailRegex.test(text)
        } else if (PropertyID == 37 || PropertyID == 10) {
            const phoneRegex = /^[0][\d]{9,10}$/;
            isValid = phoneRegex.test(text)
        } else if (PropertyID == 25 && AirTimeTransactionTypeID == 1533) {
            let cleaned = text.replace(",", ".");
            cleaned = cleaned.replace(/[^0-9.]/g, "");
            
            // Sử dụng regex để kiểm tra pattern: số + dấu chấm + tối đa 2 chữ số
            const validPattern = /^\d*\.?\d{0,2}$/;
            isValid = validPattern.test(cleaned);
            if (isValid) {
                text = cleaned; // Cập nhật text đã được làm sạch
            }
        }
        if (!isValid) {
            Alert.alert(
                translate('common.notification'),
                `${Label} không đúng định dạng!`,
                [
                    {
                        text: translate('common.btn_accept'),
                        onPress: () => { setTextValue('') },
                    },
                ]);
        } else {
            if (helper.IsNonEmptyString(text)) {
                onSubmit(text)
            }
        }
    }

    const onChangeText = (text) => {
        let isValid = true;
        if (PropertyID == 111) {
            const idCardNumberRegex = /^\d{0,12}$/;
            isValid = idCardNumberRegex.test(text)
        } else if (PropertyID == 108) {
            const idCardNumberRegex = /^[\d|-]{0,14}$/;
            isValid = idCardNumberRegex.test(text)
        } else if (PropertyID == 38 || PropertyID == 9) {
            const nameRegex = /^([\u0020-\u007E \u00C0-\u00CD\u00D2-\u00DD\u00E0-\u00ED\u00F2-\u00FD\u0102-\u0103\u0110-\u0111\u0128-\u0129\u0168-\u0169\u01A0-\u01A1\u01AF-\u01B0\u1EA0-\u1EF9 \u1780-\u17DD\u17E0-\u17E9\u17F0-\u17F9])*$/u;
            isValid = nameRegex.test(text)
        } else if (PropertyID == 37 || PropertyID == 10) {
            const phoneRegex = /^[0]\d{0,9}$/;
            isValid = phoneRegex.test(text)
        }
        if (isValid) {
            setTextValue(text)
        }
    }

    const clearText = () => {
        setTextValue('');
        onSubmit('');
    }

    return (
        <View style={{
            width: constants.width,
            paddingVertical: 5,
            paddingHorizontal: 10
        }}>

            {PropertyID == 111 ?
                <View
                    style={{
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "flex-start"
                    }}
                >
                    <TooltipWrapper
                        placement={"bottom"}
                        content={
                            <MyText style={{
                                color: COLORS.txtFFFFFF,
                            }}
                                text={'Trường hợp trẻ em vui lòng nhập số định danh cá nhân'}
                            />
                        }
                        wrapper={
                            <View style={{ flexDirection: "row" }}>
                                <MyText
                                    text={Label}
                                    style={{
                                        color: COLORS.txt999999,
                                        fontWeight: 'bold'
                                    }}
                                    addSize={-1}
                                >
                                    {isRequired && <MyText
                                        text={' *'}
                                        style={{
                                            fontWeight: 'bold',
                                            color: COLORS.icD0021B
                                        }}
                                        addSize={1.5}
                                    />}
                                </MyText>
                                <Icon
                                    iconSet={"Ionicons"}
                                    name={"information-circle"}
                                    size={14}
                                    color={COLORS.ic2C8BD7}
                                />
                            </View>
                        }
                    />
                </View> :
                <MyText
                    text={Label}
                    style={{
                        color: COLORS.txt999999,
                        fontWeight: 'bold'
                    }}
                    addSize={-1}
                >
                    {isRequired && <MyText
                        text={' *'}
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.txtFF0000
                        }}
                        addSize={1.5}
                    />}
                </MyText>
            }
            <View
                style={{
                    width: constants.width - 40,
                    borderWidth: StyleSheet.hairlineWidth,
                    paddingHorizontal: 10,
                    paddingVertical: 6,
                    marginVertical: 5,
                    marginHorizontal: 10,
                    borderColor: COLORS.bd808080,
                    borderRadius: 5,
                    flexDirection: 'row',
                    alignItems: isMultipleLine ? 'flex-start' : 'center'
                }}
            >
                <TextInput
                    style={{
                        flex: 1,
                        paddingVertical: 2,
                        height: isMultipleLine ? 45 : 'auto'
                    }}
                    multiline={true}
                    keyboardType={isPhoneNumber ? "numeric" : "default"}
                    returnKeyType={isMultipleLine ? "default" : "done"}
                    value={textValue}
                    onChangeText={onChangeText}
                    blurOnSubmit={!isMultipleLine}
                    onEndEditing={submitText}
                    placeholder={placeholder}
                />
                {helper.IsNonEmptyString(textValue) && <TouchableOpacity
                    activeOpacity={0.6}
                    onPress={clearText}
                >
                    <Icon
                        iconSet={"Ionicons"}
                        name={"md-close"}
                        color={COLORS.txt999999}
                        size={20}
                    />
                </TouchableOpacity>}
            </View>
            {
                (PropertyID == 25) &&
                <MyText
                    text={`Vui lòng nhập ${Label} theo đơn vị là " TẤN "`}
                    style={{ color: COLORS.txtFF0000, paddingVertical: 5, paddingTop: 0, fontSize: 12, fontStyle: 'italic' }}
                >
                    {
                        AirTimeTransactionTypeID == 1533 &&
                        <MyText
                            text={` và làm tròn đến 2 chữ số thập phân (ví dụ: 1.25)`}
                            style={{ color: COLORS.txtFF0000, paddingVertical: 5, paddingTop: 0, fontSize: 12, fontStyle: 'italic' }}
                        />
                    }
                </MyText>
            }
            {
                (PropertyID == 13) &&
                <MyText
                    text={`Vui lòng nhập đúng ${Label}`}
                    style={{ color: COLORS.txtFF0000, paddingVertical: 5, paddingTop: 0, fontSize: 12, fontStyle: 'italic' }}
                />
            }
            {
                (PropertyID == 11 || PropertyID == 123) &&
                <MyText
                    text={`Không có ${Label} vui lòng nhập dấu " . "`}
                    style={{ color: COLORS.txtFF0000, paddingVertical: 5, paddingTop: 0, fontSize: 12, fontStyle: 'italic' }}
                />

            }
        </View>
    )
}