import React, { useState } from 'react'
import { StepIndicator } from './components'
import InsuranceInformation from './InsuranceInformation'
import CustomerInformation from './CustomerInformation'
import ConfirmInformation from './ConfirmInformation'
import SafeAreaView from 'react-native-safe-area-view'
import { COLORS } from '@styles'
import { connect } from 'react-redux'
import * as actionInsuranceAirtimeServiceCreator from '../../../action'
import { bindActionCreators } from 'redux'
import { hideBlockUI, showBlockUI } from '@components'
import { helper } from '@common'
import { Alert, Keyboard } from 'react-native'
import { translate } from '@translate'
import * as actionPaymentOrderCreator from "../../../../SaleOrderPayment/action";
import { clearSavedBanks } from './components/Accordion'


const CyberSecurityStep = ({
  navigation,
  inputFormTemplate,
  actionInsuranceAirtimeService,
  actionPaymentOrder,
  itemCatalog,
  updateHeaderAirtime,
  healthInsuranceFee
}) => {

  const [inputFormState, setInputFormState] = useState(inputFormTemplate);
  const [currentStep, setCurrentStep] = useState(0);

  // state xác định người mua bảo hiểm cũng là ngừoi được bảo hiểm
  const [isSamePerson, setIsSamePerson] = useState(false);

  const { insuranceProperty, customerProperty } = inputFormState;

  const onPrevious = () => {
    if (currentStep > 0) {
      Keyboard.dismiss();
      setTimeout(() => {
        setCurrentStep(currentStep - 1)
      }, 300);
    }
  }

  const onNext = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1)
    }
  }

  const goToStepTwo = (insuranceInfoState) => {
    Keyboard.dismiss();
    setTimeout(() => {
      let newCustomerProperty = [...customerProperty];

      // lấy ngày sinh đã nhập ở bước 1 gắn vào bước 2
      const groupIndexStepOne = insuranceInfoState.findIndex(group => group.InsInforGroupID == 1)
      if (groupIndexStepOne > -1) {
        const birthdayFormIndexStepOne = insuranceInfoState[groupIndexStepOne].PropertyList.findIndex(form => form.PropertyID == 112)
        const groupIndexStepTwo = newCustomerProperty.findIndex(group => group.InsInforGroupID == 8)
        console.log(newCustomerProperty[groupIndexStepTwo].PropertyList);
        if (birthdayFormIndexStepOne > -1 && groupIndexStepTwo > -1) {
          const birthdayFormIndexStepTwo = newCustomerProperty[groupIndexStepTwo].PropertyList.findIndex(form => form.PropertyID == 112)
          newCustomerProperty[groupIndexStepTwo].PropertyList[birthdayFormIndexStepTwo].Value = insuranceInfoState[groupIndexStepOne].PropertyList[birthdayFormIndexStepOne].Value
        }
      }
      setInputFormState({
        insuranceProperty: insuranceInfoState,
        customerProperty: newCustomerProperty
      })
      onNext();
    }, 300);
  }

  const goToStepThree = (customerInfoState) => {
    Keyboard.dismiss();
    setTimeout(() => {
      setInputFormState({
        ...inputFormState,
        customerProperty: [...customerInfoState]
      })
      onNext();
    }, 300);
  }

  const onEditInsuranceInfo = () => {
    setCurrentStep(0)
  }

  const onEditCustomerInfo = () => {
    setCurrentStep(1)
  }

  const createServiceRequest = () => {
    showBlockUI();
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog;
    const { AirTimeTransactionTypeID } = updateHeaderAirtime;
    let airTimeTransactionBO = {}
    const customerInfo = customerProperty.find(formGroup => formGroup.InsInforGroupID == 0 || formGroup.InsInforGroupID == 1);
    const formInfo = insuranceProperty.find(formGroup => formGroup.InsInforGroupID == 0);
    const { TotalAmount, PriceInfo: { PartnerData, InputPrice }, promotionId, productPromotion } = healthInsuranceFee;
    if (!helper.IsEmptyObject(customerInfo)) {
      const { PropertyList } = customerInfo;
      const customerPhoneNumber = PropertyList.find(form => form.PropertyID == 10);
      const customerName = PropertyList.find(form => form.PropertyID == 281);
      const customerAddress = PropertyList.find(form => form.PropertyID == 284 || form.PropertyID == 5 || form.PropertyID == 365);;
      const streetName = PropertyList.find(form => form.PropertyID == 367);
      const productid = formInfo?.PropertyList.find(form => form.PropertyID == 355);
      const filterProductId = productid?.Data?.find((item) => item.PartnerValue == productid.Value);
      airTimeTransactionBO = {
        productid: filterProductId?.ProductID,
        amount: InputPrice,
        fee: 0,
        phonenumber: customerPhoneNumber?.Value,
        inputPrice: InputPrice,
        salePrice: TotalAmount,
        customerName: customerName?.Value,
        customerAddress: `${streetName?.Value}, ${customerAddress?.Value}`,
        customerPhone: customerPhoneNumber?.Value,
      }
    }

    const data = {
      catalogId: ServiceCategoryID,
      serviceGroupId: AirtimeServiceGroupID,
      airtimeTransactionTypeId: AirTimeTransactionTypeID,
      productPromotion: helper.IsEmptyObject(productPromotion) ? {} : productPromotion,
      airTimeTransactionBO,
      listProperty: [...insuranceProperty, ...customerProperty],
      partnerData: PartnerData,
      promotionId: helper.isNumber(promotionId) ? promotionId : 0,
      promotionGift: healthInsuranceFee?.PromotionGift
    }
    actionInsuranceAirtimeService.createHealthInsuranceServiceRequest(data).then(response => {
      const { SaleOrderID } = response.SaleOrders[0];
      hideBlockUI();
      actionPaymentOrder.setDataSO({
        SaleOrderID,
        SaleOrderTypeID: 1000,
      })
        .then((success) => {
          navigation.replace("SaleOrderPayment");
          actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
          actionPaymentOrder.getReportPrinterSocket(100);
          clearSavedBanks()

        });
    }).catch(msgError => {
      Alert.alert("", msgError, [
        {
          text: translate('common.btn_skip'),
          onPress: hideBlockUI
        },
        {
          text: translate('common.btn_notify_try_again'),
          onPress: createServiceRequest,
        }
      ])
    });
  }

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <InsuranceInformation
            insuranceInfo={insuranceProperty}
            onBack={()=> navigation.goBack()}
            onNextStep={goToStepTwo}
          />
        );
      case 1:
        return (
          <CustomerInformation
            customerInfo={customerProperty}
            onBack={onPrevious}
            onNextStep={goToStepThree}
            isSamePerson={isSamePerson}
            setIsSamePerson={setIsSamePerson}
          />
        );
      case 2:
        return (
          <ConfirmInformation
            onBack={onPrevious}
            onNextStep={createServiceRequest}
            insuranceProperty={insuranceProperty}
            customerProperty={customerProperty}
            isSamePerson={isSamePerson}
            onEditInsuranceInfo={onEditInsuranceInfo}
            onEditCustomerInfo={onEditCustomerInfo}
          />
        );
    }
  }

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
      }}
    >
      <StepIndicator
        currentStep={currentStep}
      />
      {renderStep()}
    </SafeAreaView>
  )
}

const mapStateToProps = function (state) {
  return {
    itemCatalog: state.collectionReducer.itemCatalog,
    inputFormTemplate: state.insuranceAirtimeServiceReducer.inputFormTemplate,
    updateHeaderAirtime: state.insuranceAirtimeServiceReducer.updateHeaderAirtime,
    healthInsuranceFee: state.insuranceAirtimeServiceReducer.healthInsuranceFee
  }
}

const mapDispatchToProps = function (dispatch) {
  return {
    actionInsuranceAirtimeService: bindActionCreators(actionInsuranceAirtimeServiceCreator, dispatch),
    actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch)
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(CyberSecurityStep);
