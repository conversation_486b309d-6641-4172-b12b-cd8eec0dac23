import React, { Component } from 'react';
import {
    View,
    TextInput,
    TouchableOpacity,
    ViewPropTypes,
    Platform,
} from 'react-native';
import PropTypes from 'prop-types';
import { Icon, MyText } from "@components";
import { COLORS } from '@styles';
import TooltipWrapper from '../../../../../ShoppingCart/component/TooltipWrapper';

class InputTextBankAccount extends Component {
    constructor(props) {
        super(props);
        this.state = {
            heightContent: this.props.height,
            isFocus: false
        }
    }

    focus() {
        if (this.textInput) {
            this.textInput.focus();
        }
    }

    render() {
        let {
            width,
            height,
            styleInput,
            multiline,
            editable,
            value,
            onFocus,
            onBlur,
            title,
            isRequired,
            alertMessage,
            onTooltipClose,
            closeItem
        } = this.props;
        let { heightContent } = this.state;
        return (
            <View style={{
                flexDirection: "row",
                alignItems: "center",
            }}>
                <View>
                    <MyText
                        text={title}
                        addSize={-1.5}
                        style={{
                            color: "#4DA8DA",
                            fontWeight: "bold",
                            width: width,
                            paddingBottom: 5
                        }}>
                        {
                            isRequired &&
                            <MyText
                                text={"*"}
                                addSize={-1.5}
                                style={{
                                    color: COLORS.txtFF0000
                                }}
                            />
                        }
                    </MyText>
                    <TooltipWrapper
                        text={alertMessage}
                        isVisible={!!alertMessage}
                        onClose={onTooltipClose}
                        disabled
                        wrapper={
                            <View
                                style={[styleInput, {
                                    flexDirection: "row",
                                    alignItems: (heightContent > height) ? "flex-start" : "center",
                                    width: width,
                                    minHeight: height,
                                }]}
                            >
                                {editable ? (
                                    <>
                                        <TextInput
                                            {...this.props}
                                            style={{
                                                backgroundColor: 'transparent',
                                                borderWidth: 0,
                                                color: COLORS.txt333333,
                                                flex: 1,
                                                height: multiline
                                                    ? (Platform.OS == "ios" ? "auto" : heightContent)
                                                    : height,
                                            }}
                                            onContentSizeChange={({ nativeEvent }) => {
                                                if (multiline) {
                                                    this.setState({ heightContent: nativeEvent.contentSize.height });
                                                }
                                            }}
                                            ref={ref => this.textInput = ref}
                                            scrollEnabled={false}
                                            onFocus={() => this.setState({ isFocus: true }, onFocus)}
                                            onBlur={() => this.setState({ isFocus: false }, onBlur)}
                                        />

                                    </>
                                ) : (
                                    <View style={{
                                        flex: 1,
                                        justifyContent: "center",
                                        paddingVertical: 5,
                                        backgroundColor: 'transparent',
                                    }}>
                                        <MyText
                                            text={value}
                                            style={{
                                                color: COLORS.txt333333,
                                            }}
                                        />
                                    </View>
                                )}
                            </View>
                        }
                    />
                </View>
                <TouchableOpacity
                    onPress={closeItem}
                    style={{
                        paddingHorizontal: 8,
                        justifyContent: "center",
                        alignItems: "center",
                        // width: 60,
                        // height: 40,
                        backgroundColor: COLORS.bgFFFFFF,
                        // borderWidth: 1,
                        // borderColor: "#4DA8DA",
                        // borderRadius: 30,
                    }}
                >
                    <Icon iconSet={'MaterialIcons'} name="delete-forever" size={25} color={COLORS.bdED1164} />
                </TouchableOpacity>
            </View>
        );
    }

    onClear = () => {
        let { height, clearText } = this.props;
        clearText();
        this.setState({
            heightContent: height,
        })
    }
}

InputTextBankAccount.propTypes = {
    title: PropTypes.string.isRequired,
    width: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
    ]),
    height: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
    ]),
    styleInput: ViewPropTypes.style,
    multiline: PropTypes.bool,
    editable: PropTypes.bool,
    value: PropTypes.string,
    clearText: PropTypes.func,
    onFocus: PropTypes.func,
    onBlur: PropTypes.func,
    isRequired: PropTypes.bool,
};

InputTextBankAccount.defaultProps = {
    width: 345,
    height: 40,
    styleInput: {},
    multiline: false,
    editable: true,
    value: "",
    clearText: () => { },
    onFocus: () => { },
    onBlur: () => { },
    isRequired: false
};

export default InputTextBankAccount;