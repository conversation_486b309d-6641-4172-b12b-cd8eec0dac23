import React, { useState } from "react";
import {
    View,
    TouchableWithoutFeedback,
    StyleSheet,
    SafeAreaView,
    TouchableOpacity,
    ScrollView,
    Alert,
} from "react-native";
import { BottomSheet, MyText, Icon } from "@components";
import { constants } from "@constants";
import { COLORS } from "@styles";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import PickerMapping from "../../../../component/PickerMapping/PickerMapping";
import ButtonAccept from "../../../../component/Button/ButtonAccept";
import InputTextBankAccount from "./InputTextBankAccount";

let savedSelectedBanks = [];

export const clearSavedBanks = () => {
    savedSelectedBanks = [];
};

const BankBottomSheet = ({
    bottomSheetRef,
    snapPoints = ["80%"],
    disabledBackdrop = true,
    onPress,
    dataForm,
    onSelectedBanksChange,
}) => {
    const { Label, Data, IsObliged } = dataForm ?? "";
    const isRequired = IsObliged == 1;

    const [selectedBanks, setSelectedBanks] = useState(() => [
        ...savedSelectedBanks,
    ]);

    const updateSelectedBanks = (newBanks) => {
        const clonedBanks = Array.isArray(newBanks) ? [...newBanks] : [];
        setSelectedBanks(clonedBanks);
        savedSelectedBanks = clonedBanks;
        onSelectedBanksChange?.(clonedBanks);
    };

    const handleSelectBank = (bank) => {
        const exists = selectedBanks.some(
            (item) => item.PropertyValueID === bank.PropertyValueID
        );
        if (selectedBanks.length >= 5) {
            Alert.alert("Cảnh báo", `Chỉ chọn tối đa 5 tài khoản`, [
                { text: "OK", style: "default" },
            ]);
            return;
        }
        if (!exists) {
            updateSelectedBanks([...selectedBanks, { ...bank, bankCode: "" }]);
        }
    };

    const handleChangeBankCode = (id, value) => {
        updateSelectedBanks(
            selectedBanks.map((item) =>
                item.PropertyValueID === id ? { ...item, bankCode: value } : item
            )
        );
    };

    const handleRemoveBank = (id) => {
        updateSelectedBanks(
            selectedBanks.filter((item) => item.PropertyValueID !== id)
        );
    };

    const handleConfirm = () => {
        const invalidBank = selectedBanks.find(bank => !bank.bankCode?.trim());
        if (invalidBank) {
            Alert.alert(
                "Cảnh báo",
                `Vui lòng nhập số tài khoản cho ngân hàng ${invalidBank.PartnerValue}`,
                [{ text: "OK", style: "default" }]
            );
            return;
        }
        const validBanks = selectedBanks.filter(bank => bank.bankCode?.trim());
        updateSelectedBanks(validBanks);
        bottomSheetRef.current?.dismiss();
    };

    const handleComponent = () => (
        <View style={styles.handle_wrapper}>
            <View style={{ flex: 1 }} />
            <View style={styles.handle_title_wrapper}>
                <MyText style={styles.handle_title} text={"TÀI KHOẢN NGÂN HÀNG"} />
            </View>
            <View style={{ flex: 1 }}>
                <TouchableWithoutFeedback onPress={() => handleConfirm()}>
                    <Icon
                        iconSet={"MaterialIcons"}
                        name={"clear"}
                        color={COLORS.bdEB3B3B}
                        size={22}
                    />
                </TouchableWithoutFeedback>
            </View>
        </View>
    );

    const renderInner = () => (
        <SafeAreaView style={styles.inner_container}>
            <KeyboardAwareScrollView
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
                contentContainerStyle={styles.scroll_container}
            >
                {selectedBanks.map((bank, index) => (
                    <View key={bank.PropertyValueID} style={styles.bank_item}>
                        <View style={{ flex: 1 }}>
                            <InputTextBankAccount
                                closeItem={() => handleRemoveBank(bank.PropertyValueID)}
                                title={`${index + 1}. ${bank.PartnerValue}`}
                                placeholder="Nhập số tài khoản"
                                value={bank.bankCode}
                                onChangeText={(text) => {
                                    const cleanedText = text
                                        .trim()
                                        .replace(/\s/g, '')
                                        .replace(/[^a-zA-Z0-9]/g, '');

                                    if (cleanedText.length <= 50) {
                                        handleChangeBankCode(bank.PropertyValueID, cleanedText);
                                    }
                                }}
                                styleInput={styles.bank_input}
                                keyboardType="default"
                                returnKeyType="done"
                                width={constants.width - 70}
                            />
                        </View>
                    </View>
                ))}
            </KeyboardAwareScrollView>
            <View style={styles.bottom_action_row}>
                <PickerMapping
                    label={"PartnerValue"}
                    value={"PropertyValueID"}
                    data={Data}
                    valueSelected={null}
                    onChange={handleSelectBank}
                    style={styles.picker_style}
                    defaultLabel={"Thêm tài khoản"}
                />
                <ButtonAccept
                    title={"Xác nhận"}
                    onPress={() => handleConfirm()}
                    style={styles.confirm_btn}
                />
            </View>
        </SafeAreaView>
    );

    return (
        <>
            <View style={styles.label_row}>
                <View style={styles.label_left}>
                    <MyText
                        text={Label}
                        style={styles.label_text}
                        addSize={-1}
                    />
                    {isRequired && (
                        <MyText
                            text="*"
                            style={styles.required_star}
                            addSize={1.5}
                        />
                    )}
                </View>
                {selectedBanks.length > 0 && (
                    <TouchableOpacity onPress={onPress} style={styles.edit_btn}>
                        <Icon iconSet="Ionicons" name="pencil" size={18} color="#33A1E0" />
                        <MyText
                            text="Chỉnh sửa"
                            style={styles.change_text}
                            addSize={1.5}

                        />
                    </TouchableOpacity>
                )}
            </View>

            {selectedBanks.length > 0 ? (
                <View style={styles.selected_list}>
                    {selectedBanks.map((bank, index) => (
                        <View key={bank.PropertyValueID} style={styles.bank_card}>
                            <MyText
                                text={`${index + 1}. ${bank.PartnerValue}`}
                                style={styles.bank_name}
                            />
                            <MyText
                                text={`STK: ${bank.bankCode}`}
                                style={[
                                    styles.bank_code,
                                    { color: bank.bankCode ? COLORS.txt000000 : COLORS.txt999999 }
                                ]}
                            />
                        </View>
                    ))}
                </View>
            ) : (
                <View style={styles.empty_container}>
                    <TouchableOpacity style={styles.select_bank_btn} onPress={onPress}>
                        <MyText text={"Chọn ngân hàng"} style={styles.select_bank_text} />
                        <Icon iconSet="Feather" name="chevron-right" size={18} color="#33A1E0" />
                    </TouchableOpacity>
                </View>
            )}

            <BottomSheet
                enableHandlePanningGesture={false}
                enableContentPanningGesture={false}
                bs={bottomSheetRef}
                snapPoints={snapPoints}
                handleComponent={handleComponent}
                disabledBackdrop={disabledBackdrop}
            >
                {renderInner()}
            </BottomSheet>
        </>
    );
};

export default BankBottomSheet;

const styles = StyleSheet.create({
    handle_wrapper: {
        backgroundColor: COLORS.bgFFFFFF,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: 50,
        elevation: 2,
        borderTopStartRadius: 22,
        borderTopEndRadius: 22,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4,
    },
    handle_title_wrapper: {
        flex: 6,
        justifyContent: "center",
        alignItems: "center",
    },
    handle_title: {
        fontWeight: "bold",
        color: "#3FA2F6",
    },
    inner_container: {
        flex: 1,
        backgroundColor: "white",
    },
    scroll_container: {
        padding: 6,
    },
    bank_item: {
        flexDirection: "row",
        alignItems: "center",
        padding: 10,
        borderRadius: 16,
        backgroundColor: COLORS.bgFFFFFF,
        marginBottom: 12,
        shadowColor: "#000",
        shadowOpacity: 0.06,
        shadowRadius: 8,
        shadowOffset: { width: 0, height: 3 },
        elevation: 3,
        borderWidth: 1,
        borderColor: COLORS.bdF5F5F5,
    },
    bank_input: {
        backgroundColor: COLORS.bgFFFFFF,
        width: "100%",
    },
    bottom_action_row: {
        padding: 10,
        flexDirection: "row",
    },
    picker_style: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        height: 55,
        borderRadius: 60,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        width: constants.width - 150,
        backgroundColor: "#33A1E0",
    },
    confirm_btn: {
        width: 120,
        height: 55,
        borderRadius: 30,
        borderWidth: 1,
        borderColor: '#33A1E0',
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "white",
    },
    label_row: {
        flexDirection: "row",
        alignItems: "center",
        paddingVertical: 10,
        paddingHorizontal: 10,
        justifyContent: "space-between",
    },
    label_left: {
        flexDirection: "row",
        alignItems: "center",
    },
    label_text: {
        color: COLORS.txt999999,
        fontWeight: "bold",
    },
    required_star: {
        fontWeight: "bold",
        color: COLORS.txtFF0000,
        marginLeft: 2,
    },
    edit_btn: {
        padding: 6,
        backgroundColor: "#E6F3FF",
        borderRadius: 8,
        flexDirection: 'row'
    },
    selected_list: {
        paddingHorizontal: 10,
        justifyContent: "center",
        alignItems: "center",
    },
    bank_card: {
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 12,
        padding: 12,
        marginBottom: 10,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        shadowColor: "#000",
        shadowOpacity: 0.05,
        shadowRadius: 4,
        shadowOffset: { width: 0, height: 2 },
        elevation: 2,
        width: constants.width - 35,
    },
    bank_name: {
        fontWeight: "600",
        fontSize: 14,
        color: "#4DA8DA",
        marginBottom: 4,
    },
    bank_code: {
        fontSize: 13,
    },
    empty_container: {
        justifyContent: "center",
        alignItems: "center",
    },
    select_bank_btn: {
        borderWidth: 1,
        borderColor: COLORS.bdCCCCCC,
        borderRadius: 8,
        paddingVertical: 10,
        paddingHorizontal: 10,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        backgroundColor: COLORS.bgFFFFFF,
        marginBottom: 12,
        width: constants.width - 35,
    },
    select_bank_text: {
        color: "#33A1E0",
    },
    change_text: {
        textAlign: 'center',
        marginTop: 3,
        fontSize: 15,
        fontWeight: '500',
        fontStyle: 'italic',
        color: "#33A1E0",
    }
});
