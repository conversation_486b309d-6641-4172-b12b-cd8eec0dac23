import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState, useCallback } from 'react';
import { View, TouchableOpacity, Alert } from 'react-native';
import { AddressPicker, Icon, MyText, PickerLocation, showBlockUI, hideBlockUI } from '@components';
import { COLORS } from '@styles';
import { getWard, getDistrict, getProvince, getAddressMapping } from './action';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { debounce } from 'lodash';

const TYPE_ADDRESS = { PROVINCE: 'PROVINCE', WARD: 'WARD' };
const defaultAddress = { provinceID: 0, districtID: 0, wardID: 0 };

const AddressPickerSection = ({
  title,
  isLocked,
  onReset,
  provinceData,
  districtData,
  wardData,
  provinceID,
  districtID,
  wardID,
  onSelectProvince,
  onSelectDistrict,
  onSelectWard,
  indexPager,
  setIndexPager,
  isShowIndicator,
  isNewAddress,
  isRequire,
  enableDistrictSelection
}) => (
  <View style={{ marginTop: 7 }}>
    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingBottom: 7 }}>
      <MyText style={{ fontWeight: 'bold', marginBottom: 5 }} text={enableDistrictSelection ? "" : title} >
        {
          isRequire && <MyText addSize={2} style={{ color: COLORS.bgEA1D5D }} text={" *"} />
        }
      </MyText>
      <TouchableOpacity disabled={isLocked} onPress={onReset}>
        <Icon iconSet={'MaterialIcons'} name={'refresh'} color={COLORS.txt147EFB} size={25} />
      </TouchableOpacity>
    </View>
    {isNewAddress ? (
      <AddressPicker
        dataProvince={provinceData}
        dataDistrict={districtData}
        dataWard={wardData}
        provinceID={provinceID}
        districtID={districtID}
        wardID={wardID}
        onSelectProvince={onSelectProvince}
        onSelectDistrict={onSelectDistrict}
        onSelectWard={onSelectWard}
        indexPager={indexPager}
        onShowPicker={setIndexPager}
        updatePager={setIndexPager}
        isShowIndicator={isShowIndicator}
        disabled={isLocked}
        enableDistrictSelection={enableDistrictSelection}
      />
    ) : (
      <PickerLocation
        dataProvince={provinceData}
        dataDistrict={districtData}
        dataWard={wardData}
        provinceID={provinceID}
        districtID={districtID}
        wardID={wardID}
        onSelectProvince={onSelectProvince}
        onSelectDistrict={onSelectDistrict}
        onSelectWard={onSelectWard}
        indexPager={indexPager}
        onShowPicker={setIndexPager}
        updatePager={setIndexPager}
        isShowIndicator={isShowIndicator}
        disabled={isLocked}
      />
    )}
  </View>
);

const AddressManager = forwardRef((props, ref) => {
  const { onChangeProvince, onChangeDistrict, onChangeWard, provinceID, districtID, wardID, isShowOldAddress, newAddressTitle, oldAddressTitle, isRequireOldAddress,
    isRequireNewAddress, enableDistrictSelection } = props;

  const [locationData, setLocationData] = useState({
    provinces: [],
    districts: [],
    wards: [],
    provincesNew: [],
    districtsNew: [],
    wardsNew: [],
  });
  const [internalOldAddress, setInternalOldAddress] = useState(defaultAddress);
  const [internalNewAddress, setInternalNewAddress] = useState(defaultAddress);
  const [indexPager, setIndexPager] = useState(0);
  const [isShowIndicator, setIsShowIndicator] = useState(false);
  const [isLockedNewAddress, setIsLockedNewAddress] = useState(false);
  const [isLockedOldAddress, setIsLockedOldAddress] = useState(false);
  const [isProvincesLoaded, setIsProvincesLoaded] = useState(false);

  const isUserAction = useRef(false);
  const userInfo = useSelector((state) => state.userReducer);
  const { storeID, languageID, moduleID } = userInfo;
  const baseBody = { loginStoreId: storeID, languageID, moduleID };

  const handleGetAddressData = useCallback(() => ({
    oldAddress: internalOldAddress,
    newAddress: internalNewAddress,
    locationData,
  }), [internalOldAddress, internalNewAddress, locationData]);

  useImperativeHandle(ref, () => ({
    getAddressData: handleGetAddressData,
  }));

  const handleError = (error, title) => {
    Alert.alert('', error || `Lỗi khi xử lý ${title}`, [{ text: 'OK', onPress: hideBlockUI }]);
  };

  const loadLocationData = async (type, isNewAddress, params) => {
    try {
      if (isUserAction.current) {
        setIsShowIndicator(true);
        showBlockUI();
      }

      const body = {
        ...baseBody,
        ...params,
        isActive: isNewAddress ? 1 : 0,
      };
      let result;
      switch (type) {
        case 'provinces':
          result = await getProvince({
            ...body, keyWord: '', top: 200
          });

          break;
        case 'districts':
          result = await getDistrict({ ...body, keyWord: '', top: 64 });

          break;
        case 'wards':
          result = await getWard({ ...body, top: 200 });

          break;
        default:
          return null;
      }
      setLocationData((prev) => ({
        ...prev,
        [isNewAddress ? `${type}New` : type]: result,
      }));
      return result;
    } catch (error) {
      handleError(error, type);
      return null;
    } finally {
      setIsShowIndicator(false);
      hideBlockUI();
    }
  };

  const loadProvinces = async (isNewAddress) => {
    if (locationData[isNewAddress ? 'provincesNew' : 'provinces'].length > 0) return;
    await loadLocationData('provinces', isNewAddress, {});
  };

  const loadDistricts = async (provinceId, isNewAddress, defaultWardID = 0) => {
    if (!provinceId) return null;
    const result = await loadLocationData('districts', isNewAddress, { provinceID: provinceId });
    if (isNewAddress && result?.length > 0 && !enableDistrictSelection) {
      const firstDistrict = result[0];
      setInternalNewAddress((prev) => ({
        ...prev,
        districtID: firstDistrict.districtID,
        wardID: defaultWardID,
      }));
      if (onChangeDistrict) onChangeDistrict(firstDistrict, isNewAddress);
      await loadLocationData('wards', isNewAddress, {
        provinceID: provinceId,
        districtID: firstDistrict.districtID,
      });
      return firstDistrict.districtID;
    }
    return null;
  };

  const loadWards = async (districtId, provinceId, isNewAddress) => {
    if (!districtId || !provinceId) {
      return null;
    }
    const result = await loadLocationData('wards', isNewAddress, { provinceID: provinceId, districtID: districtId });
    return result;
  };

  const debouncedLoadDistricts = debounce((provinceId, isNewAddress, defaultWardID) => {
    loadDistricts(provinceId, isNewAddress, defaultWardID);
  }, 300);

  const debouncedLoadWards = debounce((districtId, provinceId, isNewAddress) => {
    loadWards(districtId, provinceId, isNewAddress);
  }, 300);

  const loadAddressMapping = async (keyWord, type, provinceId = null, districtId = null) => {
    try {
      showBlockUI();
      const body = { ...baseBody, mappingType: type, keyword: keyWord };
      const result = await getAddressMapping(body);
      const firstResult = result?.[0];
      const title = type == TYPE_ADDRESS.PROVINCE ? 'Tỉnh thành' : 'Phường xã';
      if (!firstResult) {
        resetInternalNewAddress(type);

        handleError(null, title);
        return null;
      }
      if (type == TYPE_ADDRESS.PROVINCE) {
        const mappedProvince = locationData.provincesNew.find(
          (item) => item.provinceID == firstResult.ProvinceIdNew
        );
        if (!mappedProvince) {
          resetInternalNewAddress('ALL');

          handleError(null, 'tỉnh thành');
          return null;
        }
        setInternalNewAddress((prev) => ({
          ...prev,
          provinceID: firstResult.ProvinceIdNew || 0,
          districtID: 0,
          wardID: 0,
        }));
        onChangeProvince?.({ provinceID: firstResult.ProvinceIdNew, provinceName: firstResult.ProvinceNameNew }, true);
        const selectedDistrictId = await loadDistricts(firstResult.ProvinceIdNew, true);
        return { provinceId: firstResult.ProvinceIdNew, districtId: selectedDistrictId };
      } else {
        const targetProvinceId = provinceId || internalNewAddress.provinceID;
        const targetDistrictId = districtId || internalNewAddress.districtID;
        if (!targetProvinceId || !targetDistrictId) {
          resetInternalNewAddress('WARD');
          handleError(null, 'phường xã (thiếu thông tin tỉnh/quận)');
          return null;
        }
        const wards = await loadWards(targetDistrictId, targetProvinceId, true);
        if (!wards || wards.length == 0) {
          resetInternalNewAddress('WARD');
          handleError(null, 'phường xã (không có dữ liệu)');
          return null;
        }
        const mappedWard = wards.find((item) => item.wardID == firstResult.WardIdNew);
        if (!mappedWard) {
          resetInternalNewAddress('WARD');
          handleError(null, 'phường xã (không tìm thấy trong danh sách)');
          return null;
        }
        setInternalNewAddress((prev) => ({
          ...prev,
          wardID: firstResult.WardIdNew || 0,
        }));
        onChangeWard?.({ wardID: firstResult.WardIdNew, wardName: firstResult.WardNameNew }, true);
        return { wardId: firstResult.WardIdNew };
      }
    } catch (error) {
      resetInternalNewAddress('ALL');
      handleError(error, 'thông tin');
      return null;
    } finally {
      hideBlockUI();
    }
  };

  const resetInternalNewAddress = (level = 'ALL') => {
    if (level === TYPE_ADDRESS.WARD) {
      onChangeWard?.({ wardID: null, wardName: null });
    } else {
      onChangeProvince?.({ provinceID: null, provinceName: null });
      onChangeDistrict?.({ districtID: null, districtName: null });
      onChangeWard?.({ wardID: null, wardName: null });
    }
    setInternalNewAddress((prev) => {
      switch (level) {
        case TYPE_ADDRESS.PROVINCE:
          return { ...prev, provinceID: 0, districtID: 0, wardID: 0 };
        case TYPE_ADDRESS.WARD:
          return { ...prev, wardID: 0 };
        default:
          return { provinceID: 0, districtID: 0, wardID: 0 };
      }
    });
  };



  const handleSelectProvince = async (province, isNewAddress) => {
    isUserAction.current = true;
    const setAddress = isNewAddress ? setInternalNewAddress : setInternalOldAddress;
    setAddress({ provinceID: province.provinceID, districtID: 0, wardID: 0 });
    setLocationData((prev) => ({
      ...prev,
      [isNewAddress ? 'districtsNew' : 'districts']: [],
      [isNewAddress ? 'wardsNew' : 'wards']: [],
    }));
    isNewAddress ? setIsLockedOldAddress(true) : setIsLockedNewAddress(true);
    if (isNewAddress && onChangeProvince) onChangeProvince(province, isNewAddress);
    setIndexPager((prev) => prev + 1);
    await debouncedLoadDistricts(province.provinceID, isNewAddress);
  };

  const handleSelectDistrict = async (district, isNewAddress) => {
    if (isNewAddress && !enableDistrictSelection) {
      return
    }
    isUserAction.current = true;
    const currentProvinceId = isNewAddress ? internalNewAddress.provinceID : internalOldAddress.provinceID;
    const setAddress = isNewAddress ? setInternalNewAddress : setInternalOldAddress;
    setAddress((prev) => ({ ...prev, districtID: district.districtID, wardID: 0 }));
    isNewAddress ? null : setIsLockedNewAddress(true);
    if (isNewAddress && onChangeDistrict) onChangeDistrict(district, isNewAddress);
    setIndexPager((prev) => prev + 1);
    await debouncedLoadWards(district.districtID, currentProvinceId, isNewAddress);
  };

  const handleSelectWard = async (ward, isNewAddress) => {
    isUserAction.current = true;
    const setAddress = isNewAddress ? setInternalNewAddress : setInternalOldAddress;
    setAddress((prev) => ({ ...prev, wardID: ward.wardID }));
    isNewAddress ? setIsLockedOldAddress(true) : setIsLockedNewAddress(true);
    if (!isNewAddress) {
      const provinceResult = await loadAddressMapping(internalOldAddress.provinceID, TYPE_ADDRESS.PROVINCE);
      if (provinceResult && provinceResult.provinceId && provinceResult.districtId) {
        await loadAddressMapping(ward.wardID, TYPE_ADDRESS.WARD, provinceResult.provinceId, provinceResult.districtId);
      }
    } else if (onChangeWard) {
      onChangeWard(ward, isNewAddress);
    }
    setIndexPager((prev) => prev + 1);
  };

  const handleResetOldAddress = () => {
    setInternalOldAddress(defaultAddress);
    setIsLockedNewAddress(false);
  };

  const handleResetNewAddress = () => {
    setInternalNewAddress(defaultAddress);
    setIsLockedOldAddress(false);
    onChangeProvince?.({ provinceID: null, provinceName: null });
    onChangeDistrict?.({ districtID: null, districtName: null });
    onChangeWard?.({ wardID: null, wardName: null });
  };

  useEffect(() => {
    Promise.all([loadProvinces(false), loadProvinces(true)]).then(() => {
      setIsProvincesLoaded(true);
    });
  }, []);

  useEffect(() => {
    if (isProvincesLoaded && !isUserAction.current) {
      const { provinceID: propProvinceID, districtID: propDistrictID, wardID: propWardID } = props;
      const { provinceID: stateProvinceID, districtID: stateDistrictID, wardID: stateWardID } = internalNewAddress;
      if (
        propProvinceID &&
        (propProvinceID != stateProvinceID || propDistrictID != stateDistrictID || propWardID != stateWardID)
      ) {
        const matched = locationData.provincesNew.some((item) => item.provinceID == propProvinceID);
        setInternalNewAddress({
          provinceID: propProvinceID || 0,
          districtID: propDistrictID || 0,
          wardID: propWardID || 0,
        });
        if (propProvinceID && matched) {
          debouncedLoadDistricts(propProvinceID, true, propWardID);
          if (propDistrictID) {
            debouncedLoadWards(propDistrictID, propProvinceID, true);
          }
          setIsLockedOldAddress(true);
        }
      }
    }
  }, [isProvincesLoaded, provinceID, districtID, wardID]);

  useEffect(() => {
    if (isProvincesLoaded && !isUserAction.current) {
      const { provinceID: propProvinceID, districtID: propDistrictID, wardID: propWardID } = props;
      const { provinceID: stateProvinceID, districtID: stateDistrictID, wardID: stateWardID } = internalOldAddress;
      if (
        propProvinceID &&
        (propProvinceID != stateProvinceID || propDistrictID != stateDistrictID || propWardID != stateWardID)
      ) {
        const matched = locationData.provinces.some((item) => item.provinceID == propProvinceID);
        setInternalOldAddress({
          provinceID: propProvinceID || 0,
          districtID: propDistrictID || 0,
          wardID: propWardID || 0,
        });
        if (propProvinceID && matched) {
          debouncedLoadDistricts(propProvinceID, false);
          if (propDistrictID) {
            debouncedLoadWards(propDistrictID, propProvinceID, false);
          }
          if (isShowOldAddress) {
            setIsLockedNewAddress(true);
          }
        }
      }
    }
  }, [isProvincesLoaded, provinceID, districtID, wardID, isShowOldAddress]);

  // useEffect(() => {
  //   if (isProvincesLoaded && locationData.districtsNew.length > 0 && internalNewAddress.districtID == 0) {
  //     const firstDistrict = locationData.districtsNew[0];
  //     if (firstDistrict) {
  //       setInternalNewAddress((prev) => ({
  //         ...prev,
  //         districtID: firstDistrict.districtID,
  //       }));
  //       debouncedLoadWards(firstDistrict.districtID, internalNewAddress.provinceID, true);
  //     }
  //   }
  // }, [isProvincesLoaded, locationData.districtsNew, internalNewAddress.districtID, internalNewAddress.provinceID]);

  return (
    <View>
      {isShowOldAddress && (
        <AddressPickerSection
          title={oldAddressTitle}
          isLocked={isLockedOldAddress}
          onReset={handleResetOldAddress}
          provinceData={{ data: locationData.provinces, id: 'provinceID', value: 'provinceName' }}
          districtData={{ data: locationData.districts, id: 'districtID', value: 'districtName' }}
          wardData={{ data: locationData.wards, id: 'wardID', value: 'wardName' }}
          provinceID={internalOldAddress.provinceID}
          districtID={internalOldAddress.districtID}
          wardID={internalOldAddress.wardID}
          onSelectProvince={(province) => handleSelectProvince(province, false)}
          onSelectDistrict={(district) => handleSelectDistrict(district, false)}
          onSelectWard={(ward) => handleSelectWard(ward, false)}
          indexPager={indexPager}
          setIndexPager={setIndexPager}
          isShowIndicator={isShowIndicator}
          isNewAddress={false}
          isRequire={isRequireOldAddress}
          enableDistrictSelection={enableDistrictSelection}
        />
      )}
      <AddressPickerSection
        title={newAddressTitle}
        isLocked={isLockedNewAddress}
        onReset={handleResetNewAddress}
        provinceData={{ data: locationData.provincesNew, id: 'provinceID', value: 'provinceName' }}
        districtData={{ data: locationData.districtsNew, id: 'districtID', value: 'districtName' }}
        wardData={{ data: locationData.wardsNew, id: 'wardID', value: 'wardName' }}
        provinceID={internalNewAddress.provinceID}
        districtID={internalNewAddress.districtID}
        wardID={internalNewAddress.wardID}
        onSelectProvince={(province) => handleSelectProvince(province, true)}
        onSelectDistrict={(district) => handleSelectDistrict(district, true)}
        onSelectWard={(ward) => handleSelectWard(ward, true)}
        indexPager={indexPager}
        setIndexPager={setIndexPager}
        isShowIndicator={isShowIndicator}
        isNewAddress={true}
        isRequire={isRequireNewAddress}
        enableDistrictSelection={enableDistrictSelection}
      />
    </View>
  );
});

AddressManager.propTypes = {
  provinceID: PropTypes.number,
  districtID: PropTypes.number,
  wardID: PropTypes.number,
  isShowOldAddress: PropTypes.bool,
  newAddressTitle: PropTypes.string,
  oldAddressTitle: PropTypes.string,
  onChangeProvince: PropTypes.func,
  onChangeDistrict: PropTypes.func,
  onChangeWard: PropTypes.func,
};

AddressManager.defaultProps = {
  isShowOldAddress: false,
  newAddressTitle: 'Thông tin địa chỉ sau sáp nhập',
  oldAddressTitle: 'Thông tin địa chỉ trước sáp nhập',
  isRequireOldAddress: false,
  isRequireNewAddress: false,
  enableDistrictSelection: false
};

export default AddressManager;

/**
 * Component quản lý địa chỉ Cũ và Mới sau sáp nhập.
 *
 * Props:
 * - provinceID, districtID, wardID: Giá trị khởi tạo cho địa chỉ
 * - isShowOldAddress: true/false để hiển thị địa chỉ cũ
 * - onChangeProvince, onChangeDistrict, onChangeWard: callback khi thay đổi
 *
 * Ref expose:
 * - getAddressData(): { oldAddress, newAddress, locationData }
 *
 * Dùng như sau:
 *
 * const addressRef = useRef();
 * <AddressManager ref={addressRef} isShowOldAddress={true} />
 * const data = addressRef.current.getAddressData();
 */

