import { Animated, StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useRef } from 'react';
import { COLORS } from '@styles';
import { dateHelper, helper } from '@common';
import { Icon, MyText } from '@components';
import moment from 'moment';

const GuideItem = ({ index, onPressDetail, item, onReceiveTicket }) => {
    const {
        CustomerCareID,
        VoucherConcern,
        CreatedDate,
        NumberDate,
        CreatedUser,
        ReceiverUser,
        RemindDate
    } = item;
    const disabledReceive = Boolean(ReceiverUser?.length);
    const translateY = useRef(new Animated.Value(50)).current;
    const opacity = useRef(new Animated.Value(0)).current;

    const textDay = getTextDay(RemindDate)

    useEffect(() => {
        Animated.parallel([
            Animated.timing(translateY, {
                toValue: 0,
                duration: 1000,
                delay: index * 333,
                useNativeDriver: true
            }),
            Animated.timing(opacity, {
                toValue: 1,
                duration: 1000,
                delay: index * 333,
                useNativeDriver: true
            })
        ]).start();
    }, [index]);

    const formattedDate = dateHelper.formatDateDDMMYYYY(
        new Date(CreatedDate || '')
    );


    return (
        <Animated.View
            style={[styles.cardItem, { opacity, transform: [{ translateY }] }]}>
            <FieldText
                label="Mã phiếu chăm sóc:"
                value={CustomerCareID}
                bold
                color={COLORS.bg64B74F}
            />
            <FieldText label="Mã SO:" value={VoucherConcern} />
            <View style={{ flexWrap: "wrap", flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
                <View style={{ flex: 4 }}>
                    <FieldText label="User tạo:" value={CreatedUser} />

                </View>
                <View style={{ flex: 4 }}>
                    {
                        !!ReceiverUser && <FieldText label="User nhận:" value={ReceiverUser} />
                    }

                </View>
            </View>


            {/* <FieldText
                label="Ngày còn lại:"
                value={textDay}
                bold
                color={COLORS.bg64B74F}
            /> */}
            <FieldText
                label="Ngày nhắc lịch:"
                value={helper.IsNonEmptyString(textDay) ? textDay : dateHelper.formatDateDDMMYYYY(
                    new Date(RemindDate || '')
                )}
                bold
                color={COLORS.bg64B74F}
            />
            <FieldText label="Ngày tạo lịch:" value={formattedDate} />

            <View style={styles.actionWrapper}>
                <ActionButton
                    onPress={onPressDetail}
                    text="Xem chi tiết"
                    icon="chevron-right"
                    color={COLORS.bg1E88E5}
                />
                <ActionButton
                    onPress={onReceiveTicket}
                    text="Nhận phiếu"
                    icon="package-down"
                    color={COLORS.bgFFFFFF}
                    bgColor={
                        disabledReceive ? COLORS.bg8E8E93 : COLORS.bg64B74F
                    }
                    disabled={disabledReceive}
                />
            </View>
        </Animated.View>
    );
};

export default GuideItem;

export const FieldText = ({ label, value, color = COLORS.bg000000, bold = false }) => (
    <View style={styles.fieldText}>
        <MyText style={styles.label} text={label} />
        <MyText
            style={[styles.value, bold && styles.boldText, { color }]}
            text={value}
        />
    </View>
);

const ActionButton = ({
    onPress,
    text,
    icon,
    color,
    bgColor = COLORS.bgFFFFFF,
    disabled = false
}) => (
    <TouchableOpacity
        activeOpacity={0.7}
        onPress={onPress}
        disabled={disabled}
        style={[
            styles.actionButton,
            { backgroundColor: bgColor, opacity: disabled ? 0.5 : 1 }
        ]}>
        <MyText style={[styles.actionText, { color }]} text={text} />
        <Icon
            iconSet="MaterialCommunityIcons"
            name={icon}
            color={color}
            size={18}
        />
    </TouchableOpacity>
);

export const getTextDay = (RemindDate) => {
    const firstDate = new Date()
    const secondDate = new Date(RemindDate)

    const firstDateInMs = firstDate.getTime()
    const secondDateInMs = secondDate.getTime()

    const differenceBtwDates = secondDateInMs - firstDateInMs

    const aDayInMs = 24 * 60 * 60 * 1000

    const daysDiff = Math.round(differenceBtwDates / aDayInMs)
    console.log("🚀 ~ getTextDay ~ daysDiff:", daysDiff)
    // const createdDate = moment(new Date(), 'YYYY/MM/DD');
    // const remindDate = moment(RemindDate, 'YYYY/MM/DD');
    // const days = remindDate.diff(createdDate, 'days');
    if (daysDiff === 0) return 'Hôm nay';
    if (daysDiff === 1) return 'Ngày mai';
    return "";
};

const styles = StyleSheet.create({
    cardItem: {
        marginTop: 10,
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: COLORS.bgE0E0E0,
        padding: 10,
        borderRadius: 5,
        backgroundColor: COLORS.bgFFFFFF,
        elevation: 8,
        shadowColor: COLORS.bg8E8E93,
        shadowOffset: { width: 5, height: 5 },
        shadowOpacity: 0.26
    },
    actionWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 4
    },
    actionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 5,
        borderRadius: 10
    },
    actionText: {
        paddingRight: 5,
        fontWeight: '500'
    },
    fieldText: {
        flexDirection: 'row',
        marginVertical: 4,
        alignItems: 'center'
    },
    label: {
        color: COLORS.bg7F7F7F
    },
    value: {
        flex: 1,
        color: COLORS.bg000000,
        paddingLeft: 10
    },
    boldText: {
        fontWeight: 'bold'
    }
});
