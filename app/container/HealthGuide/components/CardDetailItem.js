import { StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import React, { useRef } from 'react';
import { COLORS } from '@styles';
import { Icon, MyText } from '@components';
import { constants } from '@constants';
import { FieldText, getTextDay } from './GuideItem';
import { dateHelper, helper } from '@common';
import { validateNumber } from '../Screens/CreateCard';


const CardDetailItem = ({
    item,
    statusSelected,
    onChangeDate,
    onChangeStatus
}) => {
    const {
        ProductName,
        cus_BuyBackQuantity,
        Quantity,
        InstockQuantity,
        NumberDate,
        originStatus,
        RemindDate
    } = item;

    const initStatus = '1,2'.includes(originStatus);
    const textDay = getTextDay(RemindDate)

    return (
        <View
            style={{
                borderBottomWidth: StyleSheet.hairlineWidth,
                borderColor: COLORS.bdCCCCCC,
                marginBottom: 20,
                paddingBottom: 10,
                backgroundColor: COLORS.bgFFFFFF,
                // Android
                elevation: 8,
                // shadow color
                shadowColor: COLORS.bg8E8E93,
                //iOS
                shadowOffset: { width: 5, height: 5 },
                shadowOpacity: 0.26
            }}>
            <View style={{ paddingTop: 10 }}>
                <MyText
                    style={{
                        color: COLORS.bg7F7F7F,
                        paddingBottom: 5
                    }}
                    text={'Tên sản phẩm: '}>
                    <MyText
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.bg64B74F,
                            fontVariant: ''
                        }}
                        text={ProductName}>
                        <MyText
                            style={{
                                fontVariant: '',
                                color: COLORS.bg000000
                            }}
                            text={` (Tồn: ${InstockQuantity} Mới)`}
                        >
                            {/* <MyText style={{ color: COLORS.bg7F7F7F }} text={" Mới)"} /> */}
                        </MyText>
                    </MyText>
                </MyText>
                <FieldText
                    label="Ngày nhắc lịch:"
                    value={helper.IsNonEmptyString(textDay) ? textDay : dateHelper.formatDateDDMMYYYY(
                        new Date(RemindDate || '')
                    )}
                    bold
                    color={COLORS.bg64B74F}
                />
                <View
                    style={{
                        paddingTop: 5,
                        flexDirection: 'row',
                        justifyContent: 'space-between'
                    }}>
                    <View style={{ justifyContent: 'center' }}>
                        <MyText style={{ color: COLORS.bg7F7F7F }} text="SL: ">
                            <MyText
                                style={{
                                    color: COLORS.txtF50537,
                                    fontVariant: ''
                                }}
                                text={Quantity}
                            />
                        </MyText>
                    </View>

                    <View
                        style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <MyText
                            style={{ fontVariant: '', color: COLORS.bg7F7F7F }}
                            text={'Ngày uống thuốc:'}
                        />

                        <TextInput
                            editable={initStatus}
                            keyboardType={'numeric'}
                            returnKeyType={'done'}
                            value={String(NumberDate)}
                            onChangeText={(number) => {
                                if (validateNumber(number)) {
                                    onChangeDate({
                                        productId: item.ProductID,
                                        numberDay: number
                                    });
                                }
                            }}
                            style={{
                                borderRadius: 4,
                                borderColor: COLORS.bg8E8E93,
                                paddingHorizontal: 10,
                                backgroundColor: COLORS.bgFFFFFF,
                                color: COLORS.txt333333,
                                borderWidth: StyleSheet.hairlineWidth,
                                width: constants.width - 100,
                                height: 40,
                                width: 50,
                                height: 35,
                                marginLeft: 5,
                                alignItems: 'center',
                                justifyContent: 'center',
                                alignContent: 'center',
                                alignSelf: 'center'
                            }}
                        />
                    </View>
                </View>

                <View
                    style={{
                        paddingTop: 5,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        flex: 1
                    }}>
                    <View style={{ justifyContent: 'center', flex: 3 }}>
                        <MyText
                            style={{ fontVariant: '', color: COLORS.bg7F7F7F }}
                            text="Mua lại: ">
                            <MyText
                                style={{
                                    color: COLORS.txtF50537,
                                    fontVariant: ''
                                }}
                                text={cus_BuyBackQuantity}
                            />
                        </MyText>
                    </View>

                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            flex: 5,
                            justifyContent: 'flex-end'
                        }}>
                        {/* <MyText text={'Trạng thái:'} /> */}
                        <TouchableOpacity
                            disabled={!initStatus}
                            onPress={() =>
                                onChangeStatus({
                                    productId: item.ProductID,
                                    statusID: item.Status
                                })
                            }
                            activeOpacity={0.5}
                            style={[styles.container_status]}>
                            <View
                                style={{
                                    flex: 1,
                                    alignItems: 'center'
                                }}>
                                <MyText
                                    style={{
                                        fontWeight: '500',
                                        color: initStatus
                                            ? COLORS.txt147EFB
                                            : COLORS.bg7F7F7F,
                                        fontVariant: ''
                                    }}
                                    text={statusSelected?.value || 'Trạng thái'}
                                />
                            </View>

                            <Icon
                                iconSet={'Ionicons'}
                                name={'chevron-down-outline'}
                                color={
                                    initStatus
                                        ? COLORS.txt147EFB
                                        : COLORS.bg7F7F7F
                                }
                                size={13}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default CardDetailItem;

const styles = StyleSheet.create({
    container_status: {
        flex: 1,
        flexDirection: 'row',
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgA7A7A7,
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        alignItems: 'center',
        height: 35,
        alignContent: 'center',
        width: '100%'
    }
});
