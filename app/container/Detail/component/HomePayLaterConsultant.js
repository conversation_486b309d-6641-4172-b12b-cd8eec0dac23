import {
    Alert,
    Keyboard,
    StyleSheet,
    View
} from 'react-native';
import React, { useEffect, useState } from 'react';
import {
    hideBlockUI,
    MyText,
    NumberInput,
    showBlockUI
} from '@components';
import DropDownSearch from '../../AppleCarePlus/components/DropDownSearch';
import { COLORS } from '@styles';
import { helper } from '@common';
import { useSelector } from 'react-redux';
import { TouchableOpacity } from 'react-native';
import { getInstallmentConsultation } from '../action';
import { translate } from '@translate';

const HomePayLaterConsultant = ({ productInfo, saleProgramInfo }) => {
    const [dataLoanTerm, setDataLoanTerm] = useState([]);
    const [typeSearch, setTypeSearch] = useState(null);
    const [dataType, setDataType] = useState([]);
    const [dataConsultant, setDataConsultant] = useState({});
    const [money, setMoney] = useState(0);

    const { storeID, languageID, moduleID } = useSelector(
        (_state) => _state.userReducer
    );

    useEffect(() => {
        if (!helper.IsNonEmptyArray(dataLoanTerm)) return;
        const formattedData = dataLoanTerm.reduce((acc, itemArray) => {
            // Tìm phần tử có KEY là "KỲ HẠN"
            const termItem = itemArray.find(({ KEY }) => KEY === 'KỲ HẠN');
            const termValue = termItem ? termItem.VALUE : null;

            // Loại bỏ phần tử "KỲ HẠN" ra khỏi mảng
            const filteredArray = itemArray.filter(
                ({ KEY }) => KEY !== 'KỲ HẠN'
            );

            // Thêm vào object kết quả
            if (termValue !== null) {
                acc[termValue] = filteredArray;
            }
            return acc;
        }, {});

        const convertedArray = Object.keys(formattedData).map((key) => ({
            value: Number(key),
            label: `${key} tháng`
        }));
        setDataConsultant(formattedData);
        setDataType(convertedArray);
    }, [dataLoanTerm]);
    useEffect(() => {
        if (!helper.IsNonEmptyArray(dataType)) return;
        setTypeSearch(dataType[0].value);
    }, [dataType]);
    useEffect(() => {
        if (helper.IsNonEmptyArray(dataType)) setDataType([]);
    }, [money]);

    const handleChangeLoanTerm = (item) => {
        setTypeSearch(item.value);
    };

    const handleGetLoanTerm = () => {
        Keyboard.dismiss();
        if (money < 30000 || money > 50000000) {
            return Alert.alert(
                '',
                'Số tiền vay phải lớn hơn 30,000 và nhỏ hơn 50,000,090'
            );
        }
        showBlockUI();
        const { partnerID, saleProgramID } = saleProgramInfo;
        const { productID, salePriceVAT } = productInfo;

        const body = {
            loginStoreId: storeID,
            languageID: languageID,
            moduleID: moduleID,
            SaleProgramID: saleProgramID,
            PartnerInstallmentID: partnerID,
            ProductID: productID,
            ExtraData: {
                SALEPRICE: salePriceVAT,
                INSTALLMENTPRICE: money
            }
        };
        getInstallmentConsultation(body)
            .then((result) => {
                hideBlockUI();
                setDataLoanTerm(result);
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: hideBlockUI,
                            style: 'cancel'
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () =>
                                handleGetLoanTerm(),
                            style: 'default'
                        }
                    ],
                    { cancelable: false }
                );
            });
    };

    return (
        <View style={styles.container_wrapper}>
            <View style={{ flexDirection: 'row' }}>
                <View
                    style={{
                        height: 40,
                        backgroundColor: COLORS.bgFFFFFF,
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderWidth: StyleSheet.hairlineWidth,
                        borderColor: COLORS.bd778899,
                        borderRadius: 15,
                        marginBottom: 10,
                        flex: 6.5
                    }}>
                    <NumberInput
                        style={{
                            height: 50,
                            paddingHorizontal: 15,
                            fontSize: 13
                        }}
                        placeholder={'Nhập số tiền vay'}
                        placeholderTextColor={COLORS.txt778899}
                        value={money}
                        onChangeText={(value) => {
                            setMoney(value);
                        }}
                        underlineColorAndroid={'transparent'}
                        autoCorrect={false}
                    />
                </View>
                <TouchableOpacity
                    onPress={handleGetLoanTerm}
                    activeOpacity={0.7}
                    style={{
                        flex: 1.5,
                        backgroundColor: COLORS.bg1E88E5,
                        marginLeft: 10,
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: 40,
                        borderRadius: 15
                    }}>
                    <MyText
                        style={{ fontWeight: 'bold', color: COLORS.bgFFFFFF }}
                        text={'Tìm'}
                    />
                </TouchableOpacity>
            </View>

            {helper.IsNonEmptyArray(dataType) && (
                <View>
                    <MyText style={styles.text} text={'Chọn kỳ hạn vay'} />
                    <DropDownSearch
                        typeSearch={typeSearch}
                        dataTypes={dataType}
                        onChange={handleChangeLoanTerm}
                    />
                    <View style={styles.card_wrapper}>
                        {helper.IsNonEmptyArray(dataConsultant[typeSearch]) && (
                            <View>
                                {dataConsultant[typeSearch].map(
                                    ({ KEY, VALUE }, index) => {
                                        const hasValue =
                                            helper.IsNonEmptyString(VALUE);
                                        return (
                                            <View
                                                key={index}
                                                style={styles.cards}>
                                                <View
                                                    style={
                                                        styles.card_item_right
                                                    }>
                                                    <MyText
                                                        style={
                                                            styles.textContent
                                                        }
                                                        text={KEY}
                                                    />
                                                </View>
                                                <View
                                                    style={
                                                        styles.card_item_left
                                                    }>
                                                    <MyText
                                                        style={[
                                                            {
                                                                fontWeight:
                                                                    'bold'
                                                            },
                                                            styles.textContent
                                                        ]}
                                                        text={VALUE}
                                                    />
                                                </View>
                                            </View>
                                        );
                                    }
                                )}
                            </View>
                        )}
                    </View>
                </View>
            )}
        </View>
    );
};

export default HomePayLaterConsultant;

const styles = StyleSheet.create({
    container_wrapper: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF,
        padding: 10
    },
    title: {
        borderBottomWidth: 0.5,
        alignItems: 'center',
        marginVertical: 10,
        fontWeight: 'bold',
        color: COLORS.bg1E88E5,
        paddingBottom: 10,
        justifyContent: 'center'
    },
    text: {
        fontWeight: 'bold',
        color: COLORS.txt505050,
        paddingBottom: 5
    },
    card_wrapper: {
        borderLeftWidth: 1,
        borderRightWidth: 1,
        borderTopWidth: 1,
        borderColor: COLORS.txtC0C0C0,
        borderRadius: 5,
        justifyContent: 'center',
        marginTop: 10
    },
    cards: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: COLORS.txtC0C0C0,
        borderRadius: 5
    },
    card_item_right: {
        flex: 5,
        borderRightWidth: 1,
        borderColor: COLORS.txtC0C0C0
    },
    card_item_left: {
        flex: 3,
        alignItems: 'flex-end'
    },
    textContent: {
        padding: 7
    }
});
