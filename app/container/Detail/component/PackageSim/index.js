/**
 * Sample React Native App
 * 
 *
 * @format
 * @flow strict-local
 */

import React, { useState, useEffect } from 'react';
import {
    ScrollView,
    View,
} from 'react-native';
import { constants } from "@constants";
import { BaseLoading } from "@components";
import { helper } from "@common";
import SimImage from '../../SimInfo/component/SimImage';
import CheckPackage from '../../SimInfo/component/CheckPackage';
import SimOtherInfo from '../../SimInfo/component/SimOtherInfo';
import SimTitle from '../../SimInfo/component/SimTitle';


const PackageSim = ({
    imageUrl,
    dataPackage,
    statePackage,
    productInfo,
    updateSimInfo,
    getPackage,
    packagesId
}) => {
    const [description, setDescription] = useState("");

    const effectChangePackage = () => {
        if (helper.IsNonEmptyArray(dataPackage)) {
            onChangePackage(dataPackage[0]);
        }
    }

    useEffect(
        effectChangePackage,
        [dataPackage]
    )

    const onRetryPackage = () => {
        const {
            productID,
            salePrice,
            vat,
            vatPercent,
        } = productInfo;
        getPackage({
            "productID": productID,
            "salePrice": salePrice,
            "vat": vat,
            "vatPercent": vatPercent,
        });
    }

    const onChangePackage = (packageInfo) => {
        const {
            description,
        } = packageInfo;
        updateSimInfo(packageInfo);
        setDescription(description);
    }

    return (
        <View style={{
            flex: 1,
        }}>
            <ScrollView
                contentContainerStyle={{
                    flexGrow: 1,
                }}>
                <View style={{
                    width: constants.width,
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: 10,
                    paddingBottom: 54
                }}>
                    <SimTitle
                        productInfo={productInfo}
                    />

                    <View style={{
                        width: constants.width,
                        padding: 8,
                        flexDirection: "row",
                    }}>
                        <SimImage
                            imageUrl={imageUrl}
                        />

                        <BaseLoading
                            isLoading={statePackage.isFetching}
                            isEmpty={statePackage.isEmpty}
                            textLoadingError={statePackage.description}
                            isError={statePackage.isError}
                            onPressTryAgains={onRetryPackage}
                            content={
                                <CheckPackage
                                    dataPackage={dataPackage}
                                    packagesId={packagesId}
                                    onChangePackage={onChangePackage}
                                />
                            }
                        />
                    </View>
                    <SimOtherInfo
                        description={description}
                    />
                </View>
            </ScrollView>
        </View>
    );
}

export default PackageSim;