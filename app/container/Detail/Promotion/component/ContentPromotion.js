import React from 'react';
import { View } from 'react-native';
import { MyText } from "@components";
import { constants } from "@constants";
import { helper } from "@common";
import { translate } from '@translate';
import { COLORS } from "@styles";

const ContentPromotion = ({ product }) => {
    const {
        isDiscount,
        isPercentDiscount,
        discountValue,
        productName,
        quantity,
        salePriceVAT,
        instockQuantity,
        productTechSpecsValueList,
        inventoryStatusID,
        inventoryStatusName
    } = product;
    if (isDiscount) {
        return (
            <MyText style={{
                color: COLORS.txt333333,
                marginLeft: 4,
            }}
                text={translate('common.discount')}
            >
                <MyText style={{ color: COLORS.txtFF0000, }}
                    text={!isPercentDiscount ? helper.convertNum(discountValue) : `${discountValue}%`}
                />
            </MyText>
        );
    }
    else {
        return (
            <View style={{
                marginLeft: 4,
            }}>
                <MyText style={{
                    color: COLORS.txt333333,
                }}
                    text={`${productName} (${translate('detail.quantity_short')}: ${quantity})`}
                >
                    {
                        helper.IsNonEmptyString(productTechSpecsValueList) &&
                        <MyText style={{ color: COLORS.txt333333, }}
                            text={` (Size: ${productTechSpecsValueList})`}
                        />
                    }
                    {
                        (instockQuantity != undefined) &&
                        <MyText style={{ color: COLORS.txt008000, }}
                            text={` (${translate('common.inventory')} ${instockQuantity})`}
                        />
                    }
                    {
                        inventoryStatusID > 1 &&
                        <MyText style={{ color: COLORS.txt147EFB }}
                            text={` (${inventoryStatusName})`}
                        />
                    }
                </MyText>
            </View>
        );
    }
}

export default ContentPromotion;