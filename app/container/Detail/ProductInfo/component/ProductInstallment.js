import React from 'react';
import {
    View,
    TouchableOpacity,
    Alert,
    Image
} from 'react-native';
import { Button, Icon, MyText, ImageURI } from "@components";
import { constants, API_CONST } from "@constants";
import { translate } from '@translate';
import { COLORS } from "@styles";
import { helper } from '@common';
import { useSelector } from 'react-redux';

const {
    API_LOGO_PARTNER_INSTALLMENT
} = API_CONST;
const { PARTNER_ID, PARTNER_REWARD_VALUE, EREA_APPLY_REWARD_20, EREA_APPLY_REWARD_7 } = constants

const Installment = ({
    logo,
    saleProgramID,
    saleProgramName,
    onShowInstallment,
    onDelete,
    isCartEmpty,
    onPressProductInformation,
    partnerID,
    dataRewardInstallment
}) => {
    const { storeGroupID } = useSelector((_state) => _state.userReducer)
    const hotRewardPoint = getRewardPoint(partnerID, storeGroupID)
    const partnerName = getPartnerName(partnerID)
    const rewardPointStaffUser = partnerID == PARTNER_ID.MIRAE_ASSET ? 30000 : dataRewardInstallment?.rewardPointStaffUser
    const deleteInstallment = () => {
        Alert.alert("",
            translate('detail.remove_instalment_information'),
            [
                {
                    text: translate('common.btn_skip'),
                    style: "cancel",
                },
                {
                    text: translate('common.btn_continue'),
                    style: "default",
                    onPress: onDelete
                }
            ]
        );
    }
    return (
        <View style={{
            flex: 1,
        }}>
            <TouchableOpacity
                style={{
                    width: constants.width - 40,
                    padding: 10,
                    borderWidth: 1,
                    borderColor: COLORS.bdE4E4E4,
                    marginBottom: 10,
                    borderRadius: 4,
                }}
                onPress={onShowInstallment}
                disabled={!isCartEmpty}
            >
                <View style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: 5,
                    backgroundColor: COLORS.bgFFFFFF,
                    margin: 15,
                    flexWrap: "wrap",
                    width: 150,
                    height: 50,
                    borderRadius: 2,
                    shadowColor: COLORS.sd000000,
                    shadowOffset: {
                        width: 0,
                        height: 1,
                    },
                    shadowOpacity: 0.25,
                    shadowRadius: 3.84,
                    elevation: 2,
                    borderColor: COLORS.bd2FB47C,
                    borderWidth: 2
                }}>
                    {/* <ImageURI
                    uri={API_LOGO_PARTNER_INSTALLMENT + logo}
                    style={{
                        width: 100,
                        height: 40,
                    }}
                    resizeMode={"contain"}
                /> */}
                    {
                        !!logo && <Image
                            source={{ uri: logo }}
                            style={{
                                width: 100,
                                height: 40,
                            }}
                            resizeMode={"contain"}
                        />
                    }
                </View>
                <View style={{
                    marginHorizontal: 10,
                    padding: 10,
                    backgroundColor: COLORS.bgFFFFFF,
                    borderRadius: 4,
                    shadowColor: COLORS.sd000000,
                    shadowOffset: {
                        width: 0,
                        height: 1,
                    },
                    shadowOpacity: 0.25,
                    shadowRadius: 3.84,
                    elevation: 2
                }}>
                    <MyText style={{
                        width: "100%",
                        color: COLORS.txt333333,
                        fontStyle: 'italic',
                    }}
                        text={`${saleProgramID} - ${saleProgramName}`}
                    />
                </View>
                <View style={{
                    marginTop: 10,
                    alignSelf: 'flex-end',
                    flexDirection: "row",
                }}>
                    {!!hotRewardPoint && <View style={{
                        flexDirection: 'row',
                        paddingRight: 20
                    }}>
                        <MyText style={{
                            color: COLORS.txt333333
                        }}
                            text={'TN: '}
                        />
                        <MyText style={{
                            color: COLORS.bgEA1D5D
                        }}
                            text={helper.convertMaskString(hotRewardPoint)}
                        />
                    </View>}

                    {rewardPointStaffUser > 0 &&
                        <View style={{
                            flexDirection: 'row'
                        }}>
                            <MyText style={{
                                color: COLORS.txt333333
                            }}
                                text={'ĐT: '}
                            />
                            <MyText style={{
                                color: COLORS.txt008000
                            }}
                                text={helper.convertMaskString(rewardPointStaffUser)}
                            />
                        </View>
                    }
                </View>
                {
                    isCartEmpty &&
                    <TouchableOpacity style={{
                        width: 40,
                        height: 26,
                        top: 0,
                        right: 0,
                        borderBottomLeftRadius: 20,
                        alignItems: 'center',
                        justifyContent: "center",
                        backgroundColor: COLORS.btnF5F5F5,
                        position: 'absolute',
                    }}
                        onPress={deleteInstallment}
                        activeOpacity={1}
                    >
                        <Icon
                            iconSet={"MaterialIcons"}
                            name={"close"}
                            color={COLORS.icD0021B}
                            size={20}
                        />
                    </TouchableOpacity>
                }
            </TouchableOpacity>
            {
                !!partnerName && <MyText style={{
                    flex: 1,
                    color: COLORS.bg1E88E5,
                    fontStyle: 'italic',
                    textDecorationLine: 'underline',
                }}
                    text={`Kiểm tra thông tin trả góp qua ${partnerName}`}
                    onPress={onPressProductInformation}
                />
            }
        </View>
    );
}

const ProductInstallment = ({
    saleProgramInfo,
    onShowInstallment,
    onDelete,
    isCartEmpty,
    onPressProductInformation,
    dataRewardInstallment

}) => {
    const {
        logo,
        partnerID,
        interestRate,
        saleProgramID,
        saleProgramName,
    } = saleProgramInfo;
    const isInstallment = (saleProgramID > 0);

    return (
        isInstallment
            ? <Installment
                logo={logo}
                saleProgramID={saleProgramID}
                saleProgramName={saleProgramName}
                onShowInstallment={onShowInstallment}
                onDelete={onDelete}
                isCartEmpty={isCartEmpty}
                onPressProductInformation={onPressProductInformation}
                partnerID={partnerID}
                dataRewardInstallment={dataRewardInstallment}
            />
            : <View style={{
                width: constants.width,
                padding: 10,
                justifyContent: "center",
                alignItems: "center"
            }}>
                {
                    isCartEmpty &&
                    <Button
                        text={translate('detail.sale_installment')}
                        onPress={onShowInstallment}
                        styleContainer={{
                            flexDirection: 'row',
                            paddingHorizontal: 30,
                            height: 44,
                            borderRadius: 4,
                            backgroundColor: COLORS.btn147EFB,
                        }}
                        styleText={{
                            color: COLORS.txtFFFFFF,
                            fontSize: 14,
                            fontWeight: "bold",
                        }}
                    />
                }
            </View>
    );
}

export default ProductInstallment;

const getRewardPoint = (partnerID, storeGroupID) => {
    switch (`${partnerID}`) {
        case PARTNER_ID.FE_CREDIT:
            if (EREA_APPLY_REWARD_7.includes(storeGroupID)) return PARTNER_REWARD_VALUE[partnerID].FE_7
            if (EREA_APPLY_REWARD_20.includes(storeGroupID)) return PARTNER_REWARD_VALUE[partnerID].FE_20
            return 0
        case PARTNER_ID.HOME_CREDIT:
            if (EREA_APPLY_REWARD_20.includes(storeGroupID)) return PARTNER_REWARD_VALUE[partnerID]
            return 0
        default:
            return PARTNER_REWARD_VALUE[partnerID]
    }
}


const getPartnerName = (partnerID) => {
    const partnerMap = {
        [String(PARTNER_ID.SMART_POS)]: "thẻ tín dụng qua SMARTPOS",
        [String(PARTNER_ID.MOMO)]: "thẻ tín dụng qua MOMO",
        [String(PARTNER_ID.HOME_PAY_LATER)]: "thẻ tín dụng qua HOMEPAY_LATER",
        [String(PARTNER_ID.KREDIVO)]: "KREDIVO",
        [String(PARTNER_ID.CAKE)]: "CAKE",
        [String(PARTNER_ID.TPBanhEVO)]: "Evo",
    };

    return partnerMap[String(partnerID)] || '';
};

