
import { API_CONST, constants, ENUM } from '@constants';
import { helper } from "@common";
import { apiBase, METHOD } from '@config';
import { translate } from '@translate';
import _ from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { SIM_PACKAGE_COMPONENTS } from '../../constants/constants';

const { AN_KHANG_PHARMACY, SALE } = ENUM.SALE_SCENARIO_TYPE;
const {
    API_DETAILS_PRODUCT,
    API_PROMOTION,
    API_GET_STORE_NEAREST,
    API_GET_STORE_SHIPPING,
    API_GET_SUGGEST_TIMES,
    API_GET_EMPLOYEE_AT_STORE,
    API_CHECK_CUSTOMER_PHONE_PROMOTION,
    API_CHECK_CUSTOMER_PHONE_PROMOTION_NEW,
    API_CHECK_BARCODE_PROMOTION,
    API_CHECK_BARCODE_PROMOTION_NEW,
    API_GET_INSTALLMENT,
    API_GET_PROGRAM_INSTALLMENT,
    API_GET_FIFO_INFO,
    API_GET_CONFIG_PRODUCT,
    API_GET_FEATURE_PRODUCT,
    API_GET_LOCK_PRODUCT_INFO,
    API_GET_STORE_BY_LOCATION,
    API_GET_SIM_PACKAGE,
    API_GET_SIM_PRICE,
    API_PROMOTION_DELIVERYTYPE,
    API_PROMOTION_LOSTSALE,
    API_PROMOTION_LOSTSALE_NEW,
    API_CHECK_IMEI_SALE,
    API_GET_EXPAND_PROMOTION,
    API_INSTALLATION_REPAIR,
    API_GET_PRE_ORDER_LOCK_PRODUCT_INFO,
    API_PRODUCT_LOYALTY_POINT,
    API_GET_SUGGEST_PRODUCT,
    API_GET_TIMES_BY_DATE,
    API_PROMOTION_FOR_VIEW,
    API_GET_WARRANTY_PRODUCT,
    API_PROMOTION_NEW,
    API_PROMOTION_DELIVERYTYPE_NEW,
    API_GET_DISCOUNT_VALUE,
    API_GET_OUTPUT_STORE,
    GET_PRODUCTID_BY_IDREF,
    API_GET_SERVICE_GROUP_TYPE,
    API_GET_SERVICE_PROGRAM,
    API_GET_SERVICE_PROGRAM_FEE,
    API_CHECK_PRODUCT_PRE,
    API_UPDATE_STATUS_RANDOM_DISCOUNT_PROMOTION,
    API_CHECK_PHONE_NUMBER_APPLY_PROMOTION,
    API_GET_PACKAGE_SERVICE,
    API_CONFIRM_PREORDER_EVENT,
    API_GET_INSTALLMENT_PACKAGE_SMART_POS,
    API_GET_REWARD_INSTALLMENT,
    API_GET_PARTNER_INSTALLMENT_USER_CODE,
    API_GET_DELIVERY_SERVICE_PACKAGE,
    API_PROMOTION_WEB_SERVICE_ENGINE,
    API_PROMOTION_BARCODE_WEB_SERVICE_ENGINE,
    API_GET_STANDARD_POINT,
    API_SEARCH_ADDRESS,
    API_GET_INFO_ADDRESS,
    API_VALIDATE_IMEI_WITH_PARTNER,
    API_GET_PRICE_POLICY_PROGRAM
} = API_CONST;

const { PROMOTION_CONFIG } = constants;

const START_GET_DATA_PRODUCT = "START_GET_DATA_PRODUCT";
const STOP_GET_DATA_PRODUCT = "STOP_GET_DATA_PRODUCT";
const START_GET_DATA_INVENTORY_TAB = "START_GET_DATA_INVENTORY_TAB";
const STOP_GET_DATA_INVENTORY_TAB = "STOP_GET_DATA_INVENTORY_TAB";
const SET_DETAIL_PRODUCT = "SET_DETAIL_PRODUCT";
const START_GET_SECOND_STOCK = "START_GET_SECOND_STOCK";
const STOP_GET_SECOND_STOCK = "STOP_GET_SECOND_STOCK";
const START_GET_PROMOTION = "START_GET_PROMOTION";
const STOP_GET_PROMOTION = "STOP_GET_PROMOTION";
const GET_ALL_KEY_PROMOTION = "GET_ALL_KEY_PROMOTION";
const START_GET_STORE_NEAREST = "START_GET_STORE_NEAREST";
const STOP_GET_STORE_NEAREST = "STOP_GET_STORE_NEAREST";
const START_GET_STORE_SHIPPING = "START_GET_STORE_SHIPPING";
const STOP_GET_STORE_SHIPPING = "STOP_GET_STORE_SHIPPING";
const START_GET_EMPLOYEE = "START_GET_EMPLOYEE";
const STOP_GET_EMPLOYEE = "STOP_GET_EMPLOYEE";
const START_GET_DATA_AT_HOME = "START_GET_DATA_AT_HOME";
const STOP_GET_DATA_AT_HOME = "STOP_GET_DATA_AT_HOME";
const START_GET_PARTNER_INSTALLMENT = "START_GET_PARTNER_INSTALLMENT";
const STOP_GET_PARTNER_INSTALLMENT = "STOP_GET_PARTNER_INSTALLMENT";
const START_GET_PROGRAM_INSTALLMENT = "START_GET_PROGRAM_INSTALLMENT";
const STOP_GET_PROGRAM_INSTALLMENT = "STOP_GET_PROGRAM_INSTALLMENT";
const START_GET_EXHIBIT_STOCK = "START_GET_EXHIBIT_STOCK";
const STOP_GET_EXHIBIT_STOCK = "STOP_GET_EXHIBIT_STOCK";
const START_GET_FIFO = "START_GET_FIFO";
const STOP_GET_FIFO = "STOP_GET_FIFO";
const START_GET_CONFIG = "START_GET_CONFIG";
const STOP_GET_CONFIG = "STOP_GET_CONFIG";
const START_GET_FEATURE = "START_GET_FEATURE";
const STOP_GET_FEATURE = "STOP_GET_FEATURE";
const RESET_DETAIL_STATE = "RESET_DETAIL_STATE";
const START_GET_LOCK_INFO = "START_GET_LOCK_INFO";
const STOP_GET_LOCK_INFO = "STOP_GET_LOCK_INFO";
const START_GET_STORE_OTHER = "START_GET_STORE_OTHER";
const STOP_GET_STORE_OTHER = "STOP_GET_STORE_OTHER";
const START_GET_SIM_PACKAGE = "START_GET_SIM_PACKAGE";
const STOP_GET_SIM_PACKAGE = "STOP_GET_SIM_PACKAGE";
const START_GET_PRE_ORDER_LOCK_INFO = "START_GET_PRE_ORDER_LOCK_INFO";
const STOP_GET_PRE_ORDER_LOCK_INFO = "STOP_GET_PRE_ORDER_LOCK_INFO";
const START_GET_PROMOTION_FOR_VIEW = "START_GET_PROMOTION_FOR_VIEW";
const START_GET_WARRANTY = "START_GET_WARRANTY";
const STOP_GET_WARRANTY = "STOP_GET_WARRANTY";
const START_GET_DISCOUNT_PRICE_VAT = 'START_GET_DISCOUNT_PRICE_VAT';
const STOP_GET_DISCOUNT_PRICE_VAT = 'STOP_GET_DISCOUNT_PRICE_VAT';
const START_GET_PRODUCT_SERVICE_GROUP_TYPE = 'START_GET_PRODUCT_SERVICE_GROUP_TYPE';
const STOP_GET_PRODUCT_SERVICE_GROUP_TYPE = 'STOP_GET_PRODUCT_SERVICE_GROUP_TYPE';
const START_GET_PRODUCT_SERVICE_PROGRAM = 'START_GET_PRODUCT_SERVICE_PROGRAM';
const STOP_GET_PRODUCT_SERVICE_PROGRAM = 'STOP_GET_PRODUCT_SERVICE_PROGRAM';
const START_GET_PRODUCT_SERVICE_INFO = 'START_GET_PRODUCT_SERVICE_INFO';
const STOP_GET_PRODUCT_SERVICE_INFO = 'STOP_GET_PRODUCT_SERVICE_INFO';
const START_CHECK_PRE = 'START_CHECK_PRE';
const STOP_CHECK_PRE = 'STOP_CHECK_PRE';
const SET_PACKAGE_SERVICE = 'SET_PACKAGE_SERVICE';
const RESET_PACKAGE_SERVICE = 'RESET_PACKAGE_SERVICE';
const SET_SUGGEST_PRODUCTS = 'SET_SUGGEST_PRODUCTS';
const RESET_SUGGEST_PRODUCTS = 'RESET_SUGGEST_PRODUCTS';
const START_CHECK_PHONE_NUMBER_APPLY_PROMOTION = 'START_CHECK_PHONE_NUMBER_APPLY_PROMOTION';
const STOP_CHECK_PHONE_NUMBER_APPLY_PROMOTION = 'STOP_CHECK_PHONE_NUMBER_APPLY_PROMOTION';
const SET_MAP_CONTENT_PROMOTION_INPUT = 'SET_MAP_CONTENT_PROMOTION_INPUT';
const RESET_MAP_CONTENT_PROMOTION_INPUT = 'RESET_MAP_CONTENT_PROMOTION_INPUT';
const DELETE_MAP_CONTENT_PROMOTION_INPUT = "DELETE_MAP_CONTENT_PROMOTION_INPUT";
const START_GET_REWARD_INSTALLMENT = 'STATE_GET_REWARD_INSTALLMENT';
const STOP_GET_REWARD_INSTALLMENT = 'STOP_GET_REWARD_INSTALLMENT';
const START_GET_PARTNER_INSTALLMENT_USER_CODE = 'START_GET_PARTNER_INSTALLMENT_USER_CODE';
const STOP_GET_PARTNER_INSTALLMENT_USER_CODE = 'STOP_GET_PARTNER_INSTALLMENT_USER_CODE';
const CLEAR_PARTNER_INSTALLMENT_USER_CODE = 'CLEAR_PARTNER_INSTALLMENT_USER_CODE';
const SET_PHONE_NUMBER = 'SET_PHONE_NUMBER';



export const detailAction = {
    SET_MAP_CONTENT_PROMOTION_INPUT,
    RESET_MAP_CONTENT_PROMOTION_INPUT,
    START_GET_DATA_PRODUCT,
    STOP_GET_DATA_PRODUCT,
    START_GET_DATA_INVENTORY_TAB,
    STOP_GET_DATA_INVENTORY_TAB,
    SET_DETAIL_PRODUCT,
    START_GET_SECOND_STOCK,
    STOP_GET_SECOND_STOCK,
    STOP_GET_PROMOTION,
    START_GET_PROMOTION,
    GET_ALL_KEY_PROMOTION,
    START_GET_STORE_NEAREST,
    STOP_GET_STORE_NEAREST,
    START_GET_STORE_SHIPPING,
    STOP_GET_STORE_SHIPPING,
    START_GET_EMPLOYEE,
    STOP_GET_EMPLOYEE,
    START_GET_DATA_AT_HOME,
    STOP_GET_DATA_AT_HOME,
    START_GET_PARTNER_INSTALLMENT,
    STOP_GET_PARTNER_INSTALLMENT,
    START_GET_PROGRAM_INSTALLMENT,
    STOP_GET_PROGRAM_INSTALLMENT,
    START_GET_EXHIBIT_STOCK,
    STOP_GET_EXHIBIT_STOCK,
    START_GET_FIFO,
    STOP_GET_FIFO,
    START_GET_CONFIG,
    STOP_GET_CONFIG,
    START_GET_FEATURE,
    STOP_GET_FEATURE,
    RESET_DETAIL_STATE,
    START_GET_LOCK_INFO,
    STOP_GET_LOCK_INFO,
    START_GET_STORE_OTHER,
    STOP_GET_STORE_OTHER,
    START_GET_SIM_PACKAGE,
    STOP_GET_SIM_PACKAGE,
    START_GET_PRE_ORDER_LOCK_INFO,
    STOP_GET_PRE_ORDER_LOCK_INFO,
    START_GET_PROMOTION_FOR_VIEW,
    START_GET_WARRANTY,
    STOP_GET_WARRANTY,
    START_GET_DISCOUNT_PRICE_VAT,
    STOP_GET_DISCOUNT_PRICE_VAT,
    START_GET_PRODUCT_SERVICE_GROUP_TYPE,
    STOP_GET_PRODUCT_SERVICE_GROUP_TYPE,
    START_GET_PRODUCT_SERVICE_INFO,
    STOP_GET_PRODUCT_SERVICE_INFO,
    START_GET_PRODUCT_SERVICE_PROGRAM,
    STOP_GET_PRODUCT_SERVICE_PROGRAM,
    START_CHECK_PRE,
    STOP_CHECK_PRE,
    START_CHECK_PHONE_NUMBER_APPLY_PROMOTION,
    STOP_CHECK_PHONE_NUMBER_APPLY_PROMOTION,
    SET_PACKAGE_SERVICE,
    RESET_PACKAGE_SERVICE,
    SET_SUGGEST_PRODUCTS,
    RESET_SUGGEST_PRODUCTS,
    DELETE_MAP_CONTENT_PROMOTION_INPUT,
    START_GET_REWARD_INSTALLMENT,
    STOP_GET_REWARD_INSTALLMENT,
    START_GET_PARTNER_INSTALLMENT_USER_CODE,
    STOP_GET_PARTNER_INSTALLMENT_USER_CODE,
    CLEAR_PARTNER_INSTALLMENT_USER_CODE,
    SET_PHONE_NUMBER
};

const ERROR = true;
const EMPTY = true;

export const getInfoProductSearch = function (productInfo, isFirstCall) {
    return async (dispatch, getState) => {
        const brandID = getState().userReducer.brandID;
        const isAVA = (brandID == '14') || (brandID == '15') || (brandID == '17');
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
            "imei": productInfo.imei,
            "productID": productInfo.productID,
            "productIDRef": productInfo.productIDRef,
            "inventoryStatusID": productInfo.inventoryStatusID,
            "storeID": productInfo.storeID,
            "saleProgramID": productInfo.saleProgramID,
            "isInstalment": productInfo.isInstalment,
            "isImeiSim": productInfo.isImeiSim
        };
        let bodyThreePrice = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
            "productProlicyBO": {
                "productID": productInfo.productID,
                "imei": !!productInfo.productID ? productInfo.imei : "",
                "inventoryStatusID": productInfo.inventoryStatusID
            },
            "outputStoreID": getState().userReducer.storeID,
            "dateTime": null,
            "saleProgramID": productInfo.saleProgramID,
            "deliveryTypeID": productInfo.deliveryType,
            "customerInfoProlicyBO": {
                "customerPhone": ""
            },
            "salePricePolicyID": productInfo.salePricePolicyID,
        };

        if (isFirstCall) {
            dispatch(start_get_data_product());
        } else {
            dispatch(start_get_data_inventory_tab());
        }
        if (isAVA && !body.productID) {
            body.productID = await getProductIdERP(productInfo.productIDRef);
        }
        let allPromise = [apiBase(API_DETAILS_PRODUCT, METHOD.POST, body)];
        if (productInfo.isCallThreePrice) {
            allPromise.push(dispatch(getPackageService(bodyThreePrice, isFirstCall)));
        }
        Promise.all(allPromise)
            .then(res => {
                const infoProductSearch = res[0];
                const dataPackageService = res[1] ?? [];
                console.log("getInfoProductSearch success", res);
                const { object } = infoProductSearch;
                if (isFirstCall) {
                    dispatch(set_package_services({ id: `${productInfo.productID}_${productInfo.saleProgramID ?? 0}`, package: dataPackageService }));
                }
                if (helper.IsNonEmptyArray(object)) {
                    dispatch(setDetailProduct(object, productInfo, bodyThreePrice));
                }
                else {
                    if (isFirstCall) {
                        dispatch(stop_get_data_product([], EMPTY, translate('detail.product_information_not_available')));
                    } else {
                        dispatch(stop_get_data_inventory_tab([], EMPTY, translate('detail.product_information_not_available')));
                    }
                    dispatch(set_detail_product({}));
                }
            })
            .catch(error => {
                console.log("getInfoProductSearch error", error);
                if (isFirstCall) {
                    dispatch(stop_get_data_product([], !EMPTY, error.msgError, ERROR));
                } else {
                    dispatch(stop_get_data_inventory_tab([], !EMPTY, error.msgError, ERROR));
                }
                dispatch(set_detail_product({}));
            });
    };
};

export const getNewStockDiscount = (productID, productIDRef) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "storeID": getState().userReducer.storeID,
                "imei": "",
                "inventoryStatusID": 8,
                "saleProgramID": 0,
                "isInstalment": false,
                "isImeiSim": false,
                "productID": productID,
                "productIDRef": productIDRef
            };
            apiBase(API_DETAILS_PRODUCT, METHOD.POST, body)
                .then((response) => {
                    console.log("getNewStockDiscount success", response);

                    const { object } = response

                    resolve(object)
                })
                .catch((error) => {
                    console.log("getNewStockDiscount error", error);

                    reject(error)
                })
        })
    }
}

const getProductIdERP = async (idRef) => {
    let productID = '';
    let body = { "productIDRef": idRef };
    try {
        const response = await apiBase(GET_PRODUCTID_BY_IDREF, METHOD.POST, body);
        const { object } = response;
        if (helper.hasProperty(object, 'ProductID')) {
            productID = object.ProductID;
        }
    } catch (error) {
        console.log("getProductIdERP error", error);
    }
    return productID;
};

export const getInfoProductSecond = function (productInfo) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "imei": "",
            "productID": productInfo.productID,
            "productIDRef": productInfo.productIDRef,
            "inventoryStatusID": 2,
            "storeID": productInfo.storeID,
            "saleProgramID": productInfo.saleProgramID,
            "isInstalment": productInfo.isInstalment,
            "isImeiSim": ""
        };
        dispatch(start_get_second_stock());
        apiBase(API_DETAILS_PRODUCT, METHOD.POST, body).then((response) => {
            console.log("getInfoProductSecond success", response);
            const { object, errorReason } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_second_stock(object));
            }
            else {
                const content = errorReason || translate('detail.product_information_not_available');
                dispatch(stop_get_second_stock([], EMPTY, content));
            }
        }).catch(error => {
            console.log("getInfoProductSecond error", error);
            dispatch(stop_get_second_stock([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getInfoProductExhibit = function (productInfo) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "imei": "",
            "productID": productInfo.productID,
            "productIDRef": productInfo.productIDRef,
            "inventoryStatusID": 3,
            "storeID": productInfo.storeID,
            "saleProgramID": productInfo.saleProgramID,
            "isInstalment": productInfo.isInstalment,
            "isImeiSim": ""
        };
        dispatch(start_get_exhibit_stock());
        apiBase(API_DETAILS_PRODUCT, METHOD.POST, body).then((response) => {
            console.log("getInfoProductExhibit success", response);
            const { object, errorReason } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_exhibit_stock(object));
            }
            else {
                const content = errorReason || translate('detail.product_information_not_available');
                dispatch(stop_get_exhibit_stock([], EMPTY, content));
            }
        }).catch(error => {
            console.log("getInfoProductExhibit error", error);
            dispatch(stop_get_exhibit_stock([], !EMPTY, error.msgError, ERROR));
        });
    };
};

// PROMOTION_DELIVERY
export const getPromotion = function (productInfo) {
    return (dispatch, getState) => {
        const { storeID, brandID, provinceID } = getState().userReducer;
        const { saleScenarioTypeID } = getState().specialSaleProgramReducer;
        global.applyProduct = {
            "productID": productInfo.productID,
            "deliveryTypeID": productInfo.deliveryTypeID,
            "inventoryStatusID": productInfo.inventoryStatusID,
            "saleProgramID": productInfo.saleProgramID,
            "outputStoreID": productInfo.outputStoreID
        };
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
            "imei": productInfo.imei,
            "productID": productInfo.productID,
            "inventoryStatusID": productInfo.inventoryStatusID,
            "price": productInfo.price,
            "VAT": productInfo.VAT,
            "VATPercent": productInfo.VATPercent,
            "storeID": productInfo.storeID,
            "appliedQuantity": productInfo.appliedQuantity,
            "outputStoreID": productInfo.outputStoreID,
            "deliveryTypeID": productInfo.deliveryTypeID,
            "storeRequests": productInfo.storeRequests,
            "saleProgramID": productInfo.saleProgramID,
            "cartRequest": productInfo.cartRequest,
            "partnerInstallmentID": productInfo.partnerID,
            "isSpecialSaleProgram": productInfo.isSpecial,
            "isExistStoreChange": helper.IsNonEmptyArray(productInfo.storeRequests)
        };
        dispatch(start_get_promotion());
        let allPromotion = [];
        const isPromotionEngine = productInfo.isPromotionEngine && (saleScenarioTypeID == SALE) || (saleScenarioTypeID == AN_KHANG_PHARMACY);
        const hasConfig = !helper.checkConfigStoreNCPromotion(storeID);
        if (isPromotionEngine && hasConfig) {
            allPromotion = [
                apiBase(API_PROMOTION_NEW, METHOD.POST, body),
                apiBase(API_PROMOTION_DELIVERYTYPE_NEW, METHOD.POST, body),
                apiBase(API_PROMOTION_LOSTSALE_NEW, METHOD.POST, body)
            ];
        }
        else {
            allPromotion = [
                apiBase(API_PROMOTION, METHOD.POST, body),
                apiBase(API_PROMOTION_DELIVERYTYPE, METHOD.POST, body),
                apiBase(API_PROMOTION_LOSTSALE, METHOD.POST, body)
            ];
        }

        Promise.all(allPromotion).then(result => {
            console.log("getPromotion success", result);
            const { object: {
                giftPromotion,
                salePromotion
            } } = result[0];
            const dataPromotion = helper.handelGiftPromotion(giftPromotion);
            const dataSalePromotion = helper.handelSalePromotion(salePromotion);
            const { object: {
                giftPromotion: giftPromotionDelivery,
                salePromotion: salePromotionDelivery,
                isDisableTabDelivery
            } } = result[1];
            const dataPromotionDelivery = helper.handelGiftPromotion(giftPromotionDelivery);
            const dataSalePromotionDelivery = helper.handelSalePromotion(salePromotionDelivery);
            const { object: {
                giftPromotion: giftPromotionLostSale,
            } } = result[2];
            const dataPromotionLostSale = helper.handelGiftPromotion(giftPromotionLostSale);
            const allPromotion = [...dataPromotion, ...dataPromotionDelivery, ...dataPromotionLostSale];
            const allSalePromotion = [...dataSalePromotion, ...dataSalePromotionDelivery];
            const {
                allKeyPromotion,
                allGroupID,
                allPromotionID
            } = helper.getAllKeyPromotion(allPromotion, allSalePromotion);
            dataPromotion.forEach(groupPromotion => {
                const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                    groupPromotion,
                    allPromotionID,
                    dataPromotionDelivery,
                    dataSalePromotionDelivery,
                );
                groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
                groupPromotion.contentExclude = contentExclude;
            });
            dataSalePromotion.forEach(salePromotion => {
                const { promotionGroups } = salePromotion;
                promotionGroups.forEach(groupPromotion => {
                    const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                        groupPromotion,
                        allPromotionID,
                        dataPromotionDelivery,
                        dataSalePromotionDelivery,
                    );
                    groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
                    groupPromotion.contentExclude = contentExclude;
                });
            });
            dataPromotionDelivery.forEach(groupPromotion => {
                const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                    groupPromotion,
                    allPromotionID,
                    dataPromotion,
                    dataSalePromotion,
                    translate('detail.promotion'),
                    translate('detail.attached')
                );
                groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
                groupPromotion.contentExclude = contentExclude;
            });
            dataSalePromotionDelivery.forEach(salePromotion => {
                const { promotionGroups } = salePromotion;
                promotionGroups.forEach(groupPromotion => {
                    const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                        groupPromotion,
                        allPromotionID,
                        dataPromotion,
                        dataSalePromotion,
                        translate('detail.promotion'),
                        translate('detail.attached')
                    );
                    groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
                    groupPromotion.contentExclude = contentExclude;
                });
            });
            dataPromotionLostSale.forEach(groupPromotion => {
                const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                    groupPromotion,
                    allPromotionID,
                    dataPromotion,
                    dataSalePromotion,
                    "LostSale:"
                );
                groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
                groupPromotion.contentExclude = contentExclude;
            });
            const { applyProduct } = global;
            if (
                applyProduct.productID == productInfo.productID &&
                applyProduct.deliveryTypeID == productInfo.deliveryTypeID &&
                applyProduct.inventoryStatusID == productInfo.inventoryStatusID &&
                applyProduct.saleProgramID == productInfo.saleProgramID &&
                applyProduct.outputStoreID == productInfo.outputStoreID
            ) {
                dispatch(getAllKeyPromotion({
                    allKeyPromotion,
                    allGroupID,
                    dataPromotion,
                    dataSalePromotion,
                    dataPromotionDelivery,
                    dataSalePromotionDelivery,
                    dataPromotionLostSale,
                    isDisableTabDelivery
                }));
            }
        }).catch(error => {
            console.log("getPromotion error", error);
            if (
                applyProduct.productID == productInfo.productID &&
                applyProduct.deliveryTypeID == productInfo.deliveryTypeID &&
                applyProduct.inventoryStatusID == productInfo.inventoryStatusID
            ) {
                dispatch(stop_get_promotion([], [], [], [], [], false,
                    error.msgError, ERROR));
            }
        });
    };
};

export const getStoreNearest = function (data) {
    return (dispatch, getState) => {
        const { brandID, storeID, languageID, moduleID } = getState().userReducer;
        let body = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
            "productID": data.productID,
            "quantity": data.quantity,
            "storeID": data.storeID,
            "inventoryStatusID": data.inventoryStatusID,
            "distance": constants.DISTANCE[brandID] ?? 20,
            "getStockType": 1,
            "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID
        };
        dispatch(start_get_store_nearest());
        apiBase(API_GET_STORE_NEAREST, METHOD.POST, body).then((response) => {
            console.log("getStoreNearest success", response);
            const { object, errorReason } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_store_nearest(object));
            }
            else {
                const content = errorReason || translate('detail.no_store_found');
                dispatch(stop_get_store_nearest([], EMPTY, content));
            }
        }).catch(error => {
            console.log("getStoreNearest error", error);
            dispatch(stop_get_store_nearest([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getStoreShipping = function (data) {
    return (dispatch, getState) => {
        global.productRequest = {
            "productID": data.productID,
            "inventoryStatusID": data.inventoryStatusID,
        };
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
            "productID": data.productID,
            "quantity": data.quantity,
            "storeID": data.storeID,
            "inventoryStatusID": data.inventoryStatusID,
            "salePriceVAT": data.salePriceVAT,
            "getStockType": 1,
            "stockQuantity": data.stockQuantity,
            "saleProgramID": data.saleProgramID

        };
        dispatch(start_get_store_shipping());
        apiBase(API_GET_STORE_SHIPPING, METHOD.POST, body).then((response) => {
            console.log("getStoreShipping success", response);
            const { object, errorReason } = response;
            for (let i = object?.length - 1; i >= 0; i--) {
                if (object?.[i]?.isExtra) {
                    object?.splice(i, 1);
                    break;
                }
            }
            if (helper.IsNonEmptyArray(object)) {
                const { storeRequests, suggestTimes, storeID, messageError } = object[0];
                const isEmptyStore = helper.IsEmptyObject(storeRequests);
                const isEmptyTime = helper.IsEmptyObject(suggestTimes);
                if (messageError) {
                    dispatch(stop_get_store_shipping([], EMPTY, messageError));
                }
                else if (isEmptyStore) {
                    dispatch(stop_get_store_shipping([], EMPTY, translate('detail.no_store_delivery_found')));
                }
                else if (isEmptyTime) {
                    dispatch(stop_get_store_shipping([], EMPTY, translate('detail.expired_time')));
                }
                else if (storeID != data.storeID) {
                    reject(translate('detail.no_delivery_information_found'));
                }
                else {
                    const { productRequest } = global;
                    if (
                        productRequest.productID == data.productID &&
                        productRequest.inventoryStatusID == data.inventoryStatusID
                    ) {
                        dispatch(stop_get_store_shipping(object[0]));
                    }
                }
            }
            else {
                const content = errorReason || translate('detail.no_delivery_information_found');
                dispatch(stop_get_store_shipping([], EMPTY, content));
            }
        }).catch(error => {
            console.log("getStoreShipping error", error);
            dispatch(stop_get_store_shipping([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getEmployeeAtStore = function (storeID) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "storeID": storeID,
            "partnerInstallmentID": -1
        };
        dispatch(start_get_employee());
        apiBase(API_GET_EMPLOYEE_AT_STORE, METHOD.POST, body).then((response) => {
            console.log("getEmployeeAtStore success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_employee(object));
            }
            else {
                dispatch(stop_get_employee([], EMPTY, `${translate('detail.no_check_information_found')} ${storeID}`));
            }
        }).catch(error => {
            console.log("getEmployeeAtStore error", error);
            dispatch(stop_get_employee([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const checkCustomerPhone = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "promotionID": data.promotionID,
                "customerPhone": data.customerPhone,
                "productID": data.productID,
                "storeID": data.storeID,
                "saleOrderID": data.saleOrderID,
                "quantity": 1
            };
            apiBase(API_CHECK_CUSTOMER_PHONE_PROMOTION, METHOD.POST, body).then((response) => {
                console.log("checkCustomerPhone success", response);
                const { errorReason } = response;
                if (helper.IsEmptyString(errorReason)) {
                    resolve(true);
                } else {
                    reject(translate('detail.no_verified_information'));
                }
            }).catch(error => {
                console.log("checkCustomerPhone error", error);
                reject(error.msgError);
            });
        });
    };
};

export const searchPromotionProducts = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const { storeID, brandID, provinceID } = getState().userReducer;
            const { saleScenarioTypeID } = getState().specialSaleProgramReducer;
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "productID": data.productID,
                "imei": data.imei,
                "promotionListGroupID": data.promotionListGroupID,
                "storeID": data.storeID,
                "outputStoreID": data.outputStoreID,
                "barcode": data.barcode,
                "price": data.price,
                "VAT": data.VAT,
                "VATPercent": data.VATPercent,
                "isFixed": data.isFixed,
                "deliveryTypeID": data.deliveryTypeID,
                "promotionListGroupIDForSaleProduct": data.promotionListGroupIDForSalePromotion,
                "cartRequest": data.cartRequest,
                "promotionId": data.promotionID
            };
            let url = API_CHECK_BARCODE_PROMOTION_NEW;
            const isPromotionId = data.promotionID > 0;
            const isPromotionEngine = data.isPromotionEngine && (saleScenarioTypeID == SALE) || (saleScenarioTypeID == AN_KHANG_PHARMACY);
            const hasConfig = !helper.checkConfigStoreNCPromotion(storeID);
            if (isPromotionId && isPromotionEngine && hasConfig) {
                url = API_CHECK_BARCODE_PROMOTION;
            }
            apiBase(url, METHOD.POST, body).then((response) => {
                console.log("searchPromotionProducts success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve({
                        isEmpty: !EMPTY,
                        description: "",
                        products: object
                    });
                }
                else {
                    resolve({
                        isEmpty: EMPTY,
                        description: translate('detail.no_product_information'),
                        products: []
                    });
                }
            }).catch(error => {
                reject(error.msgError);
                console.log("searchPromotionProducts error", error);
            });
        });
    };
};

export const getStoreOther = function (data) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "quantity": 1,
            "productID": data.productID,
            "inventoryStatusID": data.inventoryStatusID,
            "provinceID": data.provinceID,
            "districtID": data.districtID,
            "deliveryAddress": data.deliveryAddress,
            "getStockType": 2
        };
        dispatch(start_get_store_other());
        apiBase(API_GET_STORE_BY_LOCATION, METHOD.POST, body).then((response) => {
            console.log("getStoreOther success", response);
            const { object, errorReason } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_store_other(object));
            }
            else {
                const content = errorReason || translate('detail.no_store_found');
                dispatch(stop_get_store_other([], EMPTY, content));
            }
        }).catch(error => {
            console.log("getStoreOther error", error);
            dispatch(stop_get_store_other([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getStoreOtherShipping = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "productID": data.productID,
                "quantity": data.quantity,
                "storeID": data.storeID,
                "inventoryStatusID": data.inventoryStatusID,
                "salePriceVAT": data.salePriceVAT,
                "getStockType": 2,
                "stockQuantity": data.stockQuantity
            };
            apiBase(API_GET_STORE_SHIPPING, METHOD.POST, body).then((response) => {
                console.log("getStoreOtherShipping success", response);
                const { object } = response;
                for (let i = object?.length - 1; i >= 0; i--) {
                    if (object?.[i]?.isExtra) {
                        object?.splice(i, 1);
                        break;
                    }
                }
                if (helper.IsNonEmptyArray(object)) {
                    const { storeRequests, suggestTimes, storeID, messageError } = object[0];
                    const isEmptyStore = helper.IsEmptyObject(storeRequests);
                    const isEmptyTime = helper.IsEmptyObject(suggestTimes);
                    if (messageError) {
                        reject(messageError);
                    }
                    else if (isEmptyStore) {
                        reject(translate('detail.no_store_delivery_found'));
                    }
                    else if (isEmptyTime) {
                        reject(translate('detail.expired_time'));
                    }
                    else if (storeID != data.storeID) {
                        reject(translate('detail.no_delivery_information_found'));
                    }
                    else {
                        resolve(object[0]);
                    }
                }
                else {
                    reject(translate('detail.no_delivery_information_found'));
                }
            }).catch(error => {
                console.log("getStoreOtherShipping error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getDataAtHome = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "imei": data.imei,
                "productID": data.productID,
                "quantity": data.quantity,
                "storeID": 0,
                "inventoryStatusID": data.inventoryStatusID,
                "provinceID": data.provinceID,
                "districtID": data.districtID,
                "wardID": data.wardID,
                "deliveryAddress": data.deliveryAddress,
                "saleProgramID": data.saleProgramID,
                "salePriceVAT": data.salePriceVAT,
                "getStockType": 3,
                "stockQuantity": data.stockQuantity,
                "extensionProperty": data.extensionProperty,
                "coordinates": data.coordinates
            };
            dispatch(start_get_data_at_home());
            apiBase(API_GET_STORE_SHIPPING, METHOD.POST, body).then((response) => {
                console.log("getDataAtHome success", response);
                const { object, errorReason } = response;
                let storeDelivery = {};
                if (helper.IsNonEmptyArray(object)) {
                    for (let i = object.length - 1; i >= 0; i--) {
                        if (object[i].isExtra) {
                            storeDelivery = object[i];
                            object.splice(i, 1);
                            break;
                        }
                    }
                    dispatch(stop_get_data_at_home(object));
                }
                else {
                    const content = errorReason || translate('detail.no_store_found');
                    dispatch(stop_get_data_at_home([], EMPTY, content));
                }
                resolve({
                    isBackupCRM: false,
                    store: object,
                    storeDelivery: storeDelivery
                });
            }).catch(error => {
                console.log("getDataAtHome error", error);
                const isBackupCRM = (error.errorType == 3);
                dispatch(stop_get_data_at_home([], !EMPTY, error.msgError, ERROR));
                resolve({
                    isBackupCRM,
                    store: [],
                    storeDelivery: {}
                });
            });
        });
    };
};

export const getStoreInfoShipping = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "imei": data.imei,
                "productID": data.productID,
                "quantity": data.quantity,
                "storeID": data.storeID,
                "inventoryStatusID": data.inventoryStatusID,
                "salePriceVAT": data.salePriceVAT,
                "getStockType": 3,
                "stockQuantity": data.stockQuantity,
                "storeID": data.storeID,
                "distance": data.distance,
                "districtID": data.districtID,
                "wardID": data.wardID,
                "deliveryAddress": data.deliveryAddress,
                "saleProgramID": data.saleProgramID,
                "extensionProperty": data.extensionProperty
            };
            apiBase(API_GET_SUGGEST_TIMES, METHOD.POST, body, { setTimeOut: 10000 }).then((response) => {
                console.log("getStoreInfoShipping success", response);
                const { object } = response;
                let storeDelivery = {};
                for (let i = object?.length - 1; i >= 0; i--) {
                    if (object?.[i]?.isExtra) {
                        storeDelivery = object[i];
                        object?.splice(i, 1);
                        break;
                    }
                }
                if (helper.IsNonEmptyArray(object)) {
                    const { suggestTimes, storeID } = object[0];
                    const isEmptyTime = helper.IsEmptyObject(suggestTimes);
                    if (isEmptyTime) {
                        reject(translate('detail.expired_time'));
                    }
                    else if (storeID != data.storeID) {
                        reject(translate('detail.no_delivery_information_found'));
                    }
                    else {
                        resolve({
                            storeInfo: object[0],
                            storeDelivery: storeDelivery
                        });
                    }
                }
                else {
                    reject(translate('detail.no_delivery_information_found'));
                }
            }).catch(error => {
                console.log("getStoreInfoShipping error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getSuggestTimeByDate = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "imei": data.imei,
                "productID": data.productID,
                "quantity": data.quantity,
                "storeID": data.storeID,
                "inventoryStatusID": data.inventoryStatusID,
                "salePriceVAT": data.salePriceVAT,
                "getStockType": 3,
                "stockQuantity": data.stockQuantity,
                "storeID": data.storeID,
                "distance": data.distance,
                "provinceID": data.provinceID,
                "districtID": data.districtID,
                "wardID": data.wardID,
                "deliveryAddress": data.deliveryAddress,
                "saleProgramID": data.saleProgramID,
                "dateFrom": data.deliveryTime,
                "dateTo": null,
                "deliveryTypeID": data.deliveryTypeID,
                "deliveryVehicles": data.deliveryVehicles,
                "isPOProduct": data.isCreatedPO,
                "extensionProperty": data.extensionProperty
            };
            apiBase(API_GET_TIMES_BY_DATE, METHOD.POST, body, { setTimeOut: 10000 }).then((response) => {
                console.log("getSuggestTimeByDate success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    const { suggestTimes } = object[0];
                    if (helper.IsNonEmptyArray(suggestTimes)) {
                        resolve(suggestTimes);
                    }
                    else {
                        reject(translate("editSaleOrder.no_delivery_information"));
                    }
                }
                else {
                    reject(translate("editSaleOrder.no_delivery_information"));
                }
            }).catch(error => {
                console.log("getSuggestTimeByDate error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getPartnerInstallment = function (storeID) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "storeID": storeID,
            "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
        };
        dispatch(start_get_partner_installment());
        apiBase(API_GET_INSTALLMENT, METHOD.POST, body).then((response) => {
            console.log("getPartnerInstallment success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_partner_installment(object));
            }
            else {
                dispatch(stop_get_partner_installment([], EMPTY, translate('detail.no_instalment_partner_information')));
            }
        }).catch(error => {
            console.log("getPartnerInstallment error", error);
            dispatch(stop_get_partner_installment([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getProgramInstallment = function (data) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "storeID": data.storeID,
            "partnerInstallmentID": data.partnerInstallmentID,
            "productID": data.productID,
            "salePriceVAT": data.salePriceVAT,
            "inventoryStatusID": data.inventoryStatusID,
            "promotionListGroupID": data.promotionListGroupID
        };
        dispatch(start_get_program_installment());
        apiBase(API_GET_PROGRAM_INSTALLMENT, METHOD.POST, body).then((response) => {
            console.log("getProgramInstallment success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_program_installment(object));
            }
            else {
                dispatch(stop_get_program_installment([], EMPTY, translate('detail.no_instalment_information')));
            }
        }).catch(error => {
            console.log("getProgramInstallment error", error);
            dispatch(stop_get_program_installment([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getExpandSalePromotion = function (productInfo) {
    return (dispatch, getState) => {
        const { storeID, brandID, provinceID } = getState().userReducer;
        const { saleScenarioTypeID } = getState().specialSaleProgramReducer;
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "imei": "",
                "isGetPromotionForSaleProduct": true,
                "productID": productInfo.productID,
                "inventoryStatusID": productInfo.inventoryStatusID,
                "price": productInfo.price,
                "VAT": productInfo.VAT,
                "VATPercent": productInfo.VATPercent,
                "storeID": productInfo.storeID,
                "appliedQuantity": productInfo.appliedQuantity,
                "outputStoreID": productInfo.outputStoreID,
                "deliveryTypeID": productInfo.deliveryTypeID,
                "storeRequests": productInfo.storeRequests,
                "saleProgramID": productInfo.saleProgramID,
                "cartRequest": productInfo.cartRequest,
                "promotionListGroupIDForSaleProduct": productInfo.promotionGroupID,
                "IsApplyTotalPromotion": productInfo.isApplyTotalPromotion,
                "partnerInstallmentID": productInfo.partnerID,
                "isSpecialSaleProgram": productInfo.isSpecial,
                "promotionListIDForSaleProduct": productInfo.promotionListId
            };
            let url = API_PROMOTION_NEW;
            const isPromotionEngine = productInfo.isPromotionEngine && ((saleScenarioTypeID == SALE) || (saleScenarioTypeID == AN_KHANG_PHARMACY));
            const hasConfig = !helper.checkConfigStoreNCPromotion(storeID);
            if (isPromotionEngine && hasConfig) {
                url = API_GET_EXPAND_PROMOTION;
            }
            apiBase(url, METHOD.POST, body).then((response) => {
                console.log("getExpandSalePromotion success", response);
                const { object: {
                    giftPromotion,
                    giftPromotionByDelivery,
                    mainProductObject
                } } = response;
                const expPromotion = helper.handelGiftPromotion(giftPromotion).filter(ele => !ele.isRandomDiscount);
                const expPromotionDelivery = helper.handelGiftPromotion(giftPromotionByDelivery).filter(ele => !ele.isRandomDiscount);
                const isApplyTotalPromotion = mainProductObject.IsApplyTotalPromotion;
                resolve({ expPromotion, expPromotionDelivery, isApplyTotalPromotion });
            }).catch(error => {
                console.log("getExpandSalePromotion error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getFifoInfo = function (productID) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "productID": productID
        };
        dispatch(start_get_fifo());
        apiBase(API_GET_FIFO_INFO, METHOD.POST, body).then((response) => {
            console.log("getPartnerInstallment success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_fifo(object));
            }
            else {
                dispatch(stop_get_fifo([], EMPTY, translate('detail.no_fifo_information')));
            }
        }).catch(error => {
            console.log("getPartnerInstallment error", error);
            dispatch(stop_get_fifo([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getWarrantyPolicyInfo = (productIDRef) => {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "ProductIdRef": productIDRef
        };
        dispatch(start_get_warranty());
        apiBase(API_GET_WARRANTY_PRODUCT, METHOD.POST, body).then((response) => {
            console.log("getWarrantyPolicyInfo success", response);
            const { object } = response;
            if (helper.IsNonEmptyString(object)) {
                dispatch(stop_get_warranty(JSON.parse(object)));
            }
            else {
                dispatch(stop_get_warranty("", EMPTY, "Không có chính sách bảo hành"));
            }
        }).catch(error => {
            console.log("getWarrantyPolicyInfo error", error);
            dispatch(stop_get_warranty("", !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getConfigInfo = function (productIDRef) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "productIdRef": productIDRef
        };
        dispatch(start_get_config());
        apiBase(API_GET_CONFIG_PRODUCT, METHOD.POST, body).then((response) => {
            console.log("getConfigInfo success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_config(object));
            }
            else {
                dispatch(stop_get_config([], EMPTY, translate('detail.no_detail_information')));
            }
        }).catch(error => {
            console.log("getConfigInfo error", error);
            dispatch(stop_get_config([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getFeatureInfo = function (productIDRef) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "productIdRef": productIDRef
        };
        dispatch(start_get_feature());
        apiBase(API_GET_FEATURE_PRODUCT, METHOD.POST, body).then((response) => {
            console.log("getFeatureInfo success", response);
            const { object } = response;
            if (helper.IsNonEmptyString(object)) {
                dispatch(stop_get_feature(JSON.parse(object)));
            }
            else {
                dispatch(stop_get_feature("", EMPTY, translate('detail.no_feature_information')));
            }
        }).catch(error => {
            console.log("getFeatureInfo error", error);
            dispatch(stop_get_feature("", !EMPTY, error.msgError, ERROR));
        });
    };
};

export const resetDetailState = function () {
    return (dispatch, getState) => {
        dispatch(reset_detail_state());
    };
};

export const getLockProductInfo = function (productInfo) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "storeID": productInfo.storeID,
            "imei": productInfo.imei,
            "productID": productInfo.productID,
            "productIDRef": productInfo.productIDRef,
            "inventoryStatusID": productInfo.inventoryStatusID,
            "saleProgramID": 0,
            "isInstalment": false,
        };
        dispatch(start_get_lock_info());
        apiBase(API_GET_LOCK_PRODUCT_INFO, METHOD.POST, body).then((response) => {
            console.log("getLockProductInfo success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_lock_info(object));
            }
            else {
                dispatch(stop_get_lock_info([], EMPTY, translate('detail.no_lock_information')));
            }
        }).catch(error => {
            console.log("getLockProductInfo error", error);
            dispatch(stop_get_lock_info([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getSimPackage = function (data) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "productID": data.productID,
            "imei": data.imei
        };
        dispatch(start_get_sim_package());
        apiBase(API_GET_SIM_PACKAGE, METHOD.POST, body).then((response) => {
            console.log("getSimPackage success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(updateSimPackage(data, object));
            }
            else {
                dispatch(stop_get_sim_package([], EMPTY, translate('detail.no_package_information')));
            }
        }).catch(error => {
            console.log("getSimPackage error", error);
            dispatch(stop_get_sim_package([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getSalePriceProduct = function (productInfo) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "imei": productInfo.imei,
                "productID": productInfo.productID,
                "productIDRef": productInfo.productIDRef,
                "inventoryStatusID": productInfo.inventoryStatusID,
                "storeID": productInfo.storeID,
                "saleProgramID": productInfo.saleProgramID,
                "isInstalment": productInfo.isInstalment,
                "isImeiSim": false
            };
            apiBase(API_DETAILS_PRODUCT, METHOD.POST, body).then((response) => {
                console.log("getInfoProductSearch success", response);
                const { object } = response;
                if (!helper.IsNonEmptyArray(object)) {
                    return resolve(productInfo);
                }

                const matchedProduct = object.find(({ inventoryStatusID, productID }) =>
                    productInfo.productID == productID && productInfo.inventoryStatusID == inventoryStatusID
                );

                resolve(matchedProduct ? matchedProduct : productInfo);
            }).catch(error => {
                resolve(productInfo);
                console.log("getInfoProductSearch error", error);
            });
        });
    };
};

export const checkImeiAllowSale = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "mainProduct": data.mainProduct,
                "shoppingCartRequest": data.cartRequest,
            };
            apiBase(API_CHECK_IMEI_SALE, METHOD.POST, body).then((response) => {
                console.log("checkImeiAllowSale success", response);
                resolve(true);
            }).catch(error => {
                console.log("checkImeiAllowSale error", error);
                reject(error.msgError);
            });
        });
    };
};

export const checkProductPre = function (body) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            dispatch(start_check_pre());
            apiBase(API_CHECK_PRODUCT_PRE, METHOD.POST, body).then((response) => {
                console.log("checkProductPre success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(object.IsEnableGetPromotion);
                    dispatch(stop_check_pre(object.IsEnableGetPromotion));

                }
                else {
                    const contents = "Lỗi kiểm tra thông tin gọi khuyến mãi.";
                    reject(contents);
                }
            }).catch(error => {
                console.log("checkProductPre error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getConsultantInfo = function (productInfo) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "productIDRef": productInfo.productIDRef,
                "productID": productInfo.productID,
            };
            apiBase(API_INSTALLATION_REPAIR, METHOD.POST, body).then((response) => {
                console.log("getConsultantInfo success", response);
                const { object } = response;
                if (helper.IsEmptyObject(object)) {
                    resolve({});
                }
                else {
                    resolve(object);
                }
            }).catch(error => {
                console.log("getConsultantInfo error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getPreOrderLockProductInfo = productInfo => {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "storeID": productInfo.storeID,
            "productID": productInfo.productID
        };
        dispatch(start_get_pre_order_lock_info());
        apiBase(API_GET_PRE_ORDER_LOCK_PRODUCT_INFO, METHOD.POST, body).then((response) => {
            console.log("getPreOrderLockProductInfo success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_pre_order_lock_info(object));
            }
            else {
                dispatch(stop_get_pre_order_lock_info([], EMPTY, translate("detail.no_infomation_pre")));
            }
        }).catch(error => {
            console.log("getPreOrderLockProductInfo error", error);
            dispatch(stop_get_pre_order_lock_info([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getProductLoyaltyPoint = function (params) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = params;
            apiBase(API_PRODUCT_LOYALTY_POINT, METHOD.POST, body).then((response) => {
                console.log("getProductLoyaltyPoint success", response);
                const { responseData } = response;
                if (helper.IsEmptyObject(responseData)) {
                    resolve({});
                }
                else {
                    resolve(responseData);
                }
            }).catch(error => {
                console.log("getProductLoyaltyPoint error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getSuggestProduct = function (body) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            apiBase(API_GET_SUGGEST_PRODUCT, METHOD.POST, body).then((response) => {
                console.log("getSuggestProduct success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                }
                else {
                    reject("Không có sản phẩm bán kèm");
                }
            }).catch(error => {
                console.log("getSuggestProduct error", error);
                reject("Không có sản phẩm bán kèm");
            });
        });
    };
};

const updateSimPackage = (data, dataPackage) => {
    return (dispatch, getState) => {
        const promiseAll = dataPackage.map(ele => {
            return dispatch(getSimPrice({
                "PackagesTypeID": ele.packagesTypeId,
                "SalePrice": data.salePrice,
                "VAT": data.vat,
                "VATPercent": data.vatPercent,
                "Imei": data.imei,
            }));
        });
        Promise.all(promiseAll).then(result => {
            const newDataPackage = dataPackage.map((ele, index) => {
                ele.salePriceVAT = result[index];
                return ele;
            });
            dispatch(stop_get_sim_package(newDataPackage));
        }).catch(msgError => {
            dispatch(stop_get_sim_package([], !EMPTY, msgError, ERROR));
        });
    };
};

export const getSimPrice = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "PackagesTypeID": data.PackagesTypeID,
                "SalePrice": data.SalePrice,
                "VAT": data.VAT,
                "VATPercent": data.VATPercent,
                "IMEI": data.Imei,
            };
            apiBase(API_GET_SIM_PRICE, METHOD.POST, body).then((response) => {
                console.log("getSimPrice success", response);
                const { object } = response;
                if (helper.hasProperty(object, 'salePriceVAT')) {
                    resolve(object.salePriceVAT);
                }
                else {
                    reject(translate('detail.no_sim_price_information_found'));
                }
            }).catch(error => {
                console.log("getSimPrice error", error);
                reject(error.msgError);
            });
        });
    };
};

export const validateIMEIWithPartner = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_VALIDATE_IMEI_WITH_PARTNER, METHOD.POST, body).then((response) => {
            console.log("validateIMEIWithPartner success", response);
            if (response.object?.length > 0) {
                resolve(response.object)
            }
            else {
                reject("IMEI không hợp lệ. Vui lòng nhập IMEI khác!")
            }
        }).catch((error) => {
            console.log("validateIMEIWithPartner error", error);
            reject(error.msgError)
        })
    })
}

const reset_detail_state = () => {
    return ({
        type: RESET_DETAIL_STATE,
    });
};

const setDetailProduct = function (dataProduct, productInfo, bodyThreePrice) {
    const { productID, isCallThreePrice } = productInfo;
    return function (dispatch, getState) {
        let product = dataProduct[0];
        const { subErpProductInfos, purposesApplyonPos } = product;
        if (purposesApplyonPos) {
            product = subErpProductInfos[0];
            dataProduct.forEach(({ subErpProductInfos }) => {
                if (helper.IsNonEmptyArray(subErpProductInfos)) {
                    const item = subErpProductInfos.find(ele => productID == ele.productID);
                    if (item) {
                        product = item;
                    }
                }
            });
        }
        else {
            const item = dataProduct.find(ele => productID == ele.productID);
            if (item) {
                product = item;
            }
        }
        const {
            extensionProperty
        } = product;
        const packageIngredients = extensionProperty?.ProductServicePackageBO?.ServiceComponents
        if (helper.IsNonEmptyArray(packageIngredients)) {
            const newProductPackage = { ...product }
            const ImeiIngredient = packageIngredients.find((_item) => _item.GroupCode?.includes(SIM_PACKAGE_COMPONENTS.PHONE_NUMBER))
            if (!helper.IsEmptyObject(ImeiIngredient?.ExtensionProperty?.ProductInstockIMEI)) {
                const { ExtensionProperty: { ProductInstockIMEI } } = ImeiIngredient
                const lowerCase = (str) => str[0].toLowerCase() + str.slice(1);
                const lowercaseProductInstockIMEI = Object.fromEntries(
                    Object.entries(ProductInstockIMEI).map(([k, v]) => {
                        if (!(`VATIn,VATPercent,VAT,ExtensionProperty,IMEI`.includes(k))) {
                            return [lowerCase(k), v]
                        }
                        return [k, v]
                    })
                )
                const {
                    VATPercent,
                    IMEI,
                    VAT
                } = lowercaseProductInstockIMEI
                const imeiInfo = { ...lowercaseProductInstockIMEI, vat: VAT, VATPercent: VATPercent, imei: IMEI }
                product = { ...product, ...imeiInfo }
                product.productPackage = newProductPackage
            }

        }
        ////
        if (productID != product.productID && isCallThreePrice) {
            bodyThreePrice.productProlicyBO.productID = product.productID;
            dispatch(getPackageService(bodyThreePrice)).then(() => {
                dispatch(set_detail_product(product));
                dispatch(stop_get_data_product(dataProduct));
                dispatch(stop_get_data_inventory_tab(dataProduct));
            }).catch((error) => {
                dispatch(stop_get_data_product([], !EMPTY, error.msgError, ERROR));
                dispatch(stop_get_data_inventory_tab([], !EMPTY, error.msgError, ERROR));
                dispatch(set_detail_product({}));
            });
        }
        else {
            dispatch(set_detail_product(product));
            dispatch(stop_get_data_product(dataProduct));
            dispatch(stop_get_data_inventory_tab(dataProduct));
        }
    };
};

const getAllKeyPromotion = function ({
    allKeyPromotion,
    allGroupID,
    dataPromotion = [],
    dataSalePromotion = [],
    dataPromotionDelivery = [],
    dataSalePromotionDelivery = [],
    dataPromotionLostSale = [],
    isDisableTabDelivery
}) {
    return function (dispatch, getState) {
        const allPromotion = [
            ...dataPromotion,
            ...dataPromotionDelivery,
            ...dataPromotionLostSale
        ];
        const allSalePromotion = [
            ...dataSalePromotion,
            ...dataSalePromotionDelivery
        ];
        const defaultKeyPromotion = helper.getDefaultKeyPromotion(allPromotion);
        const allExcludeDisabled = helper.getAllExcludeDisabled(allPromotion, allSalePromotion);
        dispatch(get_all_key_promotion(allKeyPromotion, allGroupID, defaultKeyPromotion, allExcludeDisabled));
        dispatch(stop_get_promotion(
            dataPromotion,
            dataSalePromotion,
            dataPromotionDelivery,
            dataSalePromotionDelivery,
            dataPromotionLostSale,
            isDisableTabDelivery
        ));
    };
};

export const getPromotionForView = function (productInfo) {
    return (dispatch, getState) => {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "imei": productInfo.imei,
            "productID": productInfo.productID,
            "inventoryStatusID": productInfo.inventoryStatusID,
            "price": productInfo.price,
            "VAT": productInfo.VAT,
            "VATPercent": productInfo.VATPercent,
            "storeID": productInfo.storeID,
            "appliedQuantity": productInfo.appliedQuantity,
            "outputStoreID": productInfo.outputStoreID,
            "deliveryTypeID": productInfo.deliveryTypeID,
            "storeRequests": productInfo.storeRequests,
            "saleProgramID": productInfo.saleProgramID,
            "cartRequest": productInfo.cartRequest,
            "promotionDate": productInfo.promotionDate,
        };
        dispatch(start_get_promotion_for_view());
        apiBase(API_PROMOTION_FOR_VIEW, METHOD.POST, body).then(result => {
            console.log("getPromotionForView success", result);
            const { object: {
                giftPromotion,
                salePromotion
            } } = result;
            const dataPromotion = helper.handelGiftPromotion(giftPromotion);
            const dataSalePromotion = helper.handelSalePromotion(salePromotion);
            const allPromotion = [...dataPromotion];
            const allSalePromotion = [...dataSalePromotion];
            const {
                allKeyPromotion,
                allGroupID,
                allPromotionID
            } = helper.getAllKeyPromotion(allPromotion, allSalePromotion);
            dataPromotion.forEach(groupPromotion => {
                const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                    groupPromotion,
                    allPromotionID
                );
                groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
                groupPromotion.contentExclude = contentExclude;
            });
            dataSalePromotion.forEach(salePromotion => {
                const { promotionGroups } = salePromotion;
                promotionGroups.forEach(groupPromotion => {
                    const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                        groupPromotion,
                        allPromotionID
                    );
                    groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
                    groupPromotion.contentExclude = contentExclude;
                });
            });
            dispatch(getAllKeyPromotion({
                allKeyPromotion,
                allGroupID,
                dataPromotion,
                dataSalePromotion,
                dataPromotionDelivery: [],
                dataSalePromotionDelivery: [],
                dataPromotionLostSale: []
            }));
        }).catch(error => {
            console.log("getPromotion error", error);
            dispatch(stop_get_promotion([], [], [], [],
                error.msgError, ERROR));
        });
    };
};

export const getDiscountPriceVAT = (request) => {
    return (dispatch, getState) => {
        const body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            ...request,
            // saleScenarioTypeID
            // "productID"
            // "imei"
            // "inventoryStatusID"
            // "pointLoyalty"
            // "outputTypeID"
            // "appliedQuantity"
            // "outputStoreID"
            // "deliveryTypeID"
            // "saleProgramID"
            // "packagesTypeId"
        };
        dispatch(start_get_discount_price_vat());
        apiBase(API_GET_DISCOUNT_VALUE, METHOD.POST, body).then((response) => {
            console.log("getDiscountPriceVAT success", response);
            const { object } = response;
            if (!helper.IsEmptyObject(object)) {
                dispatch(stop_get_discount_price_vat({
                    discountPriceVAT: object.DiscountValue,
                    productID: request.productID
                }));
            }
            // else {
            //     dispatch(stop_get_discount_price_vat(currentValue, "Không lấy được giảm giá. Vui lòng thử lại.", ERROR));
            // }
        }).catch(error => {
            console.log("getDiscountPriceVAT error", error);
            const currentValue = getState().detailReducer.dataDiscount;
            const newValue = request.productID != currentValue.productID
                ? { discountPriceVAT: 0, productID: request.productID }
                : currentValue;
            dispatch(stop_get_discount_price_vat(newValue, error.msgError, ERROR));
        });
    };
};

export const getOutputStoreList = function (keyword = "") {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "keyword": keyword,
                "storeIDList": ""
            };
            apiBase(API_GET_OUTPUT_STORE, METHOD.POST, body).then((response) => {
                console.log("getOutputStoreList success: ", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                }
                else {
                    resolve([]);
                }
            }).catch(error => {
                console.log("getOutputStoreList error: ", error);
                reject(error);
            });
        });
    };
};

export const getProductServiceGroupType = function (data, salOrderDetailID = "") {
    return (dispatch, getState) => {
        dispatch(start_get_product_service_group_type(salOrderDetailID));
        let body = {
            "ProductID": data.productID,
            "InventorystatusID": data.inventoryStatusID,
            "SaleOrderTypeID": data.saleOrderTypeID,
            "SaleProgramID": data.saleProgramID,
            "DeliverytypeID": data.deliveryTypeID,
            "OutputStoreID": data.outputStoreID,
            "ServiceGroupTypeList": data.serviceGroupTypeList
        };
        apiBase(API_GET_SERVICE_GROUP_TYPE, METHOD.POST, body).then((response) => {
            console.log("getProductServiceGroupType success: ", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                const initialServiceGroup = object.map(serviceGroup => ({ ...serviceGroup, selectedService: {} }));
                dispatch(stop_get_product_service_group_type(initialServiceGroup));
            } else {
                const message = helper.IsNonEmptyArray(data.serviceGroupTypeList) ?
                    "*Sản phẩm không còn dịch vụ tham gia" :
                    "*Sản phẩm chưa được khai báo chương trình dịch vụ";
                dispatch(stop_get_product_service_group_type([], message));
            }
        }).catch(error => {
            console.log("getProductServiceGroupType error: ", error);
            dispatch(stop_get_product_service_group_type([], error.msgError));
        });
    };
};

export const getProductServiceProgram = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const productServiceGroupType = getState().detailReducer.productServiceGroupType;
            const groupTypeIndex = productServiceGroupType.findIndex(serviceGroup =>
                serviceGroup.ServiceGroupTypeID == data.ServiceVoucherTypeCacheBO.ServiceGroupTypeID
            );
            const providerIndex = productServiceGroupType[groupTypeIndex].cus_ServiceVoucherTypeCacheBOList.findIndex(provider =>
                provider.ServiceProviderID == data.ServiceVoucherTypeCacheBO.ServiceProviderID
            );
            if (helper.IsNonEmptyArray(productServiceGroupType[groupTypeIndex].cus_ServiceVoucherTypeCacheBOList[providerIndex].cus_InsuranceProgramBOList)) {
                dispatch(stop_get_product_service_program(productServiceGroupType));
                resolve(productServiceGroupType[groupTypeIndex].cus_ServiceVoucherTypeCacheBOList[providerIndex].cus_InsuranceProgramBOList);
            } else {
                dispatch(start_get_product_service_program());
                let body = {
                    "ProductID": data.productID,
                    "InventorystatusID": data.inventoryStatusID,
                    "SaleOrderTypeID": 100,
                    "RetailPriceVAT": data.retailPriceVAT,
                    // "RetailPriceVAT": data.retailPriceVAT,
                    "ServiceVoucherTypeCacheBO": data.ServiceVoucherTypeCacheBO
                };
                apiBase(API_GET_SERVICE_PROGRAM, METHOD.POST, body).then((response) => {
                    console.log("getProductServiceProgram success: ", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        const newCusServiceVoucherTypeCacheBOList = productServiceGroupType[groupTypeIndex].cus_ServiceVoucherTypeCacheBOList;
                        newCusServiceVoucherTypeCacheBOList[providerIndex] = object;

                        const newServiceGroup = {
                            ...productServiceGroupType[groupTypeIndex],
                            cus_ServiceVoucherTypeCacheBOList: newCusServiceVoucherTypeCacheBOList
                        };
                        let newProductServiceGroupType = productServiceGroupType;
                        newProductServiceGroupType[groupTypeIndex] = newServiceGroup;
                        dispatch(stop_get_product_service_program(newProductServiceGroupType));
                        resolve(object.cus_InsuranceProgramBOList);
                    }
                    else {
                        dispatch(stop_get_product_service_program(productServiceGroupType, EMPTY, "Không có chương trình dịch vụ"));
                    }
                }).catch(error => {
                    console.log("getProductServiceProgram error: ", error);
                    dispatch(stop_get_product_service_program(productServiceGroupType, false, error.msgError, ERROR));
                });
            }
        });
    };
};

export const selectProductService = function (serviceInformation) {
    return (dispatch, getState) => {
        const productServiceGroupType = getState().detailReducer.productServiceGroupType;
        const newProductServiceGroupType = productServiceGroupType.map((service) => {
            if (service.ServiceGroupTypeID == serviceInformation.ServiceGroupTypeID) {
                return { ...service, selectedService: serviceInformation };
            } else {
                return service;
            }
        });
        dispatch(stop_get_product_service_group_type(newProductServiceGroupType));
    };
};

export const removeProductService = function (serviceInformation) {
    return (dispatch, getState) => {
        const productServiceGroupType = getState().detailReducer.productServiceGroupType;
        const newProductServiceGroupType = productServiceGroupType.map((service) => {
            if (service.ServiceGroupTypeID == serviceInformation.ServiceGroupTypeID) {
                return { ...service, selectedService: {} };
            } else {
                return service;
            }
        });
        dispatch(stop_get_product_service_group_type(newProductServiceGroupType));
    };
};

export const getProductServiceProgramFee = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            dispatch(start_get_product_service_info());
            let body = {
                "ProductID": data.productID,
                "RetailPriceVat": data.retailPriceVAT,
                "InventorystatusID": data.inventoryStatusID,
                "SaleOrderTypeID": 100,
                "ParnertranstypeID": data.partnerTransTypeID,
                "InsuranceProgramBO": data.insuranceProgramB0
            };
            apiBase(API_GET_SERVICE_PROGRAM_FEE, METHOD.POST, body).then((response) => {
                console.log("getProductServiceProgramFee success: ", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object) && helper.hasProperty(object, "cus_InsuranceInsProductBOList") && helper.IsNonEmptyArray(object.cus_InsuranceInsProductBOList)) {
                    resolve(object.cus_InsuranceInsProductBOList);
                    dispatch(stop_get_product_service_info(object));
                }
                else {
                    dispatch(stop_get_product_service_info({}, EMPTY, "Không có thông tin chương trình dịch vụ"));
                }
            }).catch(error => {
                console.log("getProductServiceProgramFee error: ", error);
                dispatch(stop_get_product_service_info({}, false, error.msgError, ERROR));
            });
        });
    };
};

export const clearProductServiceProgramFeeData = function () {
    return (dispatch, getState) => {
        dispatch(stop_get_product_service_info({}));
    };
};

export const updateStatusRandomDiscountPromotion =
    (appliedRandomDiscountList) => (_, getState) =>
        new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID:
                    getState().specialSaleProgramReducer.saleScenarioTypeID,
                randomDiscountApplyBOLst: appliedRandomDiscountList
            };

            apiBase(
                API_UPDATE_STATUS_RANDOM_DISCOUNT_PROMOTION,
                METHOD.POST,
                body
            )
                .then((response) => {
                    console.log(
                        'updateStatusRandomDiscountPromotion success: ',
                        response
                    );
                    resolve();
                })
                .catch((error) => {
                    console.log(
                        'updateStatusRandomDiscountPromotion error: ',
                        error
                    );
                    reject(error.msgError);
                });
        });

export const checkPhoneNumberApplyPromotion = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "shoppingCartRequest": data.shoppingCartRequest,
                "customerPhone": data.customerPhone,
                "promotionId": data.promotionId,
                "productId": data.productId
            };
            dispatch(start_check_phone_number_apply_promotion());
            apiBase(API_CHECK_PHONE_NUMBER_APPLY_PROMOTION, METHOD.POST, body).then((response) => {
                console.log("checkPhoneNumberApplyPromotion success: ", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response);
                    dispatch(stop_check_phone_number_apply_promotion(response.object, false, '', false));
                }
                else {
                    dispatch(stop_check_phone_number_apply_promotion({}, true, "Không có thông tin chương trình áp dụng vòng quay may mắn!", false));
                    resolve(response);
                }
            }).catch(error => {
                console.log("checkPhoneNumberApplyPromotion error: ", error);
                dispatch(stop_check_phone_number_apply_promotion({}, false, error.msgError, ERROR));
                reject(error.msgError);
            });
        });
    };
};

export const getPackageService = (body, isFirstCall) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            apiBase(API_GET_PACKAGE_SERVICE, METHOD.POST, body).then((response) => {
                const { productProlicyBO: { productID }, saleProgramID = 0 } = body;
                if (!helper.IsEmptyObject(response.object)) {
                    const packageService = response.object.PricePolicyProgramPolicyBOs ?? [];
                    const newPackageService = packageService.map((item) => {
                        return {
                            ...item,
                            packageStatus: uuidv4(),
                            pricePolicyProgramID: response.object.PricePolicyProgramID,
                            originTotalAmount: item.TotalAmount ?? 0,
                            originTotalAmountNOVAT: item.TotalAmountNOVAT ?? 0,
                            productID: productID,
                            IsSelected: item.PricePolicyTypeID == body.PricePolicyTypeID,
                            cus_GroupPricePolicyBO: response.object?.cus_GroupPricePolicyBO
                        };
                    });
                    if (!newPackageService.some(({ IsSelected }) => IsSelected)) {
                        newPackageService[0].IsSelected = true;
                    }
                    !isFirstCall && dispatch(set_package_services({ id: `${productID}_${saleProgramID}`, package: newPackageService }));
                    resolve(newPackageService);
                }
                else {
                    !isFirstCall && dispatch(set_package_services({ id: `${productID}_${saleProgramID}`, package: [] }));
                    resolve([]);
                }
            }).catch(error => {
                console.log("getPackageService error: ", error);
                body.isEdit ? reject(error.msgError) : reject(error);
            });
        });
    };

};

export const confirmPreorderEvent = (data) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                eventID: data.eventID,
                customerID: data.customerID,
                status: data.status,
                saleOrderID: data.saleOrderID,
                customerPhone: data.customerPhone
            };
            apiBase(API_CONFIRM_PREORDER_EVENT, METHOD.POST, body).then((response) => {
                console.log("confirmPreorderEvent success: ", response);
                resolve(response);
            }).catch(error => {
                console.log("confirmPreorderEvent error: ", error);
                reject(error);
            });
        });
    };
};

export const getInstallmentPackageSmartPos = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "firstCardNO": data.firstCardNO,
                "lastCardNO": data.lastCardNO,
                "amount": data.amount,
                "saleProgramID": data.saleProgramID,
                "partnerInstallmentID": data.partnerInstallmentID,
                "cardExpiryDate": data.cardExpiryDate,
                "productID": data.productID
            };
            apiBase(API_GET_INSTALLMENT_PACKAGE_SMART_POS, METHOD.POST, body).then(response => {
                console.log("getInstallmentPackageSmartPos success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Không tìm thấy chương trình trả góp từ đối tác");
                }
            }).catch(error => {
                console.log("getInstallmentPackageSmartPos error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getRewardInstallment = (PartnerInstallmentID) => {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            dispatch(start_get_reward_installment());
            let body = {
                "epPartnerInstallmentID": PartnerInstallmentID,
                "soOrderTypeID": 6,
                "epCreatedStoreID": getState().userReducer.storeID,
            };
            apiBase(API_GET_REWARD_INSTALLMENT, METHOD.POST, body).then((response) => {
                console.log("getRewardInstallment success: ", response);
                const { responseData } = response;
                if (!helper.IsEmptyObject(responseData)) {
                    resolve(responseData);
                    dispatch(stop_get_reward_installment(responseData));
                }
                else {
                    dispatch(stop_get_reward_installment({}, EMPTY, "Không tìm thấy điểm thưởng áp dụng cho chường trình trả góp"));
                }
            }).catch(error => {
                console.log("getRewardInstallment error: ", error);
                dispatch(stop_get_reward_installment({}, false, error.msgError, ERROR));
            });
        });
    };
};

export const getPartnerInstallmentUserCode = (PartnerInstallmentID) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID: null,
                appliedDate: null,
                partnerInstallmentID: PartnerInstallmentID
            };
            dispatch(start_get_partner_installment_user_code());
            apiBase(API_GET_PARTNER_INSTALLMENT_USER_CODE, METHOD.POST, body).then(response => {
                console.log("getPartnerInstallmentUserCode success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                    dispatch(stop_get_partner_installment_user_code(response.object, false, '', false));
                }
                else {
                    dispatch(stop_check_phone_number_apply_promotion({}, true, "Không tìm thấy chương trình trả góp từ đối tác!", false));
                    resolve(response);
                }
            }).catch(error => {
                console.log("getPartnerInstallmentUserCode error", error);
                dispatch(stop_get_partner_installment_user_code({}, false, error.msgError, ERROR));
                reject(error.msgError);
            });
        });
    };
};


export const getSIMServicePackage = (data) => (dispatch, getState) =>
    new Promise(async (resolve, reject) => {
        const body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "productID": data.productID,
            "imei": data.imei,
            "extensionProperty": data.extensionProperty
        };
        try {
            const response = await apiBase(API_GET_SIM_PACKAGE, METHOD.POST, body)
            const { object } = response;
            console.log("getSIMServicePackage success:", object)
            if (helper.IsNonEmptyArray(object)) {
                const promiseAll = await object.map(ele => {
                    const bodyPrice = {
                        "PackagesTypeID": ele.packagesTypeId,
                        "SalePrice": data.salePrice,
                        "VAT": data.vat,
                        "VATPercent": data.vatPercent
                    }
                    return apiBase(API_GET_SIM_PRICE, METHOD.POST, bodyPrice)
                })
                const responsePrice = await Promise.all(promiseAll)
                const newSimPackage = object.map((ele, index) => {
                    ele.salePriceVAT = responsePrice[index]?.object?.salePriceVAT
                    return ele
                })

                if (!helper.IsEmptyArray(newSimPackage)) {
                    resolve(newSimPackage);
                }
                else {
                    const Err = {
                        msgError: "Không lấy được thông tin gói cước."
                    }
                    reject(Err)
                }
            }
            else {
                const Err = {
                    msgError: "Không lấy được thông tin gói cước."
                }
                reject(Err)
            }

        } catch (error) {
            console.log("getSIMServicePackage error:", object)
            reject(error)
        }


    });
export const getDeliveryServicePackage =
    () => (_, getState) =>
        new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleScenarioTypeID:
                    getState().specialSaleProgramReducer.saleScenarioTypeID,
                servicePackageCodes: [
                    "deliveryservice"
                ],
                productId: null
            };

            apiBase(
                API_GET_DELIVERY_SERVICE_PACKAGE,
                METHOD.POST,
                body
            )
                .then((response) => {
                    const { object } = response
                    console.log(
                        'getDeliveryServicePackage success: ',
                        response
                    );
                    if (helper.IsNonEmptyArray(object)) {
                        resolve(object);
                    }
                    else {
                        resolve([]);
                    }
                })
                .catch((error) => {
                    console.log(
                        'getDeliveryServicePackage error: ',
                        error
                    );
                    resolve([])
                });
        });

export const getInstallmentConsultation = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_INSTALLMENT_PACKAGE_SMART_POS, METHOD.POST, body).then(response => {
            console.log("getInstallmentConsultation success", response);
            if (helper.IsNonEmptyArray(response.object?.PackageInfo)) {
                resolve(response.object.PackageInfo);
            }
            else {
                reject("Không tìm thấy chương trình trả góp từ đối tác");
            }
        }).catch(error => {
            console.log("getInstallmentConsultation error", error);
            reject(error.msgError);
        });
    });

};

export const getStandardPoint = (body) => (dispatch, getState) =>
    new Promise((resolve, reject) => {
        apiBase(API_GET_STANDARD_POINT, METHOD.POST, {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            ...body
        })
            .then((response) => {
                if (!helper.IsEmptyObject(response.object)) {
                    console.log('getStandardPoint success', response);
                    if (response.object.ProductID) {
                        resolve({
                            standardPoint: response.object.StandardPoint,
                            toDate: response.object.ToDate
                        });
                    } else {
                        resolve({});
                    }
                } else {
                    resolve({});
                }
            })
            .catch((error) => {
                console.log('getStandardPoint error', error);
                resolve({});
            });
    });


export const searchAddress = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_SEARCH_ADDRESS, METHOD.POST, body).then(response => {
            console.log("searchAddress success", response);
            const defaultAddress = { id: uuidv4(), address: "Thêm Địa Chỉ Mới", isIcon: true };
            const searchResult = response.object?.searchResult?.map(item => ({
                ...item,
                session: response.object?.session
            })) ?? [];
            resolve(helper.IsNonEmptyArray(searchResult)
                ? [...searchResult, defaultAddress]
                : [defaultAddress])
        }).catch(error => {
            console.log("searchAddress error", error);
            resolve([{ id: uuidv4(), address: "Thêm Địa Chỉ Mới", isIcon: true }]);
        });
    });

};

export const getAddressByID = (data) => {
    const url = API_GET_INFO_ADDRESS + `v1/address/get?id=${data.id}&session=${data.session}`
    return new Promise((resolve, reject) => {
        apiBase(url, METHOD.GET, null).then(response => {
            console.log("getAddressByID success", response);
            if (!helper.IsEmptyObject(response.object?.objectUnits)) {
                resolve(response.object);
            }
            else {
                reject(false)
            }
        }).catch(error => {
            console.log("getAddressByID error", error);
            reject(false)
        });
    });

};

export const getPricePolicyProgram = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_PRICE_POLICY_PROGRAM, METHOD.POST, body).then(response => {
            console.log("getPricePolicyProgram success", response);
            if (!helper.IsEmptyObject(response.object)) {
                const newData = {
                    ...response.object,
                    packageStatus: uuidv4(),
                    originTotalAmount: response.object.TotalAmount ?? 0,
                    originTotalAmountNOVAT: response.object.TotalAmountNOVAT ?? 0,
                }
                resolve(newData)
            }
            else {
                reject("Các gói dịch vụ đang chọn không thuộc bất kỳ chính sách giá nào. Vui lòng liên hệ ngành hàng và KSNB để được xử lý hoặc chọn lại gói dịch vụ khác")
            }
        }).catch(error => {
            console.log("getPricePolicyProgram error", error);
            reject(error.msgError)
        });
    });

};

const start_get_discount_price_vat = () => {
    return ({
        type: START_GET_DISCOUNT_PRICE_VAT,
    });
};

export const stop_get_discount_price_vat = (
    dataDiscount,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_DISCOUNT_PRICE_VAT,
        dataDiscount,
        description,
        isError,
    });
};

const start_get_data_product = () => {
    return ({
        type: START_GET_DATA_PRODUCT,
    });
};

export const stop_get_data_product = (
    dataProduct,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_DATA_PRODUCT,
        dataProduct,
        isEmpty,
        description,
        isError,
    });
};

const start_get_data_inventory_tab = () => {
    return ({
        type: START_GET_DATA_INVENTORY_TAB,
    });
};

const stop_get_data_inventory_tab = (
    dataProduct,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_DATA_INVENTORY_TAB,
        dataProduct,
        isEmpty,
        description,
        isError,
    });
};

export const set_detail_product = (productSearch) => {
    return ({
        type: SET_DETAIL_PRODUCT,
        productSearch
    });
};

const start_get_second_stock = () => {
    return ({
        type: START_GET_SECOND_STOCK
    });
};

const stop_get_second_stock = (
    secondStock,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_SECOND_STOCK,
        secondStock,
        isEmpty,
        description,
        isError,
    });
};

const start_get_exhibit_stock = () => {
    return ({
        type: START_GET_EXHIBIT_STOCK
    });
};

const stop_get_exhibit_stock = (
    exhibitStock,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_EXHIBIT_STOCK,
        exhibitStock,
        isEmpty,
        description,
        isError,
    });
};

const start_get_promotion = () => {
    return ({
        type: START_GET_PROMOTION
    });
};

// const stop_get_promotion = (
//     promotion,
//     salePromotion,
//     description = "",
//     isError = false,
// ) => {
//     return ({
//         type: STOP_GET_PROMOTION,
//         promotion,
//         salePromotion,
//         isError,
//         description,
//     });
// }

// PROMOTION_DELIVERY

export const stop_get_promotion = (
    promotion,
    salePromotion,
    promotionDelivery,
    salePromotionDelivery,
    dataPromotionLostSale,
    isDisableTabDelivery,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PROMOTION,
        promotion,
        salePromotion,
        promotionDelivery,
        salePromotionDelivery,
        dataPromotionLostSale,
        isDisableTabDelivery,
        description,
        isError
    });
};

const start_get_promotion_for_view = () => {
    return ({
        type: START_GET_PROMOTION_FOR_VIEW
    });
};
const get_all_key_promotion = (allKeyPromotion, allGroupID, defaultKeyPromotion, allExcludeDisabled) => {
    return ({
        type: GET_ALL_KEY_PROMOTION,
        allKeyPromotion,
        allGroupID,
        defaultKeyPromotion,
        allExcludeDisabled
    });
};

const start_get_store_nearest = () => {
    return ({
        type: START_GET_STORE_NEAREST
    });
};

export const stop_get_store_nearest = (
    storeNearest,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_STORE_NEAREST,
        storeNearest,
        isEmpty,
        description,
        isError,
    });
};

const start_get_store_shipping = () => {
    return ({
        type: START_GET_STORE_SHIPPING
    });
};

const stop_get_store_shipping = (
    storeShipping,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_STORE_SHIPPING,
        storeShipping,
        isEmpty,
        description,
        isError,
    });
};

const start_get_employee = () => {
    return ({
        type: START_GET_EMPLOYEE
    });
};

const stop_get_employee = (
    employee,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_EMPLOYEE,
        employee,
        isEmpty,
        description,
        isError,
    });
};

const start_get_data_at_home = () => {
    return ({
        type: START_GET_DATA_AT_HOME
    });
};

export const stop_get_data_at_home = (
    storeAtHome,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_DATA_AT_HOME,
        storeAtHome,
        isEmpty,
        description,
        isError,
    });
};

const start_get_partner_installment = () => {
    return ({
        type: START_GET_PARTNER_INSTALLMENT
    });
};

const stop_get_partner_installment = (
    partnerInstallment,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PARTNER_INSTALLMENT,
        partnerInstallment,
        isEmpty,
        description,
        isError,
    });
};

const start_get_program_installment = () => {
    return ({
        type: START_GET_PROGRAM_INSTALLMENT
    });
};

const stop_get_program_installment = (
    programInstallment,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PROGRAM_INSTALLMENT,
        programInstallment,
        isEmpty,
        description,
        isError,
    });
};

const start_get_fifo = () => {
    return ({
        type: START_GET_FIFO
    });
};

const stop_get_fifo = (
    dataFifo,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_FIFO,
        dataFifo,
        isEmpty,
        description,
        isError,
    });
};

const start_get_warranty = () => {
    return ({
        type: START_GET_WARRANTY
    });
};

const stop_get_warranty = (
    dataWarranty,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_WARRANTY,
        dataWarranty,
        isEmpty,
        description,
        isError,
    });
};

const start_get_config = () => {
    return ({
        type: START_GET_CONFIG
    });
};

const stop_get_config = (
    dataConfig,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_CONFIG,
        dataConfig,
        isEmpty,
        description,
        isError,
    });
};

const start_get_feature = () => {
    return ({
        type: START_GET_FEATURE
    });
};

const stop_get_feature = (
    dataFeature,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_FEATURE,
        dataFeature,
        isEmpty,
        description,
        isError,
    });
};

const start_get_lock_info = () => {
    return ({
        type: START_GET_LOCK_INFO
    });
};

const stop_get_lock_info = (
    dataLock,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LOCK_INFO,
        dataLock,
        isEmpty,
        description,
        isError,
    });
};

const start_get_store_other = () => {
    return ({
        type: START_GET_STORE_OTHER
    });
};

const stop_get_store_other = (
    storeOther,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_STORE_OTHER,
        storeOther,
        isEmpty,
        description,
        isError,
    });
};

const start_get_sim_package = () => {
    return ({
        type: START_GET_SIM_PACKAGE
    });
};

const stop_get_sim_package = (
    dataPackage,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_SIM_PACKAGE,
        dataPackage,
        isEmpty,
        description,
        isError,
    });
};

const start_get_pre_order_lock_info = () => {
    return ({
        type: START_GET_PRE_ORDER_LOCK_INFO
    });
};

const stop_get_pre_order_lock_info = (
    dataPreOrderLock,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PRE_ORDER_LOCK_INFO,
        dataPreOrderLock,
        isEmpty,
        description,
        isError,
    });
};

const start_get_product_service_group_type = (salOrderDetailID) => {
    return ({
        type: START_GET_PRODUCT_SERVICE_GROUP_TYPE,
        salOrderDetailID
    });
};

export const stop_get_product_service_group_type = (
    productServiceGroupType,
    description = ""
) => {
    return ({
        type: STOP_GET_PRODUCT_SERVICE_GROUP_TYPE,
        productServiceGroupType,
        description
    });
};

const start_get_product_service_program = () => {
    return ({
        type: START_GET_PRODUCT_SERVICE_PROGRAM,
    });
};

const stop_get_product_service_program = (
    productServiceProgram,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PRODUCT_SERVICE_PROGRAM,
        productServiceProgram,
        isEmpty,
        description,
        isError,
    });
};

const start_get_product_service_info = () => {
    return ({
        type: START_GET_PRODUCT_SERVICE_INFO,
    });
};

export const stop_get_product_service_info = (
    productServiceInfo,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PRODUCT_SERVICE_INFO,
        productServiceInfo,
        isEmpty,
        description,
        isError,
    });
};


const start_check_pre = () => {
    return ({
        type: START_CHECK_PRE
    });
};

const stop_check_pre = (
    shouldCallPromotion
) => {
    return ({
        type: STOP_CHECK_PRE,
        shouldCallPromotion
    });
};

const start_check_phone_number_apply_promotion = () => {
    return ({
        type: START_CHECK_PHONE_NUMBER_APPLY_PROMOTION
    });
};

export const stop_check_phone_number_apply_promotion = (
    dataCheckPhoneNumberApplyPromotion,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_CHECK_PHONE_NUMBER_APPLY_PROMOTION,
        dataCheckPhoneNumberApplyPromotion,
        isEmpty,
        description,
        isError,
    });
};

export const set_package_services = (packageService) => {
    return ({
        type: SET_PACKAGE_SERVICE,
        packageService
    });
};

export const reset_package_services = () => {
    return ({
        type: RESET_PACKAGE_SERVICE,
    });
};

export const set_suggest_products = (products) => {
    return ({
        type: SET_SUGGEST_PRODUCTS,
        products
    });
};


export const reset_suggest_products = () => {
    return ({
        type: RESET_SUGGEST_PRODUCTS,
    });
};

export const set_map_content_promotion_input = (infoPromo) => {
    return ({
        type: SET_MAP_CONTENT_PROMOTION_INPUT,
        infoPromo
    });
};

export const delete_map_content_promotion_input = ({ key }) => {
    return ({
        type: DELETE_MAP_CONTENT_PROMOTION_INPUT,
        key
    });
};

export const reset_map_content_promotion_input = () => {
    return ({
        type: RESET_MAP_CONTENT_PROMOTION_INPUT,
    });
};

const start_get_reward_installment = () => {
    return ({
        type: START_GET_REWARD_INSTALLMENT
    });
};

export const stop_get_reward_installment = (
    dataRewardInstallment,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_REWARD_INSTALLMENT,
        dataRewardInstallment,
        isEmpty,
        description,
        isError,
    });
};

const start_get_partner_installment_user_code = () => {
    return ({
        type: START_GET_PARTNER_INSTALLMENT_USER_CODE
    });
};

export const stop_get_partner_installment_user_code = (
    dataUserCode,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PARTNER_INSTALLMENT_USER_CODE,
        dataUserCode,
        isEmpty,
        description,
        isError,
    });
};

export const cleard_partner_installment_user_code = () => ({
    type: CLEAR_PARTNER_INSTALLMENT_USER_CODE
});
export const set_phone_number_create_at_home = (numberPhone) => {
    return ({
        type: SET_PHONE_NUMBER,
        numberPhone
    });
}

