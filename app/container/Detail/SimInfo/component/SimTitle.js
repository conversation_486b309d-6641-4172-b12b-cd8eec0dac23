import React, { useState } from 'react';
import { View } from 'react-native';
import { constants } from "@constants";
import { MyText } from "@components";
import { helper, dateHelper } from "@common";
import { translate } from '@translate';
import { COLORS } from "@styles";

const SimTitle = ({
    productInfo,
    stateStandardPoint
}) => {
    const {
        productName,
        salePriceVAT,
        imei,
        totalreward
    } = productInfo;
    const isVisible = (salePriceVAT > 0);
    return (
        <>
            <MyText style={{
                color: COLORS.txt0099E5,
                fontWeight: "bold",
                width: constants.width - 20,
                textAlign: "center"
            }}
                addSize={4}
                text={productName}
            />
            <View style={{
                marginLeft: 10,
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'row',
                padding: 5
            }}>
                <MyText style={{
                    color: COLORS.txt333333,
                    fontSize: 12.5,
                    fontWeight: "normal",
                    textAlign: "center",
                }}
                    addSize={-1, 5}
                    text={translate('detail.emei')}
                />
                <MyText style={{
                    color: COLORS.txtEA1D5D,
                    fontWeight: "bold",
                    textAlign: 'center',
                }}
                    text={imei}
                />
                {
                    totalreward >= 0 &&
                    <View style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        flexDirection: 'row',
                    }}>
                        <MyText style={{
                            color: COLORS.bg000000,
                            fontWeight: "bold",
                            textAlign: 'center',
                            marginLeft: 5
                        }}
                            text={"-"}
                        />
                        <MyText style={{
                            color: COLORS.bg147EFB,
                            fontWeight: "bold",
                            textAlign: 'center',
                            marginLeft: 5
                        }}
                            text={"Điểm chuẩn:"}
                        />
                        <MyText style={{
                            color: COLORS.bgEA1D5D,
                            fontWeight: "bold",
                            textAlign: 'center',
                            marginLeft: 5
                        }}
                            text={helper.convertNum(
                                totalreward,
                                false
                            )}
                        />
                    </View>

                }
                {
                    stateStandardPoint?.standardPoint > 0 &&
                    <View style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        flexDirection: 'row',
                    }}>
                        <MyText style={{
                            color: COLORS.bg000000,
                            fontWeight: "bold",
                            textAlign: 'center',
                            marginLeft: 5
                        }}
                            text={"-"}
                        />
                        <MyText style={{
                            color: COLORS.bg147EFB,
                            fontWeight: "bold",
                            textAlign: 'center',
                            marginLeft: 5
                        }}
                            text={"TN:"}
                        />
                        <MyText style={{
                            color: COLORS.bgEA1D5D,
                            fontWeight: "bold",
                            textAlign: 'center',
                            marginLeft: 5
                        }}
                            text={helper.convertNum(
                                stateStandardPoint.standardPoint,
                                false
                            )}
                        >
                            <MyText
                                style={{ color: COLORS.txtBE0027 }}
                                text={`(${dateHelper.formatDateDDMM(
                                    new Date(stateStandardPoint.toDate) || ""
                                )})`}
                                addSize={-2}
                            />
                        </MyText>

                    </View>

                }
            </View>
            <View style={{
                width: constants.width - 20,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                marginTop: 8
            }}>
                <View style={{
                    width: 130,
                }}>
                    {
                        isVisible &&
                        <MyText style={{
                            color: COLORS.txt333333,
                            width: 130,
                        }}
                            text={translate('common.price')}
                        >
                            <MyText style={{ color: COLORS.txtFF0000, }}
                                text={helper.convertNum(salePriceVAT)}
                                addSize={4}
                            />
                        </MyText>
                    }
                </View>
                <MyText style={{
                    color: COLORS.txt0000FF,
                    width: constants.width - 160,
                }}
                    text={translate('detail.select_package')}
                    addSize={2}
                />
            </View>
        </>
    );
}

export default SimTitle;