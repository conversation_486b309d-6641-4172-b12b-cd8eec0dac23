import {
    Alert,
    Keyboard,
    KeyboardAvoidingView,
    Modal,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableWithoutFeedback,
    View
} from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { Button, Icon, MyText } from '@components';
import { UIActivityIndicator } from 'react-native-indicators';
import { debounce, helper } from '@common';
import { useDispatch, useSelector } from 'react-redux';
import { translate } from '@translate';
import BaseAnimatedView from '../../../../../components/base/BaseAnimatedView';
import {
    getDataDistrict,
    getDataProvince,
    getWard
} from '../../../../Location/action';
import InputField from '../../../../AnKhangNew/components/InputField';
import { getAddressByID, searchAddress } from '../../../../Detail/action';
import AddressManager from '../../../../AddressManager';

const ModalAddressReceive = ({
    isShow,
    hideModal,
    title,
    blockUI,
    itemSelected,
    onPress,
}) => {
    const dispatch = useDispatch();
    const pickerRef = useRef(null);

    const { dataDistrict, dataProvince, stateDistrict } = useSelector(
        (state) => state.locationReducer
    );
    const { provinceID: provinceIDDefault, storeID } = useSelector(
        (state) => state.userReducer
    );

    const [indexPager, setIndexPager] = useState(0);
    const [isWardFetching, setIsWardFetching] = useState(false);
    const [provinceID, setProvinceID] = useState(provinceIDDefault);
    const [districtID, setDistrictID] = useState(0);
    const [ward, setWard] = useState([]);
    const [wardID, setWardID] = useState(0);
    const [stateCustomer, setStateCustomer] = useState({
        contactAddress: ''
    });
    const [stateDataSearch, setStateDataSearch] = useState({
        isFetching: false,
        data: []
    })
    const [isCreateAddress, setIsCreateAddress] = useState(true)
    const isSelectedInsert = useRef(true)
    const handleApiGetDistrict = (provinceId) => {
        dispatch(getDataDistrict(provinceId));
    };
    const handleApiGetWard = ({ provinceId, districtId }) => {
        setIsWardFetching(true);
        getWard(provinceId, districtId)
            .then((wards) => {
                setIsWardFetching(false);
                setWard(wards);
            })
            .catch((error) => {
                setIsWardFetching(false);
                setWard([]);
                console.log(error);
            });
    };
    const validateAddress = () => {
        const addressData = pickerRef?.current?.getAddressData() || {};
        const { newAddress } = addressData;
        const { districtID, provinceID, wardID } = newAddress || {};


        if (provinceID == 0) {
            Alert.alert("", "Vui lòng chọn Tỉnh/Thành.");
            return false;
        }
        if (districtID == 0) {
            Alert.alert("", "Vui lòng chọn Quận/Huyện.");
            return false;
        };
        if (wardID == 0) {
            Alert.alert("", "Vui lòng chọn Phường/Xã.");
            return false;
        };
        if (!helper.IsNonEmptyString(stateCustomer.contactAddress)) {
            Alert.alert("", "Vui lòng nhập địa chỉ nhận.");
            return false;
        };
        return true;
    }
    useEffect(() => {
        setIsWardFetching(blockUI);
    }, [blockUI]);

    useEffect(() => {
        if (helper.IsEmptyObject(itemSelected)) {
            return;
        }
        setIsCreateAddress(true)
        const { provinceId, districtId, wardId, address } = itemSelected;
        setProvinceID(provinceId);
        setDistrictID(districtId);
        setWardID(wardId);
        setStateCustomer({ ...stateCustomer, contactAddress: address });
        if (!!address) {
            handleOnSearchAddress(address)
        }
        if (provinceIDDefault != provinceId) {
            // dispatch(getDataDistrict(provinceId));
        }
        // handleApiGetWard({
        //     provinceId,
        //     districtId
        // });
    }, [itemSelected]);
    // useEffect(
    //     () => {
    //         !helper.IsEmptyString(provinceID) && handleApiGetDistrict(provinceID)
    //     },
    //     [provinceID]
    // )

    useEffect(() => {
        // add config
        if (helper.configStoreApplyMapping(storeID)) {
            isSelectedInsert.current = false
            setIsCreateAddress(false)
        }

    }, [])


    const onChangeAddress = (text) => {
        if (helper.IsNonEmptyString(text)) {
            !isSelectedInsert.current && debounceSearchAddress(text);

        }
        setStateCustomer({
            ...stateCustomer,
            contactAddress: text
        });
    }

    const debounceSearchAddress = useCallback(
        debounce((text) => handleOnSearchAddress(text), 1500),
        []
    );
    const handleOnSearchAddress = (text) => {
        setStateDataSearch((pre) => ({ ...pre, isFetching: true, data: [] }))
        const body = {
            "searchAddress": text,
            "ip": null,
            "provinceId": null,
            "districtId": null,
            "wardId": null,
            "coordinate": null,
            "maxResult": 4
        }
        searchAddress(body).then((response) => {
            setStateDataSearch((pre) => ({ ...pre, isFetching: false, data: response }))
        }).catch(() => {
            setStateDataSearch((pre) => ({ ...pre, isFetching: false, data: [] }))
            setIsCreateAddress(true)
        })
    }
    const onGetAddress = (item) => {
        if (item.isIcon) {
            isSelectedInsert.current = true
            setStateCustomer((pre) => ({ ...pre, contactAddress: "" }))
            setIsCreateAddress(true)
        }
        else {
            getAddressByID(item).then((response) => {
                const { searchAddress, objectUnits, ward, province, district, searchName } = response
                // setStateCustomer({ ...stateCustomer, contactAddress: "" });

                if (objectUnits.provinceId == 0) {
                    Alert.alert("", "Vui lòng chọn Tỉnh/Thành.");
                    return setIsCreateAddress(true)
                }
                if (objectUnits.districtId == 0) {
                    Alert.alert("", "Vui lòng chọn Quận/Huyện.");
                    return setIsCreateAddress(true)
                };
                if (objectUnits.wardId == 0) {
                    Alert.alert("", "Vui lòng chọn Phường/Xã.");
                    return setIsCreateAddress(true)
                };
                onPress({
                    address: searchName,
                    wardID: objectUnits.wardId,
                    districtID: objectUnits.districtId,
                    provinceID: objectUnits.provinceId,
                    headerTitle: searchAddress,
                    provinceName: province,
                    districtName: district,
                    wardName: ward
                });
            }).catch((error) => {
                console.log("🚀 ~ getAddressByID ~ error:", error)
                setIsCreateAddress(true)
            });
        }
        Keyboard.dismiss()
    }

    return (
        <View>
            <Modal
                visible={isShow}
                animationType="fade"
                transparent
                onRequestClose={hideModal}>
                <SafeAreaView
                    style={{
                        backgroundColor: 'white',
                        flex: 1,
                        alignItems: 'center'
                    }}>
                    <View style={styles.header}>
                        <View style={{ flex: 1, alignItems: 'flex-start' }}>
                            <TouchableOpacity
                                style={{ padding: 8 }}
                                touchOpacity={0.6}
                                onPress={() => {
                                    hideModal();
                                    setStateCustomer({
                                        contactName: '',
                                        contactPhone: '',
                                        gender: null
                                    });
                                }}>
                                <Icon
                                    iconSet="MaterialCommunityIcons"
                                    name="arrow-left"
                                    color={COLORS.bg2FB47C}
                                    size={25}
                                />
                            </TouchableOpacity>
                        </View>
                        <Text style={styles.headerText}>{title}</Text>
                        <View style={{ flex: 1 }} />
                    </View>
                    <View style={styles.headerShadow} />
                    {
                        !isSelectedInsert.current && <View style={{ width: constants.width - 20 }} >
                            <InputField
                                autoFocus
                                label={"Nhập địa chỉ"}
                                placeholder={translate(
                                    'detail.text_input_address_contact'
                                )}
                                value={stateCustomer.contactAddress}
                                onChangeText={(text) => {
                                    if (helper.isValidateCharVN(text)) {
                                        onChangeAddress(text);
                                        if (text.length == 0) {
                                            setStateDataSearch((pre) => ({ ...pre, data: [] }))
                                        }
                                    }
                                }}
                                onClear={() => {
                                    onChangeAddress("");
                                    setStateDataSearch((pre) => ({ ...pre, data: [] }))

                                }}
                                onFocus={() => {
                                    !isSelectedInsert.current && setIsCreateAddress(false)
                                }}
                                onBlur={() => {
                                    // setIsCreateAddress(true)

                                }}
                                selectTextOnFocus={false}

                            />
                        </View>
                    }


                    <ScrollView keyboardShouldPersistTaps="always" style={{ flex: 1 }}>
                        {
                            helper.IsNonEmptyString(stateCustomer.contactAddress) && !isCreateAddress ? <View style={{ flex: 1 }}>
                                {
                                    stateDataSearch.isFetching ?
                                        <UIActivityIndicator
                                            size={25}
                                            color={COLORS.bg8E8E93}
                                        />
                                        : <View style={{}}>
                                            {
                                                stateDataSearch?.data?.map((item) => {
                                                    return <View style={{ width: constants.width }}>
                                                        <TouchableOpacity
                                                            onPress={() => { onGetAddress(item) }}
                                                            style={{ margin: 25, flexDirection: "row", alignItems: "center", justifyContent: item.isIcon ? "center" : "" }}
                                                            key={item.id}>
                                                            {item.isIcon && <Icon
                                                                style={{ paddingRight: 10 }}
                                                                iconSet={"Foundation"}
                                                                name={"plus"}
                                                                color={COLORS.bg2FB47C}
                                                                size={20}
                                                            />}
                                                            <MyText text={item.address} />
                                                        </TouchableOpacity>
                                                        <View style={{ borderWidth: StyleSheet.hairlineWidth, borderColor: COLORS.bgC4C4C4 }} />
                                                    </View>
                                                })
                                            }
                                        </View>
                                }
                            </View> : (isCreateAddress && <View>
                                {/* <PickerLocation
                                    dataProvince={{
                                        data: dataProvince,
                                        id: 'provinceID',
                                        value: 'provinceName'
                                    }}
                                    dataDistrict={{
                                        data: dataDistrict,
                                        id: 'districtID',
                                        value: 'districtName'
                                    }}
                                    dataWard={{
                                        data: ward,
                                        id: 'wardID',
                                        value: 'wardName'
                                    }}
                                    wardID={wardID}
                                    districtID={districtID}
                                    provinceID={provinceID}
                                    onSelectProvince={(item) => {
                                        setProvinceID(item.provinceID);
                                        setDistrictID(0);
                                        setWardID(0);
                                        setIndexPager((idx) => idx + 1);
                                        handleApiGetDistrict(item.provinceID);
                                    }}
                                    onSelectDistrict={(item) => {
                                        setDistrictID(item.districtID);
                                        setWardID(0);
                                        setIndexPager((idx) => idx + 1);
                                        handleApiGetWard({
                                            provinceId: provinceID,
                                            districtId: item.districtID
                                        });
                                    }}
                                    onSelectWard={(item) => {
                                        if (item.wardID !== wardID) {
                                            setWardID(item.wardID);
                                        }
                                    }}
                                    indexPager={indexPager}
                                    onShowPicker={setIndexPager}
                                    updatePager={setIndexPager}
                                    isShowIndicator={isWardFetching || stateDistrict.isFetching}
                                />
                          */}

                                <AddressManager
                                    ref={pickerRef}
                                    wardID={wardID}
                                    districtID={districtID}
                                    provinceID={provinceID}
                                    enableDistrictSelection={helper.configApplyLocation()}
                                />


                                {isSelectedInsert.current && <InputField
                                    label={translate(
                                        'detail.text_input_address_contact'
                                    )}
                                    placeholder={translate(
                                        'detail.text_input_address_contact'
                                    )}
                                    value={stateCustomer.contactAddress}
                                    onChangeText={(text) => {
                                        if (helper.isValidateCharVN(text)) {
                                            setStateCustomer({
                                                ...stateCustomer,
                                                contactAddress: text
                                            });
                                        }
                                    }}
                                    onClear={() => {
                                        setStateCustomer({
                                            ...stateCustomer,
                                            contactAddress: ''
                                        });
                                    }}
                                // style={{ width: constants.width - 60 }}
                                />}
                            </View>)
                        }
                    </ScrollView>

                    <View style={styles.bottomBar}>
                        <Button
                            onPress={() => {
                                if (validateAddress()) {
                                    const addressData = pickerRef?.current?.getAddressData() || {
                                    }
                                    const { newAddress, locationData } = addressData;
                                    const { districtID, provinceID, wardID } = newAddress || {};

                                    const provinceName = getNameById(
                                        {
                                            data: locationData.provincesNew || [],
                                            id: 'provinceID',
                                            value: 'provinceName'
                                        },
                                        provinceID
                                    );
                                    const districtName = getNameById(
                                        {
                                            data: dataDistrict,
                                            id: 'districtID',
                                            value: 'districtName'
                                        },
                                        districtID
                                    );

                                    const wardName = getNameById(
                                        {
                                            data: locationData.wardsNew || [],
                                            id: 'wardID',
                                            value: 'wardName'
                                        },
                                        wardID
                                    );
                                    const headerTitle = getTitleHeader(
                                        provinceName,
                                        districtName,
                                        wardName
                                    );


                                    onPress({
                                        address: stateCustomer.contactAddress,
                                        wardID,
                                        districtID,
                                        provinceID,
                                        headerTitle,
                                        provinceName,
                                        districtName,
                                        wardName
                                    });
                                }

                            }}
                            text="HOÀN TẤT"
                            styleContainer={[
                                {
                                    backgroundColor: COLORS.bg2FB47C,
                                    borderColor: COLORS.bg2FB47C,
                                    borderRadius: constants.getSize(10),
                                    borderWidth: 2,
                                    height: constants.getSize(40),
                                    marginHorizontal: 15,
                                    width: constants.width - 50
                                }
                            ]}
                            styleText={{
                                fontSize: 14,
                                fontWeight: 'bold',
                                color: 'white'
                            }}
                        />
                    </View>
                    <BlockUI visible={isWardFetching || stateDistrict.isFetching} />
                </SafeAreaView>
            </Modal>
        </View>
    );
};

export default ModalAddressReceive;

const styles = StyleSheet.create({
    bottomBar: {
        flexDirection: 'row',
        justifyContent: 'center'
    },

    header: {
        alignItems: 'center',
        flexDirection: 'row',
        padding: 8
    },
    headerShadow: {
        // height: Config.isAndroid ? 0.2 : 1,
        elevation: 4,
        backgroundColor: 'lightgrey'
    },
    headerText: {
        color: 'black',
        fontSize: 22,
        textAlign: 'center',
        textAlignVertical: 'center'
    }
});

const BlockUI = ({ visible }) => {
    const [key, setKey] = useState(0);
    useEffect(() => {
        setKey((pre) => pre + 1);
    }, [visible]);
    return (
        <BaseAnimatedView key={key} show={visible}>
            <UIActivityIndicator size={25} color={COLORS.icFFFFFF} />
        </BaseAnimatedView>
    );
};

const getNameById = (dataLocation, ID) => {
    console.log('🚀 ~ getNameById ~ dataLocation:', dataLocation);
    const { data, id, value } = dataLocation;
    const item = data.find((ele) => ele[id] == ID);
    return item ? item[value] : '';
};

const getTitleHeader = (provinceName, districtName, wardName) => {
    if (!provinceName) {
        return 'Chọn Tỉnh/Thành';
    } else if (!districtName) {
        return provinceName;
    } else if (!wardName) {
        return `${districtName} - ${provinceName}`;
    } else {
        return `${wardName} - ${districtName} - ${provinceName}`;
    }
};


