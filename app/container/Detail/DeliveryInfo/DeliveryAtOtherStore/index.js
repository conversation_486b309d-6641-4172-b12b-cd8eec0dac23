/**
 * Sample React Native App
 * 
 *
 * @format
 * @flow strict-local
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Keyboard,
  Alert
} from 'react-native';
import { helper } from "@common";
import {
  BaseLoading,
  FieldInput,
  showBlockUI,
  hideB<PERSON>UI,
} from "@components";
import { constants } from "@constants";
import { getDistrict } from "../../../Location/action";
import ButtonAddToCart from "../component/ButtonAddToCart";
import ModalDatePicker from "../component/Modal/ModalDatePicker";
import ModalDayPicker from "../component/Modal/ModalDayPicker";
import PickerLocation from "./component/PickerLocation";
import SelecteStoreOther from "../component/SelecteStoreOther";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { translate } from '@translate';
import { COLORS } from "@styles";
import AddressManager from '../../../AddressManager';

const DeliveryAtOtherStore = ({
  dataStore,
  quantity,
  productOrder,
  stateStoreOther,
  province,
  defaultDistrict,
  onUpdateOutputStore,
  getDataStore,
  addToShoppingCart,
  disabled,
  defaultProvinceID,
  children,
  actionDetail
}) => {
  // const [provinceID, setProvinceID] = useState(defaultProvinceID);
  // const [district, setDistrict] = useState([]);
  // const [districtID, setDistrictID] = useState(0);
  // const [indexPager, setIndexPager] = useState(0);
  // const [isShowIndicator, setIsShowIndicator] = useState(false);
  const pickerRef = useRef(null);


  const [keyword, setKeyword] = useState("");

  const [storeInfo, setStoreInfo] = useState({});

  const [contactNote, setContactNote] = useState("");
  const [deliveryTime, setDeliveryTime] = useState("");

  const [isStoreCM, setIsStoreCM] = useState(false);

  const [minDate, setMinDate] = useState("");
  const [maxDate, setMaxDate] = useState("");
  const [dataDate, setDataDate] = useState({});
  const [currentDate, setCurrentDate] = useState("");
  const [currentHour, setCurrentHour] = useState("");

  const isVisible = !helper.IsEmptyObject(storeInfo);

  const getInfoOtherStore = () => {
    const addressData = pickerRef?.current?.getAddressData() || {};
    const { newAddress } = addressData;
    const { districtID, provinceID } = newAddress || {};
    getDataStore({
      "productID": productOrder.productID,
      "inventoryStatusID": productOrder.inventoryStatusID,
      "provinceID": provinceID,
      "districtID": districtID,
      "deliveryAddress": keyword
    });
  }

  // const effectChangeProvince = () => {
  //   if (provinceID > 0) {
  //     if (provinceID != defaultProvinceID) {
  //       getDataDistrict(provinceID);
  //     }
  //     else {
  //       setDistrict(defaultDistrict);
  //       setIndexPager(1);
  //     }
  //   }
  // }

  const effectChangeDistrict = () => {
    const addressData = pickerRef?.current?.getAddressData() || {};
    const { newAddress } = addressData;
    const { districtID } = newAddress || {};
    if (districtID > 0) {
      getInfoOtherStore();
    }
  }

  const effectChangeStore = () => {
    if (isVisible) {
      const {
        quantity: quantityInStock
      } = storeInfo
      const quantityMissing = (quantity - quantityInStock);
      const isMissingQuantity = (quantityMissing > 0);
      const quantityChangeStore = (quantityInStock > 0) ? quantityMissing : quantity;
      if (isMissingQuantity) {
        console.log("isMissingQuantity: ", isMissingQuantity);
        console.log("quantityChangeStore: ", quantityChangeStore);
        getStoreShipping({
          "productID": productOrder.productID,
          "inventoryStatusID": productOrder.inventoryStatusID,
          "quantity": quantityChangeStore,
          "storeID": storeInfo.storeID,
          "stockQuantity": quantityInStock
        });
      }
      else {
        onUpdateOutputStore(storeInfo);
        getSuggestTimes(storeInfo);
        setIsStoreCM(false);
      }
    }
  }

  // useEffect(
  //   effectChangeProvince,
  //   [provinceID]
  // )

  // useEffect(
  //   effectChangeDistrict,
  //   [districtID]
  // )

  useEffect(
    effectChangeStore,
    [storeInfo]
  )

  // const getDataDistrict = (provinceID) => {
  //   setIsShowIndicator(true);
  //   getDistrict(provinceID).then(data => {
  //     setDistrict(data);
  //     setIndexPager(1);
  //     setIsShowIndicator(false);
  //   }).catch(msgError => {
  //     Alert.alert(translate('common.notification_uppercase'), msgError,
  //       [
  //         {
  //           text: translate('common.btn_skip'),
  //           style: "cancel",
  //           onPress: () => setIsShowIndicator(false)
  //         },
  //         {
  //           text: translate('common.btn_notify_try_again'),
  //           style: "default",
  //           onPress: () => getDataDistrict(provinceID)
  //         }
  //       ]
  //     )
  //   });
  // }

  const getStoreShipping = (productInfo) => {
    showBlockUI();
    actionDetail.getStoreOtherShipping(productInfo).then(data => {
      hideBlockUI();
      onUpdateOutputStore({
        outputStoreID: storeInfo.storeID,
        storeRequests: data.storeRequests,
      });
      getSuggestTimes(data);
      setIsStoreCM(true);
    }).catch(msgError => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: () => {
              hideBlockUI();
              setStoreInfo({});
              setIsStoreCM(false);
            }
          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => getStoreShipping(productInfo)
          }
        ]
      )
    })
  }

  const getSuggestTimes = (storeInfo) => {
    const { suggestTimes } = storeInfo;
    if (helper.IsNonEmptyArray(suggestTimes)) {
      const {
        beginDate,
        endDate,
        data
      } = helper.getDateTimeSuggest(suggestTimes);
      const {
        deliveryText,
        deliveryValue
      } = beginDate;
      const min = deliveryValue.split("T")[0];
      const hour = deliveryText;
      const max = endDate.deliveryValue.split("T")[0];
      setCurrentDate(min);
      setCurrentHour(hour);
      setMinDate(min);
      setMaxDate(max);
      setDataDate(data);
      setDeliveryTime(deliveryValue);
    }
    else {
      Alert.alert("", translate('detail.expired_time'),
        [
          {
            text: "OK",
            style: "default",
            onPress: () => {
              setStoreInfo({});
            }
          }
        ]
      )
    }
  }

  const checkValidateInfo = () => {
    if (helper.IsEmptyObject(storeInfo)) {
      Alert.alert("", translate('detail.please_select_store'));
      return false;
    }
    if (!helper.IsNonEmptyString(deliveryTime)) {
      Alert.alert("", translate('detail.please_select_delivery_time'));
      return false;
    }
    return true;
  }

  const onAddToCart = () => {
    const addressData = pickerRef?.current?.getAddressData() || {};
    const { newAddress } = addressData;
    const { districtID, provinceID } = newAddress || {};
    Keyboard.dismiss();
    const isValidate = checkValidateInfo();
    if (isValidate) {
      const {
        storeRequests,
        storeID,
        deliveryTypeID,
        vehicleTypeID,
        distance,
        shippingCost,
      } = storeInfo;
      // const shippingCost = getShippingCost(storeRequests);
      const delivery = {
        "deliveryStoreID": storeID,
        "deliveryTypeID": deliveryTypeID,
        "deliveryVehicles": vehicleTypeID,
        "deliveryDistance": distance,
        "shippingCost": shippingCost,

        "deliveryTime": deliveryTime,

        "deliveryProvinceID": provinceID,
        "deliveryDistrictID": districtID,
        "deliveryWardID": 0,
        "deliveryAddress": "",
        "contactGender": 1,
        "contactPhone": "",
        "contactName": "",
        "customerNote": contactNote,
      };
      addToShoppingCart({
        storeRequests: storeRequests,
        delivery: delivery
      });
    }
  }

  return (
    <View
      style={{
        flex: 1
      }}>
      <KeyboardAwareScrollView
        style={{
          flex: 1
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps="always"
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}>
        <View
          style={{
            width: constants.width,
            paddingVertical: 10,
            alignItems: 'center'
          }}>
          {/* <PickerLocation
            dataProvince={{
              data: province,
              id: "provinceID",
              value: "provinceName"
            }}
            dataDistrict={{
              data: district,
              id: "districtID",
              value: "districtName"
            }}
            districtID={districtID}
            provinceID={provinceID}
            onSelectProvince={(item) => {
              setProvinceID(item.provinceID);
              setDistrictID(0);
            }}
            onSelectDistrict={(item) => {
              setDistrictID(item.districtID);
            }}
            indexPager={indexPager}
            onShowPicker={(index) => {
              setIndexPager(index);
            }}
            updatePager={(index) => {
              setIndexPager(index);
            }}
            isShowIndicator={isShowIndicator}
          /> */}
          <AddressManager
            ref={pickerRef}
            wardID={0}
            districtID={0}
            provinceID={defaultProvinceID}
            onChangeWard={() => {
              effectChangeDistrict();
            }}
            enableDistrictSelection={helper.configApplyLocation()}
          />
          <FieldInput
            styleInput={{
              borderWidth: 1,
              borderRadius: 4,
              borderColor: COLORS.bdCCCCCC,
              paddingHorizontal: 10,
              backgroundColor: COLORS.bgFFFFFF,
              justifyContent: 'center',
              paddingVertical: 8
            }}
            textAlignVertical={'center'}
            underlineColorAndroid={'transparent'}
            placeholder={translate(
              'detail.text_input_street_to_find_store'
            )}
            value={keyword}
            onChangeText={(text) => {
              if (helper.isValidateCharVN(text)) {
                setKeyword(text);
              }
            }}
            returnKeyType={'default'}
            blurOnSubmit={true}
            // onSubmitEditing={effectChangeDistrict}
            width={constants.width - 20}
            multiline={true}
            height={40}
            clearText={() => {
              setKeyword('');
            }}
            maxLength={100}
          />
          {
            <BaseLoading
              isLoading={stateStoreOther.isFetching}
              isEmpty={stateStoreOther.isEmpty}
              textLoadingError={stateStoreOther.description}
              isError={stateStoreOther.isError}
              onPressTryAgains={getInfoOtherStore}
              content={
                <View
                  style={{
                    width: constants.width,
                    alignItems: 'center'
                  }}>
                  <SelecteStoreOther
                    title={translate('common.store')}
                    storeIdSeclect={storeInfo.storeID}
                    dataStore={dataStore}
                    onSelectStore={(store) => {
                      setCurrentDate('');
                      setCurrentHour('');
                      setMinDate('');
                      setMaxDate('');
                      setDataDate({});
                      setDeliveryTime('');
                      setStoreInfo(store);
                    }}
                    onSearchStore={() => {
                      console.log('onSearchStore');
                    }}
                    quantity={quantity}
                  />
                  {isVisible && (
                    <View
                      style={{
                        width: constants.width,
                        alignItems: 'center'
                      }}>
                      {isStoreCM && (
                        <ModalDatePicker
                          minDate={minDate}
                          maxDate={maxDate}
                          dataDate={dataDate}
                          currentDate={currentDate}
                          currentHour={currentHour}
                          onChangeDay={(dateString) => {
                            setCurrentDate(
                              dateString
                            );
                            setCurrentHour('');
                            setDeliveryTime('');
                          }}
                          onChangeHour={(ele) => {
                            const {
                              deliveryValue,
                              deliveryText
                            } = ele;
                            setCurrentHour(
                              deliveryText
                            );
                            setDeliveryTime(
                              deliveryValue
                            );
                          }}
                        />
                      )}
                      {!isStoreCM && (
                        <ModalDayPicker
                          currentDate={currentDate}
                          minDate={minDate}
                          maxDate={maxDate}
                          onChangeDay={(dateString) => {
                            setCurrentDate(
                              dateString
                            );
                            setDeliveryTime(
                              `${dateString}T22:59:00`
                            );
                          }}
                        />
                      )}
                      <FieldInput
                        styleInput={{
                          borderWidth: 1,
                          borderRadius: 4,
                          borderColor: COLORS.bdCCCCCC,
                          marginVertical: 10,
                          paddingHorizontal: 10,
                          backgroundColor:
                            COLORS.bgFFFFFF,
                          justifyContent: 'center',
                          paddingVertical: 8
                        }}
                        textAlignVertical={'center'}
                        underlineColorAndroid={
                          'transparent'
                        }
                        placeholder={translate(
                          'detail.text_input_note'
                        )}
                        value={contactNote}
                        onChangeText={(text) => {
                          if (
                            helper.isValidateCharVN(
                              text
                            )
                          ) {
                            setContactNote(text);
                          }
                        }}
                        returnKeyType={'default'}
                        blurOnSubmit={true}
                        onSubmitEditing={() => {
                          Keyboard.dismiss();
                        }}
                        width={constants.width - 20}
                        multiline={true}
                        height={40}
                        clearText={() => {
                          setContactNote('');
                        }}
                        maxLength={500}
                      />
                      {
                        // PROMOTION_DELIVERY
                        children
                      }
                      <ButtonAddToCart
                        onAddToCart={onAddToCart}
                        disabled={disabled}
                      />
                    </View>
                  )}
                </View>
              }
            />
          }
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
}

export default DeliveryAtOtherStore;

const getShippingCost = (storeRequests) => {
  let sum = 0;
  if (helper.IsNonEmptyArray(storeRequests)) {
    storeRequests.forEach(ele => {
      const { shippingCost } = ele;
      sum += shippingCost
    });
  }
  return sum;
}
