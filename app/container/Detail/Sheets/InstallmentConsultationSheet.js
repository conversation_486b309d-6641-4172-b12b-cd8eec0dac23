import React, { useMemo, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { COLORS } from '@styles';
import { MyText, BottomSheet } from '@components';
import HomePayLaterConsultant from '../component/HomePayLaterConsultant';
import KredivoConsultant from '../component/KredivoConsultant';
import { Icon } from '@components';
import { TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { constants } from '@constants';

const { PARTNER_ID } = constants
const InstallmentConsultationSheet = ({
    bottomSheetRef,
    onChangeStatusSheet,
    productInfo,
    saleProgramInfo,
    quantity
}) => {
    const handleComponent = () => {
        return (
            <View style={styles.handle}>
                <View style={{ flex: 1 }} />
                <View
                    style={{
                        flex: 6,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <MyText
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.txt000000,
                            fontVariant: ''
                        }}
                        text={'Bảng tư vấn tham khảo'.toUpperCase()}
                    />
                </View>
                <View style={{ flex: 1 }}>
                    <TouchableWithoutFeedback
                        style={{ marginLeft: 10 }}
                        onPress={() => {
                            bottomSheetRef.current?.dismiss();
                        }}>
                        <Icon
                            iconSet={'MaterialIcons'}
                            name={'clear'}
                            color={COLORS.txt000000}
                            size={22}
                        />
                    </TouchableWithoutFeedback>
                </View>
            </View>
        );
    };
    const snapPoints = useMemo(() => ['99.99%'], []);

    const renderPartnerConsultant = () => {
        console.log("🚀 ~ renderPartnerConsultant ~ saleProgramInfo.partnerID:", saleProgramInfo.partnerID)

        switch (String(saleProgramInfo.partnerID)) {
            case PARTNER_ID.KREDIVO:
                return <KredivoConsultant productInfo={productInfo} saleProgramInfo={saleProgramInfo} quantity={quantity} />
            case PARTNER_ID.HOME_PAY_LATER:
                return <HomePayLaterConsultant productInfo={productInfo} saleProgramInfo={saleProgramInfo} />
            default:
                return <View style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}></View>;
        }
    }

    return (
        <BottomSheet
            bs={bottomSheetRef}
            handleComponent={handleComponent}
            snapPoints={snapPoints}
            onChangeStatusSheet={onChangeStatusSheet}>
            {
                renderPartnerConsultant()
            }

        </BottomSheet>
    );
};

const styles = StyleSheet.create({
    handle: {
        backgroundColor: COLORS.bgFFFFFF,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 50,
        elevation: 2,
        borderTopStartRadius: 22,
        borderTopEndRadius: 22,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4
    }
});

export default InstallmentConsultationSheet;
