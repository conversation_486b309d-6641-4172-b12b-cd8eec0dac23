import { StyleSheet, View, Alert, TouchableOpacity } from 'react-native'
import React, { useState, useEffect } from 'react'
import { MyText, PickerSearch, hideBlock<PERSON>, showBlockUI, Icon } from '@components'
import { translate } from '@translate'
import { COLORS } from '@styles'
import { constants } from '@constants'
import { connect } from 'react-redux';
import * as actionInsuranceCreator from "../action";
import { bindActionCreators } from 'redux'
import ButtonAction from '../components/ButtonAction'
import ModalProductOtherInfo from '../components/Modal/ModalProductOtherInfo'
import { helper } from "@common";

const CreateDeclaration = ({
    actionInsurance,
    navigation,
    dataSaleOrderCart,
    route,
    userInfo
}) => {
    const [province, setProvince] = useState('');
    const [itemProvince, setItemProvince] = useState({});
    const [provinceAfterMerge, setProvinceAfterMerge] = useState('');
    const [itemProvinceAfterMerge, setItemProvinceAfterMerge] = useState({});
    const [dataProvince, setDataProvince] = useState([]);
    const [dataProvinceAfterMerge, setDataProvinceAfterMerge] = useState([]);
    const [updatDataProvinceMaping, setUpdataDataProvinceMaping] = useState({});
    const [isVisibleProductOtherInfo, setIsVisibleProductOtherInfo] = useState(false);
    const [disabledWebView, setDisabledWebView] = useState(false);
    const [disableDeclarationInformation, setDisableDeclarationInformation] = useState(true);
    const isConfigInsuranceLocationFlowNew = helper.checkConfigStoreInsuranceLocationFlowNew(userInfo?.storeID);
    const [addressSeparated, setAddressSeparated] = useState(false);
    const [addressMerrged, setAddressMerrged] = useState(false);

    useEffect(() => {
        getProvince();
    }, [actionInsurance])

    useEffect(() => {
        if (
            !helper.IsEmptyObject(updatDataProvinceMaping) &&
            Array.isArray(dataProvinceAfterMerge) &&
            dataProvinceAfterMerge.length > 0
        ) {
            const { ProvinceIdNew } = updatDataProvinceMaping;
            const matched = dataProvinceAfterMerge.find(
                item => item.MWGVALUE == ProvinceIdNew
            );
            setProvinceAfterMerge(matched.MWGVALUE)
            setItemProvinceAfterMerge(matched)
        }
    }, [updatDataProvinceMaping, dataProvinceAfterMerge]);


    const getProvince = async () => {
        showBlockUI();
        try {
            const res1 = await actionInsurance.getPropertyServiceRequest();
            setDataProvince(res1);

            if (isConfigInsuranceLocationFlowNew) {
                const res2 = await actionInsurance.getProvinceAfterMerge();
                setDataProvinceAfterMerge(res2);
            }
        } catch (error) {
            Alert.alert("", error.msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        navigation.goBack();
                    },
                },
            ]);
        } finally {
            hideBlockUI();
        }
    };


    const getDataGetAddressMapping = (keywork) => {
        const data = {
            keyword: keywork
        }
        showBlockUI();
        actionInsurance.getAddressMapping(data).then((res) => {
            hideBlockUI();
            setUpdataDataProvinceMaping(res?.[0]);
        }).catch((error) => {
            Alert.alert("", error.msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        });
    };


    const handleConnectWebview = () => {
        const selectedProvince = isConfigInsuranceLocationFlowNew
            ? itemProvinceAfterMerge
            : itemProvince;

        if (helper.IsEmptyObject(selectedProvince)) {
            Alert.alert("", "Vui lòng chọn Tỉnh tham gia BHYT/BHXH");
            return false;
        }

        showBlockUI();
        const data = {
            lstproperty: [selectedProvince]
        };

        actionInsurance.addToSaleOrderCart(data)
            .then((response) => {
                hideBlockUI();
                setIsVisibleProductOtherInfo(true);
                setDisabledWebView(true);
                setDisableDeclarationInformation(false);
            })
            .catch((error) => {
                Alert.alert("", error.msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    };


    const handleDeclarationInformation = () => {
        showBlockUI();
        const {
            cus_AirtimeTransactionSVMapBO
        } = dataSaleOrderCart ?? {};
        const {
            ServiceVoucherID
        } = cus_AirtimeTransactionSVMapBO ?? {};
        const data = {
            "serviceVoucherID": ServiceVoucherID
        }
        actionInsurance.getDataInfo(data).then((reponseCreate) => {
            hideBlockUI();
            navigation.navigate("DeclarationInformation", { reponseCreate });
            setDisabledWebView(false);
            setDisableDeclarationInformation(true);
        }).catch((error) => {
            Alert.alert("", error.msgError, [
                {
                    text: "BỎ QUA",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
                {
                    text: "THỬ LẠI",
                    onPress: () => {
                        handleDeclarationInformation();
                    },
                },
            ]);
        });
    }

    return (
        <View style={{
            flex: 1,
            backgroundColor: COLORS.bgFFFFFF,
            alignItems: 'center'
        }}>
            <View style={{
                margin: 10
            }}>
                <MyText
                    text={"Tỉnh tham gia BHYT/BHXH"}
                    addSize={-1.5}
                    style={{
                        color: COLORS.bgF49B0C,
                        fontWeight: "bold",
                        fontStyle: "italic",
                        width: 'auto',
                        paddingBottom: 15,
                        fontSize: 16
                    }} />
                {
                    helper.IsNonEmptyArray(dataProvinceAfterMerge) &&
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingBottom: 7 }}>
                        <MyText style={{ fontWeight: 'bold', marginBottom: 5 }} text={'Địa chỉ Tỉnh thành trước khi sáp nhập'} >
                            {
                                <MyText addSize={2} style={{ color: COLORS.bgEA1D5D }} text={" *"} />
                            }
                        </MyText>
                        <TouchableOpacity
                            disabled={addressMerrged}
                            onPress={() => {
                                setProvince('')
                                setItemProvince({});
                                setAddressSeparated(false);
                                setProvinceAfterMerge('')
                                setItemProvinceAfterMerge({})
                            }}>
                            <Icon iconSet={'MaterialIcons'} name={'refresh'} color={COLORS.txt147EFB} size={25} />
                        </TouchableOpacity>
                    </View>
                }
                <PickerSearch
                    opacity={0.5}
                    label={"MWGVALUENAME"}
                    value={"MWGVALUE"}
                    valueSelected={province}
                    data={dataProvince}
                    onChange={(item) => {
                        setProvinceAfterMerge('')
                        setItemProvinceAfterMerge({})
                        setDisabledWebView(false);
                        setDisableDeclarationInformation(true);
                        console.log(item.MWGVALUE)
                        setProvince(item.MWGVALUE)
                        setItemProvince(item);
                        getDataGetAddressMapping(item.MWGVALUE);
                        setAddressSeparated(true);
                        setAddressMerrged(false);
                    }}
                    style={{
                        flexDirection: "row",
                        justifyContent: "center",
                        alignItems: "center",
                        height: 40,
                        borderRadius: 5,
                        borderWidth: 1,
                        borderColor: COLORS.bd0099E5,
                        width: constants.width - 20,
                        backgroundColor: COLORS.btnFFFFFF,
                        marginBottom: 10
                    }}
                    defaultLabel={"Chọn tỉnh"}
                    disabled={addressMerrged}
                />
                {
                    helper.IsNonEmptyArray(dataProvinceAfterMerge) &&
                    <View>
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingBottom: 7 }}>
                            <MyText style={{ fontWeight: 'bold', marginBottom: 5 }} text={'Địa chỉ Tỉnh thành sau khi sáp nhập'} >
                                {
                                    <MyText addSize={2} style={{ color: COLORS.bgEA1D5D }} text={" *"} />
                                }
                            </MyText>
                            <TouchableOpacity
                                disabled={addressSeparated}
                                onPress={() => {
                                    setProvinceAfterMerge('')
                                    setItemProvinceAfterMerge({})
                                    setAddressSeparated(false);
                                    setAddressMerrged(false);
                                }}>
                                <Icon iconSet={'MaterialIcons'} name={'refresh'} color={COLORS.txt147EFB} size={25} />
                            </TouchableOpacity>
                        </View>
                        <PickerSearch
                            label={"MWGVALUENAME"}
                            value={"MWGVALUE"}
                            valueSelected={provinceAfterMerge}
                            data={dataProvinceAfterMerge}
                            onChange={(item) => {
                                setDisabledWebView(false);
                                setDisableDeclarationInformation(true);
                                setProvinceAfterMerge(item.MWGVALUE)
                                setItemProvinceAfterMerge(item)
                                setAddressMerrged(true);
                                setAddressSeparated(false);
                            }}
                            style={{
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                height: 40,
                                borderRadius: 5,
                                borderWidth: 1,
                                borderColor: COLORS.bd0099E5,
                                width: constants.width - 20,
                                backgroundColor: COLORS.btnFFFFFF,
                                marginBottom: 10
                            }}
                            defaultLabel={"Chọn tỉnh"}
                            isRequired={true}
                            disabled={addressSeparated}
                        />
                    </View>
                }

                <View style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                    marginTop: 20
                }}>
                    <ButtonAction
                        onPress={() => handleConnectWebview()}
                        title={"LÀM TỜ KHAI BẢO HIỂM"}
                        iconSet={"Ionicons"}
                        nameIcon={"newspaper-outline"}
                        style={{
                            backgroundColor: COLORS.bgF49B0C
                        }}
                        opacity={disabledWebView ? 0.5 : 1}
                        disabled={disabledWebView}
                    />
                    <View style={{ flex: 1 }} />
                    <ButtonAction
                        onPress={() => handleDeclarationInformation()}
                        title={"LẤY THÔNG TIN TỜ KHAI"}
                        iconSet={"Ionicons"}
                        nameIcon={"reload"}
                        style={{
                            backgroundColor: COLORS.bg00A98F,
                        }}
                        opacity={disableDeclarationInformation ? 0.5 : 1}
                        disabled={disableDeclarationInformation}
                    />
                </View>
                <MyText
                    text={` Lưu ý: Nhân viên thu cần thông tin cho Khách hàng được biết "Các thông tin Khách hàng cung cấp sẽ được ghi nhận trên hệ thống của PVI"`}
                    addSize={-1.5}
                    style={{
                        color: COLORS.bgEA1D5D,
                        fontWeight: "bold",
                        fontStyle: "italic",
                        width: 'auto',
                        paddingBottom: 5,
                        marginTop: 20
                    }} />
            </View>
            {
                isVisibleProductOtherInfo &&
                <ModalProductOtherInfo
                    isVisible={isVisibleProductOtherInfo}
                    hideModal={() => {
                        setIsVisibleProductOtherInfo(false);
                        handleDeclarationInformation();
                    }}
                    webInfo={{
                        uriWebView: dataSaleOrderCart?.ExtraData?.Webview,
                        title: "BẢO HIỂM XÃ HỘI"
                    }}
                />
            }
        </View>
    )
}

const mapStateToProps = function (state) {
    return {
        dataSaleOrderCart: state.insuranceReducer.dataSaleOrderCart,
        userInfo: state.userReducer
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsurance: bindActionCreators(actionInsuranceCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(CreateDeclaration);


const styles = StyleSheet.create({})
