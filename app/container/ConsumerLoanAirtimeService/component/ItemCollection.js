import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Icon, MyText } from "@components";
import { constants } from '@constants';
import { translate } from '@translate';
import { dateHelper, helper } from '@common';
import { COLORS } from "@styles";

const ItemCollection = ({
  item,
  onPressDetail,
  onAskStatus,
  onDelete,
  handleAllowerCall,
  printPromotion
}) => {
  const {
    SERVICEVOUCHERID,
    CUSTOMERALIAS,
    AIRTIMESTATUSNAME,
    CUSTOMERNAME,
    OFFERAMOUNT,
    APPROVEAMOUNT,
    ISPENDING,
    DESCRIPTION,
    AIRTIMETRANSACTIONTYPEID,
    INPUTTIME,
    ISALLOWDELETE,
    ISALLOWRECALL,
    PROMOTIONSALEORDERID
  } = item;

  const [isShowDetail, setIsShowDetail] = useState(false);

  return (
    <View style={{
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 5,
      paddingVertical: 5
    }}>
      <View
        style={{
          width: constants.width,
          padding: 8,
          width: '97%',
          padding: 10,
          borderColor: COLORS.bd218DEB,
          backgroundColor: COLORS.bgFFFFFF,
          borderRadius: 10,
          shadowColor: COLORS.bg000000,
          shadowOffset: {
            width: 0,
            height: 2
          },
          shadowOpacity: 0.25,
          shadowRadius: 4,
          elevation: 5,
        }}>

        {/* Thông tin chung */}
        <View style={{
          flexDirection: "row",
        }}>
          <TouchableOpacity
            disabled={AIRTIMETRANSACTIONTYPEID == 1513 ? false : true}
            onPress={onPressDetail}
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center'
            }}
          >
            <MyText
              text={SERVICEVOUCHERID}
              style={{
                color: COLORS.bg2C8BD7,
                fontWeight: 'bold',
                textAlign: 'center',
              }}
            />
            <MyText
              text={" - "}
              addSize={-1.5}
              style={{
                color: COLORS.bgEA1D5D,
                fontWeight: 'bold'
              }}
            />
            <MyText
              text={CUSTOMERALIAS}
              addSize={-1.5}
              style={{
                color: COLORS.bg000000,
                fontSize: 15,
              }}
            />
            <Icon
              iconSet={'Ionicons'}
              name={"create-outline"}
              color={COLORS.bg2C8BD7}
              size={16}
              style={{
                marginLeft: 4,
                marginBottom: 4
              }}
            />
          </TouchableOpacity>
        </View>

        {/* Các field khác */}
        <TextField
          name={"Ngày tạo hồ sơ: "}
          value={dateHelper.formatStrDateFULL(INPUTTIME)}
          key={"INPUTTIME"}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        <TextField
          name={translate("collection.customer_name")}
          value={CUSTOMERNAME}
          key={"CUSTOMERNAME"}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        {
          AIRTIMETRANSACTIONTYPEID != 1992 && (
            <MoneyField
              name={"Số tiền KH đề nghị vay: "}
              value={OFFERAMOUNT}
              color={COLORS.txtFF0000}
              key={"OFFERAMOUNT"}
            />
          )
        }
        <MoneyField
          name={"Số tiền KH được phê duyệt: "}
          value={APPROVEAMOUNT}
          color={COLORS.txtFF0000}
          key={"APPROVEAMOUNT"}
        />
        <TextField
          name={"Trạng thái hồ sơ: "}
          value={AIRTIMESTATUSNAME}
          key={"AIRTIMESTATUSNAME"}
          extraStyle={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
        />
        <MyText
          text={`${translate("collection.note")} ${DESCRIPTION}`}
          addSize={-1.5}
          style={{
            color: COLORS.bg8E8E93,
            marginTop: 10,
            fontStyle: 'italic'
          }}
        />
        {
          helper.IsNonEmptyArray(item?.TRANSACTIONDETAIL) &&
          <TouchableOpacity
            onPress={() => setIsShowDetail(!isShowDetail)}
          >
            <MyText
              text={isShowDetail ? "Ẩn chi tiết" : "Xem chi tiết"}
              addSize={-1.5}
              style={{
                color: COLORS.txt2529D8,
                fontWeight: 'bold',
                marginTop: 5,
                textDecorationLine: 'underline',
                fontStyle: 'italic'
              }}
            />
          </TouchableOpacity>
        }
        {isShowDetail && (
          <View style={{
            borderColor: COLORS.bgF0F0F0,
            borderWidth: 1,
            borderRadius: 10,
            marginTop: 5,

          }}>
            {item?.TRANSACTIONDETAIL?.map((item, index) => (
              <View style={{
                marginLeft: 5
              }}>
                <TextField
                  name={`${item.Label}: `}
                  value={item.Value}
                  key={"AIRTIMESTATUSNAME"}
                  extraStyle={{ color: COLORS.bg000000, }}
                />
              </View>
            ))}
          </View>
        )}
        <ButtonAction
          ISPENDING={ISPENDING}
          onAskStatus={onAskStatus}
          ISALLOWRECALL={ISALLOWRECALL}
          handleAllowerCall={handleAllowerCall}
          PROMOTIONSALEORDERID={PROMOTIONSALEORDERID}
          printPromotion={printPromotion}
        />
        {
          ISALLOWDELETE == 1 ?
            <ButtonDelete
              isVisible={true}
              onDelete={onDelete}
              appName={"POS"}
            />
            :
            null
        }
      </View>
    </View>
  );
}

const ButtonAction = ({
  ISPENDING,
  onAskStatus,
  handleAllowerCall,
  ISALLOWRECALL,
  PROMOTIONSALEORDERID,
  printPromotion
}) => {
  return (
    <View style={{
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 6,
      width: constants.width - 15,
      paddingRight: 15
    }}>
      {
        ISPENDING == 1 ?
          <IconField
            title={translate("collection.click_check_result")}
            name={"reload-outline"}
            color={COLORS.bgF49B0C}
            textAlign={"right"}
            onPress={onAskStatus}
          />
          :
          null
      }
      {
        ISALLOWRECALL == 1 ?
          <IconField
            title={'Gọi lại'}
            name={"repeat-outline"}
            color={COLORS.bg1E88E5}
            textAlign={"right"}
            onPress={handleAllowerCall}
          />
          :
          null
      }
      {
        PROMOTIONSALEORDERID != null ?
          <IconField
            title={"In PMH"}
            name={"card-giftcard"}
            color={COLORS.icFF0000}
            textAlign={"right"}
            onPress={printPromotion}
            iconSet={true}
          />
          :
          null
      }
    </View>
  );
}

const IconField = ({
  title,
  name,
  color,
  textAlign,
  onPress,
  iconSet = 'Ionicons'
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.6}
      onPress={onPress}
    >
      <MyText
        text={title}
        addSize={-1}
        style={{
          color: color,
          textAlign: textAlign,
          paddingVertical: 2
        }}>
        {" "}
        <Icon
          iconSet={iconSet}
          name={name}
          color={color}
          size={14}
        />
      </MyText>
    </TouchableOpacity>
  );
}

const TextField = ({ name, value, extraStyle, isWarning }) => {
  return (
    <MyText
      text={name}
      addSize={-1.5}
      style={{
        color: COLORS.txt8E8E93,
        marginTop: 10
      }}>
      <MyText
        text={value}
        style={[{
          color: isWarning ? COLORS.txtFF0000 : COLORS.txt333333,
        }, extraStyle]}
      />
    </MyText>
  );
}

const MoneyField = ({ name, value, color = COLORS.txt333333 }) => {
  return (
    <View style={{
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 10
    }}>
      <MyText
        text={name}
        addSize={-1.5}
        style={{
          color: COLORS.txt8E8E93,
        }}
      />
      <MyText
        text={value}
        addSize={-1.5}
        style={{
          color: color,
          fontWeight: 'bold'
        }}
      />
    </View>
  );
}

const ButtonDelete = ({ onDelete, isVisible }) => {
  return (
    <View style={{
      position: "absolute",
      top: 0,
      right: 0,
      alignItems: "flex-end"
    }}>
      {
        isVisible &&
        <TouchableOpacity style={{
          padding: 6,
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: COLORS.btnDCE1E1,
          borderBottomStartRadius: 14,
        }}
          activeOpacity={0.6}
          onPress={onDelete}
        >
          <Icon
            iconSet={"Ionicons"}
            name={"trash"}
            color={COLORS.icFF0000}
            size={14}
          />
          <MyText
            text={translate('common.btn_cancel')}
            addSize={-1.5}
            style={{
              color: COLORS.txtFF0000
            }}
          />
        </TouchableOpacity>
      }
    </View>
  );
}
export default ItemCollection;
