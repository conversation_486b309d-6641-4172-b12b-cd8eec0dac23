import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
    View,
    TouchableOpacity,
    SafeAreaView,
    Animated,
    Alert,
    StyleSheet,
    FlatList
} from 'react-native';
import {
    MyText,
    Icon,
    BaseLoading,
    hideBlock<PERSON>,
    showBlock<PERSON>,
    PickerSearch
} from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { translate } from '@translate';
import CheckFilter from '../component/CheckFilter';
import SearchInput from '../component/SearchInput';
import ModalFilter from '../component/ModalFilter';
import ItemCollection from '../component/ItemCollection';
import { bindActionCreators } from "redux";
import * as actionCollectionManagerCreator from "../../CollectionTransferManager/action";
import * as actionSaleOrderCreator from "../../SaleOrderPayment/action";
import * as actionManagerSOCreator from "../../SaleOrderManager/action";
import * as actionCollectionCreator from "../../CollectionTransfer/action";
import moment from 'moment';
import { helper } from '@common';
import TransactionFilter from '../component/TransactionFilter';
import ModalCalendar from '../component/Modal/ModalCalendar';
import * as actionConsumerLoanAirtimeServiceCreator from "../action";
import { BackHeader } from "@header";
const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;
export class PullConsumerLoanPackage extends Component {
    constructor(props) {
        super(props)
        this.state = {
            scrollY: new Animated.Value(0),
            isRegistered: 0,
            isVisibleModalFilter: false,
            fromDate: new Date(),
            toDate: new Date(),
            createdUser: '',
            countFilter: 0,
            isDelete: false,
            isCreate: true,
            IsActive: false,
            keyword: '',
            service: 0,
            isShowCalendar: false,
            catalogID: '',
            serviceGroupID: '',
            isFailedNotDeleted: false
        }
        this.isScrolling = false;
    }

    componentDidMount() {
        const {
            SaleOrderID,
        } = this.props?.route?.params ?? {};
        const { itemCatalog } = this.props;
        const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? '';
        if (SaleOrderID != null) {
            this.setState({ keyword: SaleOrderID });
            this.getListDataCollectionManager({
                keyword: SaleOrderID,
                fromDate: this.state.fromDate,
                toDate: this.state.toDate,
                isCreate: this.state.isCreate,
                isDelete: this.state.isDelete,
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                isFailedNotDeleted: this.state.isFailedNotDeleted

            });
        } else {
            this.getListDataCollectionManager({
                keyword: this.state.keyword,
                fromDate: this.state.fromDate,
                toDate: this.state.toDate,
                isCreate: this.state.isCreate,
                isDelete: this.state.isDelete,
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                isFailedNotDeleted: this.state.isFailedNotDeleted
            });
        }

    }

    componentDidUpdate(preProps, preState) {
        // const { stateSearchCollection } = this.props;
        // if (preProps.stateSearchCollection.isFetching != stateSearchCollection.isFetching && stateSearchCollection.isFetching) {
        //     this.setState({ scrollY: new Animated.Value(0) });
        // }
    }

    getListDataCollectionManager = ({ keyword,
        fromDate,
        toDate,
        isCreate,
        isDelete,
        catalogID,
        serviceGroupID,
        isFailedNotDeleted

    }) => {
        const data = {
            keyword,
            fromDate,
            toDate,
            isCreate,
            isDelete,
            catalogID,
            serviceGroupID,
            isFailedNotDeleted

        }
        this.props.actionConsumerLoanAirtimeService.getSearchConsumerLoanManager(data)
    }

    handleScroll = ({ contentOffset }) => {
        const { y } = contentOffset;
        if (y <= 0) {
            this.setState({ scrollY: new Animated.Value(0) });
        }
    }

    onScrollBegin = () => {
        this.isScrolling = true;
    }

    onScrollEnd = () => {
        this.isScrolling = false;
    }

    renderHeader = () => {
        return (
            <View
                style={{
                    height: 90,
                }}
            />
        );
    };

    getTitleAirtimeList = () => {
        const { AirTimeTransactionTypeName } = this.props.updateHeaderAirtime ?? "";
        const conertTransactionTypeName = AirTimeTransactionTypeName?.toUpperCase();
        return conertTransactionTypeName;
    };

    onAskStatusCollection = (item) => {
        const {
            actionConsumerLoanAirtimeService,
            itemCatalog
        } = this.props;
        const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? '';
        showBlockUI();
        const {
            SERVICEVOUCHERID,
        } = item
        const data = {
            serviceVoucherID: SERVICEVOUCHERID
        }
        actionConsumerLoanAirtimeService.parnerQueryStatusServiceRequest(data).then((reponse) => {
            this.setState({
                keyword: this.state.keyword,
                fromDate: new Date(this.state.fromDate),
                toDate: new Date(this.state.toDate),
                isCreate: this.state.isCreate,
                isDelete: this.state.isDelete,
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                isFailedNotDeleted: this.state.isFailedNotDeleted
            })
            hideBlockUI();
            if (reponse) {

                Alert.alert("", reponse?.airtimetransactionsvmapbo?.Description, [
                    {
                        text: "OK",
                        onPress: () => {
                            this.setState({
                                keyword: SERVICEVOUCHERID
                            });
                            this.getListDataCollectionManager({
                                keyword: SERVICEVOUCHERID,
                                fromDate: new Date(this.state.fromDate),
                                toDate: new Date(this.state.toDate),
                                isCreate: this.state.isCreate,
                                isDelete: this.state.isDelete,
                                catalogID: ServiceCategoryID,
                                serviceGroupID: AirtimeServiceGroupID,
                                isFailedNotDeleted: this.state.isFailedNotDeleted
                            });

                        }
                    }
                ])
            }

        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => this.onAskStatusCollection(item),
                }
            ])
        });

    }

    transactionCancellationWarning = (item) => {
        Alert.alert("", `Bạn có chắc muốn huỷ hồ sơ ${item.SERVICEVOUCHERID} ?`, [
            {
                text: translate('common.btn_skip'),
                onPress: hideBlockUI
            },
            {
                text: translate('common.btn_continue'),
                onPress: () => this.handleDeleteOrder(item),
            }
        ])
    }

    handleDeleteOrder = (item) => {
        const {
            actionConsumerLoanAirtimeService,
            itemCatalog
        } = this.props;
        const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? '';
        showBlockUI();
        const {
            AIRTIMETRANSACTIONTYPEID,
            AIRTIMETRANSACTIONID,
            SERVICEVOUCHERID
        } = item
        const data = {
            airtimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
            airtimeTransactionID: AIRTIMETRANSACTIONID
        }
        actionConsumerLoanAirtimeService.createAirtimeRefundServiceRequest(data).then((reponse) => {
            hideBlockUI();
            this.getListDataCollectionManager({
                keyword: SERVICEVOUCHERID,
                fromDate: new Date(this.state.fromDate),
                toDate: new Date(this.state.toDate),
                isCreate: this.state.isCreate,
                isDelete: this.state.isDelete,
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                isFailedNotDeleted: this.state.isFailedNotDeleted
            });
            this.setState({
                keyword: SERVICEVOUCHERID
            });
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: () => {
                        hideBlockUI()
                    }
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => this.handleDeleteOrder(item),
                }
            ])
        });
    }

    handleAllowerCall = (item) => {
        const {
            actionConsumerLoanAirtimeService,
            itemCatalog
        } = this.props;
        const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? '';
        showBlockUI();
        const {
            SERVICEVOUCHERID
        } = item
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            serviceVoucherID: SERVICEVOUCHERID
        };
        actionConsumerLoanAirtimeService.getProcessServicePrequest(data)
            .then((reponse) => {
                hideBlockUI();
                this.getListDataCollectionManager({
                    keyword: SERVICEVOUCHERID,
                    fromDate: new Date(this.state.fromDate),
                    toDate: new Date(this.state.toDate),
                    isCreate: this.state.isCreate,
                    isDelete: this.state.isDelete,
                    catalogID: ServiceCategoryID,
                    serviceGroupID: AirtimeServiceGroupID,
                    isFailedNotDeleted: this.state.isFailedNotDeleted
                });
                this.setState({
                    keyword: SERVICEVOUCHERID
                });
            })
            .catch((error) => {
                Alert.alert(translate("common.notification_uppercase"), error, [
                    {
                        text: translate("common.btn_close"),
                        onPress: () => {
                            hideBlockUI();
                            this.getListDataCollectionManager({
                                keyword: SERVICEVOUCHERID,
                                fromDate: new Date(this.state.fromDate),
                                toDate: new Date(this.state.toDate),
                                isCreate: this.state.isCreate,
                                isDelete: this.state.isDelete,
                                catalogID: ServiceCategoryID,
                                serviceGroupID: AirtimeServiceGroupID,
                                isFailedNotDeleted: this.state.isFailedNotDeleted
                            });
                            this.setState({
                                keyword: SERVICEVOUCHERID
                            });
                        }
                    },
                ]);
            });

    }

    printPromotionCollection = (item) => {
        const { PROMOTIONSALEORDERID } = item;
        const {
            navigation,
            actionSaleOrder,
            actionManagerSO,
        } = this.props;
        actionSaleOrder.setDataSO({
            SaleOrderID: PROMOTIONSALEORDERID,
            SaleOrderTypeID: 100
        }).then(success => {
            actionManagerSO.getContentTypeReport(PROMOTIONSALEORDERID);
            actionSaleOrder.getReportPrinterSocket(100);
            navigation.navigate("ReprintSaleOrder");
        })
    }

    render() {
        const {
            scrollY,
            isVisibleModalFilter,
            isDelete,
            isCreate,
            IsActive,
            keyword,
            isShowCalendar,
            isFailedNotDeleted,
        } = this.state;
        const diffClamp = Animated.diffClamp(scrollY, 0, DISTANCE);
        const translateY = diffClamp.interpolate({
            inputRange: [0, DISTANCE],
            outputRange: [0, -DISTANCE],
        });
        const {
            dataLoanConsumerManager,
            stateLoanConsumerManager: {
                isFetching,
                isError,
                isEmpty,
                description
            },
            itemCatalog
        } = this.props;
        const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? '';
        return (
            <View style={{
                flex: 1
            }}>
                <BackHeader
                    onGoBack={() => this.props.navigation.navigate("MenuConsumerLoan")}
                    title={this.getTitleAirtimeList()}
                />
                <SafeAreaView
                    style={{ flex: 1, backgroundColor: COLORS.bgF0F0F0 }}>
                    <Animated.View style={{
                        transform: [{ translateY: translateY }],
                        backgroundColor: COLORS.bgF5F5F5,
                        position: 'absolute',
                        top: 0, left: 0, right: 0, zIndex: 2,
                    }}>
                        <View style={{
                            width: constants.width
                        }}>
                            <CheckFilter
                                isDelete={isDelete}
                                isCreate={isCreate}
                                onChangeParam={(value1, value2) => {
                                    if (!this.isScrolling) {
                                        this.setState({
                                            isCreate: value1, isDelete: value2,
                                            scrollY: new Animated.Value(0)
                                        }, () => this.getListDataCollectionManager({
                                            keyword: this.state.keyword,
                                            fromDate: new Date(this.state.fromDate),
                                            toDate: new Date(this.state.toDate),
                                            isCreate: this.state.isCreate,
                                            isDelete: this.state.isDelete,
                                            catalogID: ServiceCategoryID,
                                            serviceGroupID: AirtimeServiceGroupID,
                                            isFailedNotDeleted: this.state.isFailedNotDeleted
                                        }));
                                    }
                                }}
                                disabled={isFetching}
                            />
                            <View style={{ justifyContent: 'center' }}>
                                <TouchableOpacity style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    backgroundColor: COLORS.bgFFFFFF,
                                    borderRadius: 5,
                                    width: '95%',
                                    paddingHorizontal: 5,
                                    borderWidth: 1,
                                    borderColor: COLORS.bdDDDDDD,
                                    height: 44,
                                    alignSelf: 'center',
                                    marginTop: 12
                                }}
                                    onPress={() => this.setState({ isShowCalendar: true })}
                                >
                                    <MyText
                                        style={{
                                            width: '87%',
                                            paddingHorizontal: 5
                                        }}
                                        text={`${moment(this.state.fromDate).format(
                                            'DD/MM/YYYY'
                                        )
                                            } - ${moment(this.state.toDate).format(
                                                'DD/MM/YYYY'
                                            )
                                            } `}
                                    />
                                    <Icon
                                        iconSet="Feather"
                                        name="calendar"
                                        style={{
                                            fontSize: 30,
                                            color: COLORS.ic2C8BD7
                                        }}
                                    />
                                </TouchableOpacity>
                                <View style={{
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    marginTop: 5
                                }}>
                                    <SearchInput
                                        onSubmit={() => this.getListDataCollectionManager({
                                            keyword: this.state.keyword,
                                            fromDate: new Date(this.state.fromDate),
                                            toDate: new Date(this.state.toDate),
                                            isCreate: this.state.isCreate,
                                            isDelete: this.state.isDelete,
                                            catalogID: ServiceCategoryID,
                                            serviceGroupID: AirtimeServiceGroupID,
                                            isFailedNotDeleted: this.state.isFailedNotDeleted
                                        }

                                        )}
                                        inputText={keyword}
                                        onChangeText={(text) => {
                                            this.setState({ keyword: text })
                                        }}
                                        onClearText={() => {
                                            this.setState({ keyword: "" })
                                        }}
                                        style={{
                                            marginTop: 5
                                        }}
                                    />
                                </View>
                            </View>
                        </View>
                    </Animated.View>
                    <BaseLoading
                        isLoading={isFetching}
                        isError={isError}
                        isEmpty={isEmpty}
                        textLoadingError={description}
                        onPressTryAgains={() => this.getListDataCollectionManager({
                            keyword: this.state.keyword,
                            fromDate: new Date(this.state.fromDate),
                            toDate: new Date(this.state.toDate),
                            isCreate: this.state.isCreate,
                            isDelete: this.state.isDelete,
                            catalogID: ServiceCategoryID,
                            serviceGroupID: AirtimeServiceGroupID,
                            isFailedNotDeleted: this.state.isFailedNotDeleted
                        })}
                        content={
                            <View style={{
                            }}>
                                <FlatList
                                    style={{ marginTop: 50 }}
                                    data={dataLoanConsumerManager}
                                    keyExtractor={(item, index) => `${index}`}
                                    renderItem={({ item, index }) => (
                                        <ItemCollection
                                            item={item}
                                            index={index}
                                            onPressDetail={() => this.onPressDetail(item)}
                                            isDelete={isDelete}
                                            IsActive={IsActive}
                                            onAskStatus={() => this.onAskStatusCollection(item)}
                                            onDelete={() => this.transactionCancellationWarning(item)}
                                            handleAllowerCall={() => this.handleAllowerCall(item)}
                                            printPromotion={() => this.printPromotionCollection(item)}
                                        />
                                    )
                                    }
                                    renderSectionHeader={({ section: { title } }) => (<DayTitle title={title} />)}
                                    stickySectionHeadersEnabled={false}
                                    alwaysBounceVertical={false}
                                    bounces={false}
                                    scrollEventThrottle={16}
                                    onMomentumScrollBegin={this.onScrollBegin}
                                    onMomentumScrollEnd={this.onScrollEnd}
                                    ListHeaderComponent={this.renderHeader}
                                />
                            </View>
                        }
                    />
                    <ModalCalendar
                        isVisible={isShowCalendar}
                        hideModal={() => {
                            this.setState({ isShowCalendar: false })
                        }}
                        startDate={this.state.fromDate}
                        endDate={this.state.toDate}
                        isFailedNotDeleted={isFailedNotDeleted}
                        setDate={(day) => {
                            const formattedFromDate = moment(day.startDate).format('YYYY-MM-DD');
                            const formattedToDate = moment(day.endDate).format('YYYY-MM-DD');
                            this.setState({
                                fromDate: formattedFromDate,
                                toDate: formattedToDate,
                            }, () => {
                                this.getListDataCollectionManager({
                                    keyword: this.state.keyword,
                                    fromDate: new Date(day.startDate),
                                    toDate: new Date(day.endDate),
                                    isCreate: this.state.isCreate,
                                    isDelete: this.state.isDelete,
                                    catalogID: ServiceCategoryID,
                                    serviceGroupID: AirtimeServiceGroupID,
                                    isFailedNotDeleted: this.state.isFailedNotDeleted
                                });
                            });
                        }}
                    />
                </SafeAreaView>
            </View>
        )
    }
}

const mapStateToProps = function (state) {
    return {
        userInfo: state.userReducer,
        itemCatalog: state.collectionReducer.itemCatalog,
        dataLoanConsumerManager: state.consumerLoanAirtimeServiceReducer.dataLoanConsumerManager,
        stateLoanConsumerManager: state.consumerLoanAirtimeServiceReducer.stateLoanConsumerManager,
        dataSaleOrder: state.saleOrderPaymentReducer.dataSaleOrder,
        dataSO: state.saleOrderPaymentReducer.dataSO,
        updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionConsumerLoanAirtimeService: bindActionCreators(actionConsumerLoanAirtimeServiceCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(PullConsumerLoanPackage)

const styles = StyleSheet.create({})

const Filter = ({ showFilter, countFilter }) => {
    return (
        <TouchableOpacity
            style={{
                marginRight: 10,
                width: constants.getSize(40),
                alignItems: 'center',
                justifyContent: 'center',
            }}
            onPress={showFilter}
            activeOpacity={0.7}>
            <MyText
                text={countFilter}
                style={{
                    position: 'absolute',
                    zIndex: 99999,
                    bottom: 3,
                    right: 3,
                    overflow: 'hidden',
                    backgroundColor: COLORS.bgFC8926,
                    borderRadius: 8,
                    paddingVertical: 1,
                    paddingHorizontal: 5,
                    fontSize: 12,
                    color: COLORS.txtFFFFFF,
                    fontWeight: 'bold',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}
            />
            <Icon
                iconSet="FontAwesome"
                name="filter"
                style={{
                    color: COLORS.ic2C8BD7,
                    fontSize: 20,
                }}
            />
        </TouchableOpacity>
    )
}

const DayTitle = ({ title }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bg199092,
            padding: 6,
            justifyContent: "center",
        }}>
            <MyText
                text={title}
                style={{
                    color: COLORS.txtFFFFFF,
                    fontWeight: 'bold'
                }}
            />
        </View>
    );
}
