import React, { Component } from "react";
import { SafeAreaView, StyleSheet, Alert, Keyboard, View } from "react-native";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { showBlockUI, hideBlockUI, MyText } from "@components";
import { helper } from "@common";
import { COLORS } from "@styles";
import { translate } from "@translate";
import { constants } from "@constants";
import * as actionConsumerLoanAirtimeServiceCreator from "../action";
import * as actionShoppingCartCreator from "../../ShoppingCart/action";
import OtpCode from "../../ShoppingCart/component/InstallmentOTP/component/OtpCode";

class OTPConsumerAirtimeService extends Component {
  constructor() {
    super();
    this.state = {
      info: {
        customerPhone: "",
        customerName: "",
      },
      expireTime: 0,
      otpCode: "",
      isVisible: false,
    };
    this.intervalId = null;
    this.onlySms = false;
    this.isCall = false;
  }

  componentDidMount() { }

  componentWillUnmount() { }

  render() {
    const { info, expireTime, otpCode } = this.state;
    const { dataUpdateCustomer } = this.props;
    return (
      <KeyboardAwareScrollView
        style={{
          flex: 1,
          backgroundColor: COLORS.bgFFFFFF,
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps="always"
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}
      >
        <SafeAreaView
          style={{
            flex: 1,
          }}
        >
          <Guide />
          <Customer info={dataUpdateCustomer} />
          <OtpCode
            onCreate={this.onCreateOTP}
            expireTime={expireTime}
            code={otpCode}
            onChange={(text) => {
              const regExpOTP = new RegExp(/^\d{0,4}$/);
              const isValidate = regExpOTP.test(text);
              if (isValidate) {
                this.setState({ otpCode: text });
              }
            }}
            onVerify={this.onCheckOTP}
            onlySms={this.onlySms}
          />
        </SafeAreaView>
      </KeyboardAwareScrollView>
    );
  }

  countDown = () => {
    const { expireTime } = this.state;
    const second = expireTime - 1;
    if (second > 0) {
      this.setState({ expireTime: second });
    } else {
      this.resetCountDown();
    }
  };

  resetCountDown = () => {
    if (!this.isCall) {
      this.onlySms = true;
    }
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    this.setState({
      expireTime: 0,
      otpCode: "",
    });
  };

  setCountDown = () => {
    this.setState({
      expireTime: 60,
    });
    this.intervalId = setInterval(this.countDown, 1000);
  };

  onCreateOTP = (type) => {
    const { dataUpdateCustomer } = this.props;
    const { customerPhone } = dataUpdateCustomer ?? "";
    const {
      userInfo: { brandID },
    } = this.props;
    this.isCall = type == "CALLCENTER";
    // showBlockUI();
    actionShoppingCartCreator
      .createOTP({
        type: type,
        phoneNumber: customerPhone,
        typeContent: "CASHLOAN",
        lenOtp: 4,
        brandID: brandID,
        onlySms: this.onlySms,
      })
      .then((isSMS) => {
        if (!this.isCall) {
          this.onlySms = isSMS;
        }
        hideBlockUI();
        this.setCountDown();
      })
      .catch((msgError) => {
        this.setCountDown();
        // Alert.alert(translate("common.notification_uppercase"), msgError, [
        //   {
        //     text: translate("common.btn_skip"),
        //     style: "cancel",
        //     onPress: hideBlockUI,
        //   },
        //   {
        //     text: translate("common.btn_notify_try_again"),
        //     style: "default",
        //     onPress: () => this.onCreateOTP(type),
        //   },
        // ]);
      });
  };

  onCheckOTP = () => {
    Keyboard.dismiss();
    const { otpCode } = this.state;
    const { dataUpdateCustomer } = this.props;
    const { customerPhone } = dataUpdateCustomer ?? "";
    const isValidate = this.validateOTP(otpCode);
    if (isValidate) {
      this.verifyOTP(otpCode, customerPhone);
    }
  };

  verifyOTP = (otpCode, customerPhone) => {
    showBlockUI();
    actionShoppingCartCreator
      .verifyOTP(otpCode, customerPhone)
      .then((data) => {
        this.getDataCreateServiceRequest();
      })
      .catch((msgError) => {
        this.getDataCreateServiceRequest();
      });
  };

  validateOTP = (code) => {
    const regExpOTP = new RegExp(/^\d{4}$/);
    const isValidate = regExpOTP.test(code);
    if (!helper.IsNonEmptyString(code)) {
      Alert.alert("", translate("shoppingCart.validate_empty_otp"));
      return false;
    }
    if (!isValidate) {
      Alert.alert("", translate("shoppingCart.validate_otp"));
      return false;
    }
    return true;
  };

  processServiceRequest = (reponse) => {
    const {
      itemCatalog,
      actionConsumerLoanAirtimeService,
      navigation,
      updateHeaderAirtime,
    } = this.props;
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? "";
    const { ServiceVoucherID } = reponse?.object?.cus_AirtimeTransactionSVMapBO;
    const { AirTimeTransactionTypeID } = updateHeaderAirtime ?? "";
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      serviceVoucherID: ServiceVoucherID,
    };
    actionConsumerLoanAirtimeService
      .getProcessServicePrequest(data)
      .then((reponse) => {
        hideBlockUI();
        if (AirTimeTransactionTypeID == 1972) {
          navigation.navigate("WebViewScreen");
        } else if (AirTimeTransactionTypeID == 1992) { 
          navigation.navigate("QrCodeCathayLife");
        } else if (AirTimeTransactionTypeID == 2192) {
          Alert.alert(translate("common.notification_uppercase"), `Tạo giao dịch thành công \n(${reponse?.object?.cus_AirtimeTransactionSVMapBO?.Description})`, [
            {
              text: translate("common.btn_accept"),
              style: "cancel",
              onPress: () => {
                navigation.navigate("PullConsumerLoanPackage", {
                  SaleOrderID: ServiceVoucherID,
                })
              },
            }
          ]);
        }
      })
      .catch((error) => {
        Alert.alert(translate("common.notification_uppercase"), error, [
          {
            text: translate("common.btn_close"),
            onPress: () => {
              hideBlockUI();
              navigation.navigate("PullConsumerLoanPackage", {
                SaleOrderID: ServiceVoucherID,
              });
            },
          },
        ]);
      });
  };

  getDataCreateServiceRequest = () => {
    const {
      itemCatalog,
      updateHeaderAirtime,
      dataUpdateCustomer,
      actionConsumerLoanAirtimeService,
      dataCouponConsumerLoan,
    } = this.props;
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? "";
    const { AirTimeTransactionTypeID } = updateHeaderAirtime ?? "";
    const {
      customerPhone,
      customerName,
      customerIdCard,
      customerEmail,
      dateOfBirth,
      loanAmount,
      monthlyIncome,
      companyName
    } = dataUpdateCustomer ?? "";
    const promotion = dataCouponConsumerLoan?.PromotionInfo ?? "";
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimeTransactionTypeID: AirTimeTransactionTypeID,
      airTimeTransactionBO: {
        customerName: customerName,
        customerPhone: customerPhone,
        PromotionPlanCustomer: promotion,
        ExtraData: {
          PromotionListGroupID: promotion?.PromotionListGroupID,
        },
      },
      ExtraData: {
        customerIDCard: customerIdCard,
        customerEmail: customerEmail,
        dateOfBirth: dateOfBirth,
        loanAmount: loanAmount,
        monthlyIncome: monthlyIncome,
        companyName: companyName
      },
    };
    actionConsumerLoanAirtimeService
      .getDataCreateServiceRequest(data)
      .then((reponse) => {
        this.processServiceRequest(reponse);
      })
      .catch((err) => {
        Alert.alert(translate("common.notification_uppercase"), err, [
          {
            text: translate("saleExpress.retry"),
            style: "cancel",
            onPress: () => this.getDataCreateServiceRequest(),
          },
          {
            text: translate("common.btn_skip"),
            style: "cancel",
            onPress: hideBlockUI,
          },
        ]);
      });
  };
}

const mapStateToProps = function (state) {
  return {
    itemCatalog: state.collectionReducer.itemCatalog,
    dataUpdateCustomer:
      state.consumerLoanAirtimeServiceReducer.dataUpdateCustomer,
    dataCreateOTP: state.consumerLoanAirtimeServiceReducer.dataCreateOTP,
    updateHeaderAirtime:
      state.consumerLoanAirtimeServiceReducer.updateHeaderAirtime,
    dataCreateServiceRequest:
      state.consumerLoanAirtimeServiceReducer.dataCreateServiceRequest,
    userInfo: state.userReducer,
    dataCouponConsumerLoan:
      state.consumerLoanAirtimeServiceReducer.dataCouponConsumerLoan,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionConsumerLoanAirtimeService: bindActionCreators(
      actionConsumerLoanAirtimeServiceCreator,
      dispatch
    ),
    actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(OTPConsumerAirtimeService);

const styles = StyleSheet.create({});

const Title = ({ content }) => {
  return (
    <View
      style={{
        position: "absolute",
        top: -10,
        width: constants.width - 20,
        justifyContent: "center",
        alignItems: "center",
        height: 20,
      }}
    >
      <View
        style={{
          backgroundColor: COLORS.bgFFFFFF,
          paddingHorizontal: 10,
          height: 20,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <MyText
          style={{
            color: COLORS.txtFF7600,
            fontWeight: "bold",
          }}
          text={content}
        />
      </View>
    </View>
  );
};

const Customer = ({ info }) => {
  const { customerName, customerPhone } = info;
  return (
    <View
      style={{
        width: constants.width - 20,
        padding: 12,
        borderWidth: 1,
        borderColor: COLORS.bd2FB47C,
        borderRadius: 4,
        justifyContent: "center",
        alignSelf: "center",
      }}
    >
      <Title content={translate("editSaleOrder.customer_info_uppercase")} />
      <FieldText
        name={translate("editSaleOrder.ft_name")}
        value={customerName}
      />
      <FieldText
        name={translate("editSaleOrder.ft_phone")}
        value={customerPhone}
      />
    </View>
  );
};

const FieldTextInformation = ({ content }) => {
  return (
    <MyText
      style={{
        color: COLORS.txt333333,
        marginBottom: 4,
      }}
      text={content}
    />
  );
};

const FieldText = ({ name, value }) => {
  return (
    <View
      style={{
        width: constants.width - 40,
        flexDirection: "row",
        marginTop: 4,
      }}
    >
      <MyText
        style={{
          color: COLORS.txt333333,
          width: 100,
        }}
        text={name}
      />
      <MyText
        style={{
          fontWeight: "bold",
          color: COLORS.txt434343,
          width: constants.width - 140,
        }}
        text={value}
      />
    </View>
  );
};

const Guide = () => {
  return (
    <View
      style={{
        width: constants.width,
        padding: 10,
      }}
    >
      <MyText
        style={{
          color: COLORS.txt333333,
          fontStyle: "italic",
          fontWeight: "bold",
          marginBottom: 2,
        }}
        text={translate("editSaleOrder.guide")}
      />

      <FieldTextInformation
        content={
          "Mã OTP này dùng để xác thực việc khách hàng đã đồng ý ủy quyền cho TGDĐ thu thập và chuyển giao thông tin cho công ty tài chính để làm hồ sơ gói vay tiêu dùng."
        }
      />
    </View>
  );
};
