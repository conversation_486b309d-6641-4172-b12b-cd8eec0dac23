import { API_CONST } from '@constants';
import { apiBase, METHOD } from '@config';
import { helper } from '@common';

const {
    API_SEARCH_REQUEST_RETURN,
    API_UPDATE_REQUEST_RETURN,
    API_LOAD_INFO_REQUEST_RETURN,
    API_EDIT_REQUEST_RETURN
} = API_CONST;

export const searchRequestReturn = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_SEARCH_REQUEST_RETURN, METHOD.POST, body)
            .then((response) => {
                console.log('searchRequestReturn success', response);
                if (helper.IsNonEmptyArray(response.object?.errInfoBOs)) {
                    response.object?.errInfoBOs.sort(
                        (a, b) => b.cratedDate - a.cratedDate
                    );
                    resolve(response.object.errInfoBOs);
                } else {
                    reject('Không tìm thấy phiếu yêu cầu đổi trả.');
                }
            })
            .catch((error) => {
                console.log('searchRequestReturn error', error);
                reject(error.msgError);
            });
    });
};
export const getInfoRequestReturn = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_LOAD_INFO_REQUEST_RETURN, METHOD.POST, body)
            .then((response) => {
                console.log('getInfoRequestReturn success', response);
                if (
                    !helper.IsEmptyObject(response.object?.exchReturnRequestBO)
                ) {
                    resolve(response.object.exchReturnRequestBO);
                } else {
                    reject('Không lấy được thông tin phiếu đổi trả.');
                }
            })
            .catch((error) => {
                console.log('getInfoRequestReturn error', error);
                reject(error.msgError);
            });
    });
};
export const updateRequestReturn = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_UPDATE_REQUEST_RETURN, METHOD.POST, body)
            .then((response) => {
                console.log('updateRequestReturn success', response);
                resolve(true);
            })
            .catch((error) => {
                console.log('updateRequestReturn error', error);
                reject(error);
            });
    });
};

export const editRequestReturn = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_EDIT_REQUEST_RETURN, METHOD.POST, body)
            .then((response) => {
                console.log('editRequestReturn success', response);
                resolve(true);
            })
            .catch((error) => {
                console.log('editRequestReturn error', error);
                reject(error);
            });
    });
};

