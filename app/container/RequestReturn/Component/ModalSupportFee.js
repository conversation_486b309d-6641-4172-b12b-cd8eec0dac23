import {
    Alert,
    Image,
    Keyboard,
    KeyboardAvoidingView,
    Linking,
    Modal,
    Platform,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { Button, RadioButton, FieldNumberInput, Icon, MyText, BouncyCheckboxCustom, ImageUploader } from '@components';
import { helper } from '@common';
import LottieView from 'lottie-react-native';
import { material_wave_loading } from '@animation';
import { useSelector } from 'react-redux';
import { translate } from '@translate';

const { CONTRACT_PROCESS_TYPE: { SETTLED, NOT_SETTLED } } = constants

const ModalSupportFee = ({
    isShow,
    confirm,
    detailRequest,
    reject,
    isBlockModal,
    hideModal = () => { }
}) => {
    const [requestInfo, setRequestInfo] = useState({});
    const [productName, setProductName] = useState('');
    const [isRetrievalInvoice, setIsRetrievalInvoice] = useState(requestInfo?.saleOrderBO?.isRetrievalInvoice ?? false);
    const [retrievalInvoiceDisable, setRetrievalInvoiceDisable] = useState(false);
    const [totalAfterAdjust, setTotalAfterAdjust] = useState(0);
    const [attachmentBOs, setAttachmentBOs] = useState([])
    const [dataType, setDataType] = useState([
        {
            title: "Khách hàng đã thanh lý hợp đồng",
            selected: false,
            type: SETTLED
        },
        {
            title: "Khách hàng đã hủy hợp đồng hoặc trả hàng không thanh lý ",
            selected: false,
            type: NOT_SETTLED
        }
    ])

    const scrollRef = useRef(null)
    const uploaderRef = useRef(null);

    const netInfo = useSelector((_state) => _state.networkReducer)
    const { storeID, userName } = useSelector((state) => state.userReducer);

    const isShowFeeBOs = helper.IsNonEmptyArray(requestInfo?.feeBOs)
    const isHasInstallment = requestInfo?.saleOrderBO?.shouldShowContractOptions == 1

    const { speed } = netInfo;
    const onChange = (item, id) => (value) => {
        const newRequestInfo = { ...requestInfo };
        if (helper.IsEmptyArray(newRequestInfo.feeBOs)) {
            return;
        }
        for (let index = 0; index < newRequestInfo.feeBOs.length; index++) {
            if (newRequestInfo.feeBOs[index].exchangeFeeTypeId == id) {
                for (
                    let _index = 0;
                    _index < newRequestInfo.feeBOs[index].feeDetailBOs?.length;
                    _index++
                ) {
                    if (
                        newRequestInfo.feeBOs[index].feeDetailBOs?.[_index]
                            ?.id ==
                        item.id
                    ) {
                        newRequestInfo.feeBOs[index].feeDetailBOs[
                            _index
                        ].ajustValue = value;
                        break;
                    }
                }
            }
        }
        setRequestInfo(newRequestInfo);
    };
    // const onSelectFeeBO = (item, id) => () => {
    //     const newRequestInfo = { ...requestInfo };
    //     if (helper.IsEmptyArray(newRequestInfo.feeBOs)) {
    //         return;
    //     }
    //     for (let index = 0; index < newRequestInfo.feeBOs.length; index++) {
    //         if (newRequestInfo.feeBOs[index].exchangeFeeTypeId == id) {
    //             for (
    //                 let _index = 0;
    //                 _index < newRequestInfo.feeBOs[index].feeDetailBOs?.length;
    //                 _index++
    //             ) {
    //                 if (
    //                     newRequestInfo.feeBOs[index].feeDetailBOs?.[_index]
    //                         ?.id ==
    //                     item.id
    //                 ) {
    //                     newRequestInfo.feeBOs[index].feeDetailBOs[
    //                         _index
    //                     ].isSelected_UI =
    //                         !newRequestInfo.feeBOs[index].feeDetailBOs[_index]
    //                             .isSelected_UI;
    //                     break;
    //                 }
    //             }
    //         }
    //     }
    //     setRequestInfo(newRequestInfo);
    // };
    useEffect(() => {
        if (helper.IsEmptyObject(detailRequest)) {
            return;
        }
        setRequestInfo(detailRequest);
        setIsRetrievalInvoice(detailRequest?.saleOrderBO?.isRetrievalInvoice ?? false)
        setRetrievalInvoiceDisable(detailRequest?.saleOrderBO?.isRetrievalInvoice)
        const mainProduct = detailRequest.returnProductBOs?.find(
            (item) => item.saleItemTypeId == 1
        );
        setProductName(mainProduct.productName);
    }, [detailRequest]);

    useEffect(() => {
        if (helper.IsEmptyObject(requestInfo)) {
            return;
        }
        let newFeeDetailBOs = [];
        requestInfo.feeBOs?.forEach((element) => {
            const isDisabled = isRetrievalInvoice && element.exchangeFeeTypeId === 2
            if (isDisabled) {
                return;
            }
            if (helper.IsNonEmptyArray(element.feeDetailBOs)) {
                newFeeDetailBOs = [...newFeeDetailBOs, ...element.feeDetailBOs];
            }
        });
        const total =
            newFeeDetailBOs?.reduce((accumulator, currentValue) => {
                // const newFeeAdjust = currentValue.isSelected_UI
                //     ? currentValue.ajustValue
                //     : 0;
                const newFeeAdjust = currentValue.ajustValue ?? 0

                const value = currentValue.isReturn ? 0 : currentValue.value
                return (
                    accumulator + (value - newFeeAdjust)
                );
            }, 0) ?? 0;
        setTotalAfterAdjust(total);
    }, [requestInfo, isRetrievalInvoice]);

    const onCallToCustomer = () => {

        if (!helper.IsNonEmptyString(requestInfo?.saleOrderBO?.customerPhone)) {
            return Alert.alert(
                '',
                'Đơn hàng không có số điện thoại khách hàng.'
            );
        }
        const url = `xfone://${requestInfo?.saleOrderBO?.customerPhone}`;
        Linking.openURL(url).catch(() => {
            Alert.alert('', 'Vui lòng cài app Xfone trước khi gọi.');
        });
    };

    const onChangeRetrievalInvoice = () => {
        setIsRetrievalInvoice(!isRetrievalInvoice);
    }
    const onConfirm = () => {
        const typeContract = dataType.find(item => item.selected)?.type || null;

        if (isRetrievalInvoice && attachmentBOs?.length === 0) {
            Alert.alert('', 'Vui lòng chụp hình bổ sung.');
            return scrollToUploader();
        }

        if (isHasInstallment && !typeContract) {
            Alert.alert('', 'Vui lòng chọn trạng thái hợp đồng.');
            return;
        }

        const updatedRequestInfo = {
            ...requestInfo,
            saleOrderBO: {
                ...requestInfo.saleOrderBO,
                isRetrievalInvoice,
                contractProcessType: typeContract,
            },
            attachmentBOs,
        };

        if (isHasInstallment && typeContract === NOT_SETTLED) {
            Alert.alert(
                '',
                'HỢP ĐỒNG CỦA KHÁCH HÀNG ĐÃ HỦY HOẶC CHƯA THANH LÝ, HỆ THỐNG CHỈ CHI RA TỐI ĐA SỐ TIỀN TRẢ TRƯỚC CỦA KHÁCH HÀNG SAU KHI TRỪ ĐI CÁC KHOẢN CHI PHÍ PHÁT SINH NẾU CÓ.',
                [
                    { text: 'NO', style: 'cancel' },
                    {
                        text: 'YES',
                        style: 'default',
                        onPress: () => {
                            checkAdjustFeeBeforeConfirm(updatedRequestInfo);
                        },
                    },
                ]
            );
            return;
        }

        checkAdjustFeeBeforeConfirm(updatedRequestInfo);
    };

    const checkAdjustFeeBeforeConfirm = (info) => {
        if (requestInfo?.TotalFee === totalAfterAdjust) {
            Alert.alert(
                'THÔNG BÁO',
                'Bạn chưa nhập thông tin điều chỉnh giảm phí. Bạn có muốn tiếp tục không?',
                [
                    { text: 'NO', style: 'cancel' },
                    { text: 'YES', style: 'default', onPress: () => confirm(info)() },
                ]
            );
        } else {
            confirm(info)();
        }
    };



    const handleSelectItem = (index) => {
        const newDataType = dataType.map((item, i) => ({
            ...item,
            selected: i === index,
        }));
        setDataType(newDataType);
    };

    const scrollToUploader = () => {
        uploaderRef.current?.measureLayout(
            scrollRef.current,
            (x, y) => {
                scrollRef.current?.scrollTo({ y, animated: true });
            },
            (error) => {
                console.log('measureLayout error:', error);
            }
        );
    };

    const renderItem = (item) => (
        <MyText
            text={item.title}
            style={{ marginLeft: 2 }}
        />
    );


    return (
        <View   >
            <Modal
                visible={isShow}
                animationType="fade"
                transparent
                onRequestClose={hideModal}
            >
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    height="100%"
                >
                    <SafeAreaView
                        style={{
                            backgroundColor: 'white',
                            flex: 1,
                            alignItems: 'center',

                        }}>
                        {/* <View style={styles.header}>
                            <Text style={styles.headerText}>
                                {`Phiếu ${detailRequest.errCode}`}
                            </Text>
                            <View style={{ flex: 1 }} />
                        </View> */}
                        <View style={styles.header}>
                            <View style={{ flex: 1, alignItems: 'flex-start', flexDirection: "row", }}>
                                <TouchableOpacity
                                    touchOpacity={0.6}
                                    onPress={hideModal}>
                                    <Icon
                                        iconSet="Ionicons"
                                        name="chevron-back"
                                        color={"#EE4D2D"}
                                        size={30}
                                    />
                                </TouchableOpacity>
                                <View>
                                    <Text style={styles.headerText}>
                                        {`Phiếu ${detailRequest.errCode}`}
                                    </Text>
                                    <View style={{ marginLeft: 5 }}>
                                        <View style={{
                                            flexDirection: "row",
                                            alignItems: "center"
                                        }}>
                                            <Image
                                                style={{ width: 11.5, height: 11.5 }}
                                                source={{ uri: "logo_tgdd" }}
                                            />
                                            <MyText
                                                style={{
                                                    color: "#EE4D2D",
                                                    marginTop: 2
                                                }}
                                                text={` ${translate('common.store')} ${storeID} - User ${userName}`}
                                                addSize={-2}
                                            />

                                        </View>
                                    </View>
                                </View>
                            </View>
                            {/* <View style={{ flex: 1 }} /> */}
                            <View style={{
                                // height: 54,
                                // width: 50,
                                justifyContent: "center",
                                alignItems: "center",
                            }}>
                                <Icon
                                    iconSet={"MaterialCommunityIcons"}
                                    name={"signal-cellular-outline"}
                                    color={"#EE4D2D"}
                                    size={24}
                                />
                                <MyText
                                    style={{
                                        color: "#EE4D2D",
                                        fontSize: 10
                                    }}
                                    text={speed}
                                    children={<MyText
                                        style={{
                                            color: "#EE4D2D",
                                            fontSize: 10
                                        }}
                                        text={" KB/s"}
                                    />}
                                />
                            </View>
                        </View>

                        <View style={styles.headerShadow} />
                        <ScrollView
                            ref={scrollRef}
                            showsVerticalScrollIndicator={false}
                            style={{
                                flex: 1,
                                width: constants.width - 20,
                                marginVertical: 10
                            }}>
                            <TouchableOpacity
                                onPress={onCallToCustomer}
                                activeOpacity={0.5}
                                style={{ paddingBottom: 10, flexDirection: "row", alignItems: "center" }}>
                                <MyText
                                    style={{}}
                                    text="Gọi điện thoại cho khách hàng: "
                                />
                                <Icon
                                    iconSet="MaterialIcons"
                                    name="phonelink-ring"
                                    color={COLORS.txt147EFB}
                                    size={20}
                                />

                            </TouchableOpacity>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    flexWrap: 'wrap',
                                    justifyContent: 'space-between',
                                    paddingBottom: 7
                                }}>
                                <MyText text={productName} />
                                <MyText text="Tổng phí:    ">
                                    <MyText
                                        text={helper.convertNum(
                                            requestInfo?.TotalFee || 0
                                        )}
                                    />
                                </MyText>
                            </View>
                            {helper.IsNonEmptyString(requestInfo?.saleOrderBO?.contractStatusNote) &&
                                <View
                                    style={styles.noteContainer}>
                                    <MyText
                                        text={requestInfo?.saleOrderBO?.contractStatusNote}
                                        style={{ color: COLORS.bg1E88E5 }}
                                    />
                                </View>
                            }
                            {requestInfo?.saleOrderBO?.taxId &&
                                <BouncyCheckboxCustom
                                    isChecked={isRetrievalInvoice}
                                    onToggle={retrievalInvoiceDisable ? () => { } : onChangeRetrievalInvoice}
                                    label="Biên bản trả hàng hợp lệ"
                                />
                            }
                            {helper.IsNonEmptyString(requestInfo?.saleOrderBO?.retrievalInvoiceNote) &&
                                <View
                                    style={styles.noteContainer}>
                                    <MyText
                                        text={requestInfo?.saleOrderBO?.retrievalInvoiceNote}
                                        style={{ color: COLORS.bg199092 }}
                                    />
                                </View>
                            }
                            {
                                isHasInstallment && <View style={styles.wrapper_radio}>
                                    <View>
                                        <MyText style={styles.label} text={"XỬ LÝ HỢP ĐỒNG TRẢ GÓP/ PHÍ ĐỔI TRẢ"} >
                                            <MyText addSize={2} style={{ color: COLORS.bgEA1D5D }} text={"*"} >
                                            </MyText>
                                        </MyText>
                                    </View>
                                    <RadioButton
                                        style={{


                                            width: constants.width - 40
                                        }}
                                        containerStyle={{
                                            alignItems: 'center',
                                            paddingVertical: 3

                                        }}
                                        dataItems={dataType}
                                        selectItem={handleSelectItem}
                                        mainComponent={renderItem}
                                    />
                                </View>

                            }


                            <View
                                style={{
                                    flexDirection: 'row',
                                    flexWrap: 'wrap',
                                    justifyContent: 'flex-end',
                                    borderBottomWidth: 1,
                                    borderBottomColor: COLORS.bgD1D3D8,
                                    paddingVertical: 7
                                }}>
                                <MyText style={{ fontWeight: "bold" }} text="Tổng phí sau điều chỉnh:    ">
                                    <MyText
                                        style={{ color: 'red', fontWeight: 'bold' }}
                                        text={helper.convertNum(totalAfterAdjust)}
                                    />
                                </MyText>
                            </View>
                            {isShowFeeBOs &&
                                requestInfo.feeBOs.map(
                                    (
                                        {
                                            feeDetailBOs,
                                            feeName,
                                            exchangeFeeTypeId
                                        },
                                        index
                                    ) => {
                                        const isDisabled = isRetrievalInvoice && exchangeFeeTypeId === 2
                                        return (
                                            <View
                                                key={index}
                                                style={{
                                                    paddingTop: 9, opacity: isDisabled ? 0.2 : 1
                                                }}>
                                                <MyText
                                                    addSize={3}
                                                    style={{
                                                        fontWeight: 'bold',
                                                        color: COLORS.bg000000
                                                    }}
                                                    text={`${index + 1
                                                        }. ${feeName}`}
                                                />
                                                {helper.IsNonEmptyArray(
                                                    feeDetailBOs
                                                ) &&
                                                    feeDetailBOs.map(
                                                        (feeBO, index) => {
                                                            return (
                                                                <FeeItem
                                                                    // onSelectFeeBO={onSelectFeeBO(
                                                                    //     feeBO,
                                                                    //     exchangeFeeTypeId
                                                                    // )}
                                                                    onSelectFeeBO={() => { }}
                                                                    key={index}
                                                                    feeBO={feeBO}
                                                                    onBlur={() => { }}
                                                                    onChange={onChange(
                                                                        feeBO,
                                                                        exchangeFeeTypeId
                                                                    )}
                                                                    exchangeFeeTypeId={exchangeFeeTypeId}
                                                                    isRetrievalInvoice={isRetrievalInvoice}
                                                                />
                                                            );
                                                        }
                                                    )}
                                            </View>
                                        );
                                    }
                                )}

                            {
                                isRetrievalInvoice && <View ref={uploaderRef}>
                                    <ImageUploader
                                        maxImages={2}
                                        onImagesChange={(images) => {
                                            scrollToUploader();
                                            setAttachmentBOs(
                                                images.map((image) => ({
                                                    attachmentType: 1,
                                                    filePath: image.url,
                                                    fileName: "",
                                                    description: ""
                                                }))
                                            );

                                        }}
                                        buttonText="Chụp hình biên bản trả hàng"
                                    />
                                </View>

                            }
                        </ScrollView>

                        <View style={styles.bottomBar}>
                            <Button
                                onPress={onConfirm}
                                text="Xác nhận"
                                styleContainer={[
                                    {
                                        borderRadius: constants.getSize(10),
                                        height: constants.getSize(40),
                                        marginHorizontal: 15,
                                        width: '40%',
                                        backgroundColor: "#EE4D2D",
                                        borderWidth: 1,
                                        borderColor: "#EE4D2D"
                                    }
                                ]}
                                styleText={{
                                    fontSize: 14,
                                    fontWeight: 'bold',
                                    color: COLORS.bgFFFFFF
                                }}
                            />
                            <Button
                                onPress={reject}
                                text="Bỏ qua"
                                styleContainer={[
                                    {
                                        borderRadius: constants.getSize(10),
                                        height: constants.getSize(40),
                                        marginHorizontal: 15,
                                        width: '40%',
                                        backgroundColor: COLORS.bgFFFFFF,
                                        borderWidth: 1,
                                        borderColor: "#EE4D2D"
                                    }
                                ]}
                                styleText={{
                                    fontSize: 14,
                                    fontWeight: 'bold',
                                    color: "#EE4D2D"
                                }}
                            />
                        </View>
                    </SafeAreaView>

                    <Indicator visible={isBlockModal} />
                </KeyboardAvoidingView>
            </Modal>
        </View>
    );
};

export default ModalSupportFee;

const styles = StyleSheet.create({
    bottomBar: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center'
    },

    header: {
        alignItems: 'center',
        borderBottomColor: COLORS.bgD1D3D8,
        borderBottomWidth: 1,
        elevation: 5,
        flexDirection: 'row',
        padding: 8,
        backgroundColor: "white"
    },
    headerShadow: {
        backgroundColor: 'lightgrey',
        elevation: 4
    },
    headerText: {
        color: "#EE4D2D",
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
        textAlignVertical: 'center'
    },
    noteContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        paddingBottom: 7
    },
    checkboxText: {
        textAlignVertical: 'center',
    },
    wrapper_radio: {
        flex: 1,
        justifyContent: 'center',
        paddingVertical:
            constants.getSize(8),
        marginBottom: constants.getSize(10),
        gap: 5
    },
    label: {
        fontWeight: "bold"
    }
});

const FeeItem = ({ feeBO, onBlur, onSelectFeeBO, onChange, exchangeFeeTypeId, isRetrievalInvoice }) => {
    const { name, value, isSelected_UI, ajustValue = 0, isReturn } =
        feeBO;
    const isDisabled = isRetrievalInvoice && exchangeFeeTypeId === 2
    const editable = value > 0 && !isDisabled

    useEffect(() => {
        if (isDisabled) {
            onChange(0);
        }
    }, [isRetrievalInvoice])

    return (
        <>
            {
                isReturn ? null :
                    <View
                        style={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            paddingTop: 9,
                            borderBottomWidth: 1,
                            borderColor: COLORS.bdE4E4E4,
                            justifyContent: "center",
                        }}>
                        {/* <TouchableOpacity disabled={value <= 0} onPress={onSelectFeeBO} style={{ width: '8%' }}>
                            <Icon
                                style={{ marginVertical: 3 }}
                                iconSet="MaterialCommunityIcons"
                                name={
                                    isSelected_UI
                                        ? 'checkbox-marked'
                                        : 'checkbox-blank-outline'
                                }
                                color={value <= 0 ? COLORS.bgE0E0E0 : "#EE4D2D"}
                                size={22}
                            />
                        </TouchableOpacity> */}
                        <View style={{
                            width: '90%',
                        }}>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    marginVertical: 5,
                                    flex: 1
                                }}>
                                <View style={{ flex: 0.7 }}>
                                    <MyText style={{ fontWeight: "bold", color: "#EE4D2D" }} text={`${exchangeFeeTypeId != 3 ? "Giá trị " : ""}${exchangeFeeTypeId != 3 ? name?.toLowerCase() : name || ""}`} />
                                </View>
                                <View style={{ alignItems: "flex-end", flex: 0.3, }}>
                                    <MyText style={{
                                        alignItems: "flex-end", justifyContent: "flex-end",
                                    }} text={helper.convertNum(value)} />
                                </View>

                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    marginVertical: 5,
                                    alignItems: 'center'
                                }}>
                                <MyText style={{ fontWeight: "bold" }} text="Điều chỉnh phí giảm" />
                                <View
                                    style={{
                                        minHeight: 30,
                                        justifyContent: 'center',
                                        alignItems: 'flex-end',
                                        width: 140,
                                        borderWidth: 1,
                                        borderColor: COLORS.bgE0E0E0,
                                        backgroundColor: COLORS.bgFFFFFF
                                    }}>
                                    <FieldNumberInput
                                        styleInput={{
                                            backgroundColor: COLORS.bgFFFFFF,
                                            paddingHorizontal: 9,
                                        }}
                                        blurOnSubmit
                                        onSubmitEditting={Keyboard.dismiss}
                                        placeholder="0"
                                        value={String(ajustValue || 0)}
                                        keyboardType={"numeric"}
                                        returnKeyType="done"
                                        onChangeText={onChange}
                                        width={130}
                                        clearText={() => {
                                            onChange(0);
                                        }}
                                        editable={editable}
                                        onBlur={onBlur}
                                        maxValue={value}
                                    />
                                </View>
                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    marginVertical: 5
                                }}>
                                <MyText style={{ fontWeight: "bold" }} text="Phí sau điểu chỉnh" />
                                <MyText
                                    style={{ color: 'red', fontWeight: "bold" }}
                                    text={helper.convertNum(value - ajustValue || 0)}
                                />
                            </View>
                        </View>
                    </View>
            }
        </>

    );
};

const Indicator = ({ visible }) => (
    <View
        // eslint-disable-next-line react-native/no-color-literals
        style={{
            flex: 1,
            backgroundColor: '#00000033',
            zIndex: visible ? 999999 : -999999,
            position: 'absolute',
            left: 0,
            top: 0,
            width: visible ? '100%' : 0,
            height: visible ? constants.height : 0
        }}>
        {!visible ? null : (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                <LottieView
                    autoPlay
                    source={material_wave_loading}
                    style={{
                        height: 100,
                        width: 100
                    }}
                />
            </View>
        )}
    </View>
);

