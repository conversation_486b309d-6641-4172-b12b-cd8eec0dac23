import { API_CONST } from "@constants";
import { helper, dateH<PERSON>per } from "@common";
import { apiBase, METHOD, ERROR, EMPTY, SUCCESS } from "@config";
import { translate } from "@translate";

const {
    API_GET_PRODUCT_LIST,
    API_GET_PRODUCT_LIST_PACK_OF_DATA,
    API_GET_PRICE_SERVICE,
    API_GET_CREATE_SERVICE_REQUEST,
    API_QUERY_STATUS_SERVICE_REQUEST,
    API_GET_PRICE_AND_FEE_POSTPAID,
    API_GET_PROCESS_SERVICE_REQUEST,
    API_GET_TRANSACTION_DETAIL,
    API_GET_SERVICE_LIST,
    API_GET_DATA_COLLECTION_MANAGER_NEW,
    API_GET_QUERY_STATUS
} = API_CONST;

const START_ADD_TO_SALE_ORDER_CART = "START_ADD_TO_SALE_ORDER_CART";
const STOP_ADD_TO_SALE_ORDER_CART = "STOP_ADD_TO_SALE_ORDER_CART";
const START_GET_SERVICE_PRODUCT_LIST = "START_GET_SERVICE_PRODUCT_LIST";
const STOP_GET_SERVICE_PRODUCT_LIST = "STOP_GET_SERVICE_PRODUCT_LIST";
const START_GET_SEND_OTP_PROCESS = "START_GET_SEND_OTP_PROCESS";
const STOP_GET_SEND_OTP_PROCESS = "STOP_GET_SEND_OTP_PROCESS";
const START_SEARCH_HISTORY_INSURANCE = "START_SEARCH_HISTORY_INSURANCE";
const STOP_SEARCH_HISTORY_INSURANCE = "STOP_SEARCH_HISTORY_INSURANCE";

export const actionTopupData = {
    START_ADD_TO_SALE_ORDER_CART,
    STOP_ADD_TO_SALE_ORDER_CART,
    START_GET_SERVICE_PRODUCT_LIST,
    STOP_GET_SERVICE_PRODUCT_LIST,
    START_GET_SEND_OTP_PROCESS,
    STOP_GET_SEND_OTP_PROCESS,
    START_SEARCH_HISTORY_INSURANCE,
    STOP_SEARCH_HISTORY_INSURANCE
};

export const getProductListHomeNetwork = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
            };
            dispatch(start_get_service_product_list())
            apiBase(API_GET_PRODUCT_LIST, METHOD.POST, body)
                .then((response) => {
                    console.log("getProductListHomeNetwork success", response);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        dispatch(stop_get_service_product_list(response.object));
                        resolve(response.object);
                    }
                    else {
                        resolve("Lỗi lấy thông tin dữ liệu");
                        dispatch(stop_get_service_product_list([], false, 'Không tìm thấy nhà mạng', true));
                    }
                })
                .catch((error) => {
                    console.log("getServiceGroupListCatalog error", error);
                    dispatch(stop_get_service_product_list([], !EMPTY, error.msgError, ERROR));
                    reject(error.msgError)
                });
        });
    };
};

export const getProductListPackOfData = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                "isLoadProductInfo": 1,
                "groupBys": "ProductID,ProductName",
                phoneNumber: data.phoneNumber
            };
            apiBase(API_GET_PRODUCT_LIST_PACK_OF_DATA, METHOD.POST, body)
                .then((response) => {
                    console.log("getProductListPackOfData success", response);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getProductListPackOfData error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getPriceService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                isLoadProductInfo: 1,
                productID: data.productID,
                phoneNumber: data.phoneNumber
            };
            apiBase(API_GET_PRICE_SERVICE, METHOD.POST, body)
                .then((response) => {
                    console.log("getPriceService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPriceService error", error);
                    reject(error.msgError);
                });
        });
    };
};


export const addToSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                AirTimeTransactionBO: data.AirTimeTransactionBO,
                partnerData: data.partnerData,
            };
            dispatch(start_add_to_sale_order_cart());
            apiBase(API_GET_CREATE_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("addToSaleOrderCart success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_add_to_sale_order_cart(object));
                        resolve(response);
                    } else {
                        dispatch(stop_add_to_sale_order_cart({}));
                        reject({ msgError: translate("saleOrder.error_create_order") });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("addToSaleOrderCart error", error);
                    reject(msgError);
                });
        });
    };
};

export const getQuerysTatusServiceRequest = function (saleOrderID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: saleOrderID,
            };
            apiBase(API_QUERY_STATUS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getQuerysTatusServiceRequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getQuerysTatusServiceRequest error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getPriceAndFeePostPaid = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: 4,
                serviceGroupID: 3,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                productID: data.productID,
                phoneNumber: data.phoneNumber,
                customerName: data.customerName
            };
            apiBase(API_GET_PRICE_AND_FEE_POSTPAID, METHOD.POST, body)
                .then((response) => {
                    console.log("getPriceAndFeePostPaid success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPriceAndFeePostPaid error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getSendOTPProcessServicePrequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: data.saleOrderID,
                isRequestOTP: true
            };
            dispatch(start_get_send_otp_processs());
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getSendOTPProcessServicePrequest success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_send_otp_processs(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_send_otp_processs({}));
                        reject({ msgError: "Không tìm thấy thông tin gửi lại OTP" });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("getSendOTPProcessServicePrequest error", error);
                    reject(msgError);
                });
        });
    };
};

export const getGetTransactionDetail = function (saleOrderID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: saleOrderID,
            };
            dispatch(start_get_send_otp_processs());
            apiBase(API_GET_TRANSACTION_DETAIL, METHOD.POST, body)
                .then((response) => {
                    console.log("getGetTransactionDetail success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_send_otp_processs(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_send_otp_processs({}));
                        reject({ msgError: "Không tìm thấy thông tin gửi lại OTP" });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("getGetTransactionDetail error", error);
                    reject(msgError);
                });
        });
    };
};



export const getConfirmOTPProcessServicePrequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: data.saleOrderID,
                otp: data.otp
            };
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getPriceService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPriceService error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getServiceListHistory = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                isLoadSearchBy: data.isLoadSearchBy
            }
            apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
                .then((response) => {
                    console.log("getServiceListHistory success", response);
                    if (helper.IsNonEmptyArray(response.object)) {
                        resolve(response.object)
                    } else {
                        reject('Không lấy được dữ liệu')
                    }
                }).catch(error => {
                    reject(error.msgError)
                    console.log("getServiceListHistory error", error);
                })
        }
        )

    }
};

export const getSearchHistoryInsurance = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                Keyword: data.keyword.length > 0 ? data.keyword : '',
                ProcessUser: getState().userReducer.userName,
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                IsDeleted: false,
                airtimetrsTypeIDList: data.airtimetrsTypeIDList ? data.airtimetrsTypeIDList : '',
                searchType: data.searchType ? data.searchType : ''
            }
            dispatch(start_search_history_insurance())
            apiBase(API_GET_DATA_COLLECTION_MANAGER_NEW, METHOD.POST, body)
                .then((response) => {
                    console.log("getSearchHistoryInsurance BODY", response.object);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getSearchHistoryInsurance success", response);
                        dispatch(stop_search_history_insurance(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_search_history_insurance([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getSearchHistoryInsurance err', error);
                    dispatch(stop_search_history_insurance([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }
}

export const queryTransactionPartner = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                productID: data.productID,
                saleOrderID: data.saleOrderID
            };
            apiBase(API_GET_QUERY_STATUS, METHOD.POST, body).then((response) => {
                console.log("queryTransactionPartner success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("queryTransactionPartner error", error);
                reject(error.msgError)
            })
        });
    }
}

const start_get_service_product_list = () => ({
    type: START_GET_SERVICE_PRODUCT_LIST
});
const stop_get_service_product_list = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SERVICE_PRODUCT_LIST,
    data,
    isEmpty,
    description,
    isError
});

const start_get_send_otp_processs = () => ({
    type: START_GET_SEND_OTP_PROCESS
});
const stop_get_send_otp_processs = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SEND_OTP_PROCESS,
    data,
    isEmpty,
    description,
    isError
});

export const start_search_history_insurance = () => {
    return {
        type: START_SEARCH_HISTORY_INSURANCE,
    };
};

export const stop_search_history_insurance = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_SEARCH_HISTORY_INSURANCE,
    data,
    isEmpty,
    description,
    isError
});

export const start_add_to_sale_order_cart = () => {
    return {
        type: START_ADD_TO_SALE_ORDER_CART,
    };
};

export const stop_add_to_sale_order_cart = (dataSaleOrderCart = {}) => {
    return {
        type: STOP_ADD_TO_SALE_ORDER_CART,
        dataSaleOrderCart,
    };
};


