import { SafeAreaView, StyleSheet, Text, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import ButtonTabs from './component/ButtonTabs';
import TopupDataService from './Screen/TopupDataService';
import { COLORS } from '@styles';
import HistorySellTopupData from './Screen/HistorySellTopupData';
import { useFocusEffect } from '@react-navigation/native';

const TopupDataCollectionTab = ({
    navigation, route, stateServiceList
}) => {
    const [activeTab, setActiveTab] = useState(0)
    const { item } = route.params ?? {};
    const [data, setData] = useState({
        fromDate: '',
        toDate: ''
    })

    useFocusEffect(
        React.useCallback(() => {
            setActiveTab(0); // Thiết lập activeTab về tab "Đăng ký gói data" khi màn hình được focus lại
        }, [])
    );

    useEffect(() => {
        setData(data)
    }, [data]);

    return (
        <SafeAreaView
            style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF,
            }}>
            <ButtonTabs
                TabsOne={'Đăng ký gói data'}
                TabsTwo={'Xem lịch sử bán'}
                activeTab={activeTab}
                setActiveTab={setActiveTab} />
            {activeTab === 0 && (
                <TopupDataService
                    navigation={navigation}
                    item={item}
                />
            )}
            {activeTab === 1 && (
                <HistorySellTopupData
                    navigation={navigation}
                    data={data}
                    item={item}
                />
            )}
        </SafeAreaView>
    )
}

export default TopupDataCollectionTab

const styles = StyleSheet.create({})