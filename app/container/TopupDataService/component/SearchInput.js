import React, { useState } from 'react'
import {
  View,
  TouchableOpacity,
  TextInput,
  Keyboard
} from "react-native";
import { Icon } from "@components";
import { COLORS } from "@styles";
import { constants } from '@constants';
import { translate } from '@translate';

const SearchInput = ({ onSubmit, inputText, onChangeText, onClearText, placeholder, style }) => {

  return (
    <View style={[{
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      width: constants.width * 0.95,
      borderRadius: 20,
      borderWidth: 1,
      paddingHorizontal: 15,
      height: constants.getSize(40),
      backgroundColor: COLORS.bgFFFFFF,
      borderColor: COLORS.bdB9B9B9,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 5
    }, style]}>
      <TextInput
        placeholder={placeholder}
        style={{
          width: inputText != "" ? "85%" : "90%",
          alignItems: "center",
          justifyContent: "center",
        }}
        value={inputText}
        onChangeText={onChangeText}
        onFocus={() => {

        }}
        onSubmitEditing={() => {
          Keyboard.dismiss();
          onSubmit(inputText)
        }}
        returnKeyType="done"
      />
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        {inputText == "" ? null : (
          <TouchableOpacity
            style={{
              backgroundColor: COLORS.bdB9B9B9,
              alignItems: "center",
              justifyContent: "center",
              marginHorizontal: 5,
              width: constants.getSize(18),
              height: constants.getSize(18),
              borderRadius: 10,
            }}
            activeOpacity={0.7}
            onPress={onClearText}
          >
            <Icon
              iconSet="Ionicons"
              name="md-close"
              style={{ color: COLORS.icFFFFFF, fontSize: 15 }}
            />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={{
          }}
          onPress={() => {
            Keyboard.dismiss();
            onSubmit(inputText)
          }}
        >
          <Icon
            iconSet="Ionicons"
            name="ios-search"
            style={{ fontSize: 30, color: COLORS.ic2C8BD7 }}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default SearchInput;
