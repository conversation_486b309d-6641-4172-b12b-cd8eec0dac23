import React from 'react';
import {
    View,
    TouchableOpacity,
} from 'react-native';
import { Icon, MyText } from "@components";
import { COLORS } from "@styles";
import { translate } from '@translate';


const ItemRadio = ({
    title,
    isCheck,
    onPressItem,
    disabled
}) => {
    return (
        <TouchableOpacity
            style={{
                flexDirection: "row",
                width: 90,
                paddingVertical: 4
            }}
            activeOpacity={1}
            onPress={onPressItem}
            disabled={disabled}
        >
            <Icon
                iconSet={"MaterialIcons"}
                name={isCheck ? "radio-button-on" : "radio-button-off"}
                color={isCheck ? COLORS.icFF8900 : COLORS.ic333333}
                size={14}
                style={{ marginTop: 2 }}
            />
            <MyText
                text={title}
                style={{
                    color: COLORS.txt333333,
                    marginLeft: 4
                }}
            />
        </TouchableOpacity>
    );
}

const SubscriptionType = ({ subcription, onSubscription, disabled }) => {
    const dataRadio = [
        { title: "Trả trước", value: 1 },
        { title: "Trả sau", value: 0 },
    ]
    const onPressItem = (isCheck, value) => () => {
        if (!isCheck) {
            onSubscription(value);
        }
    }
    return (
        <View style={{
            flexDirection: "row",
            marginTop: 10,
            marginBottom: 10
        }}>
            {
                dataRadio.map(ele => {
                    const { title, value } = ele;
                    const isCheck = (value == subcription);
                    return (
                        <ItemRadio
                            key={value}
                            title={title}
                            isCheck={isCheck}
                            onPressItem={onPressItem(isCheck, value)}
                            disabled={disabled}
                        />
                    )
                })
            }
        </View>
    );
}

export default SubscriptionType;