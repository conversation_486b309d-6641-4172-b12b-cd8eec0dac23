import { View, Text, TouchableOpacity } from 'react-native'
import React from 'react'
import { Icon, MyText } from '@components'
import { COLORS } from '@styles'

const ButtonOTP = ({ title, style, onPress, iconSet, nameIcon, disabled, opacity }) => {
    return (
        <View style={{
        }}>
            <TouchableOpacity
                onPress={onPress}
                disabled={disabled}
                style={[style, {
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: 160,
                    height: 45,
                    borderRadius: 5,
                    flexDirection: 'row',
                    opacity: opacity
                }]}
            >
                <Icon
                    iconSet={iconSet}
                    name={nameIcon}
                    color={COLORS.bgFFFFFF}
                    size={20}
                />
                <MyText
                    text={title}
                    addSize={-1.5}
                    style={{
                        color: COLORS.bgFFFFFF,
                        fontSize: 13,
                        fontWeight: 'bold',
                        marginLeft: 5
                    }}
                />
            </TouchableOpacity>
        </View >
    )
}

export default ButtonOTP