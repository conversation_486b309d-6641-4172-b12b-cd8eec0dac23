import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { MyText } from '@components'
import { COLORS } from '@styles'

const Title = ({ title, isRequired }) => {
    return (
        <View>
            <MyText
                text={title}
                addSize={-1.5}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: "bold",
                    fontStyle: "italic"
                }}>
                {
                    isRequired &&
                    <MyText
                        text={"*"}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txtFF0000
                        }}
                    />
                }
            </MyText>
        </View>
    )
}

export default Title

const styles = StyleSheet.create({})