import { View, Text, TouchableOpacity } from 'react-native'
import React from 'react'
import { Icon, MyText } from '@components'
import { COLORS } from '@styles'

const ButtonAction = ({ title, style, onPress, iconSet, nameIcon, disabled, opacity, styleText, colorIcon }) => {
    return (
        <View style={{
        }}>
            <TouchableOpacity
                onPress={onPress}
                disabled={disabled}
                style={[style, {
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: 160,
                    height: 45,
                    borderRadius: 10,
                    flexDirection: 'row',
                    opacity: opacity
                }]}
            >
                <MyText
                    text={title}
                    addSize={-1.5}
                    style={[styleText, {
                        fontSize: 15,
                        fontWeight: 'bold',
                        marginLeft: 5,
                        width: 90,
                        textAlign: 'center'
                    }]}
                />
            </TouchableOpacity>
        </View >
    )
}

export default ButtonAction
