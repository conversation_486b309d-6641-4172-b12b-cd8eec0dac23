import { topupDataState } from "./state";
import { actionTopupData } from "./action";

const topupDataReducer = function (state = topupDataState, action) {
    switch (action.type) {
        case actionTopupData.STOP_ADD_TO_SALE_ORDER_CART:
            return {
                ...state,
                dataSaleOrderCart: action.dataSaleOrderCart,
            };
        case actionTopupData.START_GET_SERVICE_PRODUCT_LIST:
            return {
                ...state,
                dataServiceProductList: {},
                stateServiceProductList: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }

        case actionTopupData.STOP_GET_SERVICE_PRODUCT_LIST:
            return {
                ...state,
                dataServiceProductList: action.data,
                stateServiceProductList: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }

        case actionTopupData.START_GET_SEND_OTP_PROCESS:
            return {
                ...state,
                dataSendOTPProcess: {},
            }

        case actionTopupData.STOP_GET_SEND_OTP_PROCESS:
            return {
                ...state,
                dataSendOTPProcess: action.data,
            }
        case actionTopupData.START_SEARCH_HISTORY_INSURANCE:
            return {
                ...state,
                dataSearchListHistory: {},
                stateSearchListHistory: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }
        case actionTopupData.STOP_SEARCH_HISTORY_INSURANCE:
            return {
                ...state,
                dataSearchListHistory: action.data,
                stateSearchListHistory: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }
        default:
            return state;
    }
};

export { topupDataReducer };
