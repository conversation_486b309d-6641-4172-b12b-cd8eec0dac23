import { Al<PERSON>, FlatList, Image, Pressable, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState, useEffect } from 'react'
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { BaseLoading, TitleInput, MyText, Icon, showBlockUI, hideBlockUI } from '@components';
import { constants } from '@constants';
import { COLORS } from '@styles';
import Title from '../component/Title/Title';
import SubscriptionType from '../component/Radio/SubscriptionType';
import ButtonAction from '../component/ButtonAction';
import { helper } from '@common';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionTopupDataCreator from '../action';
import * as actionCollectionCreator from '../../CollectionTransfer/action';
import { useFocusEffect } from '@react-navigation/native';
import * as actionPaymentOrderCreator from "../../SaleOrderPayment/action";
import { translate } from '@translate';


const TopupDataService = ({ navigation, route, actionTopupData, actionCollection, itemCatalog, dataServiceProductList, stateServiceProductList, actionPaymentOrder }) => {
    const {
        ServiceCategoryID,
        AirtimeServiceGroupID
    } = itemCatalog ?? {};
    const { isFetching, isError, isEmpty, description } = stateServiceProductList;
    const [phoneNumber, setPhoneNumber] = useState('');
    const [dataHomeNetwork, setDataHomeNetWork] = useState([]);
    const [itemHomeNetwork, setItemHomeNetwork] = useState({});
    const [dataSelectPackage, setdataSelectPackage] = useState([]);
    const [itemSelectPackage, setItemSelectPackage] = useState({})
    const [updateProductList, setUpdateProductList] = useState([])
    const [customerName, setCustomerName] = useState('');
    const [note, setNote] = useState('');
    const [taxCode, setTaxCode] = useState('');
    const [companyAddress, setCompanyAddress] = useState('');
    const [checkbox, setCheckbox] = useState(false);
    const [updatePriceService, setUpdatePriceService] = useState(0);
    const { TotalAmount } = updatePriceService ?? {};
    const [airTimeID, setAirTimeID] = useState('');
    const [subscriptionType, setSubscriptionType] = useState([]);
    const [isPostPaid, setIsPostPaid] = useState(false);

    useFocusEffect(
        React.useCallback(() => {
            // Reset dataHomeNetwork 
            return () => {
                setPhoneNumber('');
                setDataHomeNetWork([]);
                setItemHomeNetwork({});
                setdataSelectPackage([])
                setItemSelectPackage({});
                setCustomerName("");
                setNote("");
                setTaxCode("");
                setCompanyAddress("");
            };
        }, [])
    );
    //data test Viettel 0326793893, 0123456789

    const dataCardName = [
        {
            CustomerID: 2787,
            source: require('../../../../assets/img_viettel.png'),
        },
        {
            CustomerID: 10608,
            source: require('../../../../assets/img_mobifone.png'),
        },
        {
            CustomerID: 16632,
            source: require('../../../../assets/img_vinaphone.png'),
        }
    ]

    const handleGetProductList = () => {
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID
        }
        actionTopupData.getProductListHomeNetwork(data);
        setItemHomeNetwork({});
    }

    const menuCatalogCollection = Array.isArray(dataServiceProductList) ? dataServiceProductList.map(catalogItem => {
        const correspondingMenuItem = dataCardName.find(menuItem => menuItem.CustomerID === catalogItem.CustomerID);
        return {
            ...catalogItem,
            ...correspondingMenuItem || null // Nếu không tìm thấy phần tử tương ứng, trả về null
        };
    }) : [];

    useEffect(() => {
        if (menuCatalogCollection.length > 0) {
            setDataHomeNetWork(menuCatalogCollection);
        };
    }, [dataServiceProductList]);

    useEffect(() => {
        if (updateProductList.length > 0) {
            setdataSelectPackage(updateProductList);
        };
    }, [updateProductList])

    const onClickItem = (item) => {
        const newSelectPackage = menuCatalogCollection?.map((r) => ({
            ...r,
            selected: r.CustomerID === item.CustomerID,
        }));
        setDataHomeNetWork(newSelectPackage);
        setItemHomeNetwork(item);
        setdataSelectPackage([]);
        setItemSelectPackage({});
        setCustomerName("");
        setNote("");
        setTaxCode("");
        setCompanyAddress("");
    }

    const onClickItemPackOfData = (item) => {
        const newSelectPackage = dataSelectPackage?.map((r) => ({
            ...r,
            selected: r.ProductID === item.ProductID,
        }));
        setdataSelectPackage(newSelectPackage);
        setItemSelectPackage(item);
        getPriceService(item);
        setCustomerName("");
        setNote("");
        setTaxCode("");
        setCompanyAddress("");
        setCheckbox(false)
    }

    const renderItem = ({ item, index }) => {
        return (
            <View style={{
                padding: 5
            }}>
                <View style={{
                    borderWidth: 0.5,
                    backgroundColor: COLORS.bgFFFFFF,
                    borderColor: item.selected ? COLORS.bg00AAFF : COLORS.bgE0E0E0,
                    borderRadius: 5,
                    alignItems: 'center',
                    shadowColor: COLORS.bg00AAFF,
                    shadowOffset: {
                        width: 0,
                        height: 2,
                    },
                    shadowOpacity: item.selected ? 0.25 : 0,
                    shadowRadius: 2,
                    elevation: item.selected ? 4 : 0,
                    width: 110,
                }}>
                    <TouchableOpacity
                        onPress={() => onClickItem(item, index)}
                        disabled={false}
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: (constants.width - 20) / 4,
                        }}>
                        <Image
                            resizeMode='contain'
                            style={{
                                width: 100,
                                height: 40,
                            }}
                            source={item.source}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };

    const getProductListPackOfDataService = (type) => {
        const { CustomerID } = itemHomeNetwork ?? {};
        setCheckbox(false)
        showBlockUI();
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: CustomerID == 2787 ? 1412 : type,
            isLoadProductInfo: 1,
            groupBys: "ProductID,ProductName",
            phoneNumber: phoneNumber
        }
        setUpdateProductList({});
        actionTopupData.getProductListPackOfData(data).then((reponse) => {
            hideBlockUI();
            setUpdateProductList(reponse)
        })
            .catch((msgError) => {
                setdataSelectPackage([]);
                setItemSelectPackage({});
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }
    useEffect(() => {
        if (itemHomeNetwork?.ListAirtimeTransactionType?.length > 0) {
            setSubscriptionType(itemHomeNetwork?.ListAirtimeTransactionType);
        };
    }, [itemHomeNetwork?.ListAirtimeTransactionType])

    const renderSubscriptionType = ({ item, index }) => {
        return (
            <View>
                {item.IsShow &&
                    <TouchableOpacity
                        onPress={() => onClickItemSubscriptionType(item, index)}
                        style={{
                            flexDirection: "row",
                            width: 90,
                            paddingVertical: 4
                        }}
                        activeOpacity={1}
                    >
                        <Icon
                            iconSet={"MaterialIcons"}
                            name={item.selected ? "radio-button-on" : "radio-button-off"}
                            color={item.selected ? COLORS.icFF8900 : COLORS.ic333333}
                            size={14}
                            style={{ marginTop: 2 }}
                        />
                        <MyText
                            text={item.Title}
                            style={{
                                color: COLORS.txt333333,
                                marginLeft: 4
                            }}
                        />
                    </TouchableOpacity>
                }
            </View>

        )
    };

    const onClickItemSubscriptionType = (item) => {
        const newSubscriptionType = itemHomeNetwork?.ListAirtimeTransactionType?.map((r) => ({
            ...r,
            selected: r.AirTimeTransactionTypeID === item.AirTimeTransactionTypeID,
        }));
        setSubscriptionType(newSubscriptionType);
        const airtimeId = item.AirTimeTransactionTypeID;
        getProductListPackOfDataService(airtimeId);
        setIsPostPaid(item.IsPostPaid)
        setItemSelectPackage({});
        setCustomerName("");
        setNote("");
        setTaxCode("");
        setCompanyAddress("");
        setCheckbox(false);
    };

    const getPriceService = (item) => {
        showBlockUI();
        const { ProductList } = item ?? {};
        const { AirtimeTransactionTypeID, ProductID } = ProductList?.[0] ?? [];
        setAirTimeID(AirtimeTransactionTypeID)
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AirtimeTransactionTypeID,
            isLoadProductInfo: 1,
            productID: ProductID,
            phoneNumber: phoneNumber
        }
        actionTopupData.getPriceService(data).then((reponse) => {
            hideBlockUI();
            setUpdatePriceService(reponse)
        })
            .catch((msgError) => {
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }

    const renderSelectPackage = ({ item, index }) => {
        return (
            <Pressable
                onPress={() => onClickItemPackOfData(item, index)}
                key={item.id}
                style={{
                    marginBottom: 10,
                    marginTop: 10,
                }}
            >
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center'
                }}>
                    <Icon
                        iconSet={"MaterialIcons"}
                        name={item.selected ? "radio-button-on" : "radio-button-off"}
                        color={item.selected ? COLORS.bg00AAFF : COLORS.bg000000}
                        size={14}
                        style={{ marginTop: 2 }}
                    />
                    <MyText
                        style={{
                            width: 140,
                            height: "auto",
                            marginLeft: 5,
                            color: COLORS.txt037EF3,
                        }}
                        text={item.ProductName}
                    />
                    <MyText
                        text={"Giá: "}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: "bold",
                            marginLeft: 5,
                            width: 140,
                        }}>
                        <MyText
                            text={item.ProductList?.[0]?.Amount}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txtFF0000,
                                marginLeft: 5
                            }}
                        />
                    </MyText>
                    <MyText
                        text={"ĐT: "}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: "bold",
                            marginLeft: 5
                        }}>
                        <MyText
                            text={item?.ProductList?.[0]?.ToTalRewardForStaff}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txtFF0000,
                                marginLeft: 5
                            }}
                        />
                    </MyText>
                </View>
                <MyText
                    text={item?.ProductList?.[0]?.ProductInfo?.ProductDescription}
                    addSize={-1.5}
                    style={{
                        color: COLORS.bg000000,
                        marginLeft: 20,
                        marginTop: 10
                    }}
                />
            </Pressable>
        )
    }

    const headerSubscriptionType = () => {
        return (
            <Title
                title='Loại thuê bao'
                isRequired={true}
            />
        );
    };

    const headerSelectPackage = () => {
        return (
            <Title
                title='Chọn gói cước'
                isRequired={true}
            />
        );
    }

    const handleAddCart = () => {
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxCode);
        const isValidateTax14 = regExpTax14.test(taxCode);
        const isValidateTax = isValidateTax10 || isValidateTax14;
        if (customerName == "") {
            Alert.alert("", "Vui lòng nhập tên khách hàng/ công ty");
            return false;
        }
        if (!isValidateTax && checkbox == true) {
            Alert.alert('', translate('editSaleOrder.validation_tax'));
            return false;
        }
        if (checkbox == true && companyAddress == "") {
            Alert.alert("", "Vui lòng nhập địa chỉ công ty");
            return false;
        }
        else {
            Alert.alert("", translate("saleOrderPayment.you_want_finish_order"), [
                {
                    text: translate("saleOrderManager.btn_cancel"),
                    style: "cancel",
                    onPress: hideBlockUI,
                },
                {
                    text: translate("saleOrderPayment.btn_continue"),
                    style: "default",
                    onPress: () => {
                        showBlockUI();
                        const { PriceInfo, FeeInfo } = updatePriceService ?? {};
                        const { Amount, SalePrice, InputPrice, ProductInfo: { ProductID } } = PriceInfo;
                        const { Fee } = FeeInfo ?? {};
                        const data = {
                            catalogID: ServiceCategoryID,
                            serviceGroupID: AirtimeServiceGroupID,
                            airtimeTransactionTypeID: airTimeID,
                            AirTimeTransactionBO: {
                                productid: ProductID,
                                amount: Amount,
                                fee: Fee,
                                phonenumber: phoneNumber,
                                inputPrice: InputPrice,
                                salePrice: SalePrice,
                                customerName: customerName,
                                cus_LogNote: note,
                                customerTaxID: taxCode,
                                customerAddress: companyAddress
                            },

                        };
                        actionTopupData.addToSaleOrderCart(data).then((reponse) => {
                            goToPaymentSO(reponse);
                        }).catch((error) => {
                            Alert.alert(translate("common.notification_uppercase"), error, [
                                {
                                    text: translate("common.btn_close"),
                                    onPress: hideBlockUI(),
                                },
                            ]);
                        });
                    },
                },
            ]);
        }
    };

    const goToPaymentSO = (rpSaleOrderCart) => {
        const dataSaleOrderCart = rpSaleOrderCart.object;
        const SaleOrders = dataSaleOrderCart.SaleOrders[0];
        const { SaleOrderID } = SaleOrders;
        actionPaymentOrder.setDataSO({ SaleOrderID: SaleOrderID, SaleOrderTypeID: 1000, }).then((success) => {
            hideBlockUI();
            navigation.navigate("SaleOrderPayment");
            actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
            actionPaymentOrder.getReportPrinterSocket(1000);
            actionPaymentOrder.getDataQRTransaction(SaleOrderID);
        });
    };

    return (
        <View
            style={{
                flex: 1,
                backgroundColor: "white",
            }}
        >
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView
                    style={{
                        flex: 1,
                        alignItems: "center",
                    }}
                >
                    <View
                        style={{
                            width: constants.width - 20,
                            marginTop: 10,
                        }}
                    >
                        <TitleInput
                            title={"Số điện thoại"}
                            isRequired={true}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 10,
                                paddingHorizontal: 10,
                                backgroundColor: COLORS.bgFFFFFF,
                                paddingVertical: 8,
                            }}
                            placeholder={"Vui lòng nhập số điện thoại"}
                            value={phoneNumber}
                            onChangeText={(text) => {
                                const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                const isValidate = regExpPhone.test(text) || (text == "");
                                if (isValidate) {
                                    setPhoneNumber(text);
                                    setDataHomeNetWork([]);
                                    setdataSelectPackage([]);
                                    setItemHomeNetwork({});
                                    setItemSelectPackage({});
                                    setCustomerName("");
                                }
                            }}
                            keyboardType="numeric"
                            returnKeyType="done"
                            onBlur={() => handleGetProductList()}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setPhoneNumber("");
                            }}
                            key="phoneNumber"
                        />
                        {phoneNumber.length == 10 ?
                            <BaseLoading
                                isLoading={isFetching}
                                isError={isError}
                                isEmpty={isEmpty}
                                textLoadingError={description}
                                onPressTryAgains={() => handleGetProductList()}
                                content={
                                    helper.IsNonEmptyArray(dataHomeNetwork) && phoneNumber.length == 10 &&
                                    <View>
                                        <Title title='Nhà mạng' />
                                        <FlatList
                                            style={{
                                                backgroundColor: COLORS.bgFFFFFF,
                                            }}
                                            data={dataHomeNetwork}
                                            keyExtractor={(item, index) => `${index}`}
                                            renderItem={(item) => renderItem(item)}
                                            bounces={false}
                                            scrollEventThrottle={16}
                                            contentContainerStyle={{
                                                flex: 1
                                            }}
                                            numColumns={3}
                                        />
                                        {!helper.IsEmptyObject(itemHomeNetwork) &&
                                            <FlatList
                                                data={subscriptionType}
                                                keyExtractor={(item, index) => `${index}`}
                                                renderItem={renderSubscriptionType}
                                                renderSectionHeader={({ section: { title } }) => (
                                                    <DayTitle title={title} />
                                                )}
                                                stickySectionHeadersEnabled={true}
                                                alwaysBounceVertical={false}
                                                bounces={false}
                                                numColumns={2}
                                                scrollEventThrottle={16}
                                                ListHeaderComponent={headerSubscriptionType}
                                            />
                                        }
                                        {helper.IsNonEmptyArray(dataSelectPackage) &&
                                            <FlatList
                                                style={{
                                                    backgroundColor: COLORS.bgFFFFFF,
                                                }}
                                                data={dataSelectPackage}
                                                keyExtractor={(item, index) => `${index}`}
                                                renderItem={(item) => renderSelectPackage(item)}
                                                stickySectionHeadersEnabled={true}
                                                alwaysBounceVertical={false}
                                                bounces={false}
                                                scrollEventThrottle={16}
                                                ListHeaderComponent={headerSelectPackage}
                                            />
                                        }
                                        {!helper.IsEmptyObject(itemSelectPackage) &&
                                            <View style={{}}>
                                                <TitleInput
                                                    title={"Tên khách lẻ/ công ty: "}
                                                    isRequired={true}
                                                    styleInput={{
                                                        borderWidth: 1,
                                                        borderRadius: 4,
                                                        borderColor: COLORS.bdCCCCCC,
                                                        marginBottom: 10,
                                                        paddingHorizontal: 10,
                                                        backgroundColor: COLORS.bgFFFFFF,
                                                        paddingVertical: 8,
                                                    }}
                                                    placeholder={"Vui lòng nhập tên khách hàng/ công ty"}
                                                    value={customerName}
                                                    onChangeText={(text) => {
                                                        setCustomerName(text)
                                                    }}
                                                    keyboardType="default"
                                                    returnKeyType={"done"}
                                                    onBlur={() => { }}
                                                    width={constants.width - 20}
                                                    height={40}
                                                    clearText={() => {
                                                        setCustomerName("");
                                                    }}
                                                    key="customerName"
                                                />
                                                <TitleInput
                                                    title={"Ghi chú: "}
                                                    isRequired={false}
                                                    styleInput={{
                                                        borderWidth: 1,
                                                        borderRadius: 4,
                                                        borderColor: COLORS.bdCCCCCC,
                                                        marginBottom: 10,
                                                        paddingHorizontal: 10,
                                                        backgroundColor: COLORS.bgFFFFFF,
                                                        paddingVertical: 8,
                                                    }}
                                                    placeholder={"Vui lòng nhập ghi chú"}
                                                    value={note}
                                                    onChangeText={(text) => {
                                                        setNote(text)
                                                    }}
                                                    keyboardType="default"
                                                    returnKeyType={"done"}
                                                    onBlur={() => { }}
                                                    width={constants.width - 20}
                                                    height={40}
                                                    clearText={() => {
                                                        setNote("");
                                                    }}
                                                    key="note"
                                                />
                                                {isPostPaid == false &&
                                                    <View>
                                                        <TouchableOpacity
                                                            onPress={() => setCheckbox(!checkbox)}
                                                            activeOpacity={1}
                                                            style={{
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                                height: 40,
                                                                width: '100%',
                                                                marginTop: 5,
                                                            }}>
                                                            <Icon
                                                                iconSet={"Ionicons"}
                                                                name={checkbox ? 'md-checkbox-outline' : 'ios-square-outline'}
                                                                color={checkbox ? COLORS.bgF49B0C : COLORS.txtA2A4A6}
                                                                size={20}
                                                            />
                                                            <MyText
                                                                style={{
                                                                    color: checkbox ? COLORS.bgF49B0C : COLORS.txtA2A4A6,
                                                                    marginLeft: 5
                                                                }}
                                                                text={"Khách lấy HĐ công ty"}
                                                            />
                                                            <View style={{ flex: 1 }} />
                                                        </TouchableOpacity>
                                                        {
                                                            checkbox ?
                                                                (
                                                                    <View>
                                                                        <TitleInput
                                                                            title={"Mã số thuế: "}
                                                                            isRequired={true}
                                                                            styleInput={{
                                                                                borderWidth: 1,
                                                                                borderRadius: 4,
                                                                                borderColor: COLORS.bdCCCCCC,
                                                                                marginBottom: 10,
                                                                                paddingHorizontal: 10,
                                                                                backgroundColor: COLORS.bgFFFFFF,
                                                                                paddingVertical: 8,
                                                                            }}
                                                                            placeholder={"Vui lòng nhập mã số thuế"}
                                                                            value={taxCode}
                                                                            onChangeText={(text) => {
                                                                                const regExpTax = new RegExp(/^[0-9-KL]{0,14}$/);
                                                                                const isValidate = regExpTax.test(text) || (text == "");
                                                                                if (isValidate) {
                                                                                    setTaxCode(text)
                                                                                }
                                                                            }}
                                                                            keyboardType="default"
                                                                            returnKeyType={"done"}
                                                                            onBlur={() => { }}
                                                                            width={constants.width - 20}
                                                                            height={40}
                                                                            clearText={() => {
                                                                                setTaxCode("");
                                                                            }}
                                                                            key="taxCode"
                                                                        />
                                                                        <TitleInput
                                                                            title={"Địa chỉ công ty: "}
                                                                            isRequired={true}
                                                                            styleInput={{
                                                                                borderWidth: 1,
                                                                                borderRadius: 4,
                                                                                borderColor: COLORS.bdCCCCCC,
                                                                                marginBottom: 10,
                                                                                paddingHorizontal: 10,
                                                                                backgroundColor: COLORS.bgFFFFFF,
                                                                                paddingVertical: 8,
                                                                            }}
                                                                            placeholder={"Vui lòng nhập địa chỉ công ty"}
                                                                            value={companyAddress}
                                                                            onChangeText={(text) => {
                                                                                setCompanyAddress(text)
                                                                            }}
                                                                            keyboardType="default"
                                                                            returnKeyType={"done"}
                                                                            onBlur={() => { }}
                                                                            width={constants.width - 20}
                                                                            height={40}
                                                                            clearText={() => {
                                                                                setCompanyAddress("");
                                                                            }}
                                                                            key="companyAddress"
                                                                        />
                                                                    </View>
                                                                )
                                                                :
                                                                null
                                                        }
                                                    </View>
                                                }
                                                <View style={{
                                                    flexDirection: 'row',
                                                    alignSelf: 'flex-end',
                                                    alignItems: 'center'
                                                }}>
                                                    <MyText
                                                        style={{
                                                            color: COLORS.bg147EFB,
                                                            fontWeight: 'bold',
                                                            fontSize: 20
                                                        }}
                                                        text={"Tổng thanh toán: "}
                                                    />
                                                    <MyText
                                                        style={{
                                                            color: COLORS.bgEA1D5D,
                                                            marginLeft: 5,
                                                            fontWeight: 'bold',
                                                            fontSize: 20
                                                        }}
                                                        text={helper.formatMoney(TotalAmount)}
                                                    />
                                                </View>
                                                <View style={{
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    flexDirection: 'row',
                                                    marginTop: 20
                                                }}>
                                                    <ButtonAction
                                                        onPress={() => navigation.goBack()}
                                                        title={"ĐÓNG"}
                                                        style={{
                                                            borderWidth: 1,
                                                            borderColor: COLORS.bg00A98F
                                                        }}
                                                        styleText={{
                                                            color: COLORS.bg00A98F,
                                                        }}
                                                        opacity={1}
                                                        disabled={false}
                                                    />
                                                    <View style={{ flex: 1 }} />
                                                    <ButtonAction
                                                        onPress={() => handleAddCart()}
                                                        title={"XÁC NHẬN"}
                                                        style={{
                                                            backgroundColor: COLORS.bg00A98F,
                                                        }}
                                                        styleText={{
                                                            color: COLORS.bgFFFFFF,
                                                        }}
                                                        opacity={1}
                                                        disabled={false}
                                                    />
                                                </View>

                                            </View>
                                        }
                                    </View>

                                }
                                stickySectionHeadersEnabled={false}
                                alwaysBounceVertical={false}
                                bounces={false}
                                scrollEventThrottle={16}
                            />
                            :
                            <MyText
                                text={"Vui lòng nhập số điện thoại đúng 10 số!"}
                                addSize={-1.5}
                                style={{
                                    color: COLORS.bgEA1D5D,
                                    fontStyle: "italic"
                                }}
                            />
                        }
                    </View>
                </SafeAreaView>
            </KeyboardAwareScrollView>
        </View>

    )
}

const mapStateToProps = function (state) {
    return {
        dataCatalogListCollection: state.collectionReducer.dataCatalogListCollection,
        dataServiceProductList: state.topupDataReducer.dataServiceProductList,
        stateServiceProductList: state.topupDataReducer.stateServiceProductList,
        itemCatalog: state.collectionReducer.itemCatalog
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionTopupData: bindActionCreators(actionTopupDataCreator, dispatch),
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(TopupDataService)

const styles = StyleSheet.create({})
