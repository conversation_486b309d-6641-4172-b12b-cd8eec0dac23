import React, { Component } from 'react';
import {
    View,
    Animated,
    Keyboard,
    Alert,
    TouchableOpacity
} from 'react-native';
import Safe<PERSON>reaView from "react-native-safe-area-view";
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
    But<PERSON>,
    MyText,
    BaseLoading,
    showB<PERSON>UI,
    hideBlockUI,
    Icon
} from "@components";
import { dateHelper, helper, storageHelper } from "@common";
import { translate } from '@translate';
import { COLORS } from "@styles";
import InputSearch from "./component/InputSearch";
import CheckFilter from "./component/CheckFilter";
import TabFilter from "./component/TabFilter";
import ItemSO from "./component/ItemSO";
import * as actionManagerSOCreator from "./action";
import * as actionSaleOrderCreator from "../SaleOrderPayment/action";
import * as actionEditSaleOrderCreator from "../EditSaleOrder/action";
import * as actionShoppingCartCreator from "../ShoppingCart/action";
import * as actionDetailCreator from '../Detail/action';
import ModalFilterSearch from './component/ModalFilterSearch';
import { constants } from '@constants';

const MAX_HEIGHT = 112;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;
const ONE_SECOND = 1000;
const ONE_MINUTES = 60 * ONE_SECOND;
const ONE_HOUR = 60 * ONE_MINUTES;
const ONE_DAY = 24 * ONE_HOUR;

const { TYPE_PROFILE } = constants

class SaleOrderManager extends Component {
    constructor(props) {
        super(props);
        this.state = {
            scrollY: new Animated.Value(0),
            isCreate: true,
            isDelete: false,
            keyword: "",
            fromDate: "",
            toDate: "",
            isOuput: false,
            numberDay: 0,
            isVisibleFilterSearch: false,
            attachmentType: -1
        };
        this.timeOutScroll = null;
        this.isScrolling = false;
        this.keySearch = "";
    }

    componentDidMount() {
        this.updateFromDate();
    }

    componentDidUpdate(preProps, preState) {
        const { stateSearchSO } = this.props;
        if (preProps.stateSearchSO.isFetching != stateSearchSO.isFetching && stateSearchSO.isFetching) {
            this.setState({ scrollY: new Animated.Value(0) });
        }
    }

    handleScroll = ({ contentOffset }) => {
        const { y } = contentOffset;
        if (y <= 0) {
            this.setState({ scrollY: new Animated.Value(0) });
        }
    }

    onScrollBegin = () => {
        this.isScrolling = true;
    }

    onScrollEnd = () => {
        this.isScrolling = false;
    }

    render() {
        const {
            keyword,
            scrollY,
            numberDay,
        } = this.state;
        const {
            dataSearchSO,
            isFilterDay,
            stateSearchSO
        } = this.props;
        const isLock = stateSearchSO.isFetching;
        const diffClamp = Animated.diffClamp(scrollY, 0, DISTANCE);
        const translateY = diffClamp.interpolate({
            inputRange: [0, DISTANCE],
            outputRange: [0, -DISTANCE],
        });
        return (
            <SafeAreaView style={{
                flex: 1,
            }}>
                <Animated.View style={{
                    transform: [{ translateY: translateY }],
                    backgroundColor: COLORS.bgF5F5F5,
                    position: 'absolute',
                    top: 0, left: 0, right: 0, zIndex: 2,
                }}>
                    <CheckFilter
                        param={this.state}
                        onChangeParam={(value1, value2) => {
                            if (!this.isScrolling) {
                                this.setState({
                                    isCreate: value1,
                                    isDelete: value2,
                                    scrollY: new Animated.Value(0)
                                }, this.onFilterParam);
                            }
                        }}
                        disabled={isLock}
                    />
                    <View style={
                        {
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "center",
                            alignSelf: "center",
                        }
                    }>
                        <InputSearch
                            value={keyword}
                            onChangeText={(text) => {
                                if (!this.isScrolling) {
                                    this.setState({
                                        keyword: text,
                                        scrollY: new Animated.Value(0),
                                    });
                                }
                            }}
                            onSubmitEditing={this.onSubmit(keyword)}
                            onSearch={this.onFilterParam}
                            disabled={isLock}
                        />
                        <TouchableOpacity
                            style={{ marginLeft: 10 }}
                            onPress={() => {
                                this.setState({
                                    isVisibleFilterSearch: true
                                });
                            }}
                            activeOpacity={0.7}>

                            <Icon
                                iconSet="FontAwesome"
                                name="filter"
                                style={{
                                    color: COLORS.ic2C8BD7,
                                    fontSize: 18,
                                }}
                            />
                        </TouchableOpacity>
                    </View>


                    <TabFilter
                        param={this.state}
                        onChangeParam={(value) => {
                            if (!this.isScrolling) {
                                this.setState({
                                    isOuput: value,
                                    scrollY: new Animated.Value(0)
                                }, this.onFilterParam);
                            }
                        }}
                        disabled={isLock}
                    />
                </Animated.View>
                <BaseLoading
                    isLoading={stateSearchSO.isFetching}
                    isEmpty={stateSearchSO.isEmpty}
                    textLoadingError={stateSearchSO.description}
                    isError={stateSearchSO.isError}
                    onPressTryAgains={this.onFilterParam}
                    content={
                        <Animated.SectionList
                            contentContainerStyle={{
                                paddingTop: MAX_HEIGHT,
                            }}
                            stickyHeaderIndices={[0]}
                            onScroll={Animated.event(
                                [{ nativeEvent: { contentOffset: { y: this.state.scrollY } } }],
                                { useNativeDriver: true, listener: ({ nativeEvent }) => this.handleScroll(nativeEvent) }
                            )}
                            sections={dataSearchSO}
                            keyExtractor={(item, index) => `${index}`}
                            renderItem={({ item, index }) => (
                                <ItemSO
                                    item={item}
                                    index={index}
                                    onEdit={this.onEditSO(item)}
                                    onPayment={this.onPaymentSO(item)}
                                    onPrint={this.onPrintSO(item)}
                                    onDelete={this.onDeleteSO(item)}
                                    onReadMore={this.onReadMore(item)}
                                    onEditPicture={this.onEditPicture(item)}
                                />
                            )}
                            renderSectionHeader={({ section: { title } }) => (<DayTitle title={title} />)}
                            removeClippedSubviews={false}
                            stickySectionHeadersEnabled={false}
                            alwaysBounceVertical={false}
                            bounces={false}
                            scrollEventThrottle={16}
                            onMomentumScrollBegin={this.onScrollBegin}
                            onMomentumScrollEnd={this.onScrollEnd}
                        />
                    }
                />
                {
                    isFilterDay &&
                    <ButtonLoadMore
                        number={numberDay}
                        onLoad={this.onLoadMore}
                        disabled={isLock}
                    />
                }
                <ModalFilterSearch
                    isVisible={this.state.isVisibleFilterSearch}
                    hideModal={() => {
                        this.setState({ isVisibleFilterSearch: false });
                    }}
                    searchFilter={(type) => {
                        this.setState({
                            attachmentType: type
                        }, this.onFilterParam);
                    }}

                />
            </SafeAreaView>
        );
    }

    onSubmit = (keyword) => () => {
        Keyboard.dismiss();
        if (this.keySearch != keyword) {
            this.onFilterParam();
        }
    }

    updateFromDate = () => {
        const { fromDate, toDate } = get_from_to_date();
        this.setState({
            fromDate,
            toDate,
            scrollY: new Animated.Value(0),
            numberDay: 0
        }, this.onFilterParam);
    }

    onFilterParam = () => {
        const {
            isCreate,
            isDelete,
            keyword,
            fromDate,
            toDate,
            isOuput,
            attachmentType
        } = this.state;
        const { userInfo: {
            storeID
        } } = this.props;
        this.keySearch = keyword;
        this.props.actionManagerSO.getDataSearchSO({
            "outputStoreID": storeID,
            "fromDate": fromDate,
            "toDate": toDate,
            "keyWord": keyword,
            "isNullInputUser": !isCreate,
            "isOutProduct": Number(isOuput),
            "isDelete": Number(isDelete),
            "attachmentType": attachmentType
        });
    }

    onLoadMore = (data) => {
        Keyboard.dismiss();
        const {
            isCreate,
            isDelete,
            keyword,
            isOuput,
            attachmentType
        } = this.state;
        const { userInfo: {
            storeID
        } } = this.props;
        this.updateDataSearchSO({
            "outputStoreID": storeID,
            "fromDate": data.fromDate,
            "toDate": data.toDate,
            "keyWord": keyword,
            "isNullInputUser": !isCreate,
            "isOutProduct": Number(isOuput),
            "isDelete": Number(isDelete),
            "numberDay": data.numberDay,
            "attachmentType": attachmentType
        });
    }

    updateDataSearchSO = (data) => {
        showBlockUI();
        this.props.actionManagerSO.updateDataSearchSO(data).then(success => {
            hideBlockUI();
            this.setState({
                numberDay: data.numberDay,
                fromDate: data.fromDate,
                toDate: data.toDate,
            });
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('common.btn_skip'),
                    style: "default",
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: "default",
                    onPress: () => { this.updateDataSearchSO(data) }
                }
            ]);
        });
    }

    handleEditSaleOrder = async (saleOrderProfile, taxID) => {
        const { userInfo: { storeID, languageID, moduleID } } = this.props
        const profiles = saleOrderProfile?.[0]?.Profile
        let profileCustomer = {}
        let profileCompany = {}
        let profileReceiveCustomer = {}
        let profileAddressCustomer = {}
        try {
            if (!helper.IsEmptyObject(profiles[TYPE_PROFILE.CUSTOMER]?.[0])) {
                const body =
                {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "keyword": profiles[TYPE_PROFILE.CUSTOMER]?.[0].profileId,
                    "type": TYPE_PROFILE.CUSTOMER,
                    "versionCode": profiles[TYPE_PROFILE.CUSTOMER]?.[0].versionCode
                }
                const dataProfileCustomer = await actionShoppingCartCreator.getSOProfile(body)
                if (!helper.IsEmptyObject(dataProfileCustomer)) {
                    profileCustomer = { [TYPE_PROFILE.CUSTOMER]: dataProfileCustomer?.[0] ?? {} }
                }
            }
            if (!helper.IsEmptyObject(profiles[TYPE_PROFILE.COMPANY]?.[0]) && !!taxID) {
                const body =
                {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "keyword": profiles[TYPE_PROFILE.COMPANY]?.[0].companyId,
                    "type": TYPE_PROFILE.COMPANY,
                    "versionCode": profiles[TYPE_PROFILE.COMPANY]?.[0].versionCode
                }
                const dataProfileCompany = await actionShoppingCartCreator.getSOProfile(body)
                if (!helper.IsEmptyObject(dataProfileCompany)) {
                    profileCompany = { [TYPE_PROFILE.COMPANY]: dataProfileCompany?.[0] ?? {} }
                }
            }
            if (!helper.IsEmptyObject(profiles[TYPE_PROFILE.ADDRESS_RECEIVE]?.[0])) {
                const body =
                {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "keyword": profiles[TYPE_PROFILE.ADDRESS_RECEIVE]?.[0].deliveryId,
                    "type": TYPE_PROFILE.ADDRESS_RECEIVE,
                    "versionCode": profiles[TYPE_PROFILE.ADDRESS_RECEIVE]?.[0].versionCode
                }
                const dataProfileAddress = await actionShoppingCartCreator.getSOProfile(body)
                if (!helper.IsEmptyObject(dataProfileAddress)) {
                    profileAddressCustomer = { [TYPE_PROFILE.ADDRESS_RECEIVE]: { ...dataProfileAddress?.[0], profileId: profiles[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId } ?? {} }
                }
            }
            if (!helper.IsEmptyObject(profiles[TYPE_PROFILE.CUSTOMER_RECEIVE]?.[0])) {
                const body =
                {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "keyword": profiles[TYPE_PROFILE.CUSTOMER_RECEIVE]?.[0].receiverId,
                    "type": TYPE_PROFILE.CUSTOMER_RECEIVE,
                    "versionCode": profiles[TYPE_PROFILE.CUSTOMER_RECEIVE]?.[0].versionCode
                }
                const dataProfileReceive = await actionShoppingCartCreator.getSOProfile(body)
                if (!helper.IsEmptyObject(dataProfileReceive)) {
                    profileReceiveCustomer = { [TYPE_PROFILE.CUSTOMER_RECEIVE]: { ...dataProfileReceive?.[0], profileId: profiles[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId } ?? {} }
                }
            }

        } catch (error) {
            console.log("🚀 ~ SaleOrderManager ~ handleEditSaleOrder= ~ error:", error)
        } finally {
            return { ...profileCompany, ...profileCustomer, ...profileAddressCustomer, ...profileReceiveCustomer }
        }

    }

    onEditSO = (info) => async () => {
        const { SALEORDERID, ORDERTYPEID } = info;
        const { actionEditSaleOrder, navigation, actionSaleOrder, userInfo, actionShoppingCart } = this.props;
        if (validateAllowEditSO(userInfo)) {
            showBlockUI();
            this.props.actionShoppingCart.reset_map_customer_confirm_policy()
            this.setState({ scrollY: new Animated.Value(0) });
            try {
                const dataSaleOrder = await actionEditSaleOrder.getSaleOrderInfo(SALEORDERID)
                if (helper.IsEmptyObject(dataSaleOrder)) return
                const { SaleOrderDetails, CustomerInfo: { TaxID } } = dataSaleOrder
                const profileSO = await this.handleEditSaleOrder(SaleOrderDetails, TaxID)
                actionSaleOrder.setDataSO({
                    SaleOrderID: SALEORDERID,
                    SaleOrderTypeID: ORDERTYPEID
                }).then(() => {
                    hideBlockUI();
                    console.log("🚀 ~ SaleOrderManager ~ onEditSO= ~ profileSO:", profileSO)
                    if (!helper.IsEmptyObject(profileSO[TYPE_PROFILE.ADDRESS_RECEIVE]) || !helper.IsEmptyObject(profileSO[TYPE_PROFILE.CUSTOMER_RECEIVE])) {
                        const newDeliveryInfo = {
                            ContactGender: profileSO[TYPE_PROFILE.CUSTOMER_RECEIVE]?.receiverGender || dataSaleOrder.ContactGender,
                            ContactName: profileSO[TYPE_PROFILE.CUSTOMER_RECEIVE]?.receiverName || dataSaleOrder.ContactName,
                            ContactPhone: profileSO[TYPE_PROFILE.CUSTOMER_RECEIVE]?.receiverPhone || dataSaleOrder.ContactPhone,
                            DeliveryAddress: profileSO[TYPE_PROFILE.ADDRESS_RECEIVE]?.address || dataSaleOrder.DeliveryAddress,
                            DeliveryDistrictID: profileSO[TYPE_PROFILE.ADDRESS_RECEIVE]?.districtId || dataSaleOrder.DeliveryDistrictID,
                            DeliveryDistrictName: profileSO[TYPE_PROFILE.ADDRESS_RECEIVE]?.districtName || dataSaleOrder.DeliveryDistrictName,
                            DeliveryProvinceID: profileSO[TYPE_PROFILE.ADDRESS_RECEIVE]?.provinceId || dataSaleOrder.DeliveryProvinceID,
                            DeliveryProvinceName: profileSO[TYPE_PROFILE.ADDRESS_RECEIVE]?.provinceName || dataSaleOrder.DeliveryProvinceName,
                            DeliveryWardID: profileSO[TYPE_PROFILE.ADDRESS_RECEIVE]?.wardId || dataSaleOrder.DeliveryWardID,
                            DeliveryWardName: profileSO[TYPE_PROFILE.ADDRESS_RECEIVE]?.wardName || dataSaleOrder.DeliveryWardName,
                        }
                        const newDataShoppingCart = { ...dataSaleOrder, DeliveryInfo: { ...dataSaleOrder.DeliveryInfo, ...newDeliveryInfo } }
                        actionEditSaleOrder.setDataSaleOrderCart(newDataShoppingCart);
                    }
                    // saleOrder;
                    // const collectedForAnKhang = IsIncome && IsSOAnKhang
                    // const isLock = IsDeleted || IsOutProduct ||
                    //     cus_IsAddPromotionSO || IsSOScreenSticker ||
                    //     helper.IsNonEmptyString(cus_SaleReceiptID) ||
                    //     collectedForAnKhang;
                    if (dataSaleOrder.IsLockEdit) {
                        navigation.navigate("LockSaleOrder");
                    } else {
                        if (!helper.IsEmptyObject(profileSO[TYPE_PROFILE.CUSTOMER_RECEIVE])) {
                            actionShoppingCart.set_map_customer_confirm_policy({
                                type: TYPE_PROFILE.CUSTOMER_RECEIVE,
                                infoCustomerCRM: [profileSO[TYPE_PROFILE.CUSTOMER_RECEIVE]]
                            });
                        }
                        if (!helper.IsEmptyObject(profileSO[TYPE_PROFILE.ADDRESS_RECEIVE])) {
                            actionShoppingCart.set_map_customer_confirm_policy({
                                type: TYPE_PROFILE.ADDRESS_RECEIVE,
                                infoCustomerCRM: [profileSO[TYPE_PROFILE.ADDRESS_RECEIVE]]
                            });
                        }
                        navigation.navigate("EditSaleOrder", { profileSO });
                    }
                })
            } catch (error) {
                console.log("🚀 ~ SaleOrderManager ~ onEditSO= ~ error:", error)
                Alert.alert(translate('common.notification_uppercase'), error.msgError, [
                    {
                        text: translate('common.btn_skip'),
                        onPress: hideBlockUI,
                    },
                    {
                        text: translate('saleOrderManager.btn_retry_uppercase'),
                        onPress: this.onEditSO(info),
                    },
                ]);
            } finally {

            }

        }
    }

    onEditPicture = (info) => () => {
        const { SALEORDERID } = info;
        const { actionEditSaleOrder } = this.props;
        showBlockUI();
        this.setState({ scrollY: new Animated.Value(0) });
        actionEditSaleOrder.checkPromotion19AndCoupon(SALEORDERID).then(res => {
            const { Data, IsCoupon, IsPromotion19, ShortSaleOrderDetaillst } = res
            hideBlockUI();
            this.editSupplementImage(Data, SALEORDERID, IsCoupon, IsPromotion19, ShortSaleOrderDetaillst);
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.message ?? error.msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI,
                },
                {
                    text: translate('saleOrderManager.btn_retry_uppercase'),
                    onPress: this.onEditPicture(info),
                },
            ]);
        })
    }

    onUpdateSaleOrder = (newUrlFiles, SaleOrderID) => {
        const data = {
            "saleOrderID": SaleOrderID,
            "attachmentTypeList": "",
            "urlFileBOList": newUrlFiles
        };
        this.updateSaleOrder(data);
    }

    updateSaleOrder = (data) => {
        setTimeout(() => {
            showBlockUI();
            this.props.actionEditSaleOrder.updateImageSaleOrder(data).then(orderInfo => {
                Alert.alert("", "Cập nhật hình ảnh thành công.",
                    [{
                        text: "OK",
                        style: "default",
                        onPress: () => {
                            hideBlockUI();
                            this.props.navigation.navigate("OrderManagement");
                        }
                    }]
                )
            }).catch(error => {
                Alert.alert(translate('common.notification_uppercase'), error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.updateSaleOrder(data)
                        }
                    ]
                )
            })
        }, 200);;
    }

    editSupplementImage = (Data, SALEORDERID, IsCoupon, IsPromotion19, ShortSaleOrderDetaillst) => {
        const { isOuput } = this.state
        // if (isOuput) {
        //     if (helper.IsNonEmptyArray(Data)) {
        //         this.props.navigation.navigate("OrderStudentInfo", {
        //             noGoBack: true,
        //             supImages: Data,
        //             dataShoppingCart: SALEORDERID,
        //             setDataShoppingCart: this.onUpdateSaleOrder,
        //             isStudentCoupon: IsCoupon,
        //             isStudentPromotion: IsPromotion19,
        //             additionalImages: ShortSaleOrderDetaillst
        //         });
        //         // this.props.navigation.navigate("OrderLockImage19AndCoupon", { supImages: Data, additionalImages: ShortSaleOrderDetaillst });
        //     }
        //     else {
        //         Alert.alert('', "Đơn hàng không có hình.\nVui lòng liên hệ IT kiểm tra lại!");
        //     }
        // }
        // else {
        this.props.navigation.navigate("OrderStudentInfo", {
            noGoBack: true,
            supImages: Data,
            dataShoppingCart: SALEORDERID,
            setDataShoppingCart: this.onUpdateSaleOrder,
            isStudentCoupon: IsCoupon,
            isStudentPromotion: IsPromotion19,
            additionalImages: ShortSaleOrderDetaillst,
            isOuputSO: isOuput,
            isManager: true
        });
        // }
    }

    handleSkipRandomDiscountPromotion = async (SOInfo) => {
        try {
            const { dataSaleOrder } = this.props;
            const appliedRandomDiscountList = [];
            dataSaleOrder.listMainProduct.forEach((product) => {
                if (product.cus_RandomDiscountApplyB0) {
                    appliedRandomDiscountList.push({
                        ...product.cus_RandomDiscountApplyB0,
                        Status: 1
                    });
                }
            });
            if (appliedRandomDiscountList.length > 0) {
                const { actionDetail } = this.props;
                await actionDetail.updateStatusRandomDiscountPromotion(appliedRandomDiscountList);
                hideBlockUI();
                this.onPaymentSO(SOInfo)();
            } else {
                throw new Error("Không thể thu tiền xuất hàng với đơn hàng có tham gia khuyến mãi Vòng quay may mắn.");
            }
        } catch (error) {
            Alert.alert(translate("common.notification"), error, [
                {
                    text: translate("common.btn_notify_try_again"),
                    style: "default",
                    onPress: () => this.handleSkipRandomDiscountPromotion(SOInfo)
                },
                {
                    text: translate("common.btn_skip"),
                    onPress: hideBlockUI
                }
            ]);
        }
    };

    handleGetDataSaleOrder = async (SOInfo) => {
        const { SALEORDERID, ORDERTYPEID } = SOInfo;
        try {
            showBlockUI();
            const { actionSaleOrder, navigation } = this.props;
            const dataSaleOrder = await actionSaleOrder.getSaleOrderPayment(SALEORDERID);
            const paymentTransactionTypeId = dataSaleOrder?.ExtensionProperty?.paymentTransactionTypeId || ""
            if (dataSaleOrder.cus_WarningMessage) {
                Alert.alert(
                    translate("common.notification"),
                    dataSaleOrder.cus_WarningMessage,
                    [
                        {
                            text: translate("common.customer_decline"),
                            onPress: () => this.handleSkipRandomDiscountPromotion(SOInfo)
                        },
                        {
                            text: translate("common.customer_accept"),
                            onPress: hideBlockUI,
                            style: "cancel"
                        }
                    ]
                );
            } else {
                const profileSO = await this.handleEditSaleOrder(dataSaleOrder.listMainProduct)
                navigation.navigate("SaleOrderPayment", { shouldOriginUpdate: true, profileSO });
                actionSaleOrder.getReportPrinterSocket(ORDERTYPEID);
                actionSaleOrder.getDataQRTransaction(SALEORDERID, paymentTransactionTypeId);
                actionSaleOrder.getDataSCTransaction(SALEORDERID);
            }
            hideBlockUI();
        } catch ({ msgError }) {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => this.handleGetDataSaleOrder(SOInfo),
                }
            ]);
        }
    };

    onPaymentSO = (info) => async () => {
        const { SALEORDERID, ORDERTYPEID, ISSOSCREENSTICKER73 } = info;
        const {
            actionSaleOrder,
            navigation,
            actionManagerSO,
            userInfo: { storeID }
        } = this.props;
        if (ISSOSCREENSTICKER73) {
            Alert.alert('', translate('saleOrderManager.warning_yellow'));
        }
        else {
            showBlockUI();
            this.setState({ scrollY: new Animated.Value(0) });
            const isHasRight = await helper.checkStorePermission(storeID);
            if (isHasRight) {
                actionManagerSO.checkSOPreOrder(SALEORDERID).then(isPre => {
                    actionSaleOrder.setDataSO({
                        SaleOrderID: SALEORDERID,
                        SaleOrderTypeID: ORDERTYPEID
                    }).then(success => {
                        if (isPre) {
                            hideBlockUI();
                            navigation.navigate("PreOrderProduct");
                            actionManagerSO.getPreOrderCart(SALEORDERID);
                        } else {
                            this.handleGetDataSaleOrder(info);
                        }
                    })
                }).catch(msgError => {
                    Alert.alert(translate('common.notification_uppercase'), msgError, [
                        {
                            text: translate('common.btn_skip'),
                            onPress: hideBlockUI,
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: this.onPaymentSO(info),
                        },
                    ]);
                })
            }
            else {
                Alert.alert(translate('common.notification_uppercase'), `Bạn không có quyền trên siêu thị ${storeID}`,
                    [{
                        text: "OK",
                        onPress: hideBlockUI
                    }]
                )
            }
        }
    }

    onPrintSO = (info) => () => {
        const { SALEORDERID, ORDERTYPEID, DEBT } = info;
        const {
            navigation,
            actionSaleOrder,
            actionManagerSO,
        } = this.props;
        actionSaleOrder.setDataSO({
            SaleOrderID: SALEORDERID,
            SaleOrderTypeID: ORDERTYPEID
        }).then(success => {
            actionManagerSO.getContentTypeReport(SALEORDERID);
            actionSaleOrder.getReportPrinterSocket(ORDERTYPEID);
            navigation.navigate("ReprintSaleOrder", { Debt: DEBT });
        })
    }

    processDeleteSO = (saleOrder, info) => {
        if (saleOrder.PrintTimes) {
            const { userInfo: {
                storeID,
                languageID,
                moduleID
            } } = this.props
            const body = {
                loginStoreId: storeID,
                languageID,
                moduleID,
                saleOrderIDList: `${saleOrder.SaleOrderID}`,
                isDelete: true
            };

            actionSaleOrderCreator.getInsuranceBySO(body).then((message) => {
                if (message) {
                    Alert.alert("THÔNG BÁO", message, [{ text: "Đồng ý", onPress: () => this.handleDeleteSO(saleOrder, info) }]);
                } else {
                    this.handleDeleteSO(saleOrder, info);
                }
            });
        } else {
            this.handleDeleteSO(saleOrder, info);
        }
    }

    onDeleteSO = (info) => () => {
        const { SALEORDERID, ORDERTYPEID } = info;
        const {
            actionManagerSO,
            navigation,
            actionSaleOrder,
        } = this.props;
        showBlockUI();
        this.setState({ scrollY: new Animated.Value(0) });
        actionManagerSO.getInfoSODelete(SALEORDERID).then(saleOrder => {
            const { NotifyPre } = saleOrder
            if (!!NotifyPre) {
                Alert.alert(translate('common.notification_uppercase'), NotifyPre, [
                    {
                        text: translate('common.btn_skip'),
                        onPress: hideBlockUI,
                    },
                    {
                        text: translate('common.btn_continue'),
                        onPress: () => this.processDeleteSO(saleOrder, info),
                    },
                ]);
            }
            else {
                this.processDeleteSO(saleOrder, info)
            }

        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI,
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: this.onDeleteSO(info),
                },
            ]);
        })
    }
    handleDeleteSO = (saleOrder, info) => {
        hideBlockUI()
        const { SALEORDERID, ORDERTYPEID } = info;
        const {
            navigation,
            actionSaleOrder,
        } = this.props;
        actionSaleOrder.setDataSO({
            SaleOrderID: SALEORDERID,
            SaleOrderTypeID: ORDERTYPEID
        }).then(success => {
            const { TotalPaid, IsIncome, OrderTypeID } = saleOrder;
            const isCancel = IsIncome && (TotalPaid > 0);
            if (isCancel) {
                navigation.navigate("CancelSO");
                actionSaleOrder.getReportPrinterSocket(OrderTypeID);
            }
            else {
                navigation.navigate("DeleteSO");
            }
        })
    }


    onReadMore = (info) => () => {
        const { SALEORDERID, ORDERTYPEID } = info;
        const { navigation, actionSaleOrder } = this.props;
        actionSaleOrder.setDataSO({
            SaleOrderID: SALEORDERID,
            SaleOrderTypeID: ORDERTYPEID
        }).then(success => {
            navigation.navigate("LogInfoSO");
        })
    }
}

const mapStateToProps = function (state) {
    return {
        dataSearchSO: state.managerSOReducer.dataSearchSO,
        isFilterDay: state.managerSOReducer.isFilterDay,
        stateSearchSO: state.managerSOReducer.stateSearchSO,
        userInfo: state.userReducer,
        paramFilter: state.managerSOReducer.paramFilter,
        dataSaleOrder: state.saleOrderPaymentReducer.dataSaleOrder
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionDetail: bindActionCreators(actionDetailCreator, dispatch),
        actionEditSaleOrder: bindActionCreators(actionEditSaleOrderCreator, dispatch),
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(SaleOrderManager);

const get_from_to_date = (number = 0) => {
    const toTime = new Date().getTime();
    const toYYMMDD = dateHelper.formatDateYYYYMMDD(new Date(toTime));
    const toDate = `${toYYMMDD}T00:00:00`;
    let fromDate = `${toYYMMDD}T23:59:59`;
    if (number != 0) {
        const fromTime = toTime + number * ONE_DAY;
        const fromYYMMDD = dateHelper.formatDateYYYYMMDD(new Date(fromTime));
        fromDate = `${fromYYMMDD}T23:59:59`;
    }
    return { fromDate, toDate };
}

const DayTitle = ({ title }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bg199092,
            padding: 6,
            justifyContent: "center",
        }}>
            <MyText
                text={title}
                style={{
                    color: COLORS.txtFFFFFF,
                    fontWeight: 'bold'
                }}
            />
        </View>
    );
}

const ButtonLoadMore = ({ onLoad, number, disabled }) => {
    const numberDay = number - 1;
    const { fromDate, toDate } = get_from_to_date(numberDay);
    const dd_mm_yyyy = dateHelper.formatStrDateDDMMYYYY(fromDate);
    const onPress = () => {
        onLoad({ fromDate, toDate, numberDay });
    }
    return (
        <View style={{
            paddingVertical: 4,
            backgroundColor: COLORS.bgF5F5F5,
            opacity: disabled ? 0.8 : 1
        }}>
            <Button
                text={`${translate('saleOrderManager.view_more')} ${dd_mm_yyyy}`}
                styleContainer={{
                    backgroundColor: COLORS.btn288AD6,
                    borderColor: COLORS.bd288AD6,
                    paddingHorizontal: 20,
                    paddingVertical: 10,
                    borderRadius: 20,
                    alignSelf: "center"
                }}
                styleText={{
                    color: COLORS.txtFFFFFF,
                    fontWeight: 'bold'
                }}
                onPress={onPress}
                disabled={disabled}
            />
        </View>
    );
}

const validateAllowEditSO = (userInfo) => {
    const { brandID, userName, storeID } = userInfo;
    if (
        !helper.checkConfigCreateSO(userName) &&
        !helper.checkConfigTimeSale(brandID) &&
        !helper.checkConfigTimeSalePreOrder(storeID)
    ) {
        Alert.alert("", "Bạn chỉ được phép thao tác chức năng này trong giờ bán hàng");
        return false;
    }
    return true
}