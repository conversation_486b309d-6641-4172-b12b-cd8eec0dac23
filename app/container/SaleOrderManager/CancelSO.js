import React, { useState, useEffect, useCallback } from "react";
import {
    View,
    Alert,
    SafeAreaView,
    StyleSheet
} from "react-native";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useFocusEffect } from '@react-navigation/native';
import {
    showBlockUI,
    hideBlockUI,
    MyText,
    Icon
} from "@components";
import { constants } from "@constants";
import { helper, dateHelper, convertHtml2Image, printSocket } from "@common";
import PaymentCard from "./component/PaymentCard/index";
import MoneyCard from "./component/PaymentCard/MoneyCard";
import DebtRefundInfo from "./component/DebtRefundInfo";
import PaymentMoney from "./component/PaymentMoney";
import InOutMoney from "./component/InOutMoney";
import PrintReport from "./component/PrintReport/index";
import Report from "./component/PrintReport/Report";
import PaymentQRCode from "./component/PaymentQRCode/index";
import MoneyQR from "./component/PaymentQRCode/MoneyQR";
import { InputReason, Title, ButtonAction } from "./component/common";
import * as actionManagerSOCreator from "./action";
import * as actionSaleOrderCreator from "../SaleOrderPayment/action";
import { translate } from '@translate';
import { COLORS } from "@styles";
import * as actionCollectionManagerCreator from '../CollectionTransferManager/action';
import ButtonCollection from "../CollectionTransfer/component/ButtonCollection";
import * as actionCollectionCreator from '../CollectionTransfer/action';
import AcceptCancel from "../CollectionTransferManager/component/AcceptCancel";
import * as actionInsuranceBrightsideCreator from "../InsuranceBrightside/action";
import InputMoney from "./component/Money/InputMoney";
const { H_BILL } = constants;

const CancelSO = ({
    route,
    navigation,
    actionManagerSO,
    saleOrder,
    paramFilter,
    statePrinter,
    printerRetail,
    actionSaleOrder,
    defaultReport,
    userInfo,
    actionCollectionManager,
    actionCollection,
    dataInserAndCreateTicket,
    itemCatalog,
    actionInsuranceBrightside
}) => {
    const [reason, setReason] = useState("");
    const [cashPayment, setCashPayment] = useState(0);
    const [dataMoney, setDataMoney] = useState([]);
    const [totalMoneyCard, setTotalMoneyCard] = useState(0);
    const [reportRetail, setReportRetail] = useState({});
    const [statusTicket, setStatusTicket] = useState({});
    const {
        IsCMOutVoucherDetailMoneyBankEdit,
        GiftVoucherAmountNotApplyByItSelf,
        TotalPointLoyalty,
        MoneyCardList,
        MoneyBank,
        CashVND,
        OutVoucherMoney,
        HoldFeePolicy,
        TotalGiftVoucherFee,
        MoneyBankTranfers,
        IsShowOutVoucherMoney,
        VoucherConcernType,
        IsIncome,
        ExtensionProperty,
        MoneyCard
    } = saleOrder;
    const dataPOS = MoneyCardList || [];
    const cashVND = cashPayment;
    const totalGiftVoucher = parseFloat(GiftVoucherAmountNotApplyByItSelf);
    const moneyBank = parseFloat(MoneyBank);
    const totalPoint = parseFloat(TotalPointLoyalty);
    const cmMoney = (totalGiftVoucher + totalPoint + moneyBank + cashVND + totalMoneyCard + MoneyCard);
    const debt = (OutVoucherMoney - cmMoney);
    const getTicketStatus = statusTicket?.STATUSID;
    const getStatusMess = statusTicket?.STATUSMESS;
    const { LISTUSERAPPROVE } = dataInserAndCreateTicket ?? '';
    const isSendTicket = ExtensionProperty?.IsSendTicket;

    const {
        TICKETID,
        AIRTIMETRANSACTIONID,
        SERVICEVOUCHERID
    } = route.params ?? '';
    const didMount = () => {
        setCashPayment(CashVND);
        setDataMoney([]);
        setTotalMoneyCard(0);
    }

    const effectChangeCard = () => {
        let sum = 0;
        dataMoney.forEach(ele => sum += ele.MoneyCard);
        setTotalMoneyCard(sum);
    }

    const effectChangeReport = () => {
        setReportRetail(defaultReport.retail);
    }

    useEffect(didMount, [])

    useEffect(effectChangeCard, [dataMoney])

    useEffect(effectChangeReport, [defaultReport])

    useFocusEffect(
        useCallback(() => {
            if (dataInserAndCreateTicket) {
                actionCollection.cleardDataTicket();
            }
        }, [dataInserAndCreateTicket.STATUSID, actionCollection])
    );

    const deleteCard = (index) => () => {
        const newData = dataMoney.filter((ele, id) => id != index);
        setDataMoney(newData);
    }

    const applyCard = (value) => {
        const newData = [...dataMoney];
        newData.push(value);
        setDataMoney(newData);
    }

    const onCheckSaleOrder = () => {
        if (!helper.IsNonEmptyString(reason)) {
            Alert.alert("", translate('saleOrderManager.please_enter_cancel_reason'));
            return false;
        }
        if (IsShowOutVoucherMoney && (debt != 0)) {
            Alert.alert("", translate('saleOrderManager.debt_must_be_0'));
            return false;
        }
        if (helper.IsEmptyObject(reportRetail)) {
            Alert.alert("", translate('saleOrderManager.please_select_printer'));
            return false;
        }
        return true;
    }

    const onCancelSOAndCreateCM = () => {
        const isValidate = onCheckSaleOrder();
        if (isValidate) {
            const dataInfo = helper.deepCopy(saleOrder);
            const content = getHoldFeePolicyInfo(saleOrder);
            dataInfo.ContentDeleted = reason;
            dataInfo.CashVND = cashVND;
            Alert.alert("", content, [
                {
                    text: translate('saleOrderManager.btn_skip_uppercase'),
                    style: "cancel"
                },
                {
                    text: translate('saleOrderManager.btn_continue_uppercase'),
                    style: "default",
                    onPress: () => completeAndCreateCM(dataInfo)
                }
            ])
        }
    }

    const completeAndCreateCM = (dataInfo) => {
        // const { storeID, brandID } = userInfo;
        // const isGetContentHTML = helper.checkConfigStorePrint(storeID);
        // const isFitContent = (`${brandID}` == '8') || isGetContentHTML;
        showBlockUI();
        actionManagerSO.cancelSaleOrder(dataInfo).then((smartPayRefund) => {
            handleResponse(smartPayRefund);
        }).catch((error) => {
            Alert.alert(translate('common.notification'), error.msgError, [
                {
                    text: translate('saleOrderManager.btn_skip_uppercase'),
                    onPress: hideBlockUI,
                },
                {
                    text: translate('saleOrderManager.btn_retry_uppercase'),
                    onPress: () => completeAndCreateCM(dataInfo),
                },
            ]);
        });
    }

    const goBack = () => {
        const { VoucherConcernType } = saleOrder;
        const { fromDate, toDate } = paramFilter;
        const { userName } = userInfo;
        hideBlockUI();
        const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? {};
        if (AirtimeServiceGroupID == 7 && ServiceCategoryID == 3) {
            const data = {
                fromDate: new Date(),
                toDate: new Date(),
                keyword: "",
                searchType: "",
                airtimetrsTypeIDList: "",
            }
            actionInsuranceBrightside.getSearchHistoryInsurance(data)
        }
        else if (VoucherConcernType == 6) {
            const data = {
                keyword: '',
                ProcessUser: userName,
                FromDate: fromDate,
                ToDate: toDate,
                IsDeleted: false,
            }
            actionCollectionManager.getSearchCollectionManager(data)
        } else {
            actionManagerSO.getDataSearchSO(paramFilter);
        }
        navigation.goBack();
    }

    const getReportPrinter = () => {
        const { OrderTypeID } = saleOrder;
        actionSaleOrder.getReportPrinterSocket(OrderTypeID);
    }

    const handleResponse = (data) => {
        const { MoneyBankTransferRefundInfo, WarningSOIncludeInsurance } = data
        let content = translate('saleOrderManager.cancel_order_success');
        if (helper.IsNonEmptyString(MoneyBankTransferRefundInfo)) {
            const parsedMoneyBankTransferRefundInfo = MoneyBankTransferRefundInfo.replace(/\\n/g, '\n');
            content += `\n${parsedMoneyBankTransferRefundInfo}`;
        }
        if (helper.IsNonEmptyString(WarningSOIncludeInsurance)) {
            content += `${WarningSOIncludeInsurance}`;
        }
        Alert.alert("", content, [
            {
                text: translate('saleOrderManager.btn_skip_uppercase'),
                onPress: goBack,
            },
            {
                text: translate('saleOrderManager.print_expense_report'),
                onPress: onCheckPrintContent(data),
            },
        ]);
    }

    const onCheckPrintContent = (data) => () => {
        data.isFitContent = true;
        data.isGetContentHTML = true;
        onConvertHTML(data, data.isFitContent);
    }

    const onPrintBillBase64PDF = (data) => {
        const requestAPI = getPrintRequestAPI(data);
        if (helper.IsNonEmptyArray(requestAPI)) {
            printAllRequest(requestAPI);
        }
        else {
            goBack();
        }
    }

    const getPrintRequestAPI = (data) => {
        const requestAPI = [];
        const {
            OutVoucherContent,
            OutVoucherContentPrinterTypeID,
            OutVoucherContentNumberOfCopy,
        } = data;
        if (helper.IsNonEmptyString(OutVoucherContent)) {
            const report = reportRetail; //getReport(OutVoucherContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                for (let i = 0; i < OutVoucherContentNumberOfCopy; i++) {
                    const printService = getPrintService(report, OutVoucherContent);
                    requestAPI.push(printService);
                }
            }
        }
        return requestAPI;
    }

    const getPrintService = (report, content) => {
        let printerConfig = {
            strPrinterName: report.PRINTERNAME,
            strPaperSize: report.PAPERSIZE,
            paperwidth: report.PAPERWIDTH,
            parperheight: report.PARPERHEIGHT,
            intCopyCount: 1,
            bolIsDuplex: false,
            bolShrinkToMargin: false,
            strBase64: content,
        };
        if (report.REPORTID == 2820) {
            printerConfig.strPaperSize = "A4 210 x 297 mm";
        }
        let formBody = [];
        for (const property in printerConfig) {
            const encodedKey = encodeURIComponent(property);
            const encodedValue = encodeURIComponent(printerConfig[property]);
            formBody.push(encodedKey + "=" + encodedValue);
        }
        formBody = formBody.join("&");
        return new Promise((resolve, reject) => {
            actionSaleOrderCreator.printBillVoucher(formBody).then(result => {
                resolve(result);
            }).catch(msgError => {
                reject(msgError);
            });
        });
    }

    const printAllRequest = (allPromise) => {
        Promise.all(allPromise).then(result => {
            console.log("PRINT RSULT", result);
            Alert.alert("", translate('saleOrderManager.print_success'), [
                {
                    text: "OK",
                    style: "default",
                    onPress: goBack
                }
            ]);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: goBack
                }
            ]);
        })
    }

    const printAllRequestFW = async (allPromise) => {
        try {
            for (const body of allPromise) {
                await actionSaleOrderCreator.printBillVoucherBit(body);
                await helper.sleep(1000);
            }
            goBack();
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: goBack
                }
            ]);
        }
    }

    const printAllRequestSocket = async (allPromise) => {
        try {
            for (const { data, ip, delay } of allPromise) {
                await printSocket(data, ip);
                if (delay > 0) {
                    await helper.sleep(delay);
                }
            }
            goBack();
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: goBack
                }
            ]);
        }
    }

    const requestConvertHtml = async (data) => {
        try {
            const requestConvert = [];
            const { OutVoucherContentHTML } = data;
            if (helper.IsNonEmptyString(OutVoucherContentHTML)) {
                const outVoucherContent = await convertHtml2Image(OutVoucherContentHTML, H_BILL);
                requestConvert.push(outVoucherContent);
            }
            return requestConvert;
        } catch (error) {
            console.log('convertHtml2Image', error);
            Alert.alert("", 'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.', [
                {
                    text: "OK",
                    style: "default",
                    onPress: hideBlockUI
                }
            ]);
        }
    }

    const onConvertHTML = async (data) => {
        const dataBit = await requestConvertHtml(data);
        if (helper.IsNonEmptyArray(dataBit)) {
            onPrintBillHTML(dataBit);
        }
        else {
            goBack();
        }
    }

    const getPrintServiceHTML = (report, info, type) => {
        const { userName } = userInfo;
        let body = {
            "Printer": report.PRINTERSHORTNAME,
            "Value": info,
            "Type": type,
            "User": userName,
            "Status": "Payment"
        }
        return body;
    }

    const getPrintServiceSocket = (report, info) => {
        let body = {
            "ip": report.IPPRINTER,
            "delay": report.DELAY,
            "data": info
        }
        return body;
    }

    const getPrintHTMLRequestAPI = (dataConvert) => {
        const requestAPI = [];
        const report = reportRetail;
        if (helper.IsNonEmptyArray(dataConvert)) {
            dataConvert.forEach(info => {
                if (!report.IPPRINTER) {
                    report.IPPRINTER = "*************";
                    report.DELAY = 500;
                }
                const printService = getPrintServiceSocket(report, info);
                requestAPI.push(printService);
            })
        }
        return requestAPI;
    }

    const onPrintBillHTML = (dataBit) => {
        const requestAPI = getPrintHTMLRequestAPI(dataBit);
        if (helper.IsNonEmptyArray(requestAPI)) {
            printAllRequestSocket(requestAPI);
        }
        else {
            goBack();
        }
    }

    const handleReplyTicket = (TICKETID, AIRTIMETRANSACTIONID) => {
        showBlockUI();
        actionCollection.createTicletServiceRequest(AIRTIMETRANSACTIONID).then((reponseDataTicket) => {
            console.log(TICKETID, AIRTIMETRANSACTIONID, "TICKETID, AIRTIMETRANSACTIONID")
            hideBlockUI();
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI,
                },
                {

                    text: "Thử lại",
                    onPress: () => handleReplyTicket(TICKETID, AIRTIMETRANSACTIONID)
                }
            ])
        });
    }

    const handleQueryStatus = () => {
        showBlockUI();
        const { TICKETID, AIRTIMETRANSACTIONID } = dataInserAndCreateTicket ?? {};
        const data = {
            TICKETID: TICKETID,
            AIRTIMETRANSACTIONID: AIRTIMETRANSACTIONID,
            TicketType: 1
        }
        actionCollection.checksSatusTicketService(data).then((reponseStatus) => {
            hideBlockUI();
            setStatusTicket(reponseStatus)
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI,
                },
                {

                    text: "Thử lại",
                    onPress: () => { handleQueryStatus() }
                }
            ])
        });
    }

    return (
        <SafeAreaView style={{
            flex: 1,
            backgroundColor: COLORS.bgFFFFFF
        }}>
            <KeyboardAwareScrollView
                style={{
                    flex: 1
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps={"never"}
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF,
                    paddingVertical: 10
                }}>
                    <InputReason
                        value={reason}
                        onChangeText={(text) => {
                            if (helper.isValidateCharVN(text)) {
                                setReason(text);
                            }
                        }}
                    />
                    <InOutMoney
                        info={saleOrder}
                        onLink={() => { }}
                    />
                    {
                        IsShowOutVoucherMoney &&
                        <View style={{
                            width: constants.width,
                        }}>
                            <Title
                                value={translate('saleOrderManager.expense_uppercase')}
                            />
                            {
                                (totalGiftVoucher > 0) &&
                                <PaymentMoney
                                    title={translate('saleOrderManager.voucher')}
                                    total={totalGiftVoucher}
                                />
                            }
                            {
                                (totalPoint > 0) &&
                                <PaymentMoney
                                    title={translate('saleOrderManager.vip_point')}
                                    total={totalPoint}
                                />
                            }
                            {
                                (moneyBank > 0) &&
                                <PaymentQRCode
                                    title={translate('saleOrderManager.qr_cash')}
                                    dataMoney={MoneyBankTranfers}
                                    renderItem={({ item, index }) => (
                                        <MoneyQR
                                            key={`PaymentQRCode ${index}`}
                                            info={item}
                                            onRemove={deleteCard(index)}
                                        />
                                    )}
                                    total={moneyBank}
                                />
                            }
                            {
                                IsCMOutVoucherDetailMoneyBankEdit &&
                                <PaymentCard
                                    title={translate('saleOrderManager.credit')}
                                    dataMoney={dataMoney}
                                    renderItem={({ item, index }) => (
                                        <MoneyCard
                                            key={`PaymentCard ${index}`}
                                            info={item}
                                            onRemove={deleteCard(index)}
                                        />
                                    )}
                                    dataPOS={dataPOS}
                                    applyMoney={applyCard}
                                    total={totalMoneyCard}
                                    maxPayment={debt}
                                />
                            }
                            {MoneyCard > 0 && <View style={{
                                backgroundColor: COLORS.bgCBE5B2,
                                width: constants.width,
                                borderTopWidth: StyleSheet.hairlineWidth,
                                borderTopColor: COLORS.bdFFFFFF,
                                marginTop: 1
                            }}>
                                <InputMoney
                                    name={"Tiền cà thẻ "}
                                    value={MoneyCard}
                                    onChange={() => { }}
                                    editable={false}
                                />

                            </View>}
                            <DebtRefundInfo
                                debt={debt}
                                cash={cashPayment}
                                onChange={(value) => {
                                    setCashPayment(value);
                                }}
                                maxValue={OutVoucherMoney}
                                editable={IsCMOutVoucherDetailMoneyBankEdit}
                            />


                        </View>
                    }
                    <PrintReport
                        title={translate('saleOrderManager.select_printer')}
                        statePrinter={statePrinter}
                        onTryAgains={getReportPrinter}
                        dataRetail={printerRetail}
                        renderItemRetail={({ item, index }) => (<Report
                            key={`ReportRetail`}
                            info={item}
                            report={reportRetail}
                            onCheck={() => {
                                setReportRetail(item);
                            }}
                        />)}
                    />
                    {
                        isSendTicket ?
                            <View>
                                {
                                    helper.IsEmptyObject(dataInserAndCreateTicket) ?
                                        <View>
                                            <AcceptCancel
                                                title="QUẢN LÝ SIÊU THỊ XÁC NHẬN"
                                                onPress={() => handleReplyTicket(TICKETID, AIRTIMETRANSACTIONID)}
                                            />
                                        </View>
                                        :
                                        <View>
                                            <View style={{
                                                padding: 10
                                            }}>
                                                <View style={{
                                                    backgroundColor: COLORS.bgFFFFFF,
                                                    borderRadius: 7,
                                                    padding: 10,
                                                    shadowColor: COLORS.bg7F7F7F,
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 0,
                                                    },
                                                    shadowOpacity: 0.5,
                                                    shadowRadius: 1,
                                                    elevation: 5,
                                                }}>
                                                    <MyText
                                                        text={"Yêu cầu hoàn tiền của giao dịch "}
                                                        addSize={-1.5}
                                                        style={{
                                                            color: COLORS.txt333333,
                                                            marginBottom: 10,
                                                            fontSize: 15,
                                                            marginTop: 10,
                                                            marginLeft: 5
                                                        }} >
                                                        {
                                                            <MyText
                                                                text={`[${SERVICEVOUCHERID}]`}
                                                                addSize={-1.5}
                                                                style={{
                                                                    color: COLORS.txtD0021B,
                                                                    fontSize: 15
                                                                }}
                                                            >
                                                                {
                                                                    <MyText
                                                                        text={` của bạn đã được gửi thông báo đến app X-Work của quản lý siêu thị có chấm công trong ca gồm: ${LISTUSERAPPROVE}. Vui lòng chờ quản lý siêu thị xác nhận trên App X-Work!`}
                                                                        addSize={-1.5}
                                                                        style={{
                                                                            color: COLORS.txt333333,
                                                                            fontSize: 15
                                                                        }}
                                                                    />
                                                                }
                                                            </MyText>
                                                        }
                                                    </MyText>
                                                    <View style={{
                                                        flexDirection: 'row'
                                                    }}>
                                                        <Icon
                                                            iconSet={'MaterialIcons'}
                                                            name={'info-outline'}
                                                            color={COLORS.bgFF0000}
                                                            size={18}
                                                        />
                                                        <MyText
                                                            text={"Ticket có hiệu lực trong 10 phút"}
                                                            addSize={-1.5}
                                                            style={{
                                                                color: COLORS.bgFF0000,
                                                                fontSize: 15,
                                                                marginLeft: 5,
                                                                fontStyle: 'italic'
                                                            }}
                                                        />
                                                    </View>
                                                </View>
                                                <View style={{
                                                    flexDirection: 'row',
                                                    backgroundColor: COLORS.bgFFFFFF,
                                                    borderRadius: 7,
                                                    padding: 10,
                                                    alignItems: 'center',
                                                    shadowColor: COLORS.bg7F7F7F,
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 0,
                                                    },
                                                    shadowOpacity: 0.5,
                                                    shadowRadius: 1,
                                                    elevation: 5,
                                                    marginTop: 10
                                                }}>
                                                    <MyText
                                                        text={"Trạng thái ticket:"}
                                                        addSize={-1.5}
                                                        style={{
                                                            color: COLORS.txt333333,
                                                            marginBottom: 10,
                                                            fontSize: 15,
                                                            marginTop: 10,
                                                            marginLeft: 5,
                                                            flex: 1,
                                                            fontWeight: 'bold',
                                                            width: 30
                                                        }} />
                                                    <MyText
                                                        text={getTicketStatus != null ? getStatusMess : "Đã gửi cho QLST"}
                                                        addSize={-1.5}
                                                        style={{
                                                            color: (getTicketStatus != "APPROVE" && getTicketStatus != null) ? COLORS.bgFF0000 : COLORS.bg00AAFF,
                                                            fontSize: 15,
                                                            fontWeight: 'bold',
                                                            width: 250
                                                        }}
                                                    />
                                                </View>
                                            </View>
                                            <View style={{
                                                padding: 10
                                            }}>
                                                {

                                                    getTicketStatus == "APPROVE" ?
                                                        <ButtonAction
                                                            onPress={onCancelSOAndCreateCM}
                                                            disabled={false}
                                                        />
                                                        :
                                                        <View style={{
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                            flexDirection: 'row'
                                                        }}>
                                                            <ButtonCollection
                                                                onPress={() => handleReplyTicket(TICKETID, AIRTIMETRANSACTIONID)}
                                                                disabled={helper.IsEmptyObject(statusTicket) || getTicketStatus == undefined || getTicketStatus == "WAITING" ? true : false}
                                                                title={"GỬI LẠI TICKET"}
                                                                iconSet={"Ionicons"}
                                                                nameIcon={"reload"}
                                                                style={{
                                                                    backgroundColor: COLORS.bg00A98F
                                                                }}
                                                                opacity={helper.IsEmptyObject(statusTicket) || getTicketStatus == undefined || getTicketStatus == "WAITING" ? 0.5 : 1}
                                                            />
                                                            <View style={{ flex: 1 }} />
                                                            <ButtonCollection
                                                                onPress={() => handleQueryStatus()}
                                                                title={"KIỂM TRA KẾT QUẢ"}
                                                                iconSet={"Ionicons"}
                                                                nameIcon={"search"}
                                                                style={{
                                                                    backgroundColor: COLORS.bg1E88E5,
                                                                }}
                                                            />
                                                        </View>
                                                }
                                            </View>
                                        </View>
                                }
                            </View>
                            :
                            <View>
                                <ButtonAction
                                    onPress={onCancelSOAndCreateCM}
                                    disabled={false}
                                />
                            </View>
                    }
                </SafeAreaView>
            </KeyboardAwareScrollView>
        </SafeAreaView>
    );
}

const mapStateToProps = (state) => ({
    saleOrder: state.managerSOReducer.infoSODelete,
    paramFilter: state.managerSOReducer.paramFilter,
    printerRetail: state.saleOrderPaymentReducer.printerRetail,
    defaultReport: state.saleOrderPaymentReducer.defaultReport,
    statePrinter: state.saleOrderPaymentReducer.statePrinter,
    userInfo: state.userReducer,
    dataInserAndCreateTicket: state.collectionReducer.dataInserAndCreateTicket,
    itemCatalog: state.collectionReducer.itemCatalog
});

const mapDispatchToProps = (dispatch) => ({
    actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
    actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
    actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
    actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    actionInsuranceBrightside: bindActionCreators(actionInsuranceBrightsideCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(CancelSO);

const getHoldFeePolicyInfo = (saleOrder) => {
    const {
        HoldFeePolicy,
        TransferAndChanceFeePolicy,
        DepositFeePolicy,
        TimeToPayDeposit
    } = saleOrder
    let info = translate('saleOrderManager.confirm_cancel');
    if (HoldFeePolicy > 0) {
        info = translate('saleOrderManager.additional_receipt_order');
        if (DepositFeePolicy > 0) {
            info += `${translate('saleOrderManager.deposit_new_line')} ${helper.convertNum(DepositFeePolicy)}`;
        }
        if (TransferAndChanceFeePolicy > 0) {
            info += `${translate('saleOrderManager.detain_from_CO_new_line')} ${helper.convertNum(TransferAndChanceFeePolicy)}`;
        }
        if (helper.IsNonEmptyString(TimeToPayDeposit)) {
            info += `${translate('saleOrderManager.deposit_term_new_line')} ${dateHelper.formatStrDateFULL(TimeToPayDeposit)}`;
        }
        return `${info}${translate('saleOrderManager.confirm_cancel_new_line')}`;
    }
    else {
        return info;
    }
}