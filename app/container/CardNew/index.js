import React, { Component } from 'react';
import {
    View,
    FlatList,
    Pressable,
    ScrollView,
    TouchableOpacity,
    Alert,
    Keyboard,
    Text
} from 'react-native';
import SafeAreaView from 'react-native-safe-area-view';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { constants, STORAGE_CONST, DEVICE } from '@constants';
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux';
import * as actionCardCreator from "./action";
import {
    MyText,
    Icon,
    Button,
    BaseLoading,
    hideBlockUI,
    showBlockUI,
    TitleInput,
} from "@components";
import { COLORS } from "@styles";
import Quantity from './component/Quantity';
import TotalMoney from './component/TotalMoney';
import TotalAmount from './component/TotalAmount';
import { helper, storageHelper } from "@common";
import TitleTable from './component/TitleTable';
import TabFilter from './component/TabFilter';
import { translate } from '@translate';
import RadioGender from "./component/Radio/RadioGender";
import * as actionShoppingCartCreator from "../ShoppingCart/action";
import * as actionPaymentOrderCreator from "../SaleOrderPayment/action";
import CheckBoxPolicy from '../ShoppingCart/component/CheckBoxPolicy';
const { TYPE_PROFILE } = constants
class SellCardManager extends Component {

    constructor(props) {
        super(props)
        this.state = {
            counter: 1,
            cardType: 0,
            brandID: 0,
            selectedPrice: {},
            gender: null,
            customerPhone: "",
            customerName: "",
            customerAddress: "",
            taxID: "",
            contactPhone: "",
            contactName: "",
            contactAddress: "",
            isLockTax: false,
            isLockName: false,
            isLockAddress: false,
            isLockPhone: false,
            brandidCardType: {},
            brandidDenominations: {},
            isSelectedPolicy: false,
            disabledPolicy: false,
        }
        this.scrollViewRef = React.createRef(null);
        this.isFirstRenderProfile = React.createRef(true)
    }

    componentDidMount() {
        const { cardType, brandID } = this.state;
        const { actionCard } = this.props;
        actionCard.getCardListRequest(cardType, brandID);
        actionCard.resetOrderCart();
    }

    componentDidUpdate(preProps, preStates) {
        this.handleChangeValueProfiles({ preProps, preStates })
    }

    handleGetCardRequest = (CardType, brandID = 0) => {
        const { actionCard } = this.props;
        this.setState({ selectedPrice: {} })
        actionCard.getCardListRequest(CardType, brandID);
    }

    handMinus = () => {
        const { counter } = this.state
        if (counter >= 2) {
            this.setState({ counter: counter - 1 })
        }
    }

    handlePlus = () => {
        this.setState({ counter: this.state.counter + 1 })
    }
    onClickItem = (item) => {
        this.setState({ brandidCardType: item })
        const { actionCard } = this.props;
        const { cardType } = this.state;
        this.setState({
            brandID: item.BrandID,
            selectedPrice: {}
        })
        actionCard.getProductCardListRequest(cardType, item.BrandID)
    }

    onClickItemDenominations = (item) => {
        this.setState({ brandidDenominations: item });
        const { actionCard } = this.props;
        showBlockUI()
        actionCard.getActualCardRequest(item).then((actualPrice) => {
            this.setState({
                selectedPrice: actualPrice,
                counter: 1
            })
            hideBlockUI();
        }).catch(msgError => {
            Alert.alert("", msgError, [{
                text: "OK",
                onPress: hideBlockUI
            }])
        });
    }

    handleAddCart = () => {
        const {
            saleOrderCart,
            actionCard,
            userInfo: {
                storeID
            },
        } = this.props;
        const {
            brandidCardType,
            brandidDenominations
        } = this.state
        const { selectedPrice, counter } = this.state;
        const {
            productID,
            imei,
            inventoryStatusID,
            outputTypeID,
            appliedQuantity,
            outputStoreID,
            price,
            deliveryTypeID,
            saleProgramID,
            packagesTypeId
        } = selectedPrice.mainProduct;
        const data = {
            mainProduct: {
                productID,
                imei,
                inventoryStatusID,
                outputTypeID,
                appliedQuantity: counter,
                outputStoreID,
                price,
                deliveryTypeID,
                saleProgramID,
                packagesTypeId
            },
            delivery: {
                "deliveryStoreID": storeID,
                "deliveryTypeID": deliveryTypeID,
                "deliveryVehicles": 0,
                "deliveryDistance": 0,
                "shippingCost": 0,
                "deliveryTime": "",
                "deliveryProvinceID": 0,
                "deliveryDistrictID": 0,
                "deliveryWardID": 0,
                "deliveryAddress": "",
                "contactGender": true,
                "contactPhone": "",
                "contactName": "",
                "customerNote": ""
            },
            cartRequest: saleOrderCart,
            storeRequests: [],
        }
        if (counter > 10) {
            Alert.alert(
                translate('common.notification_uppercase'),
                "Bạn có đòng ý với số lượng lớn 10 không? ",
                [
                    {
                        text: "Đồng ý",
                        onPress: () => {
                            showBlockUI();
                            actionCard.getAddCartRequest(data).then(success => {
                                hideBlockUI();
                            }).catch(error => {
                                Alert.alert(
                                    translate('common.notification_uppercase'), error,
                                    [
                                        {
                                            text: translate('common.btn_close'),
                                            onPress: hideBlockUI
                                        }
                                    ]
                                );
                            });
                        }
                    },
                    {
                        text: "Hủy",
                        style: "cancel",
                        onPress: () => { }
                    },
                ]
            );

        }
        if (brandidCardType.BrandID !== brandidDenominations.BrandID) {
            Alert.alert(
                translate('common.notification_uppercase'), `Hiện tại mệnh giá thẻ có giá trị là ${brandidDenominations.BrandID} không trùng khớp với loại thẻ mang giá trị ${brandidCardType.BrandID} vui lòng thử lại`,
                [
                    {
                        text: translate('common.btn_close'),
                        onPress: () => this.onClickItem(brandidCardType)
                    }

                ]
            );
        }
        else {
            showBlockUI();
            actionCard.getAddCartRequest(data).then(reponse => {
                hideBlockUI();
            }).catch(error => {
                Alert.alert(
                    translate('common.notification_uppercase'), error,
                    [
                        {
                            text: translate('common.btn_close'),
                            onPress: hideBlockUI
                        }
                    ]
                );
            });
        }
    }

    handleDelete = (index) => {
        Alert.alert(
            translate('common.notification_uppercase'),
            "Bạn có muốn xóa thẻ cào này khỏi giỏ hàng?",
            [
                {
                    text: "Đồng ý", onPress: () => {
                        showBlockUI();
                        const { saleOrderCart, actionCard } = this.props;
                        const newCart = helper.deepCopy(saleOrderCart);
                        const { SaleOrderDetails } = newCart;
                        newCart.SaleOrderDetails = SaleOrderDetails.filter((ele, id) => id != index);
                        actionCard.modifyCartRequest(newCart).then(success => {
                            hideBlockUI();
                        }).catch(error => {
                            Alert.alert(
                                translate('common.notification_uppercase'), error,
                                [
                                    {
                                        text: translate('common.btn_close'),
                                        onPress: hideBlockUI
                                    }
                                ]
                            );
                        });
                    }
                },
                {
                    text: "Hủy",
                    style: "cancel",
                    onPress: () => { }
                },
            ]
        );
    }

    renderItem = ({ item, index }) => {
        const {
            stateReceiptCards
        } = this.props;
        const isLock = stateReceiptCards.isFetching;
        return (
            <Pressable
                disabled={isLock}
                onPress={() => {
                    if (!item.selected) {
                        this.onClickItem(item, index)
                    }
                }}
                key={item.BrandID}
                style={{
                    margin: 3,
                    justifyContent: 'center',
                    padding: 10,
                    width: '48.5%',
                    height: 50,
                    borderRadius: 5,
                    shadowOffset: {
                        width: 0,
                        height: 2
                    },
                    shadowOpacity: 0.25,
                    shadowRadius: 4,
                    elevation: 5,
                    backgroundColor: item.selected ? "orange" : '#FFEFD5',
                }}>
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                }}>
                    <Icon
                        iconSet={"MaterialIcons"}
                        name={item.selected ? "radio-button-on" : "radio-button-off"}
                        color={item.selected ? COLORS.bgFFFFFF : COLORS.ic555555}
                        size={20}
                        style={{ marginRight: 3 }}
                    />
                    <MyText
                        style={{
                            color: item.selected ? COLORS.bgFFFFFF : COLORS.txt555555,
                            fontWeight: 'bold',
                            width: 'auto',
                            textAlign: 'center',
                        }}
                        text={item.BrandName}
                    />
                </View>
            </Pressable >
        )
    }

    rdDenominations = ({ item, index }) => {
        return (
            <Pressable
                onPress={() => {
                    if (!item.selected) {
                        this.onClickItemDenominations(item)
                    }
                }}
                key={item.idDenomination}
                style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: constants.width / 3 - constants.getSize(10),
                    paddingVertical: 15,
                    paddingHorizontal: 5,
                    borderWidth: item.selected ? 2 : 1,
                    borderRadius: 5,
                    margin: 2,
                    borderColor: COLORS.bgF5F5F5,
                    backgroundColor: item.selected ? "#6BC750" : '#FFEFD5',
                    shadowColor: "#000",
                    borderColor: item.selected ? '#3CB371' : '#FFFFFF'
                }}>
                <MyText
                    style={{
                        color: item.selected ? COLORS.bgFFFFFF : COLORS.txt555555,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}
                    text={item.ProductShortName}
                />
            </Pressable>
        )
    }

    renderItemCart = ({ item, index }) => {
        return (
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: COLORS.txtFFA500,
                shadowColor: '#000',
                shadowOffset: {
                    width: 0,
                    height: 2
                },
                shadowOpacity: 0.5,
                shadowRadius: 2,
                elevation: 2.5,
                height: 60
            }}>
                <MyText
                    numberOfLines={3}
                    style={{
                        color: COLORS.bgFFFFFF,
                        fontWeight: 'bold',
                        marginLeft: 10,
                        width: '40%',
                    }}
                    text={item.ProductName}
                />
                <MyText
                    style={{
                        color: COLORS.bgFFFFFF,
                        fontWeight: 'bold',
                        padding: 10,
                        width: '15%',
                        textAlign: 'center'
                    }}
                    text={item.Quantity}
                />
                <View style={{
                    backgroundColor: 'red',
                    justifyContent: 'center'
                }}>
                </View>
                <MyText
                    style={{
                        color: COLORS.bgFFFFFF,

                        fontWeight: 'bold',
                        padding: 10,
                        width: '30%',
                        textAlign: 'center'
                    }}
                    text={`${helper.convertNum(item.cus_TotalCost, true)} `}
                />
                <TouchableOpacity
                    style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '12%'
                    }}
                    onPress={() => this.handleDelete(index)}
                >
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        name={'delete-circle'}
                        style={{
                            fontSize: 30,
                            color: 'red',
                            borderRadius: 20

                        }}
                    />
                </TouchableOpacity>
            </View>
        )
    }

    onChangTypeCustomer = (isCompany) => () => {
        const {
            taxID,
            customerName,
            customerAddress,
            customerPhone,
            isLockTax
        } = this.state;
        this.setState({
            isCompany: isCompany,
            taxID: isLockTax ? taxID : "",
            customerPhone: customerPhone,
            customerName: customerName,
            customerAddress: customerAddress,
            contactPhone: customerPhone,
            contactName: customerName,
            contactAddress: customerAddress,
            gender: null,
        }, () => {
            helper.IsNonEmptyString(this.state.contactPhone) && this.handleAPIGetCustomerProfile(this.state.contactPhone)
        })
    }

    getOldCompanyInfo = () => {
        const {
            taxID,
            customerPhone,
            customerName,
            customerAddress,
            isLockPhone,
            isLockName,
            isLockAddress,
        } = this.state;
        if (taxID) {
            this.getCompanyInfo(taxID);
        }
        else {
            storageHelper.getItem(STORAGE_CONST.CUSTOMER_INFO).then(result => {
                if (helper.IsNonEmptyString(result)) {
                    const dataTopInfo = JSON.parse(result);
                    const customerInfo = dataTopInfo.find(ele => helper.IsNonEmptyString(ele.taxID));
                    if (customerInfo) {
                        this.setState({
                            taxID: customerInfo.taxID,
                            customerPhone: isLockPhone
                                ? customerPhone
                                : customerInfo.customerPhone,
                            customerName: isLockName
                                ? customerName
                                : customerInfo.customerName,
                            customerAddress: isLockAddress
                                ? customerAddress
                                : customerInfo.customerAddress,
                            customerIDCard: customerInfo.customerIDCard,
                            gender: customerInfo.contactGender,
                            contactAddress: customerInfo.deliveryAddress,
                            contactPhone: customerInfo.contactPhone,
                            contactName: customerInfo.contactName,
                        });
                    }
                }
            }).catch(error => {
                console.log("getOldCompanyInfo error", error);
            });
        }
    }

    handleChangeValueProfiles = ({ preProps, preStates }) => {
        const { customerConfirmPolicy } = this.props;
        const customerPolicy = customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER];

        if (!helper.IsNonEmptyArray(customerPolicy) || this.isFirstRenderProfile.current) return;

        const { isChange, oldInfoCustomer, infoCustomer } = this.handleGetValueChange({ preProps, preStates });

        if (isChange) {
            const isSigned = customerPolicy?.[0]?.isSigned ?? false;
            const hasChanged = helper.checkChangeValueOfPrototype(infoCustomer, oldInfoCustomer);

            this.setState({
                isSelectedPolicy: hasChanged ? false : isSigned,
                disabledPolicy: hasChanged ? false : isSigned === 1,
            });
        }

        if (preStates.isCompany && !this.state.isCompany) {
            delete this.props.customerConfirmPolicy?.[TYPE_PROFILE.COMPANY];
        }
    };

    handleGetValueChange = ({ preStates }) => {
        const {
            gender,
            contactName,
            contactPhone,
            customerIDCard
        } = this.state
        const { customerConfirmPolicy } = this.props
        let isChange = false;
        let infoCustomer = {}
        let oldInfoCustomer = {
            "customerName": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.customerName,
            "customerPhone": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber,
            "gender": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.gender,
            "cardCustomerId": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.cardCustomerId
        }
        if (!helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER])) return {
            isChange,
            infoCustomer,
            oldInfoCustomer
        }

        if (
            (preStates.contactPhone !== contactPhone ||
                preStates.contactName !== contactName ||
                preStates.gender !== gender ||
                preStates.customerIDCard !== customerIDCard)) {

            return {
                isChange: true,
                infoCustomer: {
                    "customerName": contactName,
                    "customerPhone": contactPhone,
                    "gender": gender,
                    "cardCustomerId": customerIDCard
                },
                oldInfoCustomer
            }
        }

        return {
            isChange,
            infoCustomer,
            oldInfoCustomer
        }
    }



    handleAPIGetCustomerProfile = (phoneNumber) => {
        const {
            userInfo: { storeID, languageID, moduleID },
            customerConfirmPolicy,
            actionShoppingCart,
        } = this.props;

        const {
            customerName,
            isLockName,
            isCompany,
            contactName,
            gender,
        } = this.state;

        const baseBody = {
            loginStoreId: storeID,
            languageID,
            moduleID,
        };

        const regExpPhone = /^0\d{9}$/;
        const isValidPhone = regExpPhone.test(phoneNumber);

        if (!isValidPhone) return;

        showBlockUI();
        if (customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber !== phoneNumber) {
            actionShoppingCart.set_map_customer_confirm_policy({
                type: TYPE_PROFILE.CUSTOMER,
                infoCustomerCRM: [],
            });
        }

        actionShoppingCartCreator
            .getCustomerProfile({
                ...baseBody,
                phoneNumber,
                typeProfile: TYPE_PROFILE.CUSTOMER,
            })
            .then((customerProfile) => {
                hideBlockUI();
                if (!customerProfile) {
                    actionShoppingCart.set_map_customer_confirm_policy({
                        type: TYPE_PROFILE.CUSTOMER,
                        infoCustomerCRM: [],
                    });
                    return;
                }

                const info = customerProfile[0];

                const CustomerName = info.customerName || customerName;
                const CustomerGender = actionShoppingCartCreator.getGender(info.gender);

                const commonState = {
                    isSelectedPolicy: info.isSigned,
                    disabledPolicy: info.isSigned === 1,
                    customerIDCard: info.cardCustomerId || "",
                };

                if (isCompany) {
                    this.setState(
                        {
                            ...commonState,
                            contactPhone: phoneNumber,
                            gender: CustomerGender,
                            contactName: info.customerName || contactName,
                            customerPhone: phoneNumber,
                        },
                        () => {
                            actionShoppingCart.set_map_customer_confirm_policy({
                                type: TYPE_PROFILE.CUSTOMER,
                                infoCustomerCRM: customerProfile,
                            });
                        }
                    );
                } else {
                    this.setState(
                        {
                            ...commonState,
                            customerPhone: "",
                            gender: CustomerGender ?? gender ?? 1,
                            customerName: "",
                            taxID: "",
                            contactPhone: phoneNumber,
                            contactName: CustomerName,
                        },
                        () => {
                            console.log(CustomerName);

                            actionShoppingCart.set_map_customer_confirm_policy({
                                type: TYPE_PROFILE.CUSTOMER,
                                infoCustomerCRM: customerProfile,
                            });
                        }
                    );
                }
            })
            .catch((error) => {
                console.log("lỗi profile", error);
                hideBlockUI();
            })
            .finally(() => {
                this.isFirstRenderProfile.current = false
            });
    };

    getCompanyProfile = (taxID) => {
        const {
            userInfo: { storeID, languageID, moduleID },
            actionShoppingCart,
        } = this.props;


        const baseBody = {
            loginStoreId: storeID,
            languageID,
            moduleID,
        };

        const isValidTax = /^\d{10}$/.test(taxID) || /^\d{10}-\d{3}$/.test(taxID);
        if (!isValidTax) return;

        actionShoppingCartCreator
            .getCustomerProfile({
                ...baseBody,
                phoneNumber: taxID,
                typeProfile: TYPE_PROFILE.COMPANY,
            })
            .then((customerProfile) => {
                if (!customerProfile) {
                    actionShoppingCart.set_map_customer_confirm_policy({
                        type: TYPE_PROFILE.COMPANY,
                        infoCustomerCRM: [],
                    });
                    return;
                }

                const info = customerProfile[0];

                this.setState(
                    {
                        taxID,
                        customerName: info?.companyName || "",
                        customerAddress: info?.address || "",
                    },
                    () => {
                        actionShoppingCart.set_map_customer_confirm_policy({
                            type: TYPE_PROFILE.COMPANY,
                            infoCustomerCRM: customerProfile,
                        });
                    }
                );
            });
    };



    createOutputReceipt = (dataSaleOrder) => {
        const {
            customerName,
            customerAddress,
            taxID,
            isCompany,
            gender,
            contactPhone,
            contactName,
            isSelectedPolicy
        } = this.state;

        const regExpTax10 = /^\d{10}$/;
        const regExpTax14 = /^\d{10}-\d{3}$/;

        const isValidateTax = regExpTax10.test(taxID) || regExpTax14.test(taxID);
        const isValidateTaxCAM = helper.validateTaxCAM ? helper.validateTaxCAM(taxID) : true; // fallback true nếu không có

        if (isCompany) {
            if (!helper.IsNonEmptyString(taxID)) {
                Alert.alert("", translate('shoppingCart.please_enter_tax_code'));
                return false;
            }
            if (global.isVN && !isValidateTax) {
                Alert.alert("", translate('shoppingCart.validation_tax'));
                return false;
            }
            if (!global.isVN && !isValidateTaxCAM) {
                Alert.alert("", translate('shoppingCart.validation_tax_cam'));
                return false;
            }
            if (!helper.IsNonEmptyString(customerName)) {
                Alert.alert("", translate('shoppingCart.validation_company_name'));
                return false;
            }
        }

        const hasContactInfo = gender != null || contactName !== "";
        if (hasContactInfo) {
            if (gender == null) {
                Alert.alert("", translate('shoppingCart.validation_gender'));
                return false;
            }

            // if (contactName === "") {
            //     Alert.alert("", translate('detail.please_enter_contact_name'));
            //     return false;
            // }
        }
        if (!isSelectedPolicy && helper.IsNonEmptyString(contactPhone)) {
            Alert.alert("", "Khách hàng vui lòng đồng ý với chính sách xử lý dữ liệu cá nhân.");
            return false;
        }

        Alert.alert(
            "",
            translate('saleOrderPayment.you_want_finish_order'),
            [
                {
                    text: translate('saleOrderPayment.btn_skip_uppercase'),
                    style: "cancel",
                },
                {
                    text: translate('saleOrderPayment.btn_continue'),
                    style: "default",
                    onPress: () => this.modifyShoppingCart(dataSaleOrder, isCompany),
                },
            ]
        );
    };



    modifyShoppingCart = async (dataSaleOrder, isCompany) => {
        const {
            contactPhone,
            contactName,
            contactAddress,
            gender
        } = this.state;
        const { saleOrderCart, actionCard } = this.props;
        const customer = dataSaleOrder.customerInfo;

        const dataCustomer = isCompany
            ? customer
            : {
                ...customer,
                gender: gender || 1,
                customerPhone: contactPhone,
                customerName: contactName || "Khách lẻ",
                customerAddress: contactAddress || "x"
            };

        const data = {
            customerInfo: dataCustomer,
            cartRequest: saleOrderCart
        };

        try {
            const profile = await this.handleAPIProfile(dataSaleOrder);

            data.cartRequest.SaleOrderDetails.forEach(saleOrderDetail => {
                saleOrderDetail.Profile = profile;
            });

            showBlockUI();
            await actionCard.addToSaleOrderCart(data);
            hideBlockUI();
            this.goToPaymentSO();
        } catch (msgError) {
            hideBlockUI();
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    style: "default"
                }
            ]);
        }
    };

    handleAPIProfile = async (dataSaleOrder) => {
        showBlockUI();

        const {
            customerName,
            contactName,
            contactPhone,
            gender,
            isCompany,
            taxID,
            customerIDCard,
            customerAddress
        } = this.state;

        const {
            userInfo: { storeID, languageID, moduleID },
            customerConfirmPolicy,
        } = this.props;

        const defaultCustomerInfo = {
            customerName: "",
            cardCustomerId: null,
            gender: null,
            profileId: 0,
            type: 1,
            versionCode: "",
            phoneNumber: "",
            isModify: 0,
            isSigned: 0,
            signatureId: 0,
            soProfileId: null,
            relationshipTypeId: 0,
            relationshipId: 0
        };

        const defaultCompanyInfo = {
            companyId: 0,
            companyName: "",
            companyPhone: null,
            address: "",
            email: null,
            taxNo: "",
            profileId: 0,
            type: 5,
            versionCode: "",
            phoneNumber: null,
            isModify: 0,
            isSigned: 0,
            signatureId: 0,
            soProfileId: null,
            relationshipTypeId: 0,
            relationshipId: 0
        };

        const takeFirst = (arr) => Array.isArray(arr) && arr.length ? [arr[0]] : [];

        let newProfile = {
            [TYPE_PROFILE.CUSTOMER]: takeFirst(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]),
            [TYPE_PROFILE.COMPANY]: takeFirst(customerConfirmPolicy[TYPE_PROFILE.COMPANY]),
        };
        let profileRequest = {};
        const newPhoneNumber = contactPhone;

        try {
            // ---- Xác định profile CUSTOMER ----
            const customerInfo = helper.IsNonEmptyArray(customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER])
                ? { ...customerConfirmPolicy[TYPE_PROFILE.CUSTOMER][0] }
                : { ...defaultCustomerInfo };

            const oldInfoCustomer = {
                customerName: customerInfo.customerName,
                customerPhone: customerInfo.phoneNumber,
                gender: customerInfo.gender,
                cardCustomerId: customerInfo.cardCustomerId || ""
            };

            const newInfoCustomer = {
                customerName: contactName,
                customerPhone: contactPhone,
                gender: gender,
                cardCustomerId: customerIDCard
            };

            const hasChangedCustomer = helper.checkChangeValueOfPrototype(newInfoCustomer, oldInfoCustomer);

            if ((hasChangedCustomer || !customerInfo.isSigned) && helper.IsNonEmptyString(newPhoneNumber)) {
                customerInfo.isModify = 1;
                if (customerInfo.phoneNumber !== newPhoneNumber) {
                    customerInfo.versionCode = "";
                    customerInfo.profileId = 0;
                }
                customerInfo.customerName = contactName;
                customerInfo.phoneNumber = contactPhone;
                customerInfo.gender = actionShoppingCartCreator.getGender(gender);
                customerInfo.cardCustomerId = customerIDCard;

                profileRequest[TYPE_PROFILE.CUSTOMER] = [customerInfo];
            }

            if (helper.IsEmptyString(newPhoneNumber)) {
                delete newProfile?.[TYPE_PROFILE.CUSTOMER];
            }

            // ---- Xác định profile COMPANY ----
            if (isCompany) {
                const companyInfo = helper.IsNonEmptyArray(customerConfirmPolicy?.[TYPE_PROFILE.COMPANY])
                    ? { ...customerConfirmPolicy[TYPE_PROFILE.COMPANY][0] }
                    : { ...defaultCompanyInfo };

                if (
                    companyInfo.companyName !== customerName ||
                    companyInfo.address !== customerAddress ||
                    companyInfo.taxNo !== taxID
                ) {
                    companyInfo.isModify = 1;
                    companyInfo.companyName = customerName;
                    companyInfo.address = customerAddress;
                    companyInfo.taxNo = taxID;

                    profileRequest[TYPE_PROFILE.COMPANY] = [companyInfo];
                }
            }
            else {
                delete newProfile?.[TYPE_PROFILE.COMPANY];
            }

            // ---- Gửi request nếu có thay đổi ----
            if (!helper.IsEmptyObject(profileRequest)) {
                const body = {
                    loginStoreId: storeID,
                    languageID: languageID,
                    moduleID: moduleID,
                    profile: profileRequest
                };

                const profileModify = await actionShoppingCartCreator.modifyCustomerProfile(body);

                // Cập nhật profile mới
                newProfile[TYPE_PROFILE.CUSTOMER] =
                    profileModify[TYPE_PROFILE.CUSTOMER] ||
                    newProfile[TYPE_PROFILE.CUSTOMER]?.slice(0, 1) || [];

                newProfile[TYPE_PROFILE.COMPANY] =
                    profileModify[TYPE_PROFILE.COMPANY] ||
                    newProfile[TYPE_PROFILE.COMPANY]?.slice(0, 1) || [];
            }

            return newProfile;

        } catch (error) {
            dataSaleOrder.cartRequest.SaleOrderDetails.forEach(item => {
                item.Profile = {};
            });
            console.error("🚀 handleAPIProfile ERROR:", error);
            return {};
        }
    };


    goToPaymentSO = () => {
        const {
            dataSaleOrderCart
        } = this.props;
        const {
            actionPaymentOrder,
            navigation,
            actionCard
        } = this.props;
        const SaleOrders = dataSaleOrderCart.SaleOrders[0];
        const {
            SaleOrderID
        } = SaleOrders;
        actionPaymentOrder.setDataSO({
            SaleOrderID: SaleOrderID,
            SaleOrderTypeID: 1000
        }).then(success => {
            hideBlockUI();
            navigation.navigate('SaleOrderPayment');
            actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
            actionPaymentOrder.getReportPrinterSocket(1000);
            actionPaymentOrder.getDataQRTransaction(SaleOrderID);
            actionPaymentOrder.getDataSCTransaction(SaleOrderID);
            actionCard.resetOrderCart();
        })
    }

    getCustomerInfo = (phoneNumber, CustomerInfo) => {
        const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            const {
                customerName,
                customerAddress,
                isLockName,
                isLockAddress
            } = this.state;
            actionShoppingCartCreator.getCustomerByPhone(phoneNumber).then(info => {
                const CustomerName = isLockName ? customerName : (info.customerName || customerName);
                const CustomerAddress = isLockAddress ? customerAddress : (info.customerAddress || customerAddress);
                this.setState({
                    customerPhone: phoneNumber,
                    gender: info.gender,
                    customerName: CustomerName,
                    customerAddress: CustomerAddress,
                    taxID: "",
                    contactPhone: "",
                    contactName: "",
                    contactAddress: "",
                });
                CustomerInfo.CustomerPhone = phoneNumber;
                CustomerInfo.CustomerName = CustomerName;
                CustomerInfo.CustomerAddress = CustomerAddress;
            })
        }
    }

    getContactInfo = (phoneNumber) => () => {
        this.isFirstRenderProfile.current = true;
        this.setState({
            isProfile: true
        })
        const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            // const {
            //     contactName,
            //     contactAddress
            // } = this.state;
            // actionShoppingCartCreator.getCustomerByPhone(phoneNumber).then(info => {
            //     this.setState({
            //         contactPhone: phoneNumber,
            //         gender: info.gender,
            //         contactName: info.customerName || contactName,
            //         contactAddress: info.customerAddress || contactAddress,
            //     })
            // })
            this.handleAPIGetCustomerProfile(phoneNumber)
        }
    }

    getCompanyProfile = (taxID) => {
        const {
            userInfo: { storeID, languageID, moduleID },
        } = this.props;
        const {
            customerPhone,
            isLockPhone
        } = this.state;
        const baseBody = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
        }
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidate = (isValidateTax10 || isValidateTax14);
        if (isValidate) {
            actionShoppingCartCreator.getCustomerProfile({ ...baseBody, phoneNumber: taxID, typeProfile: TYPE_PROFILE.COMPANY }).then(customerProfile => {
                if (customerProfile == null) return this.props.actionShoppingCart.set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.COMPANY,
                    infoCustomerCRM: []
                });
                const customerInfo = customerProfile[0]
                this.setState({
                    taxID: taxID,
                    customerName: customerInfo.companyName,
                    customerAddress: customerInfo.address
                    // customerPhone: isLockPhone
                    //     ? customerPhone
                    //     : customerInfo.companyPhone
                }, () => {
                    this.props.actionShoppingCart.set_map_customer_confirm_policy({
                        type: TYPE_PROFILE.COMPANY,
                        infoCustomerCRM: customerProfile
                    });
                })
            })
        }
    }

    getCompanyInfo = (taxID) => {
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidate = (isValidateTax10 || isValidateTax14);
        if (isValidate) {
            actionShoppingCartCreator.getCompanyByTax(taxID).then(info => {
                const {
                    customerPhone,
                    customerName,
                    customerAddress,
                    contactPhone,
                    contactName,
                    contactAddress,
                    isLockPhone,
                    isLockName,
                    isLockAddress,
                } = this.state;
                this.setState({
                    taxID: taxID,
                    gender: info.gender,
                    customerPhone: isLockPhone
                        ? customerPhone
                        : (info.customerPhone || customerPhone),
                    customerName: isLockName
                        ? customerName
                        : (info.customerName || customerName),
                    customerAddress: isLockAddress
                        ? customerAddress
                        : (info.customerAddress || customerAddress),
                    contactPhone: info.contactPhone || contactPhone,
                    contactName: info.contactName || contactName,
                    contactAddress: info.deliveryAddress || contactAddress,
                })
            })
        }
    }

    render() {
        const {
            brands,
            stateBrands,
            receiptCards,
            stateReceiptCards,
            saleOrderCart,
        } = this.props;
        const {
            counter,
            brandID,
            selectedPrice,
            cardType,

            gender,
            customerPhone,
            customerName,
            customerAddress,
            taxID,
            contactPhone,
            contactName,
            contactAddress,
            isCompany,
            isLockPhone,
            isLockName,
            isLockAddress,
            isLockTax,
            customerIDCard,
            isShowCustomer
        } = this.state;
        const defaultGender = gender == null;
        const dataSaleOrder = {
            "customerInfo": {
                "customerName": customerName || "Khách lẻ",
                "customerAddress": customerAddress || "x",
                "customerPhone": customerPhone,
                "taxID": taxID,
                "gender": isCompany ? gender : defaultGender,
                "contactName": taxID ? contactName : "",
                "contactPhone": taxID ? contactPhone : "",
                "deliveryAddress": taxID ? contactAddress : "",
                "contactGender": defaultGender,
                "customerIDCard": customerIDCard,
                "ageID": "",
                "birthday": ""
            },
        }

        const dataBrand = brands.map(brand => ({
            ...brand,
            selected: brand.BrandID === brandID,
        }));
        const dataPriceCard = receiptCards.map(card => ({
            ...card,
            selected: card.TransactionProductID === selectedPrice.transactionProductID,
        }));
        const moneyCard = (selectedPrice?.mainProduct?.salePriceVAT) ? selectedPrice?.mainProduct?.salePriceVAT * counter : 0;
        const totalAmount = saleOrderCart.SHAmount
        const brandsViewHeight = stateBrands.isFetching || stateBrands.isEmpty || stateBrands.isError ?
            constants.getSize(120) :
            (
                brands.length < 10 ?
                    Math.ceil(brands.length / 2) * constants.getSize(60) + 20 :
                    'auto'
            );
        const isLock = stateBrands.isFetching || stateReceiptCards.isFetching;
        const isShowPrice = !helper.IsEmptyObject(selectedPrice);
        const { dataSaleOrderPayment, staffInfo } = this.props;
        const existStaffInfo = !helper.IsEmptyObject(staffInfo)
        const { IsSOScreenSticker } = dataSaleOrderPayment;
        const isDisabledTax = isLockTax || IsSOScreenSticker || existStaffInfo;
        const isDisabledPhone = isLockPhone || IsSOScreenSticker || existStaffInfo;
        const isDisabledPhoneContact = isLockPhone || IsSOScreenSticker || existStaffInfo;
        const isDisabledName = isLockName || IsSOScreenSticker || existStaffInfo;
        const isDisabledAddress = isLockAddress || IsSOScreenSticker || existStaffInfo;
        return (
            <View style={{
                flex: 1
            }}
            >
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                    onContentSizeChange={() => {
                        this.scrollViewRef.current?.scrollToEnd({ animated: true });
                    }}
                    ref={this.scrollViewRef}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            backgroundColor: COLORS.bgFFFFFF,
                        }}
                    >

                        <TabFilter
                            cardType={cardType}
                            onChangeTab={(value) => {
                                this.setState({ cardType: value, brandID: 0 }, () => {
                                    this.handleGetCardRequest(value)
                                })
                            }}
                            disabled={isLock}
                        />

                        <View style={{
                            backgroundColor: COLORS.bgFFFFFF,
                            padding: 10,
                            height: brandsViewHeight
                        }}
                        >
                            <BaseLoading
                                isLoading={stateBrands.isFetching}
                                isError={stateBrands.isError}
                                isEmpty={stateBrands.isEmpty}
                                textLoadingError={stateBrands.description}
                                onPressTryAgains={(position) => {
                                    this.handleGetCardRequest(position)
                                }}
                                content={
                                    <View style={{
                                        justifyContent: 'center',
                                        alignItems: "center",
                                        backgroundColor: COLORS.bgF5F5F5,
                                        height: '100%',
                                        borderRadius: 10,
                                        padding: 5,
                                        alignContent: 'center',
                                        shadowColor: "#000",
                                        shadowOffset: {
                                            width: 0,
                                            height: 2
                                        },
                                        shadowOpacity: 0.25,
                                        shadowRadius: 4,
                                        elevation: 5
                                    }}>
                                        <FlatList
                                            data={dataBrand}
                                            keyExtractor={(item, index) => index.toString()}
                                            renderItem={this.renderItem}
                                            numColumns={2}
                                            bounces={false}
                                            scrollEventThrottle={16}
                                            nestedScrollEnabled={true}
                                            columnWrapperStyle={{
                                                flex: 1,
                                                justifyContent: "space-around"
                                            }}
                                        />
                                    </View>
                                }
                            />
                        </View>
                        <View style={{
                            padding: 10
                        }}>
                            <MyText
                                text={"Lưu ý:"}
                                style={{
                                    fontSize: 15,
                                    fontWeight: 'bold',
                                    color: COLORS.bgFF0000,
                                    fontStyle: "italic",
                                }}
                            />
                            <MyText
                                text={"I - Không bán thẻ cào/thẻ game cho trẻ em dưới 15 tuổi \nII - Từ 01/07/2024:\n1. Thẻ cào Mobifone KHÔNG NẠP được cho số Local \n2. Thẻ cào Vinaphone KHÔNG NẠP được cho số Itel \n3. Để nạp tiền mạng Local/Itel, vui lòng sử dụng tool: Bắn tiền điện thoại tại siêu thị (MWG) hoặc Nạp tiền Airtime trả trước (ERP)"}
                                style={{
                                    fontSize: 14,
                                    color: COLORS.bgFF0000,
                                    marginLeft: 8
                                }}
                            />

                        </View>
                        <ScrollView
                            style={{ flex: 1 }}
                            bounces={false}
                            keyboardShouldPersistTaps="always"
                        >
                            <View style={{ flex: 1, justifyContent: 'center', backgroundColor: 'white' }}>
                                {
                                    brandID != 0 &&
                                    <MyText
                                        style={{
                                            color: 'red',
                                            marginLeft: 5,
                                            fontWeight: 'bold',
                                            paddingTop: 10,
                                            textDecorationLine: 'underline',
                                        }}
                                        text={"MỆNH GIÁ THẺ"}
                                    />
                                }
                                {brandID != 0 && <BaseLoading
                                    isLoading={stateReceiptCards.isFetching}
                                    isError={stateReceiptCards.isError}
                                    isEmpty={stateReceiptCards.isEmpty}
                                    textLoadingError={stateReceiptCards.description}
                                    onPressTryAgains={(position) => {
                                        this.handleGetCardRequest(position, brandID)
                                    }}
                                    content={
                                        <View style={{
                                            padding: 10,
                                        }}>
                                            <FlatList
                                                style={{ height: 'auto' }}
                                                data={dataPriceCard}
                                                keyExtractor={(item, index) => index.toString()}
                                                numColumns={3}
                                                renderItem={this.rdDenominations}
                                                bounces={false}
                                                scrollEventThrottle={16}
                                            />
                                        </View>
                                    }
                                />}
                                {
                                    isShowPrice && (
                                        <View>
                                            <Quantity
                                                handleMinus={this.handMinus}
                                                handlePlus={this.handlePlus}
                                                counter={counter}
                                            />
                                            <TotalMoney
                                                name="Tổng tiền"
                                                TotalMoney={`${helper.convertNum(moneyCard, false)} ${selectedPrice?.mainProduct?.currencyUnitName}`}
                                            />
                                            <View
                                                style={{
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    marginBottom: 10,
                                                    marginTop: 5
                                                }}>
                                                <Button
                                                    text={"THÊM VÀO GIỎ HÀNG"}
                                                    iconLeft={{
                                                        iconSet: "FontAwesome",
                                                        name: "cart-plus",
                                                        size: 14,
                                                        color: COLORS.btnFFFFFF
                                                    }}
                                                    styleContainer={{
                                                        backgroundColor: COLORS.btn2FB47C,
                                                        borderRadius: constants.getSize(5),
                                                        height: constants.getSize(38),
                                                        marginLeft: 5,
                                                        paddingHorizontal: 12
                                                    }}
                                                    styleText={{
                                                        color: '#ffffff',
                                                        fontSize: 13,
                                                        fontWeight: '700',
                                                        marginLeft: 5
                                                    }}
                                                    onPress={this.handleAddCart}
                                                />

                                            </View>
                                        </View>
                                    )
                                }
                                {
                                    (saleOrderCart.SaleOrderDetails && saleOrderCart.SaleOrderDetails.length > 0) && (
                                        <View>
                                            <TitleTable
                                                handleTable={
                                                    <FlatList
                                                        data={saleOrderCart.SaleOrderDetails}
                                                        keyExtractor={(item, index) => index.toString()}
                                                        renderItem={this.renderItemCart}
                                                        bounces={false}
                                                        scrollEventThrottle={16}
                                                    />
                                                }
                                            />
                                            <TotalAmount
                                                title="Tổng tiền đơn hàng"
                                                total={`${helper.convertNum(totalAmount, true)}`}
                                            />
                                            <View style={{
                                                width: constants.width,
                                                paddingBottom: 2
                                            }}>
                                                <View
                                                    style={{
                                                        alignItems: 'center',
                                                        backgroundColor: COLORS.btn5B9A68,
                                                        height: 40,
                                                        paddingHorizontal: 10
                                                    }}>
                                                    <Button
                                                        text={translate('shoppingCart.customer_info')}
                                                        onPress={() => { this.setState({ isShowCustomer: !isShowCustomer }) }}
                                                        styleContainer={{
                                                            flexDirection: 'row',
                                                            width: constants.width,
                                                            height: 40,
                                                            justifyContent: 'space-between',
                                                            width: constants.width - 10
                                                        }}
                                                        styleText={{
                                                            color: COLORS.txtFFFFFF,
                                                            fontSize: 16,
                                                            marginRight: 8,
                                                            fontWeight: 'bold'
                                                        }}
                                                        iconRight={{
                                                            iconSet: 'FontAwesome',
                                                            name: isShowCustomer ? 'chevron-up' : 'chevron-down',
                                                            size: 14,
                                                            color: COLORS.icFFFFBC
                                                        }}
                                                    />
                                                </View>
                                                {
                                                    isShowCustomer ?
                                                        <View style={{
                                                            padding: 10,
                                                        }}>
                                                            <TouchableOpacity style={{
                                                                flexDirection: 'row',
                                                                alignSelf: "flex-start"
                                                            }}
                                                                onPress={this.onChangTypeCustomer(!isCompany)}
                                                            >
                                                                <Icon
                                                                    iconSet={"Ionicons"}
                                                                    name={
                                                                        isCompany
                                                                            ? "ios-checkbox-outline"
                                                                            : "ios-square-outline"
                                                                    }
                                                                    color={isCompany ? COLORS.icF89000 : COLORS.ic147EFB}
                                                                    size={16}
                                                                />
                                                                <MyText
                                                                    text={translate('shoppingCart.customer_print_company_bill')}
                                                                    style={{
                                                                        color: isCompany ? COLORS.txtF89000 : COLORS.txt147EFB,
                                                                        fontWeight: 'bold',
                                                                        marginLeft: 5
                                                                    }}
                                                                />
                                                            </TouchableOpacity>
                                                            {
                                                                isCompany ?
                                                                    <View style={{
                                                                        width: constants.width - 20,
                                                                        marginTop: 10,
                                                                    }}>
                                                                        <TitleInput
                                                                            title={translate('shoppingCart.text_input_customer_tax')}
                                                                            isRequired={true}
                                                                            styleInput={{
                                                                                borderWidth: 1,
                                                                                borderRadius: 4,
                                                                                borderColor: COLORS.bdCCCCCC,
                                                                                marginBottom: 5,
                                                                                paddingHorizontal: 10,
                                                                                paddingVertical: 8,
                                                                                backgroundColor: isDisabledTax ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                                            }}
                                                                            placeholder={translate('shoppingCart.placeholder_customer_tax')}
                                                                            value={taxID}
                                                                            onChangeText={(text) => {
                                                                                const regExpTax = new RegExp(/^[0-9-KL]{0,14}$/);
                                                                                const isValidate = regExpTax.test(text) || (text == "");
                                                                                if (isValidate) {
                                                                                    this.setState({ taxID: text });
                                                                                }
                                                                            }}
                                                                            keyboardType={DEVICE.isIOS ? "numbers-and-punctuation" : "visible-password"}
                                                                            returnKeyType={"done"}
                                                                            blurOnSubmit={true}
                                                                            // onBlur={() => this.getCompanyInfo(taxID)}
                                                                            onBlur={() => this.getCompanyProfile(taxID)}

                                                                            width={constants.width - 20}
                                                                            height={40}
                                                                            clearText={() => {
                                                                                this.setState({ taxID: "" });
                                                                            }}
                                                                            editable={!isDisabledTax}
                                                                        />

                                                                        <TitleInput
                                                                            title={translate('shoppingCart.text_input_customer_company_name')}
                                                                            isRequired={true}
                                                                            styleInput={{
                                                                                borderWidth: 1,
                                                                                borderRadius: 4,
                                                                                borderColor: COLORS.bdCCCCCC,
                                                                                marginBottom: 5,
                                                                                paddingHorizontal: 10,
                                                                                justifyContent: 'center',
                                                                                paddingVertical: 8,
                                                                                backgroundColor: isDisabledName ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                                            }}
                                                                            textAlignVertical={'center'}
                                                                            underlineColorAndroid={'transparent'}
                                                                            placeholder={translate('shoppingCart.placeholder_customer_company_name')}
                                                                            value={customerName}
                                                                            onChangeText={(text) => {
                                                                                if (helper.isValidateCharVN(text)) {
                                                                                    this.setState({ customerName: text });
                                                                                }
                                                                            }}
                                                                            keyboardType={"default"}
                                                                            returnKeyType={"done"}
                                                                            blurOnSubmit={true}
                                                                            onSubmitEditing={Keyboard.dismiss}
                                                                            width={constants.width - 20}
                                                                            multiline={true}
                                                                            height={40}
                                                                            clearText={() => {
                                                                                this.setState({ customerName: "" });
                                                                            }}
                                                                            editable={!isDisabledName}
                                                                            key={"companyName"}
                                                                            maxLength={300}
                                                                        />

                                                                        <TitleInput
                                                                            title={translate('shoppingCart.text_input_customer_company_address')}
                                                                            styleInput={{
                                                                                borderWidth: 1,
                                                                                borderRadius: 4,
                                                                                borderColor: COLORS.bdCCCCCC,
                                                                                marginBottom: 5,
                                                                                paddingHorizontal: 10,
                                                                                justifyContent: 'center',
                                                                                paddingVertical: 8,
                                                                                backgroundColor: isDisabledAddress ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                                            }}
                                                                            textAlignVertical={'center'}
                                                                            underlineColorAndroid={'transparent'}
                                                                            placeholder={translate('shoppingCart.placeholder_address')}
                                                                            value={customerAddress}
                                                                            onChangeText={(text) => {
                                                                                if (helper.isValidateCharVN(text)) {
                                                                                    this.setState({ customerAddress: text });
                                                                                }
                                                                            }}
                                                                            keyboardType={"default"}
                                                                            returnKeyType={"done"}
                                                                            blurOnSubmit={true}
                                                                            onSubmitEditing={Keyboard.dismiss}
                                                                            width={constants.width - 20}
                                                                            multiline={true}
                                                                            height={40}
                                                                            clearText={() => {
                                                                                this.setState({ customerAddress: "" });
                                                                            }}
                                                                            editable={!isDisabledAddress}
                                                                            key={"companyAddress"}
                                                                            maxLength={300}
                                                                        />



                                                                    </View>
                                                                    :
                                                                    null
                                                            }
                                                            <View style={{
                                                                width: constants.width - 20,
                                                                flexDirection: "row",
                                                                marginBottom: 4,
                                                                justifyContent: "space-between"
                                                            }}>
                                                                <RadioGender
                                                                    gender={gender}
                                                                    onSwitchGender={(value) => {
                                                                        this.setState({ gender: value });
                                                                    }}
                                                                    disabled={IsSOScreenSticker || existStaffInfo}
                                                                />
                                                                <TouchableOpacity style={{
                                                                    justifyContent: "center",
                                                                    alignItems: "center",
                                                                }}
                                                                    onPress={this.getOldCompanyInfo}
                                                                    disabled={IsSOScreenSticker || existStaffInfo}
                                                                >
                                                                    <MyText
                                                                        text={translate('shoppingCart.old_customer')}
                                                                        style={{
                                                                            color: COLORS.txtFFA500,
                                                                            textDecorationLine: 'underline',
                                                                            fontWeight: 'bold'
                                                                        }}
                                                                    />
                                                                </TouchableOpacity>
                                                            </View>
                                                            <TitleInput
                                                                title={isCompany ? translate('shoppingCart.text_input_contact_phone') : translate('additionalPromotion.phone_number')}
                                                                styleInput={{
                                                                    borderWidth: 1,
                                                                    borderRadius: 4,
                                                                    borderColor: COLORS.bdCCCCCC,
                                                                    marginBottom: 5,
                                                                    paddingHorizontal: 10,
                                                                    backgroundColor: COLORS.bgFFFFFF,
                                                                    paddingVertical: 8,
                                                                    backgroundColor: isDisabledPhone ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                                }}
                                                                placeholder={translate('shoppingCart.placeholder_phone')}
                                                                value={contactPhone}
                                                                onChangeText={(text) => {
                                                                    const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                                                    const isValidate = regExpPhone.test(text) || (text == "");
                                                                    if (isValidate) {
                                                                        this.setState({ contactPhone: text });
                                                                    }
                                                                }}
                                                                keyboardType={"numeric"}
                                                                returnKeyType={"done"}
                                                                blurOnSubmit={true}
                                                                onBlur={this.getContactInfo(contactPhone)}
                                                                width={constants.width - 20}
                                                                height={40}
                                                                clearText={() => {
                                                                    this.setState({ contactPhone: "" });
                                                                }}
                                                                key={"contactPhone"}
                                                                disabled={existStaffInfo}
                                                            />

                                                            <TitleInput
                                                                title={isCompany ? translate('shoppingCart.text_input_contact_name') : translate('saleExpress.full_name')}
                                                                styleInput={{
                                                                    borderWidth: 1,
                                                                    borderRadius: 4,
                                                                    borderColor: COLORS.bdCCCCCC,
                                                                    marginBottom: 5,
                                                                    paddingHorizontal: 10,
                                                                    backgroundColor: COLORS.bgFFFFFF,
                                                                    paddingVertical: 8,
                                                                    backgroundColor: existStaffInfo ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                                }}
                                                                placeholder={translate('shoppingCart.placeholder_name')}
                                                                value={contactName}
                                                                onChangeText={(text) => {
                                                                    if (helper.isValidateCharVN(text)) {
                                                                        this.setState({ contactName: text });
                                                                    }
                                                                }}
                                                                keyboardType={"default"}
                                                                returnKeyType={"done"}
                                                                blurOnSubmit={true}
                                                                onSubmitEditing={Keyboard.dismiss}
                                                                width={constants.width - 20}
                                                                height={40}
                                                                clearText={() => {
                                                                    this.setState({ contactName: "" });
                                                                }}
                                                                key={"contactName"}
                                                                maxLength={50}
                                                                disabled={existStaffInfo}
                                                            />
                                                            <CheckBoxPolicy
                                                                isSelected={this.state.isSelectedPolicy}
                                                                onSelectPolicy={() => {
                                                                    this.setState({ isSelectedPolicy: !this.state.isSelectedPolicy })
                                                                }}
                                                                disabled={this.state.disabledPolicy}
                                                            />

                                                        </View>
                                                        :
                                                        null
                                                }
                                            </View>
                                            <View
                                                style={{
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    marginBottom: 10,
                                                    marginTop: 5
                                                }}>
                                                <Button
                                                    onPress={() => this.createOutputReceipt(dataSaleOrder)}
                                                    text={"TẠO ĐƠN HÀNG"}
                                                    disabled={false}
                                                    styleContainer={{
                                                        borderRadius: 7,
                                                        backgroundColor: COLORS.txtFF8900,
                                                        marginLeft: 10,
                                                        height: 40,
                                                        width: constants.getSize(130),
                                                    }}
                                                    styleText={{
                                                        color: COLORS.txtFFFFFF,
                                                        fontSize: 14,
                                                        fontWeight: 'bold'
                                                    }}
                                                />
                                            </View>
                                        </View>
                                    )
                                }
                            </View>
                        </ScrollView >
                    </SafeAreaView>
                </KeyboardAwareScrollView>
            </View >
        )
    }
}

const mapStateToProps = function (state) {
    return {
        brands: state.cardReducer.brands,
        stateBrands: state.cardReducer.stateBrands,
        receiptCards: state.cardReducer.receiptCards,
        stateReceiptCards: state.cardReducer.stateReceiptCards,
        saleOrderCart: state.cardReducer.saleOrderCart,
        dataSaleOrderPayment: state.cardReducer.dataSaleOrderPayment,
        staffInfo: state.staffPromotionReducer.staffInfo,
        dataShoppingCart: state.shoppingCartReducer.dataShoppingCart,
        userInfo: state.userReducer,
        dataSaleOrderCart: state.collectionReducer.dataSaleOrderCart,
        customerConfirmPolicy: state.shoppingCartReducer.customerConfirmPolicy,

    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionCard: bindActionCreators(actionCardCreator, dispatch),
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
        actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(SellCardManager);
