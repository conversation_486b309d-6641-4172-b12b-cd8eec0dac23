import React from "react";
import LinearGradient from "react-native-linear-gradient";
import { MyText } from "@components";

const GradientTitle = ({ text }) => {
  return (
    <LinearGradient
      colors={["#FF6A00", "#FFD700"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={{
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 12,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        marginBottom: 10
      }}
    >
      <MyText
        text={text}
        addSize={-1.5}
        style={{
          color: "#fff",
          fontSize: 20,
          fontWeight: "bold",
          textAlign: "center",
          textTransform: "uppercase",
          letterSpacing: 1,
          textShadowColor: "rgba(0,0,0,0.3)",
          textShadowOffset: { width: 1, height: 1 },
          textShadowRadius: 2,
        }}
      />
    </LinearGradient>
  );
};

export default GradientTitle;