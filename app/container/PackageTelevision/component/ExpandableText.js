import React, { useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { MyText } from "@components";

const ExpandableText = ({ text, numberOfLines = 3, style }) => {
    const [expanded, setExpanded] = useState(false);

    if (!text) return null;

    return (
        <View>
            <MyText
                style={style}
                text={text}
                numberOfLines={expanded ? undefined : numberOfLines}
            />
            {text.length > numberOfLines * 20 && (
                <TouchableOpacity onPress={() => setExpanded(!expanded)}>
                    <Text style={{ color: "#3FA2F6", fontWeight: "500", textDecorationLine: 'underline' }}>
                        {expanded ? "Thu gọn" : "Xem thêm"}
                    </Text>
                </TouchableOpacity>
            )}
        </View>
    );
};

export default ExpandableText;
