// PackageFeeBottomSheet.js
import React from "react";
import {
  View,
  TouchableWithoutFeedback,
  StyleSheet,
  TextInput,
  Platform,
} from "react-native";
import { BottomSheet, MyText, Icon, TitleInput } from "@components";
import ButtonAction from "../component/ButtonAction";
import ExpandableText from '../component/ExpandableText'
import { constants } from "@constants";
import { COLORS } from "@styles";
import { helper } from "@common";
import LinearGradient from "react-native-linear-gradient";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

const PackageFeeBottomSheet = ({
  bottomSheetRef,
  snapPoints = ["80%"],
  disabledBackdrop = true,
  customerName,
  onChangeCustomerName,
  note,
  onChangeNote,
  isChecked,
  onToggleCompanyInvoice,
  taxCode,
  onChangeTaxCode,
  companyAddress,
  onChangeCompanyAddress,
  onCreateOrder,
  onClose,
  updateSelectedItem,
}) => {
  const handleComponent = () => (
    <View style={styles.handle_wrapper}>
      <View style={{ flex: 1 }} />
      <View style={styles.handle_title_wrapper}>
        <MyText
          style={styles.handle_title}
          text={"PHÍ GÓI CƯỚC TRUYỀN HÌNH"}
        />
      </View>
      <View style={{ flex: 1 }}>
        <TouchableWithoutFeedback onPress={onClose}>
          <Icon
            iconSet={"MaterialIcons"}
            name={"clear"}
            color={COLORS.bdEB3B3B}
            size={22}
          />
        </TouchableWithoutFeedback>
      </View>
    </View>
  );

  const renderInner = () => (
    <KeyboardAwareScrollView
      style={styles.btmSheetContainer}
      enableOnAndroid={true}
      extraScrollHeight={100}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.viewCustomer}>
        <View style={styles.itemContent}>
          <View style={styles.itemInfo}>
            <MyText
              style={styles.itemTitle}
              text={updateSelectedItem.ProductInfo}
            />
            <ExpandableText
              text={updateSelectedItem.Description}
              numberOfLines={3}
              style={styles.itemDiscription}
            />
          </View>
        </View>
        <TitleInput
          title={"Họ và tên khách hàng"}
          styleInput={styles.txtInput}
          placeholder={"Nhập họ và tên khách hàng"}
          value={customerName}
          onChangeText={(text) => {
            if (helper.isValidateCharVN(text)) {
              onChangeCustomerName(text);
            }
          }}
          keyboardType={"default"}
          returnKeyType={"done"}
          blurOnSubmit
          width={constants.width - 20}
          height={40}
          clearText={() => onChangeCustomerName("")}
          key="customerName"
          isRequired={true}
        />

        <View style={styles.viewCustomer}>
          <MyText text={"Ghi chú"} style={styles.note_label} />
          <View style={styles.box}>
            <TextInput
              style={styles.input}
              value={String(note || '')}
              onChangeText={onChangeNote}
              placeholder={"Không bắt buộc nhập:"}
              scrollEnabled={false}
              returnKeyType="done"
              blurOnSubmit
              {...(Platform.OS === 'android'
                ? { textAlignVertical: 'top' }
                : {})}
              underlineColorAndroid="transparent"
            />
          </View>
        </View>
      </View>

      <TouchableWithoutFeedback onPress={onToggleCompanyInvoice}>
        <View style={styles.checkboxContainer}>
          <Icon
            iconSet={"Ionicons"}
            name={isChecked ? "ios-checkbox-outline" : "ios-square-outline"}
            size={20}
            color={isChecked ? "#0F67B1" : COLORS.bd7A7A7A}
            style={styles.checkbox_icon}
          />
          <MyText
            text={"Khách hàng lấy hoá đơn công ty"}
            addSize={-1.5}
            style={[
              styles.checkbox_text,
              { color: isChecked ? "#0F67B1" : COLORS.bd7A7A7A, textDecorationLine: 'underline' }
            ]}
          />
        </View>
      </TouchableWithoutFeedback>

      {isChecked && (
        <View style={styles.viewCustomer}>
          <TitleInput
            title={"Mã số thuế: "}
            isRequired={true}
            styleInput={styles.txtInput}
            placeholder={"Vui lòng nhập mã số thuế"}
            value={taxCode}
            onChangeText={(text) => {
              const regExpTax = new RegExp(/^[0-9-KL]{0,14}$/);
              if (regExpTax.test(text) || text === "") {
                onChangeTaxCode(text);
              }
            }}
            keyboardType="default"
            returnKeyType={"done"}
            width={constants.width - 20}
            height={40}
            clearText={() => onChangeTaxCode("")}
            key="taxCode"
          />
          <TitleInput
            title={"Địa chỉ công ty: "}
            isRequired={true}
            styleInput={styles.txtInput}
            placeholder={"Vui lòng nhập địa chỉ công ty"}
            value={companyAddress}
            onChangeText={onChangeCompanyAddress}
            keyboardType="default"
            returnKeyType={"done"}
            width={constants.width - 20}
            height={40}
            clearText={() => onChangeCompanyAddress("")}
            key="companyAddress"
          />
        </View>
      )}

      <LinearGradient
        colors={["#D4E4FF", "#F3F7FF"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.summary_container}
      >
        <View style={styles.summary_left}>
          <Icon
            iconSet={"Ionicons"}
            name="wallet"
            size={22}
            color="#147EFB"
            style={styles.summary_icon}
          />
          <MyText
            style={styles.summary_label}
            text="Tổng thanh toán: "
          />
        </View>
        <MyText
          style={styles.summary_amount}
          text={helper.formatMoney(updateSelectedItem?.Amount)}
        />
      </LinearGradient>

      <View style={styles.btnAction}>
        <ButtonAction
          onPress={onCreateOrder}
          title={"TẠO ĐƠN HÀNG"}
          style={styles.btn_create}
          styleText={styles.btn_create_text}
          opacity={1}
          disabled={false}
        />
      </View>
    </KeyboardAwareScrollView>
  );

  return (
    <BottomSheet
      enableHandlePanningGesture={false}
      enableContentPanningGesture={false}
      bs={bottomSheetRef}
      snapPoints={snapPoints}
      handleComponent={handleComponent}
      disabledBackdrop={disabledBackdrop}
    >
      {renderInner()}
    </BottomSheet>
  );
};

export default PackageFeeBottomSheet;

const styles = StyleSheet.create({
  handle_wrapper: {
    backgroundColor: COLORS.bgFFFFFF,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    height: 50,
    elevation: 2,
    borderTopStartRadius: 22,
    borderTopEndRadius: 22,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: COLORS.bgC4C4C4,
  },
  handle_title_wrapper: {
    flex: 6,
    justifyContent: "center",
    alignItems: "center"
  },
  handle_title: {
    fontWeight: "bold",
    color: "#3FA2F6"
  },
  btmSheetContainer: {
    backgroundColor: "#FFFFFF",
    flex: 1,
    padding: 10,
  },
  viewCustomer: {
    justifyContent: "center",
    marginTop: 5,
  },
  txtInput: {
    borderWidth: 1,
    borderRadius: 4,
    borderColor: COLORS.bdCCCCCC,
    marginBottom: 10,
    paddingHorizontal: 10,
    backgroundColor: COLORS.bgFFFFFF,
    paddingVertical: 8,
  },
  note_label: {
    fontWeight: "bold",
    fontSize: 13
  },
  box: {
    borderWidth: 1,
    borderRadius: 4,
    minHeight: 100,
    paddingTop: 10,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    borderColor: COLORS?.bdCCCCCC
  },
  input: {
    fontSize: 16,
    lineHeight: 20,
    height: 24,
    paddingVertical: 0,
    paddingHorizontal: 0,
    includeFontPadding: false,
  },
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 13,
    marginBottom: 8,
    marginLeft: -10
  },
  checkbox_icon: {
    marginLeft: 8
  },
  checkbox_text: {
    marginLeft: 8,
    fontSize: 15
  },
  summary_container: {
    borderRadius: 16,
    paddingVertical: 15,
    paddingHorizontal: 10,
    marginHorizontal: 5,
    marginTop: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  summary_left: {
    flexDirection: "row",
    alignItems: "center"
  },
  summary_icon: {
    marginRight: 5
  },
  summary_label: {
    color: "#147EFB",
    fontWeight: "800",
    fontSize: 16,
  },
  summary_amount: {
    color: "#EA1D5D",
    fontWeight: "bold",
    fontSize: 16,
  },
  btnAction: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 10,
  },
  btn_create: {
    backgroundColor: COLORS.bg00A98F
  },
  btn_create_text: {
    color: COLORS.bgFFFFFF
  },
  itemContent: {
    backgroundColor: "#fff",
    padding: 16,
    marginBottom: 12,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 3,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#3FA2F6",
  },
  itemDiscription: {
    fontSize: 14,
    fontWeight: "200",
    color: COLORS.primary,
    marginBottom: 4,
  },
});
