import { packageTelevisionState } from "./state";
import { actionPackageTelevision } from "./action";

const packageTelevisionReducer = function (state = packageTelevisionState, action) {
    switch (action.type) {
        case actionPackageTelevision.START_GET_SERVICE_LIST:
            return {
                ...state,
                dataServiceList: {},
                stateServiceList: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }

        case actionPackageTelevision.STOP_GET_SERVICE_LIST:
            return {
                ...state,
                dataServiceList: action.data,
                stateServiceList: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }

        case actionPackageTelevision.START_GET_INFOR_PACKAGE:
            return {
                ...state,
                dataInforPackage: {},
                stateInforPackage: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }

        case actionPackageTelevision.STOP_GET_INFOR_PACKAGE:
            return {
                ...state,
                dataInforPackage: action.data,
                stateInforPackage: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }

        case actionPackageTelevision.UPDATE_HEADER_AIRTIME:
            return {
                ...state,
                updateHeaderAirtime: action.data,
            }

        case actionPackageTelevision.STOP_ADD_TO_SALE_ORDER_CART:
            return {
                ...state,
                dataSaleOrderCart: action.dataSaleOrderCart,
            };

        case actionPackageTelevision.CLEAR_DATA_INFOR_PACKAGE:
            return {
                ...state,
                dataInforPackage: {},
                stateInforPackage: {}
            }

        default:
            return state;
    }
};
export { packageTelevisionReducer };
