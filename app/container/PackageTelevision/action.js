import { API_CONST } from "@constants";
import { helper, dateHelper } from "@common";
import { apiBase, METHOD, ERROR, EMPTY, SUCCESS } from "@config";
import { translate } from "@translate";

const {
    API_GET_SERVICE_LIST,
    API_GET_PRODUCT_LIST_PACK_OF_DATA,
    API_GET_PRICE_SERVICE,
    API_GET_CREATE_SERVICE_REQUEST
} = API_CONST;

const START_GET_SERVICE_LIST = "START_GET_SERVICE_LIST";
const STOP_GET_SERVICE_LIST = "STOP_GET_SERVICE_LIST";
const UPDATE_HEADER_AIRTIME = 'UPDATE_HEADER_AIRTIME';
const START_GET_INFOR_PACKAGE = 'START_GET_INFOR_PACKAGE';
const STOP_GET_INFOR_PACKAGE = 'STOP_GET_INFOR_PACKAGE';
const START_ADD_TO_SALE_ORDER_CART = "START_ADD_TO_SALE_ORDER_CART";
const STOP_ADD_TO_SALE_ORDER_CART = "STOP_ADD_TO_SALE_ORDER_CART";
const CLEAR_DATA_INFOR_PACKAGE = "CLEAR_DATA_INFOR_PACKAGE";

export const actionPackageTelevision = {
    START_GET_SERVICE_LIST,
    STOP_GET_SERVICE_LIST,
    UPDATE_HEADER_AIRTIME,
    START_GET_INFOR_PACKAGE,
    STOP_GET_INFOR_PACKAGE,
    START_ADD_TO_SALE_ORDER_CART,
    STOP_ADD_TO_SALE_ORDER_CART,
    CLEAR_DATA_INFOR_PACKAGE
};

export const getServiceList = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: data.catalogID,
            serviceGroupID: data.serviceGroupID,
            isSearchByQuery: data.isSearchByQuery,
            isLoadSearchBy: data.isLoadSearchBy
        }
        dispatch(start_get_service_list())
        apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
            .then((response) => {
                console.log("getServiceList success", response);
                if (helper.IsNonEmptyArray(response.object)) {
                    dispatch(stop_get_service_list(response.object, false, '', false));
                } else {
                    dispatch(stop_get_service_list([], true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_get_service_list([], false, error.msgError, true))
                console.log("getServiceList error", error);
            })
    }
};

export const getInforPackage = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: data.catalogID,
            serviceGroupID: data.serviceGroupID,
            airtimeTransactionTypeID: data.airtimeTransactionTypeID,
            phoneNumber: data.phoneNumber
        }
        dispatch(start_get_infor_package())
        apiBase(API_GET_PRICE_SERVICE, METHOD.POST, body)
            .then((response) => {
                console.log("getInforPackage success", response);
                const listPartner = response?.object?.PriceInfo?.ExtraData ?? [];
                if (helper.IsNonEmptyArray([listPartner])) {
                    dispatch(stop_get_infor_package([listPartner], false, '', false));
                } else {
                    dispatch(stop_get_infor_package([], true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_get_infor_package([], false, error.msgError, true))
                console.log("getInforPackage error", error);
            })
    }
};

export const getPriceAndFeeService = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                partnerData: data.partnerData,
                productID: data.productID
            }
            apiBase(API_GET_PRICE_SERVICE, METHOD.POST, body)
                .then((response) => {
                    console.log("getPriceAndFeeService success", response?.object);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        resolve(object);
                    } else {
                        reject('Không lấy được thông tin dòng xe!')
                    }
                }).catch(error => {
                    console.log("getPriceAndFeeService error", error);
                    reject(error.msgError)
                })
        })
    }
}

export const addToSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                AirTimeTransactionBO: data.AirTimeTransactionBO,
                partnerData: data.partnerData,
            };
            dispatch(start_add_to_sale_order_cart());
            apiBase(API_GET_CREATE_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("addToSaleOrderCart success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_add_to_sale_order_cart(object));
                        resolve(response);
                    } else {
                        dispatch(stop_add_to_sale_order_cart({}));
                        reject({ msgError: translate("saleOrder.error_create_order") });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("addToSaleOrderCart error", error);
                    reject(msgError);
                });
        });
    };
};

export const start_get_service_list = () => {
    return {
        type: START_GET_SERVICE_LIST,
    };
};

export const stop_get_service_list = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SERVICE_LIST,
    data,
    isEmpty,
    description,
    isError
});

export const start_get_infor_package = () => {
    return {
        type: START_GET_INFOR_PACKAGE,
    };
};

export const stop_get_infor_package = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_INFOR_PACKAGE,
    data,
    isEmpty,
    description,
    isError
});

const update_header_airtime = (
    data
) => ({
    type: UPDATE_HEADER_AIRTIME,
    data
});

export const updateHeaderAirtime = (data) => {
    return function (dispatch, getState) {
        dispatch(update_header_airtime(data));
    }
}

export const start_add_to_sale_order_cart = () => {
    return {
        type: START_ADD_TO_SALE_ORDER_CART,
    };
};

export const stop_add_to_sale_order_cart = (dataSaleOrderCart = {}) => {
    return {
        type: STOP_ADD_TO_SALE_ORDER_CART,
        dataSaleOrderCart,
    };
};

export const clear_data_infor_package = () => ({
    type: CLEAR_DATA_INFOR_PACKAGE
});







