import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  SectionList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import React, { useEffect, useRef, useState } from "react";
import SearchInput from "../component/SearchInput";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { constants } from "@constants";
import { COLORS } from "@styles";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionPackageTelevisionCreator from "../action";
import * as actionPaymentOrderCreator from "../../SaleOrderPayment/action";
import { BaseLoading, showBlockUI, hideBlockUI } from "@components";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import { helper } from "@common";
import { translate } from "@translate";
import { useFocusEffect } from "@react-navigation/native";
import Title from "../component/Title";
import PackageFeeBottomSheet from "../component/PackageFeeBottomSheet";

const SaleOrderPackage = ({
  actionPackageTelevision,
  itemCatalog,
  updateHeaderAirtime,
  dataInforPackage,
  stateInforPackage,
  navigation,
  actionPaymentOrder,
}) => {
  const [keyword, setKeyword] = useState("");
  const [selectedId, setSelectedId] = useState(null);
  const [customerName, setCustomerName] = useState("");
  const [note, setNote] = useState("");
  const [isChecked, setIsChecked] = useState(false);
  const [taxCode, setTaxCode] = useState("");
  const [companyAddress, setCompanyAddress] = useState("");
  const [updateSelectedItem, setUpdateSelectedItem] = useState({});

  const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? {};
  const { AirTimeTransactionTypeID } = updateHeaderAirtime ?? {};
  const { isFetching, isError, isEmpty, description } = stateInforPackage;

  const bottomSheetModalRef = useRef(null);
  const afterDismissRef = useRef(null);

  useFocusEffect(
    React.useCallback(() => {
      return () => {
        actionPackageTelevision.clear_data_infor_package();
        setKeyword("");
        resetPackageData();
      };
    }, [])
  );

  const translateY = useRef(new Animated.Value(-20)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  const resetPackageData = () => {
    setSelectedId(null);
    setCustomerName("");
    setNote("");
    setIsChecked(false);
    setTaxCode("");
    setCompanyAddress("");
  };

  const getDataInforPackage = () => {
    if (!helper.IsNonEmptyString(keyword)) {
      Alert.alert("THÔNG BÁO", "Số điện thoại không được để trống!");
      return false;
    } else if (keyword?.length < 10) {
      Alert.alert("THÔNG BÁO", "Vui lòng nhập số điện thoại đúng 10 số");
    } else {
      actionPackageTelevision.getInforPackage({
        catalogID: ServiceCategoryID,
        serviceGroupID: AirtimeServiceGroupID,
        airtimeTransactionTypeID: AirTimeTransactionTypeID,
        phoneNumber: keyword,
      });
    }
  };

  const handleSelect = async (item) => {
    if (item.ProductID !== selectedId) {
      setSelectedId(item.ProductID);
      setUpdateSelectedItem(item);
      bottomSheetModalRef.current?.present();
    }
  };

  const sections = Array.isArray(dataInforPackage)
    ? dataInforPackage.map((sec) => ({
      title: sec.Subtype,
      data: sec.ProductList,
    }))
    : [];

  const onClose = () => {
    resetPackageData();
    bottomSheetModalRef.current?.dismiss?.();
    if (afterDismissRef.current) {
      const run = afterDismissRef.current;
      afterDismissRef.current = null;
      run();
    }
  };

  const renderItem = ({ item, index }) => {
    useEffect(() => {
      Animated.timing(translateY, {
        toValue: 0,
        duration: 400,
        delay: index * 100,
        useNativeDriver: true,
      }).start();
      Animated.timing(opacity, {
        toValue: 1,
        duration: 400,
        delay: index * 100,
        useNativeDriver: true,
      }).start();
    }, []);

    const isSelected = item.ProductID === selectedId;
    return (
      <Animated.View
        style={{
          transform: [{ translateY }],
          opacity,
        }}
      >
        <TouchableOpacity
          style={[styles.itemContainer, isSelected && styles.selectedContainer]}
          onPress={() => handleSelect(item)}
          activeOpacity={0.8}
        >
          <View style={styles.itemContent}>
            <View style={styles.itemInfo}>
              <Text style={styles.itemTitle}>{item.ProductInfo}</Text>
              <Text style={styles.itemDiscription}>{item.Description}</Text>
            </View>
          </View>
          <View style={styles.itemRight}>
            <View style={styles.styleAmount}>
              <View style={styles.text_amount}>
                <Text style={styles.titleAmount}>Giá: </Text>
                <Text style={styles.amount}>{item.Amount}</Text>
              </View>
              <View style={styles.style_point}>
                <Text style={styles.titleAmount}>Điểm thưởng: </Text>
                <Text style={styles.text_point}>{item.ToTalRewardForStaff}</Text>
              </View>
            </View>
            <MaterialIcons
              name={isSelected ? "check-circle" : "chevron-right"}
              size={24}
              color={isSelected ? "#7ADAA5" : COLORS.border}
            />
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderSectionHeader = ({ section: { title } }) => (
    <Title text={title} />
  );

  const validateCart = () => {
    const regExpTax10 = new RegExp(/^\d{10}$/);
    const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
    const isValidateTax10 = regExpTax10.test(taxCode);
    const isValidateTax14 = regExpTax14.test(taxCode);
    const isValidateTax = isValidateTax10 || isValidateTax14;
    if (customerName == "") {
      Alert.alert("", "Vui lòng nhập tên khách hàng/ công ty");
      return false;
    }
    if (!isValidateTax && isChecked == true) {
      Alert.alert("", translate("editSaleOrder.validation_tax"));
      return false;
    }
    if (isChecked == true && companyAddress == "") {
      Alert.alert("", "Vui lòng nhập địa chỉ công ty");
      return false;
    } else {
      Alert.alert("", translate("saleOrderPayment.you_want_finish_order"), [
        {
          text: translate("saleOrderManager.btn_cancel"),
          style: "cancel",
          onPress: hideBlockUI,
        },
        {
          text: translate("saleOrderPayment.btn_continue"),
          style: "default",
          onPress: () => {
            handleAddCart();
          },
        },
      ]);
    }
  };

  const handleAddCart = () => {
    showBlockUI();
    bottomSheetModalRef.current?.dismiss();
    const { Amount, ProductID, PartnerData } = updateSelectedItem ?? {}; //Thông tin các trường Amount gán vào inputPrice, salePrice,.. là do service nhờ gán lại NHC
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimeTransactionTypeID: AirTimeTransactionTypeID,
      AirTimeTransactionBO: {
        productid: ProductID,
        amount: Amount,
        fee: Amount,
        phonenumber: keyword,
        inputPrice: Amount,
        salePrice: Amount,
        customerName: customerName,
        cus_LogNote: note,
        customerTaxID: taxCode,
        customerAddress: companyAddress,
      },
      partnerData: PartnerData,
    };
    actionPackageTelevision
      .addToSaleOrderCart(data)
      .then((reponse) => {
        bottomSheetModalRef.current?.dismiss?.();
        goToPaymentSO(reponse);
      })
      .catch((error) => {
        Alert.alert(translate("common.notification_uppercase"), error, [
          {
            text: translate("common.btn_close"),
            onPress: () => {
              setSelectedId(null), hideBlockUI(), resetPackageData();
            },
          },
        ]);
      });
  };

  const goToPaymentSO = (rpSaleOrderCart) => {
    const dataSaleOrderCart = rpSaleOrderCart.object;
    const SaleOrders = dataSaleOrderCart.SaleOrders[0];
    const { SaleOrderID } = SaleOrders;
    actionPaymentOrder
      .setDataSO({ SaleOrderID: SaleOrderID, SaleOrderTypeID: 1000 })
      .then(() => {
        hideBlockUI();
        navigation.navigate("SaleOrderPayment");
        actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
        actionPaymentOrder.getReportPrinterSocket(1000);
        actionPaymentOrder.getDataQRTransaction(SaleOrderID);
      });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.inner}>
        <View style={styles.searchWrapper}>
          <SearchInput
            placeholder="Nhập số điện thoại"
            onSubmit={getDataInforPackage}
            inputText={keyword}
            onChangeText={(text) => {
              const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
              const isValidate = regExpPhone.test(text) || text == "";
              if (isValidate) {
                setKeyword(text);
                if (text === "") {
                  actionPackageTelevision.clear_data_infor_package();
                  resetPackageData();
                }
              }
            }}
            onClearText={() => {
              actionPackageTelevision.clear_data_infor_package();
              setKeyword("");
              resetPackageData();
            }}
            style={styles.searchInput}
          />
        </View>
      </View>
      <KeyboardAwareScrollView
        style={styles.container}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps="always"
        bounces={false}
      >
        <BaseLoading
          isLoading={isFetching}
          isError={isError}
          isEmpty={isEmpty}
          textLoadingError={description}
          onPressTryAgains={getDataInforPackage}
          content={
            <SectionList
              sections={sections}
              keyExtractor={(item) => item.ProductID}
              renderItem={renderItem}
              renderSectionHeader={renderSectionHeader}
              contentContainerStyle={styles.listContainer}
            />
          }
        />

        <PackageFeeBottomSheet
          bottomSheetRef={bottomSheetModalRef}
          customerName={customerName}
          onChangeCustomerName={setCustomerName}
          note={note}
          onChangeNote={setNote}
          isChecked={isChecked}
          onToggleCompanyInvoice={() => setIsChecked((v) => !v)}
          taxCode={taxCode}
          onChangeTaxCode={setTaxCode}
          companyAddress={companyAddress}
          onChangeCompanyAddress={setCompanyAddress}
          onCreateOrder={validateCart}
          onClose={() => onClose()}
          updateSelectedItem={updateSelectedItem}
        />
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

const mapStateToProps = (state) => ({
  itemCatalog: state.collectionReducer.itemCatalog,
  updateHeaderAirtime: state.packageTelevisionReducer.updateHeaderAirtime,
  dataInforPackage: state.packageTelevisionReducer.dataInforPackage,
  stateInforPackage: state.packageTelevisionReducer.stateInforPackage,
});

const mapDispatchToProps = (dispatch) => ({
  actionPackageTelevision: bindActionCreators(
    actionPackageTelevisionCreator,
    dispatch
  ),
  actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(SaleOrderPackage);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.bgFFFFFF,
  },
  inner: {
    alignItems: "center",
    justifyContent: "center",
  },
  searchWrapper: {
    width: constants.width - 10,
    marginTop: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  searchInput: { marginTop: 5 },
  listContainer: {
    padding: 16,
  },
  headerContainer: {
    backgroundColor: "#f2f2f2",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginBottom: 8,
    flex: 1,
  },
  headerText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#FF8040",
  },
  itemContainer: {
    backgroundColor: "#fff",
    padding: 16,
    marginBottom: 12,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 3,
  },
  selectedContainer: {
    borderWidth: 1,
    borderColor: "#3FA2F6",
  },
  itemContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  itemInfo: {},
  itemTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#3FA2F6",
  },
  itemSubtitle: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginTop: 4,
  },
  itemRight: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  itemDiscription: {
    fontSize: 14,
    fontWeight: "200",
    color: COLORS.primary,
    marginBottom: 4,
  },
  styleAmount: {
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
  },
  titleAmount: {
    justifyContent: "center",
    alignItems: "center",
  },
  amount: {
    color: "#E43636",
    fontWeight: "500",
  },
  text_amount: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  style_point: {
    marginLeft: 20,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  text_point: {
    color: '#000000',
    fontWeight: "bold",
  },
});
