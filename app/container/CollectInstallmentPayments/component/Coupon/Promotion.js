import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const VoucherCard = ({ message, promotionID }) => {
    const isAlreadyGifted = promotionID == 0;

    const colors = isAlreadyGifted
        ? {
            containerBg: '#E8F5E8',
            borderColor: '#4CAF50',
            iconBg: '#C8E6C9',
            iconColor: '#2E7D32',
            textColor: '#1B5E20',
        }
        : {
            containerBg: '#FFF9F2',  
            borderColor: '#FFB347',  
            iconBg: '#FFEDCC',      
            iconColor: '#FF0B55',  
            textColor: '#DC2525',   
        };

    return (
        <View style={styles.container}>
            <View style={[styles.card, { backgroundColor: colors.borderColor }]}>

                <View style={styles.content}>
                    <Text style={styles.title}>PHIẾU MUA HÀNG</Text>

                    <View style={styles.divider} />

                    <Text style={[styles.text, { color: colors.textColor }]}>
                        {message}
                    </Text>
                </View>

                <View style={styles.cutOutLeft} />
                <View style={styles.cutOutRight} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: '100%',
        padding: 5,
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
    },
    card: {
        width: '100%',
        borderRadius: 10,
        padding: 5,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        position: 'relative',
        overflow: 'visible',
    },
    content: {
        alignItems: 'center',
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#03A6A1',
    },
    divider: {
        height: 1,
        width: '100%',
        backgroundColor: '#eee',
        marginVertical: 5,
    },
    text: {
        fontSize: 15,
        fontWeight: '500',
        lineHeight: 24,
        flex: 1,
    },
    iconAbsoluteWrapper: {
        position: 'absolute',
        left: 20,
        top: 10,
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 2,
    },
    cutOutLeft: {
        position: 'absolute',
        left: -10,
        top: '50%',
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: '#f5f5f5',
        transform: [{ translateY: -10 }],
    },
    cutOutRight: {
        position: 'absolute',
        right: -10,
        top: '50%',
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: '#f5f5f5',
        transform: [{ translateY: -10 }],
    },
});

export default VoucherCard;
