import { <PERSON><PERSON>ist, SafeAreaView, StyleSheet, Text, Animated, TouchableOpacity, View, Alert } from 'react-native'
import React, { useState, useEffect, useCallback } from 'react'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { constants } from '@constants'
import { BaseLoading, Icon, MyText, PickerSearch, hideBlockUI, showBlockUI } from '@components'
import { COLORS } from '@styles';
import moment from 'moment';
import ModalCalendar from '../../InstallmentManager/components/ModalCalendar';
import SearchInput from '../component/SearchInput'
import ItemCollection from '../component/ItemCollection'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux';
import * as actionInsurancePVICreator from "../action";
import * as actionSaleOrderCreator from "../../SaleOrderPayment/action";
import * as actionManagerSOCreator from "../../SaleOrderManager/action";
import * as actionCollectionManagerCreator from "../action";
import { translate } from '@translate';
import { useFocusEffect } from '@react-navigation/native';

const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;

const HistorySellPVI = ({
    item,
    stateSearchListHistory,
    dataSearchListHistory,
    actionInsurancePVI,
    actionManagerSO,
    actionSaleOrder,
    navigation,
    route,
    itemCatalog
}) => {
    const {
        ServiceCategoryID,
        AirtimeServiceGroupID
    } = itemCatalog ?? {};
    const [filter, setFilter] = useState({
        scrollY: new Animated.Value(0),
        isIncome: 0,
        isCompleteInfo: 0,
        isDelivery: 0,
    });
    const { SaleOrderID } = route?.params ?? {};
    const [fromDate, setFromDate] = useState(new Date())
    const [toDate, setToDate] = useState(new Date())
    const [isShowCalendar, setIsShowCalendar] = useState(false);
    const [searchByInsurance, setSearchByInsurance] = useState('');
    const [providerInsurance, setProviderInsurance] = useState('');
    const [keyword, setkeyWork] = useState('');
    const [isDelete, setIsDelete] = useState(false);
    const [IsActive, setIsActive] = useState(false);
    const [dataInsurance, setDataInsurance] = useState([])
    const [dataProvider, setProvider] = useState([])
    const [dataHistoryInsurance, setDataHistoryInsurance] = useState()
    const diffClamp = Animated.diffClamp(filter.scrollY, 0, DISTANCE);

    const translateY = diffClamp.interpolate({
        inputRange: [0, DISTANCE],
        outputRange: [0, -DISTANCE],
    });

    useFocusEffect(
        useCallback(() => {
            const data = {
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                isLoadSearchBy: true
            };
            actionInsurancePVI.getServiceListHistory(data).then((response) => {
                setDataInsurance(response[0].SearchByList)
                setProvider(response[1].ProviderList)
            }).catch((err) => console.log(err));
        }, [actionInsurancePVI])
    );

    const getSearchHistoryInsurance = () => {
        const data = {
            fromDate: new Date(fromDate),
            toDate: new Date(toDate),
            keyword: SaleOrderID ? SaleOrderID : keyword,
            searchType: searchByInsurance,
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimetrsTypeIDList: providerInsurance,
        }
        actionInsurancePVI.getSearchHistoryInsurance(data)
        setDataHistoryInsurance(dataSearchListHistory)
    }

    useFocusEffect(
        useCallback(() => {
            getSearchHistoryInsurance()
        }, [actionInsurancePVI, keyword, fromDate, toDate, searchByInsurance, providerInsurance])
    );

    useEffect(() => {
        setDataHistoryInsurance(dataSearchListHistory)
    }, [dataSearchListHistory])

    const onSubmit = (keyword) => {
        const data = {
            keyword: keyword,
            fromDate: new Date(fromDate),
            toDate: new Date(toDate),
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            searchType: searchByInsurance,
            airtimetrsTypeIDList: providerInsurance,
        }
        actionInsurancePVI.getSearchHistoryInsurance(data)
    }

    const onAskStatusInsurance = (item) => {
        showBlockUI();
        const { SALEORDERID } = item;
        const data = { saleOrderID: SALEORDERID }
        actionInsurancePVI.queryTransactionPartner(data).then((reponse) => {
            hideBlockUI();
            if (reponse) {
                Alert.alert("", reponse.airtimestatusname, [
                    {
                        text: "OK",
                        onPress: () => {
                            setkeyWork(SALEORDERID);
                        }
                    }
                ])
            }
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () =>
                        onAskStatusInsurance(item),
                }
            ])
        });
    }

    const handleSaleOrderPayment = async (SOInfo) => {
        const { SALEORDERID } = SOInfo;
        try {
            showBlockUI();
            actionSaleOrder.setDataSO({
                SaleOrderID: SALEORDERID,
                SaleOrderTypeID: 100
            })
            hideBlockUI();
            navigation.navigate("SaleOrderPayment", { shouldOriginUpdate: true });
            actionSaleOrder.getReportPrinterSocket(100);
            actionSaleOrder.getDataQRTransaction(SALEORDERID);
            actionSaleOrder.getDataSCTransaction(SALEORDERID);
        } catch (error) {
            Alert.alert(translate('common.notification_uppercase'), error, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => this.handleSaleOrderPayment(SOInfo),
                }
            ]);
        }
    }

    const onPrint = (item) => {
        const { SALEORDERID } = item;
        actionSaleOrder.setDataSO({
            SaleOrderID: SALEORDERID,
            SaleOrderTypeID: 100
        }).then(success => {
            actionManagerSO.getContentTypeReport(SALEORDERID);
            actionSaleOrder.getReportPrinterSocket(100);
            navigation.navigate("ReprintSaleOrder");
        })
    }

    const handleDeleteSOInsurance = (info) => {
        const { SALEORDERID, TICKETID, AIRTIMETRANSACTIONID, SERVICEVOUCHERID } = info;
        showBlockUI();
        actionManagerSO.getInfoSODelete(SALEORDERID).then(saleOrder => {
            const { SaleOrderID, OrderTypeID } = saleOrder;
            hideBlockUI();
            actionSaleOrder.setDataSO({
                SaleOrderID: SaleOrderID,
                SaleOrderTypeID: OrderTypeID
            }).then(success => {
                const { TotalPaid, IsIncome, OrderTypeID, TotalMoney } = saleOrder;
                const isCancel = IsIncome && (TotalPaid > 0);
                if (isCancel) {
                    navigation.navigate("CancelSO", {
                        TICKETID,
                        AIRTIMETRANSACTIONID,
                        SERVICEVOUCHERID
                    });
                    actionSaleOrder.getReportPrinterSocket(OrderTypeID);
                }
                else {
                    navigation.navigate("DeleteSO");
                }

            })
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI,
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => handleDeleteSOInsurance(info),
                },
            ]);
        })
    }

    const renderFooter = () => {
        return (
            <View
                style={{
                    height: 300,
                }}
            />
        );
    };

    return (
        <View
            style={{
                flex: 1,
                backgroundColor: "white",
            }}
        >
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView
                    style={{
                        flex: 1,
                        alignItems: "center",
                        justifyContent: 'center'
                    }}
                >
                    <Animated.View style={{
                        transform: [{ translateY: translateY }],
                        backgroundColor: COLORS.bgF5F5F5,
                        position: 'relative',
                        top: 0, left: 0, right: 0, zIndex: 1,
                    }}>
                        <View
                            style={{
                                width: constants.width - 20,
                                marginTop: 5
                            }}
                        >
                            <TouchableOpacity style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 5,
                                width: '100%',
                                paddingHorizontal: 5,
                                borderWidth: 1,
                                borderColor: COLORS.bdDDDDDD,
                                height: 44,
                                alignSelf: 'center'
                            }}
                                onPress={() => setIsShowCalendar(true)}
                            >
                                <MyText
                                    style={{
                                        width: '87%',
                                        paddingHorizontal: 5
                                    }}
                                    text={`${moment(fromDate).format(
                                        'DD/MM/YYYY'
                                    )
                                        } - ${moment(toDate).format(
                                            'DD/MM/YYYY'
                                        )
                                        } `}
                                />
                                <Icon
                                    iconSet="Feather"
                                    name="calendar"
                                    style={{
                                        fontSize: 30,
                                        color: COLORS.ic2C8BD7
                                    }}
                                />
                            </TouchableOpacity>
                            <PickerSearch
                                label={"AirTimeTransactionTypeName"}
                                value={"AirTimeTransactionTypeID"}
                                valueSelected={providerInsurance}
                                data={dataProvider}
                                onChange={(item) => {
                                    setProviderInsurance(item.AirTimeTransactionTypeID)
                                }}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 40,
                                    borderRadius: 4,
                                    borderWidth: 1,
                                    borderColor: COLORS.bdE4E4E4,
                                    width: constants.width - 20,
                                    backgroundColor: COLORS.btnFFFFFF,
                                    marginTop: 5
                                }}
                                defaultLabel={"Nhà cung cấp"}
                            />
                            <PickerSearch
                                label={"Label"}
                                value={"SearchTypeID"}
                                valueSelected={searchByInsurance}
                                data={dataInsurance}
                                onChange={(item) => {
                                    setSearchByInsurance(item.SearchTypeID)
                                }}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 40,
                                    borderRadius: 4,
                                    borderWidth: 1,
                                    borderColor: COLORS.bdE4E4E4,
                                    width: constants.width - 20,
                                    backgroundColor: COLORS.btnFFFFFF,
                                    marginBottom: 5,
                                    marginTop: 5
                                }}
                                defaultLabel={"Tìm theo"}
                            />
                        </View>
                    </Animated.View>
                    <SearchInput
                        onSubmit={() => onSubmit(keyword)}
                        inputText={keyword}
                        onChangeText={(text) => {
                            setkeyWork(text)
                        }}
                        onClearText={() => {
                            setkeyWork('');
                        }}
                        placeholder={'Từ khoá'}
                    />
                    <BaseLoading
                        isLoading={stateSearchListHistory.isFetching}
                        isError={stateSearchListHistory.isError}
                        isEmpty={stateSearchListHistory.isEmpty}
                        textLoadingError={stateSearchListHistory.description}
                        onPressTryAgains={() => {
                            getSearchHistoryInsurance()
                        }}
                        content={
                            <FlatList
                                style={{ marginTop: 5 }}
                                data={dataHistoryInsurance}
                                keyExtractor={(item, index) => `${index} `}
                                renderItem={({ item, index }) => (
                                    <ItemCollection
                                        item={item}
                                        index={index}
                                        ServiceCategoryID={ServiceCategoryID}
                                        AirtimeServiceGroupID={AirtimeServiceGroupID}
                                        onPressDetail={() => { }}
                                        onPrint={() => onPrint(item)}
                                        isDelete={isDelete}
                                        onEdit={() => handleSaleOrderPayment(item)}
                                        onAskStatus={() => onAskStatusInsurance(item)}
                                        onActiveCollection={() => { }}
                                        IsActive={IsActive}
                                        onDelete={() => handleDeleteSOInsurance(item)}
                                        onActionList={() => onActionList(item)}
                                    />)
                                }
                                stickySectionHeadersEnabled={false}
                                alwaysBounceVertical={false}
                                bounces={false}
                                scrollEventThrottle={16}
                                ListFooterComponent={renderFooter}
                            />
                        }
                    />
                    <ModalCalendar
                        isVisible={isShowCalendar}
                        hideModal={() => {
                            setIsShowCalendar(false);
                        }}
                        startDate={fromDate}
                        endDate={toDate}
                        setDate={(day) => {
                            setFromDate(day.startDate)
                            setToDate(day.endDate)
                        }}
                    />
                </SafeAreaView>
            </KeyboardAwareScrollView>
        </View>
    )
}

const mapStateToProps = function (state) {
    return {
        dataSearchListHistory: state.insurancePVIReducer.dataSearchListHistory,
        stateSearchListHistory: state.insurancePVIReducer.stateSearchListHistory,
        itemCatalog: state.collectionReducer.itemCatalog,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsurancePVI: bindActionCreators(actionInsurancePVICreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
        actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(HistorySellPVI)

const styles = StyleSheet.create({})
