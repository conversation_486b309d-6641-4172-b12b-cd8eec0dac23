/* eslint-disable no-await-in-loop */
import { SafeAreaView, Alert, View } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import React from 'react';
import { translate } from '@translate';
import { constants } from '@constants';
import { helper, convertHtml2Image, printSocket } from '@common';
import { showBlockUI, hideBlockUI, Button } from '@components';
import { COLORS } from '@styles';
import PrintReport from '../../SaleOrderPayment/component/PrintReport';
import Report from '../../SaleOrderPayment/component/PrintReport/Report';
import { getReportContentBase64 } from '../action';

import {
    getReportPrinterSocket,
    getReportPrinter,
    printBillVoucher,
    printBillVoucherBit
} from '../../SaleOrderPayment/action';

const { H_BILL } = constants;

const SO_TYPE_OFFLINE = 100;

const ReprintCard = ({ navigation, route }) => {
    const dispatch = useDispatch();
    const { saleOrderID: saleOrderID } = route.params;
    const statePrinter = useSelector(
        (state) => state.saleOrderPaymentReducer.statePrinter
    );
    const printerRetail = useSelector(
        (state) => state.saleOrderPaymentReducer.printerRetail
    );
    const { storeID, brandID, languageID, moduleID, userName } = useSelector(
        (state) => state.userReducer
    );

    const [reportRetail, setReportRetail] = React.useState({});

    const handleGetReportPrinter = () => {
        if (helper.checkConfigStorePrint(storeID)) {
            dispatch(getReportPrinterSocket(SO_TYPE_OFFLINE));
        } else {
            dispatch(getReportPrinter(SO_TYPE_OFFLINE));
        }
    };

    const handlePrint = () => {
        if (helper.IsEmptyObject(reportRetail)) {
            Alert.alert(
                '',
                translate('saleOrderPayment.please_choose_printer')
            );
        } else {
            const data = {
                loginStoreId: storeID,
                languageID,
                moduleID,
                reportContents: [{ ReportContent: 'OutputReceiptContent' }],
                saleOrderID
            };
            showBlockUI();
            data.isGetContentHTML = helper.checkConfigStorePrint(storeID);
            data.isFitContent = `${brandID}` === '8' || data.isGetContentHTML;
            if (data.isFitContent) {
                data.isGetContentHTML = true;
                getContentHtml(data);
            } else {
                data.isGetContentHTML = false;
                getContentBase64PDF(data);
            }
        }
    };

    const getContentHtml = (body) => {
        showBlockUI();
        getReportContentBase64(body)
            .then((data) => {
                onConvertHTML(data, body.reportContents, body.isFitContent);
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate(
                                'saleOrderPayment.btn_skip_uppercase'
                            ),
                            style: 'default',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate(
                                'saleOrderPayment.btn_retry_uppercase'
                            ),
                            style: 'default',
                            onPress: () => getContentHtml(body)
                        }
                    ]
                );
            });
    };

    const getContentBase64PDF = (reportInfo) => {
        getReportContentBase64(reportInfo)
            .then((printContent) => {
                getPrintService(
                    reportRetail,
                    printContent.OutputReceiptContent
                );
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: 'OK',
                            style: 'default',
                            onPress: goBack
                        }
                    ]
                );
            });
    };

    const getPrintService = (report, content) => {
        const printerConfig = {
            strPrinterName: report.PRINTERNAME,
            strPaperSize: report.PAPERSIZE,
            paperwidth: report.PAPERWIDTH,
            parperheight: report.PARPERHEIGHT,
            intCopyCount: 1,
            bolIsDuplex: false,
            bolShrinkToMargin: false,
            strBase64: content
        };
        if (report.REPORTID === 2820) {
            printerConfig.strPaperSize = 'A4 210 x 297 mm';
        }
        let formBody = [];
        // eslint-disable-next-line guard-for-in
        for (const property in printerConfig) {
            const encodedKey = encodeURIComponent(property);
            const encodedValue = encodeURIComponent(printerConfig[property]);
            formBody.push(`${encodedKey}=${encodedValue}`);
        }
        formBody = formBody.join('&');
        printBillVoucher(formBody)
            .then(() => {
                Alert.alert(
                    '',
                    translate('saleOrderPayment.print_successfully'),
                    [
                        {
                            text: 'OK',
                            style: 'default',
                            onPress: goBack
                        }
                    ]
                );
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: 'OK',
                            style: 'default',
                            onPress: goBack
                        }
                    ]
                );
            });
    };

    const onConvertHTML = async (data, reportContents, isAnKhang) => {
        const dataBit = await requestConvertHtml(data, reportContents);
        if (helper.IsNonEmptyArray(dataBit)) {
            onPrintBillHTML(data, reportContents, dataBit, isAnKhang);
        } else {
            hideBlockUI();
        }
    };

    const requestConvertHtml = async (data, reportContents) => {
        try {
            const requestConvert = [];
            const { OutputReceiptContentHTML } = data;
            for (const ele of reportContents) {
                const { ReportContent } = ele;
                switch (ReportContent) {
                    case 'OutputReceiptContent':
                        if (helper.IsNonEmptyString(OutputReceiptContentHTML)) {
                            const OutputReceiptContent =
                                await convertHtml2Image(
                                    OutputReceiptContentHTML,
                                    H_BILL
                                );
                            requestConvert.push([OutputReceiptContent]);
                        }
                        break;
                    default:
                        console.log(ele);
                        break;
                }
            }
            return requestConvert;
        } catch (error) {
            console.log('convertHtml2Image', error);
            Alert.alert('', 'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.', [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: hideBlockUI
                }
            ]);
        }
    };

    const onPrintBillHTML = (data, reportContents, dataBit, isAnKhang) => {
        const requestAPI = getPrintHTMLRequestAPI(
            data,
            reportContents,
            dataBit,
            isAnKhang
        );
        if (helper.IsNonEmptyArray(requestAPI)) {
            if (isAnKhang) {
                printAllRequestSocket(requestAPI);
            } else {
                printAllRequestFW(requestAPI);
            }
        } else {
            hideBlockUI();
        }
    };
    const getReport = (type) => {
        switch (type) {
            case 'InvoiceRetailPrinter':
                return reportRetail;
            case 'VATContentPrint':
                return null;
            default:
                // CommonPrinter
                return null;
        }
    };
    const getPrintHTMLRequestAPI = (
        data,
        reportContents,
        dataBit,
        isAnKhang
    ) => {
        const requestAPI = [];
        const { outputReceiptContentPrinterTypeID } = data;
        reportContents.forEach((ele, index) => {
            const { ReportContent } = ele;
            const dataConvert = dataBit[index];
            let report = {};
            switch (ReportContent) {
                case 'OutputReceiptContent':
                    report = getReport(outputReceiptContentPrinterTypeID);
                    break;
                default:
                    report = reportRetail;
                    break;
            }
            if (helper.IsNonEmptyArray(dataConvert)) {
                dataConvert.forEach((info) => {
                    if (isAnKhang) {
                        if (!report.IPPRINTER) {
                            report.IPPRINTER = '*************';
                            report.DELAY = 500;
                        }
                        const printService = getPrintServiceSocket(
                            report,
                            info,
                            ReportContent
                        );
                        requestAPI.push(printService);
                    } else {
                        const printService = getPrintServiceHTML(
                            report,
                            info,
                            ReportContent
                        );
                        requestAPI.push(printService);
                    }
                });
            }
        });
        return requestAPI;
    };
    const getPrintServiceHTML = (report, info, type) => {
        const body = {
            Printer: report.PRINTERSHORTNAME,
            Value: info,
            Type: type,
            User: userName,
            Status: 'Payment'
        };
        return body;
    };
    const getPrintServiceSocket = (report, info) => {
        const body = {
            ip: report.IPPRINTER,
            delay: report.DELAY,
            data: info
        };
        return body;
    };

    const printAllRequestSocket = async (allPromise) => {
        try {
            for (const { data, ip, delay } of allPromise) {
                await printSocket(data, ip);
                if (delay > 0) {
                    await helper.sleep(delay);
                }
            }
            goBack();
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: goBack
                }
            ]);
        }
    };

    const printAllRequestFW = async (allPromise) => {
        try {
            for (const body of allPromise) {
                await printBillVoucherBit(body);
                await helper.sleep(1000);
            }
            Alert.alert('', translate('saleOrderPayment.print_successfully'), [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: goBack
                }
            ]);
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: goBack
                }
            ]);
        }
    };

    const goBack = () => {
        hideBlockUI();
        navigation.goBack();
    };

    React.useEffect(() => {
        handleGetReportPrinter();
    }, []);

    return (
        <SafeAreaView
            style={{
                flex: 1,
                backgroundColor: COLORS.bgF5F5F5
            }}>
            <View style={{ height: '30%' }}>
                <PrintReport
                    title={translate('saleOrderManager.select_printer')}
                    statePrinter={statePrinter}
                    onTryAgains={handleGetReportPrinter}
                    dataRetail={printerRetail}
                    renderItemRetail={({ item }) => (
                        <Report
                            key="ReportRetail"
                            info={item}
                            report={reportRetail}
                            onCheck={() => {
                                setReportRetail(item);
                            }}
                        />
                    )}
                />
            </View>
            <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                <Button
                    text="TIẾP TỤC IN"
                    onPress={handlePrint}
                    styleContainer={{
                        borderRadius: 5,
                        height: 40,
                        backgroundColor: COLORS.bg147EFB,
                        width: '40%'
                    }}
                    styleText={{
                        color: COLORS.bgFFFFFF,
                        fontSize: 14,
                        fontWeight: 'bold'
                    }}
                />
            </View>
        </SafeAreaView>
    );
};

export default ReprintCard;
