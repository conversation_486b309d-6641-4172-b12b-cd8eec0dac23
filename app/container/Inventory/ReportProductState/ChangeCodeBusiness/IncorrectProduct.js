import React, { useEffect, useRef, useState } from 'react';
import { View, TextInput, Keyboard, TouchableOpacity, FlatList, StyleSheet } from 'react-native';
import { MyText, CaptureCamera, Icon, showBlockUI, hideBlockUI } from '@components';
import { API_CONST, constants, ENUM } from '@constants';
import { COLORS } from '@styles';
import { helper } from '@common';
import { NoteInput, ImageProcess } from '../components';
import { SelectedProductInfo } from './components';
import { translate } from '@translate';
import { launchImageLibrary } from 'react-native-image-picker';
import { getImageCDN } from '../../../ShoppingCart/action';
const { FILE_PATH: { INCORRECT_PRODUCT } } = ENUM;

const IncorrectProduct = ({
  note,
  onChangeNote,
  trueProductSelected,
  onSelectTrueProduct,
  listProductByInventoryProcessCode,
  getListProductByInventoryProcess,
  isHasImei = false,
  checkImei,
  isShowTrueProductInfo,
  reportQuantity,
  isUpdate,
  imageUrls = [],
  setImageUrls,
  currentAttachments
}) => {

  const [trueProductId, setTrueProductId] = useState("");
  const [productImei, setProductImei] = useState("");
  const [isShowListTrueProduct, setIsShowListTrueProduct] = useState(false);
  const [editableImei, setEditableImei] = useState(false)
  const [isVisibleCamera, setIsVisibleCamera] = useState(false);

  const currentIndex = useRef(0);

  const listRenderProductItem = listProductByInventoryProcessCode.filter(item => (
    !helper.IsEmptyObject(item) &&
    helper.removeAccent(item.productname).toLowerCase().includes(helper.removeAccent(trueProductId).toLowerCase()) ||
    helper.removeAccent(item.productid).toLowerCase().includes(helper.removeAccent(trueProductId).toLowerCase())
  )).slice(0, 8)

  const isRequiredImage = trueProductSelected.isDifferentProcessCode;

  useEffect(() => {
    if (isShowTrueProductInfo) {
      setIsShowListTrueProduct(false);
      // setTrueProductId(trueProductSelected.productid.trim())
      if (isHasImei && helper.IsNonEmptyString(trueProductSelected.imei)) {
        setProductImei(trueProductSelected.imei.trim().toUpperCase());
      }
    }
  }, [isShowTrueProductInfo])

  useEffect(() => {
    if (!helper.IsEmptyObject(trueProductSelected)) {
      setTrueProductId(trueProductSelected.productid.trim());
      setIsShowListTrueProduct(false);
      setEditableImei(true)
    }
  }, [trueProductSelected])

  const onChangeTrueProductId = (text) => {
    setTrueProductId(text.trim());
    if (!isShowListTrueProduct) {
      setIsShowListTrueProduct(true)
    }
    if (isShowTrueProductInfo) {
      onSelectTrueProduct({});
      setProductImei("");
      setEditableImei(false);
    }
  }

  onSubmitProductId = () => {
    if (helper.IsNonEmptyString(trueProductId) && helper.IsEmptyArray(listRenderProductItem)) {
      Keyboard.dismiss();
      getListProductByInventoryProcess(trueProductId)
    }
  }

  onSubmitImei = () => {
    if (helper.IsNonEmptyString(productImei)) {
      Keyboard.dismiss();
      checkImei(productImei.toUpperCase(), trueProductId)
    }
  }

  const takePicture = (photo) => {
    setIsVisibleCamera(false)
    showBlockUI();
    if (helper.hasProperty(photo, 'uri')) {
      helper.resizeImage(photo).then(({ path, uri, size, name }) => {

        const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: INCORRECT_PRODUCT });
        getImageCDN(body)
          .then((response) => {
            const remoteURI = API_CONST.API_GET_IMAGE_CDN_NEW + response[0];
            let newImageUrls = [...imageUrls];
            let itemImg = {
              realProductStatusAttachId: 0,
              imageURL: remoteURI,
              isDeleted: 0,
              fileName: response[0]
            }
            newImageUrls[currentIndex.current] = itemImg;
            setImageUrls(newImageUrls)
            onGetRealProductStatusAttachmentBOs(newImageUrls)
            hideBlockUI();
          }).catch((error) => {
            hideBlockUI();
            console.log('uploadPicture', error);
          })

      }).catch((error) => {
        hideBlockUI();
        console.log("resizeImage", error);
      });
    } else { hideBlockUI(); }
  }

  const selectPicture = () => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        noData: true
      },
      (response) => {
        setIsVisibleCamera(false)
        showBlockUI();
        if (helper.hasProperty(response, 'uri')) {
          helper.resizeImage(response)
            .then(({ path, uri, size, name }) => {
              const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: INCORRECT_PRODUCT });
              getImageCDN(body)
                .then((response) => {
                  const remoteURI = API_CONST.API_GET_IMAGE_CDN_NEW + response[0];
                  let newImageUrls = [...imageUrls];
                  let itemImg = {
                    realProductStatusAttachId: 0,
                    imageURL: remoteURI,
                    isDeleted: 0,
                    fileName: response[0]
                  }
                  newImageUrls[currentIndex.current] = itemImg;
                  setImageUrls(newImageUrls)
                  onGetRealProductStatusAttachmentBOs(newImageUrls)
                  hideBlockUI();
                }).catch((error) => {
                  hideBlockUI();
                  console.log('uploadPicture', error);
                })
            })
            .catch((error) => {
              hideBlockUI();
              console.log('resizeImage', error);
            });
        } else { hideBlockUI(); }
      }
    );
  };

  const deleteImage = (index) => {
    let newImageUrls = [...imageUrls];
    if (newImageUrls[index].realProductStatusAttachId !== 0) {
      const indexImage = currentAttachments.findIndex(ele => ele.realProductStatusAttachId == newImageUrls[index].realProductStatusAttachId);
      if (indexImage !== -1) {
        currentAttachments[indexImage].isDeleted = 1;
      }
    }
    newImageUrls[index] = {}
    setImageUrls(newImageUrls)
  }

  const renderProductItem = ({ item, index }) => {
    const { productname, productid } = item
    return (
      <TouchableOpacity style={{
        width: constants.width - 20,
        padding: 5,
        marginVertical: 3,
        flexDirection: 'row',
        backgroundColor: index % 2 == 0 ? COLORS.bgFFFFFF : COLORS.bgF0F0F0
      }}
        key={`${index}`}
        onPress={() => {
          Keyboard.dismiss();
          // setTrueProductId(productid.trim());
          setIsShowListTrueProduct(false);
          setEditableImei(true)
          onSelectTrueProduct({ ...item, quantity: reportQuantity });
        }}
      >
        <MyText
          text={productid.trim()}
          style={{
            color: COLORS.txt288AD6,
            fontWeight: 'bold',
            width: constants.width - 35
          }}
        >
          <MyText
            text={` - ${productname}`}
            style={{
              color: COLORS.txt333333,
            }}
          />
        </MyText>
      </TouchableOpacity>
    )
  }

  return (
    <View style={{
      width: constants.width,
      justifyContent: 'center',
      backgroundColor: COLORS.bgFFFFFF
    }}>
      <View
        style={{
          width: constants.width,
          paddingTop: 10,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 10,

        }}
      >
        <View style={{
          width: constants.width / 5 * 2,
          justifyContent: 'center',
        }}>
          <MyText
            text={translate('inventory.label_enter_true_productid')}
            addSize={1.5}
            style={{
              color: COLORS.txt333333,
              fontWeight: 'bold'
            }}
          />
        </View>
        <View style={{ width: constants.width / 5 * 3 }}>
          <TextInput
            placeholder={translate('inventory.true_productid_placeholder')}
            style={{
              width: constants.width / 5 * 3 - 20,
              borderWidth: 1,
              borderColor: COLORS.bdB9B9B9,
              borderRadius: 5,
              padding: 10,
              opacity: isUpdate ? 0.5 : 1
            }}
            keyboardType="default"
            returnKeyType={"done"}
            value={trueProductId}
            onChangeText={onChangeTrueProductId}
            blurOnSubmit={true}
            onBlur={onSubmitProductId}
            editable={!isUpdate}
          />
        </View>
        <View
          style={{
            height: constants.getSize(35),
            alignItems: 'center',
            justifyContent: 'center',
            alignSelf: 'flex-end',
            width: constants.width / 5
          }}
        />
      </View>
      {isShowListTrueProduct && helper.IsNonEmptyArray(listRenderProductItem) &&
        <View View
          style={{
            width: constants.width - 20,
            backgroundColor: COLORS.bgFFFFFF,
            marginTop: 10,
            borderRadius: 5,
            borderWidth: StyleSheet.hairlineWidth,
            paddingHorizontal: 5
          }}
        >
          <FlatList
            data={listRenderProductItem}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderProductItem}
            removeClippedSubviews={false}
            stickySectionHeadersEnabled={false}
            alwaysBounceVertical={false}
            bounces={false}
            scrollEventThrottle={16}
            keyboardShouldPersistTaps="always"
          />
        </View>
      }
      {
        isHasImei && <View style={{
          width: constants.width,
          paddingHorizontal: 10,
          paddingTop: 10,
          flexDirection: 'row',
          alignItems: 'center'
        }}>
          <MyText
            text={translate('inventory.label_enter_true_imei')}
            style={{
              width: constants.width / 5 * 2,
              color: COLORS.txt333333,
              fontWeight: 'bold'
            }}
            addSize={1.5}
          />
          <TextInput
            placeholder={translate('inventory.true_imei_placeholder')}
            style={{
              width: constants.width / 5 * 3 - 20,
              borderWidth: 1,
              padding: 10,
              borderColor: COLORS.bdB9B9B9,
              borderRadius: 5,
              opacity: isUpdate ? 0.5 : 1
            }}
            keyboardType="default"
            returnKeyType={"done"}
            value={productImei}
            onChangeText={(text) => {
              setProductImei(text.replace(/\n/g, "").trim())
              if (isShowTrueProductInfo) {
                onSelectTrueProduct({});
              }
            }}
            blurOnSubmit={true}
            onBlur={onSubmitImei}
            editable={!isUpdate && editableImei}
          />
        </View>
      }
      {
        isShowTrueProductInfo && <View style={{
          width: constants.width,
          alignItems: 'center',
          paddingTop: 10
        }}>
          <SelectedProductInfo productInfo={trueProductSelected} />
        </View>
      }
      <NoteInput note={note} onChangeNote={onChangeNote} />
      <MyText
        text={translate('inventory.attach_image')}
        style={{
          paddingLeft: 10,
          paddingVertical: 10,
          fontWeight: 'bold',
          color: COLORS.txt333333
        }}
        addSize={1.5}
      >
        <MyText
          text={isRequiredImage ? '* ' : ''}
          style={{
            fontWeight: 'bold',
            color: COLORS.txtFF0000
          }}
          addSize={1.5}
        >
          <MyText
            text={translate('inventory.max_5_files')}
            style={{
              fontWeight: 'bold',
              color: COLORS.txt288AD6
            }}
            addSize={1.5}
          />
        </MyText>
      </MyText>
      <View style={{
        width: constants.width,
        flexDirection: 'row',
        flexWrap: "wrap"
      }}>
        {imageUrls.map((url, index) =>
          <ImageProcess
            key={index}
            onCamera={() => {
              setIsVisibleCamera(true)
              currentIndex.current = index
            }}
            urlImageLocal={url.imageURL}
            urlImageRemote={url.filePath}
            deleteImage={() => { deleteImage(index) }}
            index={index}
          />
        )}
        {imageUrls.length < 5 && <View style={{
          width: constants.width / 3,
          alignItems: 'center',
          paddingVertical: 5
        }}>
          <TouchableOpacity style={{
            height: constants.width / 3 - 10,
            width: constants.width / 3 - 10,
            justifyContent: "center",
            alignItems: "center",
            alignSelf: "center",
            backgroundColor: COLORS.bgFAFAFA,
            borderRadius: 5,
            borderWidth: 1,
            borderColor: COLORS.bd0099E5
          }}
            onPress={() => {
              setImageUrls([...imageUrls, {}])
            }}
            activeOpacity={0.6}
          >
            <View style={{ justifyContent: "center", alignItems: "center" }}>
              <Icon
                iconSet={"Ionicons"}
                name={"add-circle-outline"}
                color={COLORS.icFFB23F}
                size={60}
              />
            </View>
          </TouchableOpacity>
        </View>}
      </View>
      <CaptureCamera
        isVisibleCamera={isVisibleCamera}
        takePicture={takePicture}
        closeCamera={() => { setIsVisibleCamera(false) }}
        selectPicture={selectPicture}
      />
    </View >
  )
}

export default IncorrectProduct