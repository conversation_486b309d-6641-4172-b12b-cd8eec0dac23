import React, { useState, useRef } from 'react'
import { API_CONST, constants, ENUM } from '@constants';
import { View, TouchableOpacity, TextInput, Keyboard } from 'react-native'
import { NoteInput, ImageProcess } from '../components';
import { MyText, CaptureCamera, showBlockUI, hideBlockUI, Icon } from '@components';
import { translate } from '@translate';
import { COLORS } from '@styles';
import { launchImageLibrary } from 'react-native-image-picker';
import { helper } from '@common';
import { getImageCDN } from '../../../ShoppingCart/action';
const { FILE_PATH: { SPARE_PRODUCT } } = ENUM;

const SpareProduct = ({
  note,
  onChangeNote,
  imageUrls = [],
  setImageUrls,
  isHasImei,
  checkImei,
  isUpdate,
  isCheckedImei = true,
  productImei,
  setProductImei,
  currentAttachments
}) => {

  const [isVisibleCamera, setIsVisibleCamera] = useState(false);
  const currentIndex = useRef(0);

  const takePicture = (photo) => {
    setIsVisibleCamera(false)
    showBlockUI();
    if (helper.hasProperty(photo, 'uri')) {
      helper.resizeImage(photo).then(({ path, uri, size, name }) => {
        const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: SPARE_PRODUCT });
        getImageCDN(body)
          .then((response) => {
            const remoteURI = API_CONST.API_GET_IMAGE_CDN_NEW + response[0];
            let newImageUrls = [...imageUrls];
            let itemImg = {
              realProductStatusAttachId: 0,
              imageURL: remoteURI,
              isDeleted: 0,
              fileName: response[0]
            }
            newImageUrls[currentIndex.current] = itemImg;
            setImageUrls(newImageUrls)
            onGetRealProductStatusAttachmentBOs(newImageUrls)
            hideBlockUI();
          }).catch((error) => {
            hideBlockUI();
            console.log('uploadPicture', error);
          })

      }).catch((error) => {
        hideBlockUI();
        console.log("resizeImage", error);
      });
    } else { hideBlockUI(); }
  }

  const selectPicture = () => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        noData: true
      },
      (response) => {
        setIsVisibleCamera(false)
        showBlockUI();
        if (helper.hasProperty(response, 'uri')) {
          helper.resizeImage(response)
            .then(({ path, uri, size, name }) => {
              const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: SPARE_PRODUCT });
              getImageCDN(body)
                .then((response) => {
                  const remoteURI = API_CONST.API_GET_IMAGE_CDN_NEW + response[0];
                  let newImageUrls = [...imageUrls];
                  let itemImg = {
                    realProductStatusAttachId: 0,
                    imageURL: remoteURI,
                    isDeleted: 0,
                    fileName: response[0]
                  }
                  newImageUrls[currentIndex.current] = itemImg;
                  setImageUrls(newImageUrls)
                  onGetRealProductStatusAttachmentBOs(newImageUrls)
                  hideBlockUI();
                }).catch((error) => {
                  hideBlockUI();
                  console.log('uploadPicture', error);
                })
            })
            .catch((error) => {
              hideBlockUI();
              console.log('resizeImage', error);
            });
        } else { hideBlockUI(); }
      }
    );
  };

  const deleteImage = (index) => {
    let newImageUrls = [...imageUrls];
    if (newImageUrls[index].realProductStatusAttachId !== 0) {
      const indexImage = currentAttachments.findIndex(ele => ele.realProductStatusAttachId == newImageUrls[index].realProductStatusAttachId);
      if (indexImage !== -1) {
        currentAttachments[indexImage].isDeleted = 1;
      }
    }
    newImageUrls[index] = {}
    setImageUrls(newImageUrls)
  }

  onSubmitImei = () => {
    if (helper.IsNonEmptyString(productImei)) {
      Keyboard.dismiss();
      checkImei(productImei.toUpperCase())
    }
  }

  return (
    <View>
      {isHasImei && <View style={{
        width: constants.width,
        paddingHorizontal: 10,
        paddingTop: 10,
        flexDirection: 'row',
        alignItems: 'center'
      }}>
        <MyText
          text={translate('inventory.label_enter_spare_imei')}
          style={{
            width: constants.width / 5 * 2,
            color: COLORS.txt333333,
            fontWeight: 'bold'
          }}
          addSize={1.5}
        />
        <TextInput
          placeholder={translate('inventory.spare_imei_placeholder')}
          style={{
            width: constants.width / 5 * 3 - 20,
            borderWidth: 1,
            padding: 10,
            borderColor: COLORS.bdB9B9B9,
            borderRadius: 5,
            opacity: isUpdate ? 0.5 : 1
          }}
          keyboardType="default"
          returnKeyType={"done"}
          value={productImei}
          onChangeText={(text) => {
            setProductImei(text)
            if (isCheckedImei) {
              checkImei("")
            }
          }}
          blurOnSubmit={true}
          onBlur={onSubmitImei}
          editable={!isUpdate}
        />
      </View>}
      <NoteInput note={note} onChangeNote={onChangeNote} />
      <MyText
        text={translate('inventory.attach_image')}
        style={{
          paddingLeft: 10,
          paddingVertical: 10,
          fontWeight: 'bold',
          color: COLORS.txt333333
        }}
        addSize={1.5}
      >
        <MyText
          text={'* '}
          style={{
            fontWeight: 'bold',
            color: COLORS.txtFF0000
          }}
          addSize={1.5}
        >
          <MyText
            text={translate('inventory.max_5_files')}
            style={{
              fontWeight: 'bold',
              color: COLORS.txt288AD6
            }}
            addSize={1.5}
          />
        </MyText>
      </MyText>
      <View style={{
        width: constants.width,
        flexDirection: 'row',
        flexWrap: "wrap"
      }}>
        {imageUrls.map((url, index) =>
          <ImageProcess
            key={index}
            onCamera={() => {
              setIsVisibleCamera(true)
              currentIndex.current = index
            }}
            urlImageLocal={url.imageURL}
            urlImageRemote={url.filePath}
            deleteImage={() => { deleteImage(index) }}
            index={index}
          />
        )}
        {imageUrls.length < 5 && <View style={{
          width: constants.width / 3,
          alignItems: 'center',
          paddingVertical: 5
        }}>
          <TouchableOpacity style={{
            height: constants.width / 3 - 10,
            width: constants.width / 3 - 10,
            justifyContent: "center",
            alignItems: "center",
            alignSelf: "center",
            backgroundColor: COLORS.bgFAFAFA,
            borderRadius: 5,
            borderWidth: 1,
            borderColor: COLORS.bd0099E5
          }}
            onPress={() => {
              setImageUrls([...imageUrls, {}])
            }}
            activeOpacity={0.6}
          >
            <View style={{ justifyContent: "center", alignItems: "center" }}>
              <Icon
                iconSet={"Ionicons"}
                name={"add-circle-outline"}
                color={COLORS.icFFB23F}
                size={60}
              />
            </View>
          </TouchableOpacity>
        </View>}
      </View>
      <CaptureCamera
        isVisibleCamera={isVisibleCamera}
        takePicture={takePicture}
        closeCamera={() => { setIsVisibleCamera(false) }}
        selectPicture={selectPicture}
      />
    </View>
  )
}

export default SpareProduct