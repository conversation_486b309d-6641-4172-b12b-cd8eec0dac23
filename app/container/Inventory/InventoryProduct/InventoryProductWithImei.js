import React, { Component } from 'react';
import { View, FlatList, Alert, TouchableOpacity } from 'react-native';
import SafeAreaView from 'react-native-safe-area-view';
import {
    InputSearch,
    ItemGroupProduct,
    ModalInventoryProduct,
    InventoryBackHeader,
    ModalChangeQuantity
} from './components';
import { connect } from 'react-redux';
import * as actionInventoryCreator from '../action';
import { bindActionCreators } from 'redux';
import { constants } from '@constants';
import { COLORS } from '@styles';
import {
    MyText,
    showBlockUI,
    hideBlockUI,
    Icon,
    BaseLoading,
    ScanBarcodeML
} from '@components';
import { helper } from '@common';
import { translate } from '@translate';

class InventoryProductWithImei extends Component {
    constructor(props) {
        super(props);
        this.state = {
            valueSearch: '',
            isVisibleCamera: false,
            isVisibleModal: false,
            searchProductInfo: {},
            isHasWithImeiProductChecked: false,
            isVisiblePopup: false,
            productInfo: {},
            inventoryListData: {}
        };
    }

    componentDidMount() {
        this.getListGroupProduct();
    }

    getListGroupProduct = () => {
        const { actionInventory } = this.props;
        const {
            inventoryData: { inventoryTermData, inventoryAreaId },
            productStatus
        } = this.props.route.params;
        // const data = {
        //   inventoryTermId: inventoryTermData.inventorytermid,
        //   inventoryAreaId,
        //   inventoryStatusID: productStatus.INVENTORYSTATUSID,
        //   isHadImei: true,
        //   checkedStatus: 1,
        //   beginTime: inventoryTermData.createddate
        // }

        const data = {
            inventoryTermId: inventoryTermData.inventorytermid,
            inventoryAreaId: -1,
            inventoryStatusID: productStatus.INVENTORYSTATUSID,
            isHadImei: true,
            checkedStatus: 1,
            beginTime: inventoryTermData.createddate
        };
        actionInventory.getListGroupProduct(data);
    };

    searchProduct = (keyword, isShowCamera = false, isProduct = false) => {
        if (!helper.IsNonEmptyString(keyword)) {
            return;
        }
        const { actionInventory, listGroupProduct, navigation } = this.props;
        const {
            inventoryData: {
                inventoryAreaId,
                inventoryTermData,
                inventoryTypeId
            },
            productStatus
        } = this.props.route.params;
        showBlockUI();
        const data = {
            keyword: keyword,
            isHasImei: true,
            intProduct: 1,
            inventoryTermId: inventoryTermData.inventorytermid,
            inventoryTypeId,
            inventoryAreaId: -1,
            inventoryStatusID: productStatus.INVENTORYSTATUSID,
            beginTime: inventoryTermData.createddate,
            isProduct
        };
        actionInventory
            .searchInventoryProduct(data)
            .then((response) => {
                this.setState({ searchProductInfo: response });
                const { listProductsAfterCheckExist, listProductsAfterSearch } =
                    response;
                if (listProductsAfterCheckExist.length == 1) {
                    const inventoryData = {
                        imei: listProductsAfterSearch[0].imei,
                        inventoryTermID: inventoryTermData.inventorytermid,
                        productID: listProductsAfterCheckExist[0].productid,
                        checkedStatus:
                            listProductsAfterCheckExist[0].checkedstatus,
                        inventoryImeiList: [],
                        inventoryStatusID: productStatus.INVENTORYSTATUSID,
                        beginTime: inventoryTermData.begintime,
                        inventoryOrderID: inventoryTermData.inventoryorderid,
                        inventoryID: listProductsAfterCheckExist[0].inventoryID
                    };
                    if (
                        listProductsAfterCheckExist[0].checkedstatus == 0 &&
                        inventoryTermData.finishstatus != 1
                    ) {
                        this.lockInventoryProduct(
                            listProductsAfterCheckExist[0],
                            listProductsAfterSearch[0].imei
                        );
                    } else {
                        hideBlockUI();
                        this.setState({
                            isVisiblePopup: true,
                            productInfo: listProductsAfterCheckExist[0],
                            inventoryListData: inventoryData
                        });
                        // navigation.navigate('ProductWithImeiDetail', {
                        //     productInfo: productInfo,
                        //     productStatus: productStatus,
                        //     inventoryData,
                        //     openCamera: () => {
                        //         this.setState({ isVisibleCamera: true });
                        //     },
                        //     isShowButtonDone: () => {
                        //         this.setState({
                        //             isHasWithImeiProductChecked: true
                        //         });
                        //     },
                        //     reloadData: this.getListGroupProduct
                        // });
                    }
                } else {
                    hideBlockUI();
                    this.setState({ isVisibleModal: true });
                }
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: isShowCamera
                                ? translate('inventory.scan_try_again')
                                : translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => {
                                isShowCamera
                                    ? (hideBlockUI(),
                                      this.setState({ isVisibleCamera: true }))
                                    : this.searchProduct(
                                          keyword,
                                          false,
                                          isProduct
                                      );
                            }
                        }
                    ]
                );
            });
    };

    onSelectProduct = (productData) => {
        const {
            inventoryData: { inventoryTermData },
            productStatus,
            beginTime
        } = this.props.route.params;
        this.setState({
            isVisibleModal: false,
            valueSearch: ''
        });
        const inventoryData = {
            imei: productData.imei,
            inventoryTermID: inventoryTermData.inventorytermid,
            productID: productData.productid,
            checkedStatus: productData.checkedstatus,
            inventoryImeiList: [],
            inventoryStatusID: productStatus.INVENTORYSTATUSID,
            beginTime: inventoryTermData.begintime,
            inventoryOrderID: inventoryTermData.inventoryorderid,
            inventoryID: productData.inventoryID
        };
        if (
            productData.checkedstatus == 0 &&
            inventoryTermData.finishstatus != 1
        ) {
            showBlockUI();
            this.lockInventoryProduct(productData);
        } else {
            hideBlockUI();
            this.setState({
                isVisiblePopup: true,
                productInfo: productData,
                inventoryListData: inventoryData
            });
            // this.props.navigation.navigate('ProductWithImeiDetail', {
            //     productInfo: productData,
            //     productStatus,
            //     inventoryData: inventoryData,
            //     openCamera: () => {
            //         this.setState({ isVisibleCamera: true });
            //     },
            //     isShowButtonDone: () => {
            //         this.setState({ isHasWithImeiProductChecked: true });
            //     },
            //     reloadData: this.getListGroupProduct
            // });
        }
    };

    onPressItemSubGroupProduct = (productId) => {
        this.searchProduct(productId, false, true);
    };

    onGoBack = () => {
        const { navigation } = this.props;
        if (this.state.isHasWithImeiProductChecked) {
            Alert.alert(
                translate('common.notification_uppercase'),
                translate('inventory.select_finish')
            );
        } else {
            navigation.goBack();
        }
    };

    onDone = () => {
        const { navigation } = this.props;
        this.setState({
            isHasWithImeiProductChecked: false
        });
        navigation.goBack();
    };

    lockInventoryProduct = async (baseProduct, imei = null) => {
        // showBlockUI();
        const { actionInventory, navigation } = this.props;
        const {
            inventoryData: { inventoryTermData, inventoryTypeId },
            productStatus
        } = this.props.route.params;
        const { inventoryorderid, inventorytermid, inventoryAreaId, hadarea } =
            inventoryTermData;
        const { productid, subgroupid, itemid } = baseProduct;
        let params = {
            inventoryOrderID: inventoryorderid,
            inventoryAreaID: -1,
            productID: productid.trim(),
            inventoryTermID: inventorytermid,
            hadArea: hadarea,
            inventoryOrderTypeID: inventoryTypeId,
            subgroupid: subgroupid,
            itemID: itemid
        };
        const inventoryDataNavigate = {
            imei: imei,
            inventoryTermID: inventoryTermData.inventorytermid,
            productID: baseProduct.productid,
            checkedStatus: baseProduct.checkedstatus,
            inventoryImeiList: [],
            inventoryStatusID: productStatus.INVENTORYSTATUSID,
            beginTime: inventoryTermData.begintime,
            inventoryOrderID: inventoryTermData.inventoryorderid,
            inventoryID: baseProduct.inventoryID
        };
        await actionInventory
            .lockInventoryProduct(params)
            .then(() => {
                hideBlockUI();
                this.setState({
                    isVisiblePopup: true,
                    productInfo: baseProduct,
                    inventoryListData: inventoryDataNavigate
                });
                // this.props.navigation.navigate('ProductWithImeiDetail', {
                //     productInfo: baseProduct,
                //     productStatus,
                //     inventoryData: inventoryDataNavigate,
                //     openCamera: () => {
                //         this.setState({ isVisibleCamera: true });
                //     },
                //     isShowButtonDone: () => {
                //         this.setState({ isHasWithImeiProductChecked: true });
                //     },
                //     reloadData: this.getListGroupProduct
                // });
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok',
                        onPress: hideBlockUI
                    }
                ]);
            });
    };

    onPressPopupQuantity = async (value) => {
        const { actionInventory, navigation } = this.props;
        const {
            inventoryData: { inventoryTermData },
            productStatus,
            beginTime
        } = this.props.route.params;
        const { productInfo, inventoryListData } = this.state;
        console.log(
            '🤜 ******* InventoryProductWithImei ******* onPressPopupQuantity= ******* inventoryTermData:',
            inventoryTermData
        );

        let data = {
            productId: productInfo.productid,
            quantity: value,
            inventoryStatusId: productStatus.INVENTORYSTATUSID,
            inventoryTermId: inventoryTermData.inventorytermid,
            inventoryOrderId: inventoryTermData.inventoryorderid,
            beginTime: inventoryTermData.begintime
        };
        showBlockUI();
        await actionInventory
            .checkImeiByQuantity(data)
            .then((response) => {
                console.log(
                    '🤜 ******* InventoryProductWithImei ******* awaitactionInventory.checkImeiByQuantity ******* response:',
                    response
                );
                if (response.error == false) {
                    Alert.alert(
                        translate('common.notification'),
                        'Số lượng kiểm hợp lệ',
                        [
                            {
                                text: 'Ok',
                                onPress: () => {
                                    this.setState({ isVisiblePopup: false });
                                    hideBlockUI();
                                }
                            }
                        ]
                    );
                    return;
                }
            })
            .catch((error) => {
                if (error.errorType == 1) {
                    Alert.alert(
                        translate('common.notification'),
                        error.msgError,
                        [
                            {
                                text: 'Ok',
                                onPress: () => {
                                    this.setState({ isVisiblePopup: false });
                                    hideBlockUI();
                                    navigation.navigate(
                                        'ProductWithImeiDetail',
                                        {
                                            productInfo: productInfo,
                                            productStatus: productStatus,
                                            inventoryListData,
                                            openCamera: () => {
                                                this.setState({
                                                    isVisibleCamera: true
                                                });
                                            },
                                            isShowButtonDone: () => {
                                                this.setState({
                                                    isHasWithImeiProductChecked: true
                                                });
                                            },
                                            reloadData: this.getListGroupProduct
                                        }
                                    );
                                }
                            }
                        ]
                    );
                } else {
                    Alert.alert(
                        translate('common.notification'),
                        error.msgError,
                        [
                            {
                                text: 'Ok',
                                onPress: () => {
                                    this.setState({ isVisiblePopup: false });
                                    hideBlockUI();
                                }
                            }
                        ]
                    );
                }
            });
    };

    render() {
        const { productStatus } = this.props.route.params;
        const {
            listGroupProduct,
            stateListGroupProduct: { isFetching, isEmpty, description, isError }
        } = this.props;
        const {
            valueSearch,
            isVisibleModal,
            isVisibleCamera,
            searchProductInfo,
            isHasWithImeiProductChecked,
            isVisiblePopup
        } = this.state;

        return (
            <View
                style={{
                    flex: 1
                }}>
                <InventoryBackHeader
                    onGoBack={this.onGoBack}
                    title={translate('header.inventory_has_imei_product')}
                    key={'InventoryProductWithImeiHeader'}
                />
                <SafeAreaView
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF
                    }}>
                    <View
                        style={{
                            width: constants.width,
                            backgroundColor: COLORS.bg288AD6,
                            paddingVertical: 7,
                            paddingHorizontal: 10
                        }}>
                        <MyText
                            text={translate('inventory.inventory_status')}
                            style={{
                                color: COLORS.txtFFFFFF,
                                fontWeight: 'bold'
                            }}
                            addSize={3}>
                            <MyText
                                text={productStatus.INVENTORYSTATUSNAME}
                                style={{
                                    fontWeight: 'bold',
                                    color: COLORS.txtFFFF00
                                }}
                                addSize={3}
                            />
                        </MyText>
                    </View>
                    <View
                        style={{
                            flex: 1,
                            alignItems: 'center'
                        }}>
                        <View
                            style={{
                                width: constants.width - 20,
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                flexDirection: 'row'
                            }}>
                            <InputSearch
                                value={valueSearch}
                                onChangeText={(text) => {
                                    this.setState({ valueSearch: text });
                                }}
                                onBlur={() => {
                                    this.searchProduct(valueSearch);
                                }}
                                clearText={() => {
                                    this.setState({ valueSearch: '' });
                                }}
                                placeholder={translate(
                                    'inventory_share.input_lots_code_name'
                                )}
                            />
                            <TouchableOpacity
                                activeOpacity={0.6}
                                onPress={() => {
                                    this.setState({ isVisiblePopup: true });
                                    // this.setState({ isVisibleCamera: true });
                                }}>
                                <Icon
                                    iconSet={'MaterialCommunityIcons'}
                                    name={'barcode-scan'}
                                    color={COLORS.icF89000}
                                    size={40}
                                />
                            </TouchableOpacity>
                        </View>

                        <BaseLoading
                            isLoading={isFetching}
                            isEmpty={isEmpty}
                            textLoadingError={description}
                            isError={isError}
                            onPressTryAgains={this.getListGroupProduct}
                            content={
                                <View
                                    style={{
                                        width: constants.width,
                                        paddingHorizontal: 10,
                                        paddingBottom: 20
                                    }}>
                                    <FlatList
                                        data={listGroupProduct}
                                        renderItem={({ item, index }) => (
                                            <ItemGroupProduct
                                                // onChangeQuantity={() =>
                                                //     this.setState({
                                                //         isVisiblePopup: true
                                                //     })
                                                // }
                                                groupProductItem={item}
                                                onPressSubGroupProduct={
                                                    this.searchProduct
                                                }
                                                productStatus={
                                                    productStatus.INVENTORYSTATUSNAME
                                                }
                                            />
                                        )}
                                        keyExtractor={(item, index) =>
                                            index.toString()
                                        }
                                        showsVerticalScrollIndicator={false}
                                        showsHorizontalScrollIndicator={false}
                                        removeClippedSubviews={true}
                                        keyboardShouldPersistTaps={'always'}
                                        bounces={false}
                                    />
                                </View>
                            }
                        />
                        {isHasWithImeiProductChecked && (
                            <TouchableOpacity
                                style={{
                                    backgroundColor: COLORS.btn288AD6,
                                    paddingVertical: 10,
                                    borderRadius: 5,
                                    width: constants.width / 3,
                                    alignItems: 'center',
                                    alignSelf: 'center',
                                    marginBottom: 10
                                }}
                                activeOpacity={0.6}
                                onPress={this.onDone}>
                                <MyText
                                    text={translate('inventory.complete_input')}
                                    style={{
                                        fontWeight: 'bold',
                                        color: COLORS.txtFFFFFF
                                    }}
                                    addSize={2}
                                />
                            </TouchableOpacity>
                        )}
                    </View>
                    {isVisibleCamera && (
                        <ScanBarcodeML
                            isVisible={isVisibleCamera}
                            closeCamera={() => {
                                this.setState({ isVisibleCamera: false });
                            }}
                            resultScanBarcode={(barcode) => {
                                this.setState(
                                    {
                                        isVisibleCamera: false,
                                        valueSearch: barcode
                                    },
                                    this.searchProduct(barcode, true)
                                );
                            }}
                        />
                    )}
                    {isVisibleModal && (
                        <ModalInventoryProduct
                            isVisible={isVisibleModal}
                            searchProductInfo={searchProductInfo}
                            hideModal={() => {
                                this.setState({ isVisibleModal: false });
                            }}
                            onSelectProduct={this.onSelectProduct}
                        />
                    )}
                    {isVisiblePopup && (
                        <ModalChangeQuantity
                            isVisible={isVisiblePopup}
                            productId={this.state.productInfo.productid}
                            onChangeQuantity={(text) =>
                                this.onPressPopupQuantity(text)
                            }
                            hideModal={() =>
                                this.setState({ isVisiblePopup: false })
                            }
                        />
                    )}
                </SafeAreaView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        listGroupProduct: state.inventoryReducer.listGroupProduct,
        stateListGroupProduct: state.inventoryReducer.stateListGroupProduct
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionInventory: bindActionCreators(actionInventoryCreator, dispatch)
    };
};

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(InventoryProductWithImei);
