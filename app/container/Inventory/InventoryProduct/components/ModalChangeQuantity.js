import React, { useState } from 'react';
import {
    View,
    TouchableOpacity,
    FlatList,
    TextInput,
    Alert
} from 'react-native';
import KModal from 'react-native-modal';
import { constants } from '@constants';
import { MyText, Icon } from '@components';
import { translate } from '@translate';
import { COLORS } from '@styles';
import { helper } from '@common';

const ModalChangeQuantity = ({
    isVisible,
    hideModal,
    onChangeQuantity,
    productId
}) => {
    const [text, setText] = useState(0);
    return (
        <KModal
            animationType="fade"
            transparent={true}
            visible={isVisible}
            onRequestClose={() => {
                return;
            }}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}
            style={{ margin: 0 }}>
            <View
                style={{
                    width: constants.width,
                    height: constants.height,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: COLORS.bg0000005
                }}>
                <View
                    style={{
                        backgroundColor: COLORS.bgFFFFFF,
                        height: (constants.height * 3) / 10,
                        width: constants.width - 20,
                        padding: 10,
                        borderRadius: 10,
                        alignItems: 'center'
                    }}>
                    <View
                        style={{
                            marginTop: 10,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            width: constants.width - 60
                        }}>
                        <MyText
                            text={`Mã sản phẩm: ${productId}`}
                            addSize={2}
                            style={{
                                color: COLORS.txt037EF3,
                                fontWeight: '600'
                            }}
                        />
                    </View>
                    <View
                        style={{
                            marginTop: 12
                        }}>
                        <MyText
                            text={'Số lượng kiểm'}
                            addSize={2}
                            style={{
                                fontWeight: '600'
                            }}
                        />
                        <TextInput
                            placeholder={'Vui lòng nhập số lượng kiểm'}
                            style={{
                                marginTop: 10,
                                height: 40,
                                paddingHorizontal: 10,
                                width: constants.width - 60,
                                borderWidth: 1,
                                borderColor: COLORS.bdCCCCCC,
                                borderRadius: 12
                            }}
                            value={text}
                            onChangeText={(text) => {
                                setText(text);
                            }}
                        />
                    </View>
                    <View
                        style={{
                            flex: 1,
                            alignItems: 'flex-end',
                            flexDirection: 'row'
                        }}>
                        <TouchableOpacity
                            style={{
                                margin: 20,
                                flex: 0.5,
                                justifyContent: 'center',
                                alignItems: 'center',
                                backgroundColor: COLORS.bg46A0E0,
                                padding: 10,
                                borderRadius: 8
                            }}
                            onPress={hideModal}>
                            <MyText
                                text="Đóng"
                                style={{
                                    color: COLORS.txtFFFFFF
                                }}
                            />
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={{
                                flex: 0.5,
                                margin: 20,
                                justifyContent: 'center',
                                alignItems: 'center',
                                backgroundColor: COLORS.bg46A0E0,
                                padding: 10,
                                borderRadius: 8
                            }}
                            onPress={() => {
                                if (text) {
                                    onChangeQuantity(text);
                                    hideModal();
                                } else {
                                    Alert.alert(
                                        'Thông báo',
                                        'Số lượng kiểm không được để trống',
                                        [
                                            {
                                                text: 'OK',
                                                onPress: () =>
                                                    console.log('OK Pressed')
                                            }
                                        ],
                                        { cancelable: false }
                                    );
                                }
                            }}>
                            <MyText
                                text="Lưu"
                                style={{
                                    color: COLORS.txtFFFFFF
                                }}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </KModal>
    );
};

export default ModalChangeQuantity;
