import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { MyText, Icon } from '@components';
import { translate } from '@translate';

const ItemSubgroupProduct = ({
    productData,
    onPress,
    productStatus,
}) => {
    const { checkedQuantity, productid, productname } = productData;

    return (
        <TouchableOpacity
            style={{
                width: constants.width - 36,
                borderColor: COLORS.bd147EFB,
                borderWidth: 1,
                borderRadius: 4,
                padding: 8,
                marginVertical: 5,
                justifyContent: 'space-between',
                backgroundColor: COLORS.bgFFFFFF,
                shadowColor: COLORS.sd000000,
                shadowOffset: {
                    width: 0,
                    height: 1
                },
                shadowOpacity: 0.4,
                shadowRadius: 3.84,
                elevation: 2,
                overflow: 'hidden',
                alignSelf: 'center'
            }}
            activeOpacity={0.6}
            onPress={() => {
                onPress(productid.trim(), false, true);
            }}>
            <MyText
                text={productname}
                style={{
                    fontWeight: 'bold',
                    color: COLORS.txt333333
                }}>
                <MyText
                    text={` - ${productid.trim()}`}
                    style={{
                        fontWeight: 'bold',
                        color: COLORS.txt288AD6
                    }}
                />
            </MyText>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                }}>
                <View
                    style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'row'
                    }}>
                    <MyText
                        text={` ${translate(
                            'inventory.checked_quantity'
                        )}: ${checkedQuantity}`}
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.txtF50537
                        }}
                    />
                </View>
                <MyText
                    text={translate('inventory.product_status')}
                    style={{
                        color: COLORS.txt333333
                    }}>
                    <MyText
                        text={productStatus}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                    />
                </MyText>
                <Icon
                    iconSet={'MaterialCommunityIcons'}
                    name={'arrow-right-bold-circle-outline'}
                    color={COLORS.ic147EFB}
                    size={25}
                    style={{ marginTop: 2 }}
                />
            </View>
        </TouchableOpacity>
    );
};

export default ItemSubgroupProduct;
