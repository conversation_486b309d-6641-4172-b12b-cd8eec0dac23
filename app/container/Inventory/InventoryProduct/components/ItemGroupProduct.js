import React, { useState } from 'react';
import { View, TouchableOpacity, FlatList } from 'react-native';
import { MyText, Icon } from '@components';
import { constants } from '@constants';
import { COLORS } from '@styles';
import ItemSubgroupProduct from './ItemSubgroupProduct';
import { translate } from '@translate';
import { helper } from '@common';

const ItemGroupProduct = ({
    groupProductItem,
    onPressSubGroupProduct,
    productStatus,
}) => {
    const { firstLayerData } = groupProductItem;
    const totalChecked = firstLayerData.reduce(
        (current, element) => current + element.checkedQuantity,
        0
    );
    const [isShow, setIsShow] = useState(false);
    const { subgroupname } = groupProductItem;

    return (
        <View
            style={{
                width: constants.width - 20,
                borderColor: COLORS.bd147EFB,
                borderWidth: 1,
                borderRadius: 4,
                marginTop: 10,
                justifyContent: 'center',
                backgroundColor: COLORS.bdFFFFFF,
                shadowColor: COLORS.sd000000,
                shadowOffset: {
                    width: 0,
                    height: 1
                },
                shadowOpacity: 0.4,
                shadowRadius: 3.84,
                elevation: 2,
                overflow: 'hidden'
            }}>
            <TouchableOpacity
                style={{
                    borderColor: COLORS.bd147EFB,
                    borderBottomWidth: isShow ? 1 : 0,
                    backgroundColor: COLORS.btnDDF4F1,
                    alignItems: 'center',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    paddingHorizontal: 8,
                    paddingVertical: 8
                }}
                onPress={() => {
                    setIsShow(!isShow);
                }}
                activeOpacity={0.6}
                disabled={totalChecked == 0}>
                <MyText
                    text={subgroupname}
                    style={{
                        fontWeight: 'bold',
                        color: COLORS.txt000000,
                        width: ((constants.width - 36) * 3) / 5
                    }}
                    addSize={2}
                    numberOfLines={2}
                />
                {totalChecked != 0 && (
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                        <MyText
                            text={`${translate(
                                'inventory.check_quantity'
                            )} ${helper.take_decimal_number(totalChecked, 4)}`}
                            style={{
                                paddingRight: 5,
                                color: COLORS.txtF50537,
                                fontWeight: 'bold'
                            }}
                        />
                        <Icon
                            iconSet={'Ionicons'}
                            name={isShow ? 'chevron-up' : 'chevron-down'}
                            size={22}
                            color={COLORS.txt000000}
                        />
                    </View>
                )}
            </TouchableOpacity>
            {isShow && (
                <FlatList
                    contentContainerStyle={{
                        paddingHorizontal: 10
                    }}
                    data={firstLayerData}
                    keyExtractor={(item, index) => `${index}`}
                    renderItem={({ item, index }) => (
                        <ItemSubgroupProduct
                            productData={item}
                            onPress={onPressSubGroupProduct}
                            productStatus={productStatus}
                        />
                    )}
                    removeClippedSubviews={false}
                    stickySectionHeadersEnabled={false}
                    alwaysBounceVertical={false}
                    bounces={false}
                    scrollEventThrottle={16}
                />
            )}
        </View>
    );
};

export default ItemGroupProduct;
