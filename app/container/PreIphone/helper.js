import { v4 as uuidv4 } from 'uuid';
import { BRAND_NAME, PAYMENT_TYPES } from './constants';
import { BANKS } from './Banks';
import { PROMOS } from './Promotions';
import dayjs from "dayjs";
import { convertNum } from '../../common/helper';
import { dateHelper } from '../../common';


export const parseToCartAndPayment = (data) => {
    const {
        selectedProduct,
        customerInfo: { name, phone, gender },
        money,
        userInfo: { userName, storeID, languageID, moduleID, storeName, storeAddress },
        promotions,
        SaleOrderID,
        paymentMethod,
        cvvCode,
        selectedBank,
        selectedPOS,
    } = data;

    const {
        PRODUCTID,
        PRODUCTNAME,
        MODEL,
        STORAGE,
        VAT,
        VATPERCENT,
        SALEPRICE,
        STANDARDSALEPRICE,
        COSTPRICE,
        RETAILPRICE,
        STANDARDPRICEAREASALEPRICE,
        SALEPRICEERP,
        PRICE,
        COLOR,
        IMAGEURL
    } = selectedProduct;

    const saleOrderDetailIdMain = uuidv4();

    const currentDate = dayjs().format("YYYY-MM-DD HH:mm:ss.SSS");

    const promotionList = promotions?.map((_item) => ({
        PromotionID: _item.PROMOTIONID, //CTKM
        ProductID: _item.PRODUCTID //SP quà trong CTKM
    }))

    const dataCart = {
        loginuser: userName,
        loginstore: storeID,
        LanguageID: languageID,
        ModuleID: moduleID,
        SaleScenarioTypeID: 1,
        data_object: {
            data: {
                SaleOrderIDTemp: SaleOrderID,
                CurrencyUnitID: 1,
                SaleOrderRelationShip: {
                    RelationShipType: 0
                },
                SaleOrderDetails: [
                    {
                        SaleOrderDetailID: saleOrderDetailIdMain,
                        ProductID: PRODUCTID,
                        ProductName: PRODUCTNAME,
                        Quantity: 1,
                        VAT: VAT,
                        VATPercent: VATPERCENT,
                        SalePrice: SALEPRICE,
                        OutputTypeID: 801,
                        SalePriceERP: SALEPRICEERP,
                        IsPromotionAutoAdd: false,
                        IsOutput: true,
                        RetailPrice: RETAILPRICE,
                        CostPrice: COSTPRICE,
                        InventoryStatusID: 1,
                        StandardSalePrice: STANDARDSALEPRICE,
                        StandardPriceAreaSalePrice: STANDARDPRICEAREASALEPRICE,
                        cus_TotalCost: PRICE,
                        OutputStoreID: storeID,
                        OrderTypeID: 45,
                        IsAdditionalPromotion: false,
                        DeliveryInfoRequest: {
                            DeliveryTypeID: 1,
                            ContactGender: gender,
                            DeliveryStoreID: storeID,
                            DeliveryStoreName: storeName,
                            DeliveryStoreAddress: storeAddress
                        }
                    }
                ],
                OriginateStoreID: storeID,
                SHDepositAmount: 0,
                SHChangeTranferAmountFee: 0,
                SHForwarderFeeAmount: 0,
                ShippingCost: 0,
                CartID: null,
                CreateDate: currentDate,
                CustomerInfo: {
                    CustomerName: name,
                    CustomerAddress: '',
                    CustomerPhone: phone,
                    AgeID: null,
                    Birthday: '',
                    TaxID: '',
                    Gender: gender,
                    ContactName: '',
                    ContactPhone: '',
                    DeliveryAddress: '',
                    ContactGender: gender,
                    CustomerEmail: null,
                    ContactEmail: null
                },
                TotalAdvance: 0,
                SHAmount: PRICE,
                SHCouponDiscountAmount: 0,
                Promotions: promotionList ?? []
            }
        }
    };
    const dataPayment = {
        loginuser: userName,
        loginstore: storeID,
        moduleID: moduleID,
        languageID: languageID,
        data_object: {
            data: {
                CheckIncome: true,
                SaleOrder: {
                    CashVND: paymentMethod?.type === PAYMENT_TYPES.CASH ? money : 0,
                    MoneyBank: paymentMethod?.type === PAYMENT_TYPES.BANK_TRANSFER ? money : 0,
                    MoneyCard: paymentMethod?.type === PAYMENT_TYPES.CARD ? money : 0,
                    OrderTypeID: 45,
                    TotalMoney: PRICE,
                    SaleOrderDetails: [
                        {
                            SaleOrderDetailID: saleOrderDetailIdMain,
                            ProductID: PRODUCTID,
                            ProductName: PRODUCTNAME,
                            Quantity: 1,
                            VAT: VAT,
                            VATPercent: VATPERCENT,
                            SalePrice: SALEPRICE,
                            OutputTypeID: 801,
                            SalePriceERP: SALEPRICEERP,
                            IsPromotionAutoAdd: false,
                            IsOutput: true,
                            RetailPrice: RETAILPRICE,
                            CostPrice: COSTPRICE,
                            InventoryStatusID: 1,
                            StandardSalePrice: STANDARDSALEPRICE,
                            StandardPriceAreaSalePrice: STANDARDPRICEAREASALEPRICE,
                            cus_TotalCost: PRICE,
                            OutputStoreID: storeID,
                            OrderTypeID: 45,
                            IsAdditionalPromotion: false,
                            DeliveryInfoRequest: {
                                DeliveryTypeID: 1,
                                ContactGender: gender,
                                DeliveryStoreID: storeID,
                                DeliveryStoreName: storeName,
                                DeliveryStoreAddress: storeAddress
                            }
                        }
                    ],
                    DeliveryTypeID: 1,
                    PayableAmount: PRICE,
                    Debt: PRICE,
                    TotalAmount: PRICE,
                    CustomerID: 5,
                    CurrencyUnitID: 1,
                    CurrencyExchange: 1,
                    AgeID: 5,
                    IsSetIncome: false,
                    StaffUser: userName,
                    CreatedByOtherApps: 11,
                    CreateDate: currentDate,
                    InVoucherDate: currentDate,
                    OutputStoreID: storeID,
                    AllowPayCash: true,
                    PayableTypeID: 121,
                    SaleOrderID: SaleOrderID,
                    CDC_Version: 0,
                    InputUser: userName,
                    TotalRemain: PRICE - money || 0,
                    VoucherTypeID: 1,
                    DebtOld: PRICE,
                    CustomerName: name,
                    CustomerPhone: phone,
                    OriginateStoreID: storeID,
                    CustomerAddress: "",
                    ApplyMoneyCardDetails: paymentMethod?.type === PAYMENT_TYPES.CARD && selectedPOS
                        ? [
                            {
                                moneyCardID: selectedPOS?.MoneyCardID,
                                MoneyCardVoucherID: cvvCode,
                                MoneyCard: money
                            }
                        ]
                        : [],
                    Money: money
                }
            }
        }

    }
    return { dataCart, dataPayment };
};


export const mergeBankData = (banks) => {
    let mergedBanks = BANKS
        .map(bank => {
            const match = banks.find(bd => bd.BANKID === bank.BANKID);
            if (match) {
                return {
                    ...bank,
                    QRCodeData: match.QRCodeData
                };
            }
            return null;
        })
        .filter(Boolean);

    const otherBank = banks.find(bd => !BANKS.some(b => b.BANKID === bd.BANKID));

    if (otherBank) {
        mergedBanks.push({
            BANKID: otherBank.BANKID,
            BANKNAME: otherBank.BANKNAME,
            IMAGEURL: otherBank.ICONFILEPATH,
            QRCodeData: otherBank.QRCodeData,
            isOtherBank: true
        });
    }
    return mergedBanks;
}

export const getPromotionsForProduct = (product, brandID) => {
    if (!product) return [];

    return PROMOS.filter(promo =>
        promo.BRANDID == brandID &&
        promo.APPLYPRODUCTID?.trim() == product.PRODUCTID?.trim()
    );
};

export const generateSOid = (storeId) => {
    const store = String(storeId).padStart(5, '0');
    const now = new Date();
    const yy = String(now.getFullYear()).slice(2);
    const mm = String(now.getMonth() + 1).padStart(2, '0');
    const timePart =
        String(now.getHours()).padStart(2, '0') +
        String(now.getMinutes()).padStart(2, '0') +
        String(now.getSeconds()).padStart(2, '0');
    const randomDigit = Math.floor(Math.random() * 10);
    const serial = `${timePart}${randomDigit}`;
    return `${store}SO${yy}${mm}${serial}`;
};



export const bindDataHtmlFromSaleOrder = (dataSaleOrder, promotions) => {
    const {
        CustomerName,
        CustomerPhone,
        CustomerAddress,
        DeliveryTypeID,
        SaleOrderDetails,
        TotalAmount,
        Money,
        TotalRemain,
        CreateDate
    } = dataSaleOrder;

    const isAtHome = "collapse";
    const deliveryStatus = "Chưa giao";
    const deliveryName = "Giao tại siêu thị";
    const isInCome = "collapse";

    const productList = [];

    SaleOrderDetails.forEach((item, index) => {
        // Push sản phẩm chính
        productList.push({
            "STT": `${index + 1}.`,
            "PRODUCTID": item.ProductID,
            "PRODUCTNAME": item.ProductName,
            "SALEPRICE": convertNum(item.cus_TotalCost),
            "QUANTITY": item.Quantity,
            "TOTALCOST": convertNum(item.cus_TotalCost * item.Quantity),
            "IMEI": item.IMEI || "",
            "HAVEIMEI": item.IMEI ? "" : "collapse",
            "INVENTORYSTATUS": "",
        });

        promotions?.forEach(promo => {
            productList.push({
                "STT": "+",
                "PRODUCTID": promo.PRODUCTID,
                "PRODUCTNAME": promo.PRODUCTNAME,
                "QUANTITY": 1,
                "SALEPRICE": 0,
                "TOTALCOST": 0,
                "IMEI": "",
                "HAVEIMEI": "collapse",
                "INVENTORYSTATUS": "",
            });
        });
    });

    return {
        "LOGO": "",
        "HOTLINE": "",
        "WARRANTYPHONE": "",
        "COMPANYNAME": "",
        "WARRANTYWEBSITE": "",
        "DELIVERYADDRESS": CustomerAddress || "",
        "DELIVERYSTATUS": deliveryStatus,
        "ISDELIVERYHOME": isAtHome,
        "DELIVERYTYPENAME": deliveryName,
        "DELIVERYTIME": dateHelper.formatStrDateFULL(CreateDate),
        "CUSTOMERNAME": CustomerName,
        "CUSTOMERPHONE": CustomerPhone,
        "ISINCOME": isInCome,
        "ISNOTINCOME": "",
        "PRODUCTLIST": productList,
        "TOTALAMOUNT": convertNum(TotalAmount, false),
        "TOTALPAID": convertNum(Money, false),
        "REMAIN": convertNum(TotalRemain, false),
        "SAID": dataSaleOrder.SaleOrderID
    };
};


export const getTemplatePre = (data, brandID, fullName, storeAddress) => {
    const brandName = BRAND_NAME[brandID]
    return `<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<style type="text/css">
    body { font-family: Arial, Helvetica, sans-serif; font-size: 24px; color: black; font-weight: normal; font-style: normal; text-decoration: none; word-wrap: break-word; }
    .cls_001 { font-family: Arial, Helvetica, sans-serif; font-size: 24px; color: black; font-weight: normal; font-style: normal; text-decoration: none; word-wrap: break-word; }
    .cls_002 { font-family: Arial, Helvetica, sans-serif; font-size: 22px; color: black; font-weight: normal; font-style: normal; text-decoration: none; word-wrap: break-word; }
    .cls_003 { font-family: Arial, Helvetica, sans-serif; font-size: 20px; color: black; font-weight: normal; font-style: normal; text-decoration: none; word-wrap: break-word; }
    .bold { font-weight: bold; }
    .text-center { text-align: center; }
    .text-left { text-align: left; }
    .text-right { text-align: right; }
    .text-vertical { vertical-align: middle; }
    .collapse { display: none; }
    hr { border: none; border-top: 1px dashed black; }
    table { border-collapse: collapse; width: 550px; }
</style>
</head>
<body style="margin: 0">
<div style="page-break-after: always">
    <table>
        <tbody>
            
            <tr>
                <td colspan="2" class="text-center" style="padding-top: 5px">
                    <span class="cls_002">${brandName}</span>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-center" style="padding-top: 5px">
                    <span class="cls_002">BIÊN NHẬN THU TIỀN</span>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-center" style="padding-top: 5px"><hr /></td>
            </tr>
            <tr>
                <td colspan="2" class="text-left" style="padding-top: 5px">
                    <span class="cls_001">Ngày in: ${data.DELIVERYTIME}</span>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-left" style="padding-top: 5px">
                    <span class="cls_001">Phiếu Y/C: ${data.SAID}</span>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-left" style="padding-top: 5px">
                    <span class="cls_001">Khách hàng: ${data.CUSTOMERNAME}</span>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-left" style="padding-top: 5px">
                    <span class="cls_001">Điện thoại: ${data.CUSTOMERPHONE}</span>
                </td>
            </tr>
             <tr>
                <td colspan="2" class="text-left" style="padding-top: 5px">
                    <span class="cls_001">Địa chỉ giao: ${storeAddress}</span>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-left" style="padding-top: 5px">
                    <span class="cls_001">Hình thức giao: ${data.DELIVERYTYPENAME}</span>
                </td>
            </tr>
            
        </tbody>
    </table>

    <table>
        <thead>
            <tr class="text-center">
                <th><span class="cls_001">Đơn giá</span></th>
                <th><span class="cls_001">Số lượng</span></th>
                <th class="text-right"><span class="cls_001">Thành tiền</span></th>
            </tr>
            <tr>
                <th colspan="3"><span><hr /></span></th>
            </tr>
        </thead>
        <tbody>
            ${data.PRODUCTLIST.map(p => `
            <tr>
                <td colspan="3" style="word-break: break-word">
                    <span class="cls_001">${p.PRODUCTNAME}</span>
                </td>
            </tr>
            <tr class="text-center">
                <td><span class="cls_001">${p.SALEPRICE}</span></td>
                <td><span class="cls_001">${p.QUANTITY}</span></td>
                <td class="text-right"><span class="cls_001">${p.TOTALCOST}</span></td>
            </tr>`).join('')}
            <tr>
                <td colspan="3"><span><hr /></span></td>
            </tr>
            <tr>
                <td colspan="3">
                    <table>
                        <tbody>
                            <tr>
                                <td style="padding-left: 80px"><span class="cls_002 text-left">Tổng tiền:</span></td>
                                <td style="text-align: right"><span class="cls_002 text-right">${data.TOTALAMOUNT}</span></td>
                            </tr>
                           
                           
                            <tr>
                                <td style="padding-left: 80px"><span class="cls_001 text-left">Đã thanh toán:</span></td>
                                <td class="text-right"><span class="cls_001">${data.TOTALPAID}</span></td>
                            </tr>
                          
                            <tr>
                                <td colspan="2"><span><hr /></span></td>
                            </tr>
                            <tr>
                                <td style="padding-left: 80px"><span class="cls_002 text-left">Phải thanh toán</span></td>
                                <td style="text-align: right"><span class="cls_002 text-right">${data.REMAIN}</span></td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>

            <tr>
                <td colspan="3" style="padding-top: 20px">
                    <table>
                        <tbody>
                            <tr class="text-center">
                                <td><span class="cls_003">Khách hàng</span></td>
                                <td><span class="cls_003">Thu ngân</span></td>
                            </tr>
                            <tr class="text-center">
                                <td><span class="cls_001" style="font-style: italic">(Ký, họ tên)</span></td>
                                <td><span class="cls_001" style="font-style: italic">(Ký, họ tên)</span></td>
                            </tr>
                            <tr class="text-center">
                                <td style="padding-top: 200px"></td>
                                <td><span class="cls_003">${fullName}</span></td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>

            <tr>
                <td>
                    <hr>
                    <span  class="cls_001">
                        Cảm ơn Quý Khách hàng đã đồng ý với chính sách xử lý dữ liệu cá nhân của Chúng tôi.
                    </span>
                    </br>
                </td>
            </tr>
        </tbody>
    </table>
</div>
</body>
</html>
`;

};


