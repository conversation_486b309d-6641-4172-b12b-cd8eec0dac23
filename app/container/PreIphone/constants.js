







export const OPTIONS_TITLES = {
    MODEL: "dòng đời",
    COLOR: "màu ",
    STORAGE: "dung lượng",
}
export const OPTIONS_TYPE = {
    MODEL: "MODEL",
    COLOR: "COLOR",
    STORAGE: "STORAGE",
}

export const GENDERS = [
    { label: "Anh", value: 1 },
    { label: "Chị", value: 0 },
]

export const PAYMENT_TYPES = {
    CASH: "cash",
    CARD: "card",
    BANK_TRANSFER: "bank_transfer"
}


export const PAYMENT_METHODS = [
    { label: "Tiền mặt", type: PAYMENT_TYPES.CASH },
    { label: "Cà thẻ", type: PAYMENT_TYPES.CARD },
    { label: "Chuyển khoản", type: PAYMENT_TYPES.BANK_TRANSFER }
];

export const KEY_LOCAL = {
    LAST_NUMBER: "last_number",
    ORDERS: "orders",

}

export const BRAND_NAME = {
    1: "Thế Giới Di Động",
    2: "Điện M<PERSON>",
    16: "TopZone",
}


export const ERROR_TYPE = {
    OUT_OF_SLOT: "202502",
    TIMEOUT: "202503",
    SYSTEM_ERROR: "202504",
    BY_PRODUCT: "202505",
    BY_PHONE: "202506",
}

export const STATUS_LOCK = {
    [ERROR_TYPE.TIMEOUT]: "TIMEOUT",
    [ERROR_TYPE.SYSTEM_ERROR]: "SYSTEMERROR",
    [ERROR_TYPE.OUT_OF_SLOT]: "OUTOFSLOT",
}
export const generateOptions = (products, selectedModel, selectedStorage) => {
    const getUnique = (items, key) =>
        [...new Set(items.map((item) => (item[key] || "").trim()).filter(v => v !== ""))];

    const models = getUnique(products, OPTIONS_TYPE.MODEL);

    const filteredByModel = selectedModel
        ? products.filter((item) => item.MODEL === selectedModel)
        : [];

    const storages = getUnique(filteredByModel, OPTIONS_TYPE.STORAGE);

    const filteredByModelAndStorage = selectedStorage
        ? filteredByModel.filter((item) => item.STORAGE === selectedStorage)
        : [];

    const colors = getUnique(filteredByModelAndStorage, OPTIONS_TYPE.COLOR);

    return {
        models,
        storages,
        colors,
    };
};



