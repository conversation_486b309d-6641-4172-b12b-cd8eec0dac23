import React, { useEffect, useState, useMemo, useRef } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    Alert,
    KeyboardAvoidingView,
    Platform,
    Keyboard
} from 'react-native';
import {
    ERROR_TYPE,
    generateOptions,
    KEY_LOCAL,
    OPTIONS_TITLES,
    OPTIONS_TYPE,
    PAYMENT_METHODS,
    PAYMENT_TYPES,
    STATUS_LOCK,
} from './constants';
import { Options, CustomerInfo, Payment, ProductCard, PrintInner } from './components';
import { helper } from '@common';
import { useDispatch, useSelector } from 'react-redux';
import { Icon, hideBlockUI, showBlockUI } from '@components';
import { PRODUCTS } from './Product';
import {
    checkSlotByPhone,
    checkSlotByProduct,
    getBankInfo,
    getTransactionTransfer,
    insertPreOrder,
    unLockSlot,
    lockSlot,
    updatePreOrder
} from './action';
import { translate } from '@translate';
import { parseToCartAndPayment, getPromotionsForProduct, generateSOid, bindDataHtmlFromSaleOrder, getTemplatePre } from './helper';
import AsyncStorage from '@react-native-community/async-storage';
import { PROMOS } from './Promotions';
import { CARD_LIST } from './Cards';
import { BANKS } from './Banks';
import { useNavigation } from '@react-navigation/native';
import { PrintSheet } from './Sheets'
import { getReportPrinterSocket } from '../SaleOrderPayment/action';
import { H_BILL } from '../../constants/constants';
import { convertHtml2Image, printSocket } from '../../common';
import { COLORS } from '@styles';

const PreIphone = () => {
    const [products, setProducts] = useState([]);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [customerInfo, setCustomerInfo] = useState({
        name: '',
        phone: '',
        gender: null,
        isAgreePolicy: 0
    });
    const [money, setMoney] = useState(1000000);
    const [paymentMethod, setPaymentMethod] = useState(PAYMENT_METHODS[0]);
    const [dataBank, setDataBank] = useState([])
    const [selectedBank, setSelectedBank] = useState(null);
    const [cvvCode, setCvvCode] = useState('');
    const [selectedPOS, setSelectedPOS] = useState(null);
    const [saleOrderID, setSaleOrderID] = useState('');
    const [promotions, setPromotions] = useState([])
    const [qrBank, setQRBank] = useState("")
    const [reportRetail, setReportRetail] = useState({});
    const [disabled, setDisabled] = useState(false)

    const isCheckedSlotByPhone = useRef(null);
    const isFocusPhone = useRef(false);
    const isFocusMoney = useRef(false);
    const inputPhoneNumberRef = useRef();
    const inputCustomerNameRef = useRef();
    const scrollViewRef = useRef(null);
    const paymentRef = useRef(null)
    const printSheet = useRef(null);

    const navigation = useNavigation();
    const dispatch = useDispatch();
    const userInfo = useSelector((state) => state.userReducer);
    const { defaultReport } = useSelector((state) => state.saleOrderPaymentReducer)
    const { storeID, storeName, storeAddress, userName, brandID, fullName } = userInfo;

    const options = useMemo(
        () =>
            generateOptions(
                products,
                selectedProduct?.MODEL,
                selectedProduct?.STORAGE
            ),
        [products, selectedProduct]
    );

    const handleSelect = async (type, value) => {
        let newModel = selectedProduct?.MODEL;
        let newStorage = selectedProduct?.STORAGE;
        let newColor = selectedProduct?.COLOR;

        if (type === OPTIONS_TYPE.MODEL) {
            newModel = value;
            const modelProducts = products.filter(
                (product) => product.MODEL === newModel
            );
            if (modelProducts.length > 0) {
                const fallback = modelProducts[0];
                newStorage = fallback.STORAGE;
                newColor = fallback.COLOR;
            }
        }
        if (type === OPTIONS_TYPE.STORAGE) {
            newStorage = value;
        }
        if (type === OPTIONS_TYPE.COLOR) {
            newColor = value;
        }
        const matched = products.find(
            (product) =>
                product.MODEL === newModel &&
                product.STORAGE === newStorage &&
                product.COLOR === newColor
        );

        if (matched?.PRODUCTID == selectedProduct?.PRODUCTID) return;

        if (matched) {
            const isAllow = await handleCheckSlotByProduct(matched);
            setSelectedProduct(prev =>
                isAllow
                    ? { ...matched }
                    : { ...matched, availableSlot: 0 }
            );
        } else {
            Alert.alert('', 'Sản phẩm hiện không còn hàng!');
        }
    };

    const handlePaymentMethodChange = (method) => {
        if (method.type === paymentMethod.type) return
        handleScroll()
        setPaymentMethod(method);
        setCvvCode('');
        setSelectedBank(null);
        setSelectedPOS(method.type === PAYMENT_TYPES.CARD ? CARD_LIST[0] : null);
        setMoney(1000000);
        setDataBank([])
    };

    const handleMoneyChange = (value) => {
        setMoney(value);
        if (
            paymentMethod.type === PAYMENT_TYPES.BANK_TRANSFER &&
            value != money
        ) {
            setSelectedBank(null);
            setDataBank([])
        }
    };
    const handleScroll = () => {
        if (paymentRef.current && scrollViewRef.current) {
            paymentRef.current.measureLayout(
                scrollViewRef.current.getInnerViewNode(),
                (x, y) => {
                    scrollViewRef.current.scrollTo({ y, animated: true });
                }
            );
        }
    }
    const handleCvvChange = (value) => {
        setCvvCode(value);
    };
    const handleChangeSelectedPOS = (value) => {
        setSelectedPOS(value);
    };
    const handleChangeSelectedBank = async (item) => {
        if (money < 1000000) {
            Alert.alert('', 'Vui lòng nhập số tiền ít nhất là 1.000.000đ!');
            return;
        }

        const getOtherBank = (banks) => {
            const bank = banks.find(
                b1 => !BANKS.some(b => b.BANKID == b1.BANKID)
            );
            return bank ? { ...bank, ISOTHER: true } : null;
        };


        const setBankInfoFromList = (banks, selected) => {
            const qrCode = banks.find(d => d.BANKID == selected.BANKID)?.QRCodeData || "";
            // const qrCode = banks.find(d => d.QRCodeData).QRCodeData || "";
            setQRBank(qrCode);
        };

        let selected = item;

        // Nếu đã có dataBank thì không cần gọi lại API
        if (helper.IsNonEmptyArray(dataBank)) {
            if (item.ISOTHER) {
                selected = getOtherBank(dataBank);
            }
            setSelectedBank(selected);
            setBankInfoFromList(dataBank, selected);
            handleScroll();
            return;
        }

        try {
            showBlockUI();
            const banks = await dispatch(
                getBankInfo({
                    saleOrderID: saleOrderID,
                    paymentAmount: money
                })
            );
            hideBlockUI();

            if (helper.IsNonEmptyArray(banks)) {
                if (item.ISOTHER) {
                    selected = getOtherBank(banks);
                }
                setDataBank(banks);
                setSelectedBank(selected);
                setBankInfoFromList(banks, selected);
            } else {
                setQRBank("");
            }
        } catch (error) {
            hideBlockUI();
            Alert.alert(
                translate('common.notification_uppercase'),
                error?.msgError ?? "lỗi",
                [
                    {
                        text: translate('saleExpress.retry'),
                        style: 'cancel',
                        onPress: () => handleChangeSelectedBank(item)
                    },
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: () => {
                            setQRBank("");
                            setSelectedBank(null);
                            setDataBank([]);
                        }
                    }
                ]
            );
        } finally {
            handleScroll();
        }
    };


    const handleSaveOrder = async () => {
        const orderPayload = {
            customerInfo: customerInfo,
            payment: {
                type: paymentMethod?.type,
                money,
                bank: selectedBank,
                card: {
                    ...selectedPOS,
                    appv: cvvCode
                }
            },
            product: {
                ...(selectedProduct ?? {})
            },
            promotion: [...promotions],
            isLock: false,
            saleOrderID: saleOrderID,
            createdAt: new Date().toISOString() // để biết thời gian tạo
        };
        try {
            const existingOrders = await AsyncStorage.getItem(KEY_LOCAL.ORDERS);
            let ordersArray = existingOrders ? JSON.parse(existingOrders) : [];
            ordersArray.push(orderPayload);
            await AsyncStorage.setItem(
                KEY_LOCAL.ORDERS,
                JSON.stringify(ordersArray)
            );
            console.log('Đã lưu order ');
        } catch (error) {
            console.log(' Lỗi khi lưu order:', error);
        }
    };

    const getAllOrders = async () => {
        try {
            const value = await AsyncStorage.getItem(KEY_LOCAL.ORDERS);
            return value ? JSON.parse(value) : [];
        } catch (error) {
            console.log('Lỗi khi đọc orders từ local:', error);
            return [];
        }
    };

    const handleResetData = () => {
        setCustomerInfo({
            name: '',
            phone: '',
            gender: null,
            isAgreePolicy: 0
        })
        setMoney(1000000)
        setPaymentMethod(PAYMENT_METHODS[0])
        setDataBank([])
        setSelectedBank(null)
        setCvvCode("")
        setSelectedPOS(null)
        handleGetDefaultData()
        setPromotions([])
    }

    const handleCompleted = async () => {
        if (!handleValidateInfo()) return;

        handleSaveOrder();

        const orderData = parseToCartAndPayment({
            userInfo,
            selectedProduct,
            money,
            customerInfo,
            paymentMethod,
            cvvCode,
            selectedBank,
            selectedPOS,
            SaleOrderID: saleOrderID,
            promotions,
        });

        showBlockUI();

        try {
            if (paymentMethod.type === PAYMENT_TYPES.BANK_TRANSFER) {
                await checkTransactionTransferPayment();
            }
            await handleInsertPreOrder();
            const statusLock = await handleLockSlot();
            if (statusLock !== STATUS_LOCK[ERROR_TYPE.TIMEOUT]) {
                handleUpdatePreOrder(statusLock);
            }
            Alert.alert(
                translate('common.notification_uppercase'),
                'Đơn hàng PreOrder đã được tạo thành công.',
                [
                    {
                        text: 'OK',
                        style: 'cancel',
                        onPress: () => {

                            getContentHtml(orderData.dataPayment);
                        },
                    },
                ]
            );
        } catch (err) {
            handleError(err);
        }
    };

    const resetBankData = () => {
        const orderID = generateSOid(storeID);
        setSaleOrderID(orderID);
        setDataBank([]);
        setSelectedBank(null);
        setQRBank("");
    };

    const showErrorAlert = (message, onOk = () => { hideBlockUI() }) => {
        Alert.alert(
            translate('common.notification_uppercase'),
            message,
            [{ text: 'OK', style: 'cancel', onPress: onOk }]
        );
    };

    const handleError = (err) => {

        let status = '';
        switch (`${err.errorType}`) {
            case ERROR_TYPE.SYSTEM_ERROR:
                status = STATUS_LOCK[ERROR_TYPE.SYSTEM_ERROR];
                handleUpdatePreOrder(status)
                setDisabled(true)
                return showErrorAlert(err.msgError);
            case ERROR_TYPE.BY_PHONE:
                status = STATUS_LOCK[ERROR_TYPE.OUT_OF_SLOT];
                resetBankData();
                handleUpdatePreOrder(status)
                inputPhoneNumberRef.current?.focus();
                return showErrorAlert(err.msgError);
            case ERROR_TYPE.BY_PRODUCT:
                status = STATUS_LOCK[ERROR_TYPE.OUT_OF_SLOT];
                resetBankData();
                handleUpdatePreOrder(status)
                return showErrorAlert(err.msgError);
            default:
                return Alert.alert(
                    translate('common.notification_uppercase'),
                    err?.msgError || 'Có lỗi xảy ra',
                    [
                        { text: translate('saleExpress.retry'), style: 'cancel', onPress: handleCompleted },
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: () => {
                                hideBlockUI()
                                handleUpdatePreOrder(STATUS_LOCK[ERROR_TYPE.SYSTEM_ERROR])
                                setDisabled(true)
                                // navigation.reset({ index: 0, routes: [{ name: 'Sale' }] });
                            }
                        }
                    ]
                );
        }
    };



    const getContentHtml = (data) => {
        try {
            const dataSaleOrder = data?.data_object?.data?.SaleOrder
            const bindData = bindDataHtmlFromSaleOrder(dataSaleOrder, promotions);
            const templateData = getTemplatePre(bindData, brandID, fullName, storeAddress);
            onConvertHTML(templateData);
        } catch (error) {
            console.log("🚀 ~ getContentHtml ~ error:", error)
        }

    }
    const onConvertHTML = async (html) => {
        const dataBit = await requestConvertHtml(html);
        if (helper.IsNonEmptyArray(dataBit)) {
            onPrintBillHTML(dataBit);
        }
        else {
            hideBlockUI();
        }
    }

    const requestConvertHtml = async (html) => {
        try {
            const dataBit = await convertHtml2Image(html, H_BILL);
            return dataBit;
        } catch (error) {
            console.log('convertHtml2Image', error);
        }
    }

    const onPrintBillHTML = (dataBit) => {
        printSocket(dataBit, reportRetail.IPPRINTER).then(result => {
            Alert.alert("", translate('saleOrderPayment.print_successfully'), [
                {
                    text: "OK",
                    style: "default",
                    onPress: onComplete
                }
            ]);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: onComplete
                }
            ]);
        })
    }

    const onComplete = () => {
        hideBlockUI();
        handleResetData()
    }

    const handleInsertPreOrder = () => {
        const orderData = parseToCartAndPayment({
            userInfo,
            selectedProduct,
            money,
            customerInfo,
            paymentMethod,
            cvvCode,
            selectedBank,
            selectedPOS,
            SaleOrderID: saleOrderID,
            promotions: promotions
        });

        const body = {
            loginStoreId: storeID,
            loginUser: userName,
            saleOrderId: saleOrderID,
            saleOrderData: orderData.dataCart,
            paymentData: orderData.dataPayment,
            status: 'done'
        };

        return insertPreOrder(body);
    };

    const handleUpdatePreOrder = (status) => {
        const body = {
            loginStoreId: storeID,
            loginUser: userName,
            saleOrderId: saleOrderID,
            status: status
        };

        return updatePreOrder(body);

    }

    const handleLockSlot = () => {
        const body = {
            phoneNumber: customerInfo.phone,
            storeId: storeID,
            userId: userName,
            productId: selectedProduct.PRODUCTID?.trim(),
            referenceOrderId: saleOrderID,
            source: 2
        };

        return lockSlot(body);
    };

    const checkTransactionTransferPayment = () => {
        return getTransactionTransfer(saleOrderID)

    };

    const handleCheckSlotByPhone = () => {
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidateCustomerPhone = regExpPhone.test(customerInfo.phone);
        if (helper.IsNonEmptyString(customerInfo.phone)) {
            if (!isValidateCustomerPhone) {
                Alert.alert('', 'Vui lòng nhập số điện thoại đúng 10 chữ số ');
                // inputPhoneNumberRef.current.focus();
                return false;
            }
            showBlockUI();
            const body = {
                phoneNumber: customerInfo.phone
            };
            checkSlotByPhone(body)
                .then(() => {
                    hideBlockUI();
                    isCheckedSlotByPhone.current = true;
                    inputCustomerNameRef.current.focus();
                })
                .catch(({ msgError, errorType }) => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        msgError,
                        [
                            {
                                text: translate('saleExpress.retry'),
                                style: 'cancel',
                                onPress: handleCheckSlotByPhone
                            },
                            {
                                text: translate('common.btn_skip'),
                                style: 'cancel',
                                onPress: () => {
                                    setCustomerInfo(pre => ({ ...pre, phone: "" }))
                                    isCheckedSlotByPhone.current = false;
                                    // inputPhoneNumberRef.current.focus();
                                    hideBlockUI();
                                }
                            }
                        ]
                    );
                });
        }



    };

    const handleCheckSlotByProduct = async (product) => {
        showBlockUI();
        const body = {
            "product": product.PRODUCTID?.trim()
        };
        try {
            const checkSlotProduct = await checkSlotByProduct(body);
            hideBlockUI();
            return checkSlotProduct;
        } catch (error) {
            const { msgError, errorType } = error
            if (errorType == ERROR_TYPE.BY_PRODUCT) {
                hideBlockUI()
                return false
            }
            Alert.alert(translate('common.notification_uppercase'), msgError || "Rất tiếc sản phẩm quý khách chọn đã hết suất, vui lòng chọn phiên bản khác.", [
                {
                    text: translate('saleExpress.retry'),
                    style: 'cancel',
                    onPress: () => handleCheckSlotByProduct(product)
                },
                {
                    text: "OK",
                    style: 'cancel',
                    onPress: hideBlockUI
                }
            ]);
            return false;
        }
    };

    const handleUnLockSlot = () => {
        showBlockUI();
        const body = {
            userId: userName,
            phoneNumber: customerInfo.phone,
            referenceOrderId: saleOrderID
        };
        unLockSlot(body)
            .then(() => { })
            .catch((msgError) => { });
    };


    const handleValidateInfo = () => {
        if (isFocusPhone.current) {
            Keyboard.dismiss();
            return false;
        }
        if (!selectedProduct) {
            Alert.alert('', 'Vui lòng chọn sản phẩm!');
            return false;
        }
        if (customerInfo.gender == null) {
            Alert.alert('', 'Vui lòng chọn giới tính khách hàng!');
            return false;
        }
        if (!customerInfo.phone) {
            Alert.alert('', 'Vui lòng nhập số điện thoại khách hàng!');
            inputPhoneNumberRef.current?.focus();
            return false;
        }
        if (!customerInfo.name) {
            Alert.alert('', 'Vui lòng nhập họ tên khách hàng!');
            inputCustomerNameRef.current?.focus();
            return false;
        }
        if (!isCheckedSlotByPhone.current) {
            const content =
                isCheckedSlotByPhone.current == null
                    ? 'chưa kiểm tra'
                    : 'đã hết';
            Alert.alert('', `Số điện thoại ${content} xuất!`);
            return false;
        }
        if (
            customerInfo.phone.length !== 10 ||
            !customerInfo.phone.startsWith('0')
        ) {
            Alert.alert(
                '',
                'Số điện thoại phải bắt đầu bằng 0 và có đúng 10 số!'
            );
            return false;
        }
        if (!customerInfo.isAgreePolicy) {
            Alert.alert('', 'Vui lòng đồng ý chính sách xử lý dữ liệu cá nhân!');
            return false;
        }
        if (!paymentMethod) {
            Alert.alert('', 'Vui lòng chọn phương thức thanh toán!');
            return;
        }
        if (money < 1000000) {
            Alert.alert('', 'Số tiền cần thanh toán ít nhất là 1.000.000đ!');
            return false;
        }
        if (
            paymentMethod.type === PAYMENT_TYPES.CARD &&
            (!selectedPOS || !cvvCode || cvvCode.length !== 6)
        ) {
            Alert.alert('', 'Vui lòng chọn máy POS và nhập mã APPV 6 số!');
            return false;
        }
        if (isFocusPhone.current && paymentMethod.type === PAYMENT_TYPES.BANK_TRANSFER) {
            Keyboard.dismiss();
            return false;
        }
        if (
            paymentMethod.type === PAYMENT_TYPES.BANK_TRANSFER &&
            !selectedBank
        ) {
            Alert.alert('', 'Vui lòng chọn ngân hàng!');
            return false;
        }
        if (helper.IsEmptyObject(reportRetail)) {
            Alert.alert(
                '',
                translate('saleOrderPayment.please_choose_printer'), [
                {
                    text: "OK",
                    style: 'cancel',
                    onPress: () => {
                        printSheet.current?.present()
                    }
                }
            ]
            );
            return false
        }
        return true;
    };

    const handleGetDefaultData = async () => {
        const orderID = generateSOid(storeID);
        setProducts(PRODUCTS);
        setSaleOrderID(orderID);
        const defaultProduct = PRODUCTS.find(
            (product) => product.MODEL && product.STORAGE && product.COLOR
        );
        if (defaultProduct) {
            const isAllow = await handleCheckSlotByProduct(defaultProduct);
            setSelectedProduct(prev =>
                isAllow
                    ? { ...defaultProduct }
                    : { ...defaultProduct, availableSlot: 0 }
            );



        }
    }

    const handleGetPromotion = () => {
        if (!helper.IsEmptyObject(selectedProduct)) {
            const promosForProduct = getPromotionsForProduct(selectedProduct, brandID);
            setPromotions(promosForProduct)
        }

    }

    const getReportPrinter = () => {
        dispatch(getReportPrinterSocket("45"));
    };

    const handleBlurPhone = () => {
        isFocusPhone.current = false;
        handleCheckSlotByPhone();
    };

    const handleFocusPhone = () => {
        isFocusPhone.current = true;
    };

    const handleBlurMoney = () => {
        isFocusMoney.current = false;
    };

    const handleFocusMoney = () => {
        isFocusMoney.current = true;
    };

    const hasCheckedSlotByPhone = () => {
        isCheckedSlotByPhone.current = null;
    };

    useEffect(() => {
        const run = async () => {
            handleGetDefaultData()
        };
        run();
    }, []);
    useEffect(hasCheckedSlotByPhone, [customerInfo.phone]);
    useEffect(handleGetPromotion, [selectedProduct]);
    useEffect(getReportPrinter, [])
    useEffect(() => {
        if (!helper.IsEmptyObject(defaultReport)) {
            setReportRetail(defaultReport.retail ?? {})
        }
    }, [defaultReport])


    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}
            style={[styles.container]}>
            <View style={styles.fixedProductCard}>
                <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", paddingBottom: 10 }}>
                    <Text selectable style={[styles.sectionTitle, { color: disabled ? COLORS.bg7F7F7F : null }]}>
                        Mã đặt trước: {saleOrderID?.slice(-7)}
                    </Text>
                    <TouchableOpacity
                        onPress={() => { printSheet.current?.present() }}>
                        <Icon
                            iconSet={'Ionicons'}
                            name={"print-outline"}
                            color={'#00aa77'}
                            size={25}
                        />
                    </TouchableOpacity>

                </View>


                {/* <Text style={styles.sectionTitle}>Chọn sản phẩm </Text> */}
                <ProductCard
                    selectedProduct={selectedProduct}
                    promotions={promotions}
                />
            </View>

            <ScrollView
                // pointerEvents={disabled ? 'none' : null}
                ref={scrollViewRef}
                style={styles.scrollView}
                contentContainerStyle={styles.scrollViewContent}>
                <View pointerEvents={disabled ? 'none' : null}>
                    <View style={styles.section}>
                        <Options
                            options={options.models}
                            label={OPTIONS_TITLES.MODEL}
                            selected={selectedProduct?.MODEL}
                            onSelect={(value) =>
                                handleSelect(OPTIONS_TYPE.MODEL, value)
                            }
                        />
                        <Options
                            options={options.storages}
                            label={OPTIONS_TITLES.STORAGE}
                            selected={selectedProduct?.STORAGE}
                            onSelect={(value) =>
                                handleSelect(OPTIONS_TYPE.STORAGE, value)
                            }
                        />
                        <Options
                            options={options.colors}
                            label={OPTIONS_TITLES.COLOR}
                            selected={selectedProduct?.COLOR}
                            onSelect={(value) =>
                                handleSelect(OPTIONS_TYPE.COLOR, value)
                            }
                        />
                    </View>
                    <CustomerInfo
                        customerInfo={customerInfo}
                        setCustomerInfo={setCustomerInfo}
                        handleCheckSlotByPhone={handleCheckSlotByPhone}
                        onBlur={handleBlurPhone}
                        onFocus={handleFocusPhone}
                        inputPhoneNumberRef={inputPhoneNumberRef}
                        inputCustomerNameRef={inputCustomerNameRef}
                    />
                    <Payment
                        paymentData={{
                            paymentMethod,
                            selectedBank,
                            selectedPOS,
                            cvvCode,
                            money,
                            qrBank,
                        }}
                        handlePaymentMethodChange={handlePaymentMethodChange}
                        handleMoneyChange={handleMoneyChange}
                        handleCvvChange={handleCvvChange}
                        handleChangeSelectedPOS={handleChangeSelectedPOS}
                        handleChangeSelectedBank={handleChangeSelectedBank}
                        onBlur={handleBlurMoney}
                        onFocus={handleFocusMoney}
                        paymentRef={paymentRef}

                    />
                    <View style={{ height: 80 }} />
                </View>

            </ScrollView>
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    disabled={selectedProduct?.availableSlot == 0}
                    style={[styles.continueButton, {
                        backgroundColor: selectedProduct?.availableSlot == 0 ? "#e8e5e5ff" : '#00aa77',
                    }]}
                    onPress={handleCompleted}>
                    <Text style={styles.continueButtonText}>Hoàn tất</Text>
                </TouchableOpacity>
            </View>
            <PrintSheet
                bottomSheetRef={printSheet}
                onChangeStatusSheet={() => { }}
                handleOnPress={() => {
                    printSheet.current?.dismiss()
                }}
            >
                <PrintInner
                    reportRetail={reportRetail}
                    setReportRetail={setReportRetail}
                    getReportPrinter={getReportPrinter}
                />
            </PrintSheet>
        </KeyboardAvoidingView>
    );
};

export default PreIphone;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    },
    fixedProductCard: {
        paddingTop: 10,
        paddingHorizontal: 10,
        borderBottomWidth: 2,
        borderColor: '#eee',
        backgroundColor: '#fff',
        zIndex: 1
    },
    productCard: {
        flexDirection: 'row'
    },
    productImage: {
        width: 80,
        height: 90,
        borderRadius: 8,
        backgroundColor: '#f5f5f5'
    },
    productInfo: {
        flex: 1,
        paddingLeft: 12,
        justifyContent: 'center'
    },
    PRODUCTNAME: {
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 4
    },
    productPrice: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#d60000',
        marginBottom: 6
    },
    promoTitle: {
        color: 'green',
        fontWeight: '600'
    },
    promoText: {
        color: 'green',
        fontSize: 13
    },
    scrollView: {
        flex: 1,
        marginTop: 15
    },
    scrollViewContent: {
        paddingBottom: 20
    },
    section: {
        marginBottom: 5,
        paddingHorizontal: 10,
        borderBottomWidth: StyleSheet.hairlineWidth
    },
    sectionTitle: {
        fontSize: 15,
        fontWeight: 'bold',
        color: '#00aa77',
    },
    buttonContainer: {
        paddingVertical: 16,
        alignItems: 'center'
    },
    continueButton: {
        backgroundColor: '#00aa77',
        padding: 12,
        borderRadius: 8,
        alignItems: 'center',
        width: '80%'
    },
    continueButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16
    },
    confirmButton: {
        backgroundColor: '#d60000',
        padding: 12,
        borderRadius: 8,
        alignItems: 'center',
        marginTop: 12
    },
    confirmButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16
    },
    errorText: {
        padding: 16,
        color: 'red',
        textAlign: 'center'
    },
    buttonContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        padding: 25,
        backgroundColor: '#fff',
        borderTopWidth: 1,
        borderTopColor: '#eee'
    },
    continueButton: {
        backgroundColor: '#00aa77',
        paddingVertical: 14,
        borderRadius: 8,
        alignItems: 'center'
    },
    continueButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600'
    }
});


