import { SafeAreaView, ScrollView, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { getReportPrinterSocket } from '../../SaleOrderPayment/action'
import PrintReport from '../../SaleOrderManager/component/PrintReport'
import { translate } from '@translate'
import Report from '../../SaleOrderManager/component/PrintReport/Report'
import { storageHelper } from '../../../common'
const PrintInner = ({ reportRetail, setReportRetail, getReportPrinter }) => {

    const { statePrinter, printerRetail } = useSelector((state) => state.saleOrderPaymentReducer)

    return (
        <ScrollView scrollEnabled={false} contentContainerStyle={{
            flexGrow: 1,
            backgroundColor: "white"
        }}>
            <SafeAreaView style={{
                flex: 1,

            }}>


                <PrintReport
                    title={translate(
                        'saleOrderPayment.choose_printer'
                    )}
                    statePrinter={statePrinter}
                    onTryAgains={getReportPrinter}
                    dataRetail={printerRetail}

                    renderItemRetail={({ item, index }) => (
                        <Report
                            key="ReportRetail"
                            info={item}
                            report={reportRetail}
                            onCheck={() => {
                                storageHelper.setDefaultPrinter(
                                    item.STOREPRINTERID,
                                    0
                                );
                                setReportRetail(item);
                            }}
                        />
                    )}

                />
            </SafeAreaView>
        </ScrollView>

    );
}

export default PrintInner

const styles = StyleSheet.create({})