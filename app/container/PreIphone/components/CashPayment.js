import React from 'react';
import { Keyboard, Platform, StyleSheet, Text, View } from 'react-native';
import { FieldNumberInput } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';

const CashPayment = ({ cashAmount, setCashAmount }) => (
    <View style={styles.inputGroup} >
        <Text style={styles.inputLabel}>Số tiền (đ) </Text>
        <FieldNumberInput
            keyboardType={
                Platform.OS == 'android'
                    ? 'numeric'
                    : 'numbers-and-punctuation'
            }
            styleInput={{
                backgroundColor: COLORS.bgFFFFFF,
                paddingHorizontal: 9,
                borderColor: '#ccc',
                borderWidth: 1,
                borderRadius: 5,
                fontSize: 14
            }}
            placeholder="0"
            value={String(cashAmount || 0)}
            returnKeyType="done"
            onChangeText={setCashAmount}
            onSubmitEditing={Keyboard.dismiss}
            clearText={() => setCashAmount(0)}
            width={constants.width - 20}
            height={38}
            isActiveNumbericIOS={true}

        />

    </View >
);

export default CashPayment;

const styles = StyleSheet.create({
    inputGroup: {
        // marginVertical: 12,

    },
    inputLabel: {
        fontSize: 13,
        fontWeight: '600',
        marginBottom: 4
    }
});
