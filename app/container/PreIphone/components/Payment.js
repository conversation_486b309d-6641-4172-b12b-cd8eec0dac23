import React from 'react';
import { Animated, StyleSheet, Text, View } from 'react-native';
import { PAYMENT_METHODS, PAYMENT_TYPES } from '../constants';
import PaymentMethodSelector from './PaymentMethodSelector';
import CashPayment from './CashPayment';
import CardPayment from './CardPayment';
import BankTransferPayment from './BankTransferPayment';

const Payment = ({ handlePaymentMethodChange, paymentData, handleMoneyChange, handleCvvChange,
    handleChangeSelectedPOS, handleChangeSelectedBank, paymentRef, ...rest
}) => {
    const {
        paymentMethod,
        selectedBank,
        selectedPOS,
        cvvCode,
        money,
        qrBank,
    } = paymentData;


    return (
        <View ref={paymentRef} style={styles.section}>
            <Text style={styles.sectionTitle}>Phương thức thanh toán</Text>

            <PaymentMethodSelector
                paymentMethod={paymentMethod}
                onPress={handlePaymentMethodChange}
                methods={PAYMENT_METHODS}
            />

            <Animated.View style={{ marginTop: 10 }}>
                {paymentMethod?.type === PAYMENT_TYPES.CASH && (
                    <CashPayment cashAmount={money} setCashAmount={handleMoneyChange} />
                )}
                {paymentMethod?.type === PAYMENT_TYPES.CARD && (
                    <CardPayment
                        selectedPOS={selectedPOS}
                        setSelectedPOS={handleChangeSelectedPOS}
                        cvvCode={cvvCode}
                        setCvvCode={handleCvvChange}
                        moneyCard={money}
                        setMoneyCard={handleMoneyChange}
                    />
                )}
                {paymentMethod?.type === PAYMENT_TYPES.BANK_TRANSFER && (
                    <BankTransferPayment
                        qrBank={qrBank}
                        selectedBank={selectedBank}
                        setSelectedBank={handleChangeSelectedBank}
                        moneyBankTransfer={money}
                        setMoneyBankTransfer={handleMoneyChange}
                        {...rest}
                    />
                )}
            </Animated.View>
        </View>
    );
};

export default Payment;

const styles = StyleSheet.create({
    section: {
        marginBottom: 5,
        paddingHorizontal: 10
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 12,
        color: '#00aa77'
    }
});
