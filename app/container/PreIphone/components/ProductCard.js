import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { Icon } from '@components';
import { helper } from '@common';

const ProductCard = ({ selectedProduct, promotions }) => {
    return (
        <>
            {selectedProduct ? (
                <View style={styles.productCard}>
                    <View style={{ width: "30%", alignItems: "center" }}>
                        <Image
                            source={{ uri: selectedProduct.IMAGEURL }}
                            style={styles.productImage}
                        />
                        <Text style={styles.productPrice}>
                            {helper.convertNum(selectedProduct.PRICE)}
                        </Text>

                    </View>
                    <View style={styles.productInfo}>
                        <Text style={styles.PRODUCTNAME}>
                            {selectedProduct.PRODUCTNAME}
                        </Text>
                        {
                            selectedProduct?.availableSlot == 0 && <Text style={{ color: "red" }}>(Sản phẩm bạn chọn đã hết suất. Vui lòng chọn sản phẩm khác!)</Text>

                        }
                        <>
                            {promotions?.length > 0 && (
                                <>
                                    <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                                        <Icon
                                            iconSet={'Ionicons'}
                                            name={'gift'}
                                            size={18}
                                            color={'#00aa77'}
                                        />
                                        <Text style={styles.promoTitle}>Khuyến mãi:</Text>
                                    </View>
                                    {
                                        promotions.map((promotion, index) => (
                                            <View key={index} style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                                                <Text> - </Text>
                                                <Text style={{ flex: 1, color: '#00aa77' }}>{promotion.PRODUCTNAME}</Text>
                                            </View>
                                        ))

                                    }
                                </>
                            )}
                        </>


                    </View>
                </View>
            ) : (
                <Text style={styles.errorText}>Vui lòng chọn sản phẩm...</Text>
            )}
        </>
    );
};

const styles = StyleSheet.create({
    productCard: {
        flexDirection: 'row',
    },
    productImage: {
        width: 60,
        height: 70,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
    },
    productInfo: {
        flex: 1,
        paddingLeft: 5,

    },
    PRODUCTNAME: {
        fontWeight: 'bold',
        fontSize: 14,
    },
    productPrice: {
        fontSize: 15,
        fontWeight: 'bold',
        color: '#d60000',
        marginVertical: 6,
    },
    promoTitle: {
        color: '#00aa77',
        fontWeight: '600',
    },
    promoText: {
        color: 'green',
        fontSize: 13,
    },
    errorText: {
        padding: 16,
        color: 'red',
        textAlign: 'center',
    },
});

export default ProductCard;
