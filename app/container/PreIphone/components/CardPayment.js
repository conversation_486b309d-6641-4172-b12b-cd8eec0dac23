import React from 'react';
import { Platform, StyleSheet, Text, TextInput, View } from 'react-native';
import { TouchableOpacity } from 'react-native';
import { FieldNumberInput } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { CARD_LIST } from '../Cards';

const CardPayment = ({
    selectedPOS,
    setSelectedPOS,
    cvvCode,
    setCvvCode,
    moneyCard,
    setMoneyCard
}) => {
    return (
        <View>
            <Text style={[styles.inputLabel]}>Số tiền (đ)</Text>
            <FieldNumberInput
                keyboardType={
                    Platform.OS == 'android'
                        ? 'numeric'
                        : 'numbers-and-punctuation'
                }
                styleInput={{
                    backgroundColor: COLORS.bgFFFFFF,
                    paddingHorizontal: 9,
                    borderColor: '#ccc',
                    borderWidth: 1,
                    borderRadius: 5,
                    fontSize: 14
                }}
                placeholder="0"
                value={String(moneyCard || 0)}
                returnKeyType="done"
                onChangeText={(value) => setMoneyCard(value)}
                clearText={() => setMoneyCard(0)}
                width={constants.width - 20}
                height={38}
                isActiveNumbericIOS={true}

            />
            <Text style={styles.label}>Chọn máy POS</Text>
            <View style={styles.optionGrid}>
                {CARD_LIST.map((pos) => (
                    <TouchableOpacity
                        key={pos.MoneyCardID}
                        style={[
                            styles.optionButton,
                            selectedPOS?.MoneyCardID === pos.MoneyCardID && styles.optionButtonActive
                        ]}
                        onPress={() => setSelectedPOS(pos)}>
                        <Text
                            style={[
                                styles.optionText,
                                selectedPOS?.MoneyCardID === pos.MoneyCardID && styles.optionTextActive
                            ]}>
                            {pos.MoneyCardName}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            {/* {selectedPOS && ( */}
            <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Mã APPV</Text>
                <TextInput
                    style={styles.input}
                    value={cvvCode}
                    onChangeText={setCvvCode}
                    keyboardType="numeric"
                    placeholder="Nhập mã APPV "
                    maxLength={6}
                    returnKeyType={"done"}

                />

            </View>
            {/* )} */}
        </View>
    );
};

export default CardPayment;

const styles = StyleSheet.create({
    label: {
        fontSize: 14,
        fontWeight: 'bold',
        paddingVertical: 10
    },
    optionGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap'
    },
    optionButton: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 6,
        marginRight: 8,
        marginBottom: 8,
        backgroundColor: '#fff',
        borderWidth: StyleSheet.hairlineWidth + 0.3,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 8,
        paddingVertical: 6,
        marginBottom: 8,
        marginRight: '3%', // khoảng cách giữa các cột
        backgroundColor: '#fff',
        width: '46%', // 3 cột mỗi hàng
        maxHeight: 60,
        justifyContent: 'center',
        minHeight: 40

    },
    optionButtonActive: {
        borderColor: '#00aa77',
        shadowColor: '#00aa77',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 3
    },
    optionText: {
        color: '#333',
        fontSize: 13,
        alignSelf: "center"
    },
    optionTextActive: {
        color: '#00aa77',
    },
    inputGroup: {
        marginVertical: 12
    },
    inputLabel: {
        fontSize: 13,
        fontWeight: '600',
        marginBottom: 4
    },
    input: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 10,
        fontSize: 14
    }
});
