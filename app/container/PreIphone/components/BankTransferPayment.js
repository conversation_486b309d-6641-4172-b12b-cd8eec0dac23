import React, { useMemo } from 'react';
import {
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    Image,
    Platform
} from 'react-native';
import { FieldNumberInput } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { BANKS } from '../Banks';

const BankTransferPayment = ({
    selectedBank,
    setSelectedBank,
    moneyBankTransfer,
    setMoneyBankTransfer,
    qrBank,
    ...rest
}) => {
    const DATA_BANK = useMemo(
        () => [
            ...BANKS,
            {
                BANKID: "OTHER",
                BANKNAME: 'OTHER BANK',
                IMAGEURL: '',
                ISOTHER: true
            }
        ],
        []
    );
    return (
        <View>
            <Text style={[styles.inputLabel]}>Số tiền (đ)</Text>
            <FieldNumberInput
                styleInput={{
                    backgroundColor: COLORS.bgFFFFFF,
                    paddingHorizontal: 9,
                    borderColor: '#ccc',
                    borderWidth: 1,
                    borderRadius: 5,
                    fontSize: 14
                }}
                placeholder="0"
                value={String(moneyBankTransfer || 0)}
                keyboardType={
                    Platform.OS == 'android'
                        ? 'numeric'
                        : 'numbers-and-punctuation'
                }
                returnKeyType="done"
                onChangeText={(value) => setMoneyBankTransfer(value)}
                clearText={() => setMoneyBankTransfer(0)}
                width={constants.width - 20}
                height={38}
                isActiveNumbericIOS={true}
                {...rest}
            />
            <Text style={styles.label}>Chọn ngân hàng</Text>

            <View style={styles.optionGrid}>
                {DATA_BANK.map((bank) => (
                    <TouchableOpacity
                        key={bank.BANKID}
                        style={[
                            styles.optionButton,
                            (selectedBank?.BANKID === bank.BANKID || (selectedBank?.ISOTHER && bank.ISOTHER)) &&
                            styles.optionButtonActive
                        ]}
                        onPress={() => setSelectedBank(bank)}>
                        {bank?.ISOTHER ? (
                            <Text style={{ textAlign: 'center' }}>
                                Ngân hàng khác
                            </Text>
                        ) : (
                            <Image
                                source={{ uri: bank.IMAGEURL }}
                                style={{ height: 30, width: '100%' }}
                                resizeMode="contain"
                            />
                        )}
                    </TouchableOpacity>
                ))}
            </View>
            {selectedBank && (
                <View style={styles.qrContainer}>
                    <View style={{ alignItems: 'center', paddingBottom: 10 }}>
                        {qrBank?.length > 0 ? (
                            <>
                                <Text style={styles.qrLabel}>
                                    Mã QR Thanh Toán
                                </Text>
                                <Image
                                    style={{
                                        width: 120,
                                        height: 120
                                    }}
                                    source={{
                                        uri: `data:image/png;base64,${qrBank}`
                                    }}
                                />
                            </>
                        ) : (
                            <Text
                                style={[
                                    styles.qrLabel,
                                    { color: 'red', fontSize: 13 }
                                ]}>
                                Không có thông tin chuyển khoản. Vui lòng chọn
                                ngân hàng khác.
                            </Text>
                        )}
                    </View>
                    <Text>
                        <Text
                            style={{
                                textDecorationLine: 'underline',
                                fontWeight: 'bold'
                            }}>
                            Lưu ý:{' '}
                        </Text>
                        Vui lòng{' '}
                        <Text style={{ fontWeight: 'bold' }}>
                            ưu tiên chọn ngân hàng chuyển khoản cùng ngân hàng
                            mà khách hàng đang sử dụng.
                        </Text>
                    </Text>

                    <Text>
                        Nếu khách hàng không có ngân hàng mà khách đang sử dụng,
                        <Text style={{ fontWeight: 'bold' }}>
                            {' '}
                            Chọn ngân hàng khác.
                        </Text>{' '}
                        để tiếp tục
                    </Text>
                </View>
            )}
        </View>
    );
};

export default BankTransferPayment;

const styles = StyleSheet.create({
    label: {
        fontSize: 14,
        fontWeight: 'bold',
        paddingVertical: 10
    },
    optionGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 10
    },
    optionButton: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 6,
        marginRight: 8,
        backgroundColor: '#fff',
        width: '29%'
    },
    optionButtonActive: {
        borderColor: '#00aa77',
        shadowColor: '#00aa77',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 3
    },
    optionText: {
        color: '#333',
        fontSize: 13
    },
    optionTextActive: {
        color: '#00aa77',
        fontWeight: '600'
    },
    qrContainer: {
        // alignItems: 'center',
        marginTop: 10
    },
    qrLabel: {
        fontSize: 16,
        fontWeight: 'bold',
        marginVertical: 10,
        color: '#00aa77'
    },
    inputLabel: {
        fontSize: 13,
        fontWeight: '600',
        marginBottom: 4
    }
});
