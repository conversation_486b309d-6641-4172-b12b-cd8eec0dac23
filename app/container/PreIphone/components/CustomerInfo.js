import { Keyboard, StyleSheet, Text, TextInput, View } from 'react-native';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { GENDERS } from '../constants';
import { Icon, MyText } from '@components';
import { helper } from '@common';
import { COLORS } from '@styles';

const CustomerInfo = ({ customerInfo, setCustomerInfo, inputPhoneNumberRef, inputCustomerNameRef, ...rest }) => {
    return (
        <View style={styles.section}>
            <Text style={styles.sectionTitle}>Thông tin khách hàng</Text>
            <View style={styles.optionGroup}>
                <Text style={styles.optionLabel}>Giới tính</Text>
                <View style={styles.optionGrid}>
                    {GENDERS.map((item, index) => (
                        <View style={{ flex: 1 / 3 }} key={index}>
                            <TouchableOpacity
                                key={item.value}
                                style={[styles.optionButton]}
                                onPress={() =>
                                    setCustomerInfo({
                                        ...customerInfo,
                                        gender: item.value
                                    })
                                }>
                                <Icon
                                    iconSet={'Ionicons'}
                                    name={
                                        customerInfo.gender === item.value
                                            ? 'radio-button-on-outline'
                                            : 'radio-button-off-outline'
                                    }
                                    size={20}
                                    color={
                                        customerInfo.gender === item.value
                                            ? '#00aa77'
                                            : null
                                    }
                                />

                                <Text
                                    style={[
                                        styles.optionText,
                                        customerInfo.gender === item.value &&
                                        styles.optionTextActive
                                    ]}>
                                    {item.label}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    ))}
                </View>
            </View>
            <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Số điện thoại</Text>
                <TextInput
                    ref={inputPhoneNumberRef}
                    style={styles.input}
                    value={customerInfo.phone}
                    onChangeText={(text) => {
                        const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                        const isValidate = regExpPhone.test(text) || (text == "");
                        if (isValidate) {
                            setCustomerInfo({ ...customerInfo, phone: text });

                        }

                    }}
                    maxLength={10}
                    onSubmitEditing={Keyboard.dismiss}
                    keyboardType={'numeric'}
                    returnKeyType={'done'}
                    placeholder="Nhập số điện thoại khách hàng"
                    {...rest}
                />
            </View>
            <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Họ và tên</Text>
                <TextInput
                    ref={inputCustomerNameRef}
                    style={styles.input}
                    value={customerInfo.name}
                    onChangeText={(text) => {
                        if (helper.isValidateCharVN(text)) {
                            setCustomerInfo({ ...customerInfo, name: text })
                        }
                    }
                    }
                    returnKeyType="done"
                    onSubmitEditing={Keyboard.dismiss}
                    placeholder="Nhập họ và tên khách hàng"
                />
            </View>
            <TouchableOpacity
                style={styles.touchable_policy}
                onPress={() => {
                    setCustomerInfo({ ...customerInfo, isAgreePolicy: customerInfo.isAgreePolicy ? 0 : 1 })

                }}>
                <Icon
                    iconSet="MaterialCommunityIcons"
                    name={
                        customerInfo.isAgreePolicy
                            ? 'checkbox-marked'
                            : 'checkbox-blank-outline'
                    }
                    size={22}
                    color={COLORS.bg2FB47C}
                />
                <MyText
                    addSize={0.5}
                    style={{ paddingLeft: 10, flex: 7 }}
                    text={
                        'Khách hàng đồng ý với chính sách xử lý dữ liệu cá nhân.'
                    }
                />
            </TouchableOpacity>
        </View>
    );
};

export default CustomerInfo;

const styles = StyleSheet.create({
    section: {
        marginBottom: 5,
        paddingHorizontal: 10,
        borderBottomWidth: StyleSheet.hairlineWidth
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 12,
        color: '#00aa77'
    },

    inputGroup: {
        marginBottom: 12
    },
    inputLabel: {
        fontSize: 13,
        fontWeight: '600',
        marginBottom: 4
    },
    input: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 10,
        fontSize: 14
    },
    optionGroup: {
        marginBottom: 10,
    },
    optionLabel: {
        fontWeight: "600",
        marginBottom: 8,
    },
    optionGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
    },
    optionButton: {
        flexDirection: "row",
        // paddingHorizontal: 12,
        marginRight: 8,
        backgroundColor: "#fff",
        alignItems: "center"
    },
    optionButtonActive: {
        color: "#00aa77",

    },
    optionText: {
        color: "#333",
        fontSize: 13,
        marginLeft: 5
    },
    optionTextActive: {
    },
    touchable_policy: {
        flexDirection: 'row',
        paddingTop: 10,
        alignItems: "center",
        flexWrap: 'wrap',
        paddingVertical: 5
    },
});
