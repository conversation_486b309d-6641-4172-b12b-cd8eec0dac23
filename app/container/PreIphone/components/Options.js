import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { TouchableOpacity } from 'react-native';

const Options = ({ label, options, selected, onSelect }) => {
    return (
        <View style={styles.optionGroup}>
            <Text style={styles.optionLabel}>Chọn {label}</Text>
            <View style={styles.optionGrid}>
                {options.map((item) => (
                    <TouchableOpacity
                        key={item}
                        style={[
                            styles.optionButton,
                            selected === item && styles.optionButtonActive
                        ]}
                        onPress={() => onSelect(item)}>
                        <Text
                            style={[
                                styles.optionText,
                                selected === item && styles.optionTextActive
                            ]}>
                            {item}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>
        </View>
    );
};

export default Options;

const styles = StyleSheet.create({
    optionGroup: {
        marginBottom: 6
    },
    optionLabel: {
        fontWeight: '600',
        marginBottom: 8
    },
    optionGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap'
    },
    optionButton: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 6,
        marginRight: 8,
        marginBottom: 8,
        backgroundColor: '#fff'
    },
    optionButtonActive: {
        borderColor: '#00aa77',
        // backgroundColor: "#eafff1",
        shadowColor: '#00aa77',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 3
    },
    optionText: {
        color: '#333',
        fontSize: 13
    },
    optionTextActive: {
        color: '#00aa77',
        // fontWeight: '600'
    },
    optionGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    optionButton: {
        borderWidth: StyleSheet.hairlineWidth + 0.3,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 8,
        paddingVertical: 6,
        marginBottom: 8,
        marginRight: '3%', // khoảng cách giữa các cột
        backgroundColor: '#fff',
        width: '30%', // 3 cột mỗi hàng
        maxHeight: 60,
        justifyContent: 'center',
        minHeight: 40
    },

    optionText: {
        color: '#333',
        fontSize: 12,
        textAlign: 'center', // canh giữa text theo chiều ngang
        flexWrap: 'wrap' // Cho phép text xuống hàng
    }

});

