import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Icon } from '@components';

const PaymentMethodSelector = ({ paymentMethod, onPress, methods }) => (
    <View style={styles.radioContainer}>
        {methods.map((method) => (
            <TouchableOpacity
                key={method.type}
                style={styles.radioItem}
                onPress={() => onPress(method)}>
                <Icon
                    iconSet="Ionicons"
                    name={
                        paymentMethod?.type === method.type
                            ? 'radio-button-on-outline'
                            : 'radio-button-off-outline'
                    }
                    size={20}
                    color={paymentMethod?.type === method.type ? '#00aa77' : null}
                />
                <Text style={styles.radioText}>{method.label}</Text>
            </TouchableOpacity>
        ))}
    </View>
);

export default PaymentMethodSelector;

const styles = StyleSheet.create({
    radioContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    radioItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 5
    },
    radioText: {
        fontSize: 14,
        marginLeft: 5
    }
});
