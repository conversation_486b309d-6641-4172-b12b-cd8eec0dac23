import { useMemo } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { COLORS } from '@styles';
import { MyText, BottomSheet, Icon } from '@components';
import { TouchableWithoutFeedback } from 'react-native-gesture-handler';


const PrintSheet = ({
    bottomSheetRef,
    onChangeStatusSheet,
    handleClose,
    children,
    title = "Chọn máy in",
    handleOnPress = () => { }
}) => {
    const snapPoints = useMemo(() => ['60%'], []);
    const handleComponent = () => (
        <View style={styles.handle}>
            <View style={{ flex: 1 }} />
            <View style={styles.handleTitleContainer}>
                <MyText
                    addSize={1}
                    style={styles.handleTitleText}
                    text={title}
                />
            </View>
            <View style={{ flex: 1 }}>
                <TouchableWithoutFeedback
                    style={styles.closeIconContainer}
                    onPress={handleClose || (() => bottomSheetRef.current?.dismiss())}
                >
                    <Icon
                        iconSet="MaterialIcons"
                        name="clear"
                        color={COLORS.txt000000}
                        size={22}
                    />
                </TouchableWithoutFeedback>
            </View>
        </View>
    );
    const footerComponent = () => {
        return (
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                    height: 100,
                    paddingBottom: 50,
                    padding: 10
                }}>
                <Pressable style={styles.button} onPress={handleOnPress}>
                    <MyText addSize={2} style={styles.text}>
                        {'TIẾP TỤC'}
                    </MyText>
                </Pressable>
            </View>
        );
    };
    return (
        <BottomSheet
            bs={bottomSheetRef}
            handleComponent={handleComponent}
            snapPoints={snapPoints}
            onChangeStatusSheet={onChangeStatusSheet}
            enableHandlePanningGesture={false}
            enableContentPanningGesture={false}
            disabledBackdrop
            footerComponent={footerComponent}
        >
            {children}
        </BottomSheet>
    );
};

// Styles
const styles = StyleSheet.create({
    button: {
        backgroundColor: "#00aa77",
        width: "50%",
        padding: 10,
        borderRadius: 10,
        alignItems: 'center'
    },
    handle: {
        backgroundColor: COLORS.bgFFFFFF,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 50,
        elevation: 2,
        borderTopStartRadius: 22,
        borderTopEndRadius: 22,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4,
    },
    handleTitleContainer: {
        flex: 6,
        justifyContent: 'center',
        alignItems: 'center',
    },
    handleTitleText: {
        fontWeight: 'bold',
        color: COLORS.txt000000,
    },
    closeIconContainer: {
        marginLeft: 10,
    },
    text: {
        color: COLORS.txtFFFFFF,
        fontWeight: '600'
    },
});

export default PrintSheet;
