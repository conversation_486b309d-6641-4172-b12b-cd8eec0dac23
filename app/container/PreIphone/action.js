import { API_CONST } from '@constants';
import { apiBase, METHOD } from '@config';
import { helper } from '../../common';
import { ERROR_TYPE, STATUS_LOCK } from './constants';

const {
    API_CHECK_SLOT_BY_PHONE,
    API_CHECK_SLOT_BY_PRODUCT,
    API_CHECK_UN_LOCK_SLOT,
    API_LOCK_SLOT,
    API_INSERT_PRE_ORDER,
    API_GET_BANK_PRE,
    API_PAYMENT_BANK_TRANSFER_PRE,
    API_UPDATE_PRE_ORDER
} = API_CONST;

export const checkSlotByPhone = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_SLOT_BY_PHONE, METHOD.POST, body)
            .then((response) => {
                console.log('checkSlotByPhone success', response);
                resolve(true);
            })
            .catch((error) => {
                console.log('checkSlotByPhone error', error);
                reject(error);
            });
    });
};
export const checkSlotByProduct = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_SLOT_BY_PRODUCT, METHOD.POST, body)
            .then((response) => {
                console.log('checkSlotByProduct success', response);
                resolve(true);
            })
            .catch((error) => {
                console.log('checkSlotByProduct error', error);
                reject(error);
            });
    });
};
export const lockSlot = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_LOCK_SLOT, METHOD.POST, body)
            .then((response) => {
                console.log('lockSlot success', response);
                resolve("LOCKED");
            })
            .catch((error) => {
                const { msgError, errorType } = error
                console.log('lockSlot error', error);
                if (errorType == ERROR_TYPE.TIMEOUT) resolve(STATUS_LOCK[ERROR_TYPE.TIMEOUT]);
                reject(error);
            });
    });
};
export const unLockSlot = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_UN_LOCK_SLOT, METHOD.POST, body)
            .then((response) => {
                console.log('unLockSlot success', response);
                resolve(true);
            })
            .catch((error) => {
                console.log('unLockSlot error', error);
                reject(error.msgError);
            });
    });
};

export const getTransactionTransfer = (VoucherConcern) => {
    return new Promise((resolve, reject) => {
        apiBase(API_PAYMENT_BANK_TRANSFER_PRE, METHOD.POST, {
            "voucherConcern": VoucherConcern,
            "voucherConcernType": 19
        })
            .then((response) => {
                const { object } = response;
                if (object?.length > 0) {
                    resolve(true);
                }
                else {
                    reject({ msgError: 'Báo có không có dữ liệu.' });
                }
            })
            .catch((error) => {
                console.log('🚀 ~ getTransactionTransfer ~ error:', error);
                reject(error);
            });
    });
};

export const insertPreOrder = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_INSERT_PRE_ORDER, METHOD.POST, body)
            .then((response) => {
                resolve(true);
            })
            .catch((error) => {
                console.log('🚀 ~ insertPreOrder ~ error:', error);
                reject(error);
            });
    });
};

export const updatePreOrder = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_UPDATE_PRE_ORDER, METHOD.POST, body)
            .then((response) => {
                resolve(true);
            })
            .catch((error) => {
                console.log('🚀 ~ updatePreOrder ~ error:', error);
                resolve(true);
            });
    });
};



export const getBankInfo = (data) => (dispatch, getState) =>
    new Promise((resolve, reject) => {
        apiBase(API_GET_BANK_PRE, METHOD.POST, {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            originateStoreID: getState().userReducer.storeID,
            createdUser: getState().userReducer.userName,
            paymentAmount: data.paymentAmount,
            voucherConcern: data.saleOrderID,
            voucherConcernType: 19,
            createdBy: 11
        })
            .then((response) => {
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                } else {
                    const error = {
                        msgError: 'Không lấy được danh sách ngân hàng.'
                    };
                    reject(error);
                }
            })
            .catch((error) => {
                console.log('getBankInfo error', error);
                reject(error);
            });
    });

const BANK_DATA = [
    {
        "COMPANYID": 1,
        "COMPANYNAME": "Công Ty Cổ Phần Thế Giới Di Động",
        "BANKACCOUNT": " 111 1111 1111 (hoặc MBMWGVN01Y767VU)",
        "BANKID": 4822,
        "BANKNAME": "NH TMCP Quân Đội",
        "BANKBRANCHNAME": "CN Chợ Lớn_TPHCM",
        "BANKSHORTBRANCHNAME": "NH TMCP Quân Đội - CN Chợ Lớn_TPHCM",
        "ISPAYMENTQRCODE": 1,
        "Syntax": "Y767VU",
        "QRCodeData": "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",
        "QRCodeDataRaw": "00020101021238590010A000000727012900069704220115MBMWGVN01Y767VU0208QRIBFTTA5303704540710000005802VN62100806Y767VU630415C8",
        "ICONFILEPATH": "https://cdn.tgdd.vn/erp/BeneficiaryBankLogos/MB.png",
        "ListAccount": "[{\"BankAccount\":\"***********\",\"AccountType\":\"PA\"},{\"BankAccount\":\"MBMWGVN01Y767VU\",\"AccountType\":\"VA\"}]",
        "BankAccountNumber": "***********"
    }
]