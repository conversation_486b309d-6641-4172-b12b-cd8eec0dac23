import { Platform } from 'react-native';
import RNPermissions, {
    check,
    checkMultiple,
    PERMISSIONS,
    request,
    requestMultiple,
    RESULTS
} from 'react-native-permissions';

const PLATFORM_PERMISSIONS = Platform.select({
    android: PERMISSIONS.ANDROID,
    ios: PERMISSIONS.IOS,
    default: {}
});

const PERMISSIONS_VALUES = Object.values(PLATFORM_PERMISSIONS);

const PERMISSIONS_CAMERA = Platform.select({
    android: [PERMISSIONS.ANDROID.CAMERA, PERMISSIONS.ANDROID.RECORD_AUDIO],
    ios: [PERMISSIONS.IOS.CAMERA, PERMISSIONS.IOS.MICROPHONE],
    default: {}
});

const PERMISSIONS_GALLERY = Platform.select({
    android: [PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE, PERMISSIONS.ANDROID.READ_MEDIA_IMAGES],
    ios: PERMISSIONS.IOS.PHOTO_LIBRARY
});

const PERMISSIONS_REQUEST_CAMERA = Platform.select({
    android: PERMISSIONS.ANDROID.CAMERA,
    ios: PERMISSIONS.IOS.CAMERA
});

const PERMISSIONS_REQUEST_MICRO = Platform.select({
    android: PERMISSIONS.ANDROID.RECORD_AUDIO,
    ios: PERMISSIONS.IOS.MICROPHONE
});

const PERMISSIONS_REQUEST_WRITE_STORAGE = Platform.select({
    android: PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
    ios: ''
});

export const checkAllPermission = () => {
    RNPermissions.checkMultiple(PERMISSIONS_VALUES)
        .then((statuses) => {
            console.log('statuses:', statuses);
        })
        .then(() => RNPermissions.checkNotifications())
        .then((notifications) => {
            console.log('notification:', notifications);
        })
        .catch((error) => console.warn(error));
};

export const checkPermissionCamera = () => {
    return new Promise(async (resolve) => {
        try {
            const statuses = await RNPermissions.checkMultiple(
                PERMISSIONS_CAMERA
            );
            const grantedCamera = Platform.select({
                android:
                    statuses[PERMISSIONS.ANDROID.CAMERA] === RESULTS.GRANTED,
                ios: statuses[PERMISSIONS.IOS.CAMERA] === RESULTS.GRANTED
            });
            const grantedMicrophone = Platform.select({
                android:
                    statuses[PERMISSIONS.ANDROID.RECORD_AUDIO] ===
                    RESULTS.GRANTED,
                ios: statuses[PERMISSIONS.IOS.MICROPHONE] === RESULTS.GRANTED
            });
            resolve({ camera: grantedCamera, microphone: grantedMicrophone });
        } catch (error) {
            console.log('grantedCamera', error);
            resolve({ camera: false, microphone: false });
        }
    });
};

export const checkAndRequestPermissionCamera = () => {
    return new Promise(async (resolve) => {
        try {
            const statuses = await RNPermissions.checkMultiple(
                PERMISSIONS_CAMERA
            );
            const statusCamera = Platform.select({
                android: statuses[PERMISSIONS.ANDROID.CAMERA],
                ios: statuses[PERMISSIONS.IOS.CAMERA]
            });
            const statusMicro = Platform.select({
                android: statuses[PERMISSIONS.ANDROID.RECORD_AUDIO],
                ios: statuses[PERMISSIONS.IOS.MICROPHONE]
            });
            console.log('status of camera:', statusCamera);
            console.log('status of micro:', statusMicro);
            if (
                statusCamera === RESULTS.DENIED ||
                statusMicro === RESULTS.DENIED
            ) {
                const resultRequestMultiple =
                    await RNPermissions.requestMultiple(PERMISSIONS_CAMERA);
                console.log('result requestMultiple:', resultRequestMultiple);
            }
            resolve({ camera: statusCamera, micro: statusMicro });
        } catch (error) {
            console.log('grantedCamera', error);
            resolve({ camera: 'error', micro: 'error' });
        }
    });
};

const handleResult = (result, resolve, reject) => {
    if (
        result === RESULTS.UNAVAILABLE ||
        result === RESULTS.BLOCKED ||
        result === RESULTS.LIMITED ||
        result === RESULTS.DENIED
    ) {
        reject();
    }
    if (result === RESULTS.GRANTED) {
        resolve();
    }
};

export const checkPermissionGallery = () => {
    return new Promise((resolve, reject) => {
        try {
            check(PERMISSIONS_GALLERY).then((result) => {
                if (result === RESULTS.LIMITED) {
                    resolve();
                    return;
                }
                if (result === RESULTS.DENIED) {
                    requestMultiple(PERMISSIONS_GALLERY)
                        .then((resultRequest) => {
                            handleResult(resultRequest, resolve, reject);
                        })
                        .catch(() => {
                            reject();
                        });
                    return;
                }
                handleResult(result, resolve, reject);
            });
        } catch (error) {
            reject();
        }
    });
};

export const checkPerCamera = () => {
    return new Promise(async (resolve, reject) => {
        try {
            check(PERMISSIONS_REQUEST_CAMERA).then(async (result) => {
                if (result === RESULTS.LIMITED) {
                    resolve();
                    return;
                }
                if (result === RESULTS.DENIED) {
                    request(PERMISSIONS_REQUEST_CAMERA)
                        .then((resultRequest) => {
                            handleResult(resultRequest, resolve, reject);
                        })
                        .catch(() => {
                            reject();
                        });
                    return;
                }
                handleResult(result, resolve, reject);
            });
        } catch (error) {
            reject();
        }
    });
};

export const checkPermissionRecord = () => {
    return new Promise((resolve, reject) => {
        try {
            check(PERMISSIONS_REQUEST_MICRO).then((result) => {
                if (result === RESULTS.LIMITED) {
                    resolve();
                    return;
                }
                if (result === RESULTS.DENIED) {
                    request(PERMISSIONS_REQUEST_MICRO)
                        .then((resultRequest) => {
                            handleResult(resultRequest, resolve, reject);
                        })
                        .catch(() => {
                            reject();
                        });
                    return;
                }
                handleResult(result, resolve, reject);
            });
        } catch (error) {
            reject();
        }
    });
};

export const checkPermissionWriteStorage = () => {
    return new Promise((resolve, reject) => {
        if (Platform.OS === 'ios') {
            resolve();
        } // Quyền chỉ yêu cầu trên android
        try {
            check(PERMISSIONS_REQUEST_WRITE_STORAGE).then((result) => {
                if (result === RESULTS.LIMITED) {
                    resolve();
                    return;
                }
                if (result === RESULTS.DENIED) {
                    request(PERMISSIONS_REQUEST_WRITE_STORAGE)
                        .then((resultRequest) => {
                            handleResult(resultRequest, resolve, reject);
                        })
                        .catch(() => {
                            reject();
                        });
                    return;
                }
                handleResult(result, resolve, reject);
            });
        } catch (error) {
            reject();
        }
    });
};

export const checkAndRequestPermissionDetectScreenshot = () => {
    return new Promise(async (resolve) => {
        if (Platform.OS === 'ios') {
            resolve();
        }
        try {
            const statuses = await RNPermissions.checkMultiple(PERMISSIONS_GALLERY);
            const statusStorage = statuses[PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE];
            const statusMediaImage = statuses[PERMISSIONS.ANDROID.READ_MEDIA_IMAGES];
            console.log('status of storage:', statusStorage);
            console.log('status of media image:', statusMediaImage);
            if (
                statusStorage === RESULTS.DENIED ||
                statusMediaImage === RESULTS.DENIED
            ) {
                const resultRequestMultiple =
                    await RNPermissions.requestMultiple(PERMISSIONS_GALLERY);
                console.log('result requestMultiple:', resultRequestMultiple);
            }
            resolve({ camera: statusStorage, micro: statusMediaImage });
        } catch (error) {
            console.log('grantedCamera', error);
            resolve({ camera: 'error', micro: 'error' });
        }
    });
};
