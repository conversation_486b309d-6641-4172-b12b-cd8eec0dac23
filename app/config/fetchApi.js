import RNF<PERSON><PERSON><PERSON>lob from 'rn-fetch-blob';
import CryptoJ<PERSON> from 'rn-crypto-js';
import { Alert } from 'react-native';
import { helper, storageHelper } from '@common';
import { STORAGE_CONST, DEVICE, ENUM } from '@constants';
import { translate } from '@translate';
import {
    HEADER_BASIC_PARAMS,
    ACCEPT,
    AUTHORIZATION,
    DEVICE_TOKEN,
    CONTENT_TYPE,
    CONTENT_TYPE_FORM,
    CONTENT_TYPE_JSON,
    CONTENT_TYPE_MULTIPART,
    METHOD,
    STATUS_CODE_INVAILD_TOKEN,
    STATUS_CODE_NETWORK,
    STATUS_CODE_SUCCESS_200,
    STATUS_CODE_SUCCESS_300,
    STATUS_CODE_SUCCESS_WITH_ABORT,
    STATUS_CODE_SUCCESS_WITH_ERROR,
    STATUS_CODE_TIMEOUT,
    STATUS_CODE_UNKNOW,
    TIME_OUT,
    STATUS_CODE_SEVER_ERROR
} from './constants';
import { appendHeader, getQueryString } from './utils';

const regExpIP = /error\.security\.useraddress\.invalidated/;
const regExpStore = /error\.security\.storeadress\.invalidated/;
const regExpDeSMS = /phone \[[\d]{10}\]/;
const regExpSMSExists = /oauth\.otp\.create\.error\.otp_exists/;
const regExpIDWrong = /auth\.idenfify\.error\.wrong_key/;
const regExpIPAddress = /\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\]/;


let _timeoutAPI = -1;
/**
 * Timeout function
 * @param {Integer} time (miliseconds)
 * @param {Promise} promise
 */
const timeout = (time, promise) => {
    return new Promise((resolve, reject) => {
        _timeoutAPI = setTimeout(() => {
            clearTimeout(_timeoutAPI);
            return reject({
                statusCode: STATUS_CODE_TIMEOUT,
                errorType: 0,
                msgError: translate("fetchAPI.error_time_out")
            });
        }, time);
        promise.then(resolve, reject);
    });
};

const handleError = async (error) => {
    if (helper.hasProperty(error, 'statusCode') && error.statusCode === STATUS_CODE_TIMEOUT) {
        return Promise.reject(error);
    }
    if (error.toString().includes('TypeError: Network request failed')) {
        return Promise.reject({
            statusCode: STATUS_CODE_NETWORK,
            errorType: 0,
            msgError: translate('fetchAPI.no_connected')
        });
    }
    return Promise.reject({
        statusCode: STATUS_CODE_UNKNOW,
        errorType: 0,
        msgError: translate('fetchAPI.error_connect')
    });
};

const checkStatus = (response) => {
    if (response.status >= STATUS_CODE_SUCCESS_200 && response.status < STATUS_CODE_SUCCESS_300) {
        return response;
    }
    const status = helper.hasProperty(response, 'status') ? response.status : -1;
    return response.json().then(json => {
        console.log("checkStatus", status, json);
        const errorType = helper.hasProperty(json, 'errorType') ? json.errorType : 0;
        let msgError = '';
        if (status >= STATUS_CODE_SEVER_ERROR) {
            return Promise.reject({
                statusCode: status,
                errorType: 0,
                msgError: translate("fetchAPI.error_server")
            });
        }
        if (helper.hasProperty(json, 'error_description')) {
            const { error_description, error } = json;
            msgError = error_description;
            if (error == 'invalid_grant') {
                if (regExpIPAddress.test(error_description)) {
                    return Promise.reject({
                        statusCode: status,
                        errorType: 0,
                        msgError: translate('fetchAPI.please_use_wifi')
                    });
                }
                else {
                    return Promise.reject({
                        statusCode: status,
                        errorType: 0,
                        msgError: translate('fetchAPI.urs_pwd_incorrect')
                    });
                }
            }
            if (error == 'invalid_token') {
                const regExpDeActive = /DEACTIVATED access token/;
                if (regExpDeActive.test(error_description)) {
                    msgError = translate('fetchAPI.login_at_another_device');
                }
                else {
                    msgError = translate('fetchAPI.token_expired');
                }
                Alert.alert("", msgError, [{
                    onPress: storageHelper.forcedRestartApp
                }]);
            }
        }
        if (helper.hasProperty(json, 'errorReason')) {
            const { errorReason } = json;
            msgError = errorReason || translate('fetchAPI.error_get_data');
        }
        if (helper.hasProperty(json, 'toastMessage')) {
            const { toastMessage } = json;
            if (regExpIP.test(toastMessage)) {
                return Promise.reject({
                    statusCode: status,
                    errorType: 0,
                    msgError: translate('fetchAPI.please_use_wifi')
                });
            }
            if (regExpStore.test(toastMessage)) {
                return Promise.reject({
                    statusCode: status,
                    errorType: 0,
                    // msgError: translate('fetchAPI.please_use_wifi_store')
                    msgError: translate('fetchAPI.please_use_wifi')
                });
            }
            if (regExpSMSExists.test(toastMessage)) {
                return Promise.reject({
                    statusCode: status,
                    errorType: 0,
                    msgError: "Your OTP is exists"
                });
            }
            if (regExpIDWrong.test(toastMessage)) {
                return Promise.reject({
                    statusCode: status,
                    errorType: 0,
                    msgError: "PRINCIPAL_VERIFICATION_INVALID"
                });
            }
            if (regExpDeSMS.test(toastMessage)) {
                return Promise.reject({
                    statusCode: status,
                    errorType: 0,
                    msgError: "Bạn đã gửi quá nhiều tin nhắn xác nhận trong ngày. Vui lòng chọn nhận mã OTP từ cuộc gọi."
                });
            }
        }
        return Promise.reject({
            statusCode: status,
            errorType: errorType,
            msgError: msgError
        });
    }).catch(error => {
        return Promise.reject(error);
    });
}

const parseJSON = (response) => {
    if (response.status === 204 || response.status === 205) {
        return null;
    }
    return response.json();
};

export const apiBase = (
    url,
    method,
    body,
    options = {
        setTimeOut: TIME_OUT,
        signal: null,
        isCustomToken: false,
        isOauthenToken: false,
        isUpload: false,
        customHeader: {}
    }
) => {
    /** Configure AbortController */
    const isSignal = !helper.IsEmptyObject(options.signal);
    if (!isSignal) {
        const controller = new AbortController();
        options.signal = controller.signal;
    }
    if (options.signal.aborted) {
        return Promise.reject({
            statusCode: STATUS_CODE_SUCCESS_WITH_ABORT,
            errorType: 0,
            msgError: 'AbortError'
        });
    }
    else {
        options.signal.addEventListener('abort', () => {
            return Promise.reject({
                statusCode: STATUS_CODE_SUCCESS_WITH_ABORT,
                errorType: 0,
                msgError: 'AbortError'
            });
        });
    }
    return new Promise((resolve, reject) => {
        storageHelper.multiGet([STORAGE_CONST.ACCESS_TOKEN, STORAGE_CONST.DEVICE_TOKEN]).then(result => {
            const [token, device_token] = result;
            /** Configure header */
            let headers = new Headers();
            if (options.isCustomToken) {
                if (options.isOauthenToken) {
                    appendHeader(HEADER_BASIC_PARAMS, headers);
                } else if (options.isUpload) {
                    if (helper.IsNonEmptyString(token)) {
                        const deviceToken = CryptoJS.MD5(
                            `${DEVICE.uniqueId}:${device_token}`
                        ).toString();
                        const authen = `Bearer ${token}`;
                        headers.append(AUTHORIZATION, authen);
                        headers.append(DEVICE_TOKEN, deviceToken);
                    }
                    const params = {
                        [ACCEPT]: CONTENT_TYPE_JSON,
                        [CONTENT_TYPE]: CONTENT_TYPE_MULTIPART
                    };
                    appendHeader(params, headers);
                } else {
                    headers = options.customHeader;
                }
            } else {
                if (helper.IsNonEmptyString(token)) {
                    const deviceToken = CryptoJS.MD5(
                        `${DEVICE.uniqueId}:${device_token}`
                    ).toString();
                    const authen = `Bearer ${token}`;
                    headers.append(AUTHORIZATION, authen);
                    headers.append(DEVICE_TOKEN, deviceToken);
                }
                const params = {
                    [ACCEPT]: CONTENT_TYPE_JSON,
                    [CONTENT_TYPE]: CONTENT_TYPE_JSON
                };
                appendHeader(params, headers);
            }
            if (helper.checkConfigIPArea()) {
                headers.append("validarea", true);
            }
            /** Configure Body */
            if (!options.isUpload) {
                switch (method) {
                    case METHOD.GET:
                        // append params into url
                        if (helper.IsValidateObject(options.params)) {
                            url += getQueryString(options.params);
                        }
                        break;
                    case METHOD.POST:
                        if (
                            helper.IsValidateObject(headers) &&
                            helper.IsValidateObject(headers.map[CONTENT_TYPE])
                        ) {
                            if (headers.map[CONTENT_TYPE] === CONTENT_TYPE_FORM) {
                                body = getQueryString(body);
                            } else if (headers.map[CONTENT_TYPE] === CONTENT_TYPE_JSON) {
                                // add json object into body
                                body = JSON.stringify(body);
                            }
                        }
                        break;
                    default:
                        break;
                }
            }

            /** Configure timeOut */
            if (!options.setTimeOut) {
                options.setTimeOut = TIME_OUT;
            }
            if (global.isBHX) {
                url = url.replace("mwg-app-erp-airtime-service", "mwg-app-erp-airtime-bhx-service");
                headers.append("idarea", global?.loginStoreID);
            }
            if (helper.checkConfigIPArea(global?.defaultStoreID)) {
                headers.append("validarea", true);
            }
            console.log("API_REQUEST", { headers, url });
            console.log("TIMEOUT", options.setTimeOut);
            console.log("body", body);

            timeout(
                options.setTimeOut,
                fetch(url, {
                    signal: options.signal,
                    method,
                    headers,
                    body // <-- Post parameters
                })
            ).catch(handleError) // handle network issues
                .then(checkStatus)
                .then(parseJSON)
                .then((json) => {
                    clearTimeout(_timeoutAPI);
                    console.log('API_RESPONSE SUCCESS: ', json);
                    if (helper.hasProperty(json, 'error')) {
                        const { error } = json;
                        if (error) {
                            const errorType = helper.hasProperty(json, 'errorType') ? json.errorType : 0;
                            const errorReason = helper.hasProperty(json, 'errorReason') ? json.errorReason : '';
                            reject({
                                statusCode: STATUS_CODE_SUCCESS_WITH_ERROR,
                                errorType: errorType,
                                msgError: errorReason
                            });
                        }
                    }
                    resolve(json);
                })
                .catch((error) => {
                    clearTimeout(_timeoutAPI);
                    console.log('API_RESPONSE ERROR: ', error);
                    reject(error);
                });
        }).catch(error => {
            reject({
                statusCode: STATUS_CODE_INVAILD_TOKEN,
                errorType: 0,
                msgError: translate('fetchAPI.cannot_token')
            });
        })
    });
};

/*  */
const parseJSONFW = (response) => {
    try {
        if (response.status === 204 || response.status === 205) {
            return null;
        }
        return response.json();
    } catch (error) {
        return response.data;
    }
};

export const apiBaseFW = (
    url,
    method,
    body,
    options = {
        setTimeOut: TIME_OUT,
        isCustomHeader: false,
        isUpload: false,
        customHeader: {}
    }
) => {
    return new Promise((resolve, reject) => {
        /** Configure header */
        let headers = {
            [ACCEPT]: CONTENT_TYPE_JSON,
            [CONTENT_TYPE]: CONTENT_TYPE_JSON
        };
        if (options.isCustomHeader) {
            if (options.isUpload) {
                headers = {
                    [ACCEPT]: CONTENT_TYPE_JSON,
                    [CONTENT_TYPE]: CONTENT_TYPE_MULTIPART
                };
            } else {
                headers = options.customHeader;
            }
        }

        /** Configure Body */
        if (!options.isUpload) {
            body = JSON.stringify(body);
        }

        /** Configure timeOut */
        if (!options.setTimeOut) {
            options.setTimeOut = TIME_OUT;
        }

        console.log("API_REQUEST", { headers, url });
        console.log("TIMEOUT", options.setTimeOut);
        console.log("body", body);

        timeout(
            options.setTimeOut,
            RNFetchBlob.config({ trusty: true }).fetch(
                method,
                url,
                headers,
                body
            )
        ).then(parseJSONFW)
            .then((json) => {
                clearTimeout(_timeoutAPI);
                console.log('API_RESPONSE SUCCESS: ', json);
                if (helper.hasProperty(json, 'message')) {
                    const { message: { error, logs } } = json;
                    if (error) {
                        const errorReason = logs || translate('fetchAPI.please_use_wifi');
                        reject({
                            statusCode: STATUS_CODE_SUCCESS_WITH_ERROR,
                            errorType: 0,
                            msgError: errorReason
                        });
                    }
                }
                resolve(json);
            })
            .catch((error) => {
                clearTimeout(_timeoutAPI);
                console.log('API_RESPONSE ERROR: ', error);
                if (helper.hasProperty(error, "msgError")) {
                    reject(error)
                }
                else {
                    reject({
                        statusCode: STATUS_CODE_NETWORK,
                        errorType: 0,
                        msgError: error.toString()
                    });
                }
            });
    });
};
