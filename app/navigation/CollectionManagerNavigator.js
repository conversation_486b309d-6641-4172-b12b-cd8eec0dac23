import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
const CollectionManagerStack = createStackNavigator();
import { translate } from '@translate';
import HistorySell from '../container/CollectionTransferManager/Screen/HistorySell'
import SawDetail from '../container/CollectionTransferManager/Screen/SawDetail';
import SaleOrderPayment from '../container/SaleOrderPayment';
import QRPayment from '../container/SaleOrderPayment/QRPayment';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import DeleteSO from '../container/SaleOrderManager/DeleteSO';
import CancelSO from '../container/SaleOrderManager/CancelSO';
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import SearchCollection from '../container/CollectionTransferManager';
import CancelService from '../container/CollectionTransferManager/Screen/CancelService'
import RefundHistory from '../container/CollectionTransferManager/Screen/RefundHistory';
import CustomerExpense from "../container/BankAirtimeService/Screen/CustomerExpense";
import QrCodePay from "../container/BankAirtimeService/Screen/QrCodePay";
import CreateSaleOrder from "../container/BankAirtimeService/Screen/CreateSaleOrder";
import BankAirtimeService from '../container/BankAirtimeService';
import StepOne from "../container/BankAirtimeService/Screen/BankStep/StepOne";
import StepTwoDeposit from '../container/BankAirtimeService/Screen/BankStep/StepTwoDeposit';
import StepThreeDeposit from '../container/BankAirtimeService/Screen/BankStep/StepThreeDeposit';
import * as actionBankAirtimeServiceCreator from "../container/BankAirtimeService/action";
import OTPConsumerAirtimeService from "../container/InsuranceAirtimeService/Screen/OTPAirtimeService/index";
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PrintCoupon from '../container/CollectInstallmentPayments/PrintCoupon';
import ScanQrCodeVoucher from '../container/CollectInstallmentPayments/ScanQrCodeVoucher';
import PackageTelevision from '../container/PackageTelevision';

const CollectionManagerNavigator = ({
    updateHeaderAirtime,
    actionBankAirtimeService
}) => {

    const getTitleAirtimeList = () => {
        const { AirTimeTransactionTypeName } = updateHeaderAirtime ?? "";
        const conertTransactionTypeName = AirTimeTransactionTypeName?.toUpperCase();
        return conertTransactionTypeName;
    };

    return (
        <CollectionManagerStack.Navigator
            initialRouteName={"SearchCollection"}
            headerMode={"screen"}
        >
            <CollectionManagerStack.Screen
                name={"SearchCollection"}
                component={SearchCollection}
                options={{
                    header: ({ navigation }) => (<MenuHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={translate("collection.managerment_multicat_industry_transactions")}
                        key={"HistorySell"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"HistorySell"}
                component={HistorySell}
                options={{
                    header: ({ navigation }) => (<MenuHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={translate("collection.managerment_multicat_industry_transactions")}
                        key={"HistorySell"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"SawDetail"}
                component={SawDetail}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate("collection.deposit_money_into_your_account")}
                        key={"CollectionOne"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name="SaleOrderPayment"
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (
                        <OrderPaymentHeader
                            navigation={navigation}
                            key="OrderPaymentHeader"
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name="QRPayment"
                component={QRPayment}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"DeleteSO"}
                component={DeleteSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"CancelSO"}
                component={CancelSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"CancelSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"CancelService"}
                component={CancelService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={'TẠO YÊU CẦU HOÀN TIỀN'}
                        key={"CollectionOne"}
                    />),
                    gestureEnabled: false
                }}
            />

            <CollectionManagerStack.Screen
                name={"RefundHistory"}
                component={RefundHistory}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={'XEM LỊCH SỬ HOÀN TIỀN'}
                        key={"RefundHistory"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"CustomerExpense"}
                component={CustomerExpense}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"QrCodePay"}
                component={QrCodePay}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"CreateSaleOrder"}
                component={CreateSaleOrder}
                options={{
                    headerShown: false,
                    gestureEnabled: false,
                }}
            />
            <CollectionManagerStack.Screen
                name={"BankAirtimeService"}
                component={BankAirtimeService}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={'VPB DỊCH VỤ NẠP TIỀN VÀO TÀI KHOẢN NGÂN HÀNG'}
                            key={"CollectionAirtimeService"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <CollectionManagerStack.Screen
                name={"StepOne"}
                component={StepOne}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => {
                                actionBankAirtimeService.clear_data_customer();
                                navigation.goBack()
                            }}
                            title={getTitleAirtimeList()}
                            key={"StepOne"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <CollectionManagerStack.Screen
                name={"StepTwoDeposit"}
                component={StepTwoDeposit}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleAirtimeList()}
                            key={"StepTwo"}
                            navigation={StepTwoDeposit}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <CollectionManagerStack.Screen
                name={"StepThreeDeposit"}
                component={StepThreeDeposit}
                options={{
                    headerShown: false,
                    gestureEnabled: false,
                }}
            />
            <CollectionManagerStack.Screen
                name={"OTPAirtimeService"}
                component={OTPConsumerAirtimeService}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"PrintCoupon"}
                component={PrintCoupon}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"PrintCoupon"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionManagerStack.Screen
                name={"ScanQrCodeVoucher"}
                component={ScanQrCodeVoucher}
                options={{
                    headerShown: false,
                    gestureEnabled: false,
                }}
            />
            <CollectionManagerStack.Screen
                name="PackageTelevision"
                component={PackageTelevision}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => {
                                navigation.goBack()
                            }}
                            title="GÓI CƯỚC TRUYỀN HÌNH"
                            key={"PackageTelevision"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
        </CollectionManagerStack.Navigator>
    );
}

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'SearchCollection' }]
    });
};

const mapStateToProps = function (state) {
    return {
        updateHeaderAirtime: state.insuranceAirtimeServiceReducer.updateHeaderAirtime,
        itemCatalog: state.collectionReducer.itemCatalog,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
    };
};

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(CollectionManagerNavigator);


const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}
