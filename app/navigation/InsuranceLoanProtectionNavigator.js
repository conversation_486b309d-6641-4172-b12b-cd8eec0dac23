import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { StyleSheet } from 'react-native';
import { BackHeader } from "@header";
const InsuranceLoanProtectionStack = createStackNavigator();
import SaleOrderPayment from '../container/SaleOrderPayment';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import DeleteSO from '../container/SaleOrderManager/DeleteSO';
import CancelSO from '../container/SaleOrderManager/CancelSO';
import IninsuranceLoanProtection from '../container/InsuranceLoanProtection';
import SaleOrderInsuraneLoanProtection from '../container/InsuranceLoanProtection/screen/SaleOrderInsuraneLoanProtection';
import InsuranceInformation from '../container/InsuranceLoanProtection/screen/InsuranceInformation';
import OTPAirtimeService from '../container/InsuranceLoanProtection/OTPAirtimeService';
import HistorySellInsuranceLoan from '../container/InsuranceLoanProtection/screen/HistorySellInsuranceLoan';

const InsuranceLoanProtectionNavigator = () => {
    return (
        <InsuranceLoanProtectionStack.Navigator
            initialRouteName={"Insurance"}
            headerMode={"screen"}
        >
            <InsuranceLoanProtectionStack.Screen
                name={"IninsuranceLoanProtection"}
                component={IninsuranceLoanProtection}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"BẢO HIỂM KHOẢN VAY"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceLoanProtectionStack.Screen
                name={"SaleOrderInsuraneLoanProtection"}
                component={SaleOrderInsuraneLoanProtection}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ - BẢO HIỂM KHOẢN VAY PVI"}
                        key={"InsuranceExtendedWarranty"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceLoanProtectionStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceLoanProtectionStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceLoanProtectionStack.Screen
                name={"DeleteSO"}
                component={DeleteSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceLoanProtectionStack.Screen
                name={"CancelSO"}
                component={CancelSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"CancelSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceLoanProtectionStack.Screen
                name={"InsuranceInformation"}
                component={InsuranceInformation}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ - BẢO HIỂM KHOẢN VAY PVI"}
                        key={"InsuranceExtendedWarranty"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceLoanProtectionStack.Screen
                name={"HistorySellInsuranceLoan"}
                component={HistorySellInsuranceLoan}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"XEM LỊCH SỬ BÁN"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            < InsuranceLoanProtectionStack.Screen
                name={"OTPAirtimeService"}
                component={OTPAirtimeService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"THU HỘ - BẢO HIỂM KHOẢN VAY PVI"}
                        key={"TopupDataService"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
        </InsuranceLoanProtectionStack.Navigator>
    );
}

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'IninsuranceLoanProtection' }]
    });
};


export default InsuranceLoanProtectionNavigator

const styles = StyleSheet.create({})
