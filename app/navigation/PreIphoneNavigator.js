import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { MenuHeader, BackHeader } from '@header';
import PreIphone from '../container/PreIphone'

const { Navigator, Screen } = createStackNavigator();

const PreIphoneNavigator = () => {
    return (
        <Navigator initialRouteName="PreIphone" headerMode="screen">
            <Screen
                name="PreIphone"
                component={PreIphone}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => navigation.goBack()}
                            title="Pre 2025"
                            key="PreIphonelHeader"
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
        </Navigator>
    );
};

export default PreIphoneNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
};
