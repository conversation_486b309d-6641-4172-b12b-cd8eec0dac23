
import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
import PackageTelevision from '../container/PackageTelevision';
import SawDetail from '../container/CollectionTransferManager/Screen/SawDetail';
import SaleOrderPayment from '../container/SaleOrderPayment';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import DeleteSO from '../container/SaleOrderManager/DeleteSO';
import CancelSO from '../container/SaleOrderManager/CancelSO';
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import SearchCollection from '../container/CollectionTransferManager';
import CancelService from '../container/CollectionTransferManager/Screen/CancelService';
import SaleOrderPackage from '../container/PackageTelevision/Screen/SaleOrderPackage';
import HistorySell from '../container/CollectionTransferManager/Screen/HistorySell';
import { translate } from '@translate';

const PackageTelevisionStack = createStackNavigator();

const PackageTelevisionNavigator = () => {
    return (
        <PackageTelevisionStack.Navigator initialRouteName="CustomPackageTelevisionStackerConsultant">
            <PackageTelevisionStack.Screen
                name="PackageTelevision"
                component={PackageTelevision}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => {
                                navigation.goBack()
                            }}
                            title="GÓI CƯỚC TRUYỀN HÌNH"
                            key={"PackageTelevision"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <PackageTelevisionStack.Screen
                name="SaleOrderPackage"
                component={SaleOrderPackage}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => {
                                navigation.goBack()
                            }}
                            title="GÓI CƯỚC TRUYỀN HÌNH"
                            key={"SaleOrderPackage"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <PackageTelevisionStack.Screen
                name={"SearchCollection"}
                component={SearchCollection}
                options={{
                    header: ({ navigation }) => (<MenuHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={translate("collection.managerment_multicat_industry_transactions")}
                        key={"SearchCollection"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <PackageTelevisionStack.Screen
                name={"SawDetail"}
                component={SawDetail}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate("collection.deposit_money_into_your_account")}
                        key={"CollectionOne"}
                    />),
                    gestureEnabled: false
                }}
            />
            <PackageTelevisionStack.Screen
                name="SaleOrderPayment"
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (
                        <OrderPaymentHeader
                            navigation={navigation}
                            key="OrderPaymentHeader"
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <PackageTelevisionStack.Screen
                name={"DeleteSO"}
                component={DeleteSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <PackageTelevisionStack.Screen
                name={"CancelSO"}
                component={CancelSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"CancelSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <PackageTelevisionStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <PackageTelevisionStack.Screen
                name={"CancelService"}
                component={CancelService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={'TẠO YÊU CẦU HOÀN TIỀN'}
                        key={"CollectionOne"}
                    />),
                    gestureEnabled: false
                }}
            />
            <PackageTelevisionStack.Screen
                name={"HistorySell"}
                component={HistorySell}
                options={{
                    header: ({ navigation }) => (<MenuHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={translate("collection.managerment_multicat_industry_transactions")}
                        key={"HistorySell"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
        </PackageTelevisionStack.Navigator>
    );
};

export default PackageTelevisionNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
};
