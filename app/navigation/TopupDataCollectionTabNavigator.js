import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard } from 'react-native';
import { BackHeader } from "@header";
const TopupDataCollectionTabStack = createStackNavigator();
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import SaleOrderPayment from '../container/SaleOrderPayment';
import TopupDataCollectionTab from '../container/TopupDataService';
import HistorySellTopupData from '../container/TopupDataService/Screen/HistorySellTopupData';
import OTPAirtimeService from '../container/TopupDataService/OTPAirtimeService';
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";

const TopupDataCollectionTabNavigator = () => {
    return (
        <TopupDataCollectionTabStack.Navigator
            initialRouteName={"TopupDataCollectionTabService"}
            headerMode={"screen"}
        >
            < TopupDataCollectionTabStack.Screen
                name={"OTPAirtimeService"}
                component={OTPAirtimeService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"ĐĂNG KÝ GÓI DATA"}
                        key={"TopupDataService"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <TopupDataCollectionTabStack.Screen
                name={"TopupDataCollectionTab"}
                component={TopupDataCollectionTab}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"ĐĂNG KÝ GÓI DATA"}
                        key={"TopupDataCollectionTab"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <TopupDataCollectionTabStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <TopupDataCollectionTabStack.Screen
                name={"HistorySellTopupData"}
                component={HistorySellTopupData}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"XEM LỊCH SỬ BÁN"}
                        key={"HistorySellTopupData"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <TopupDataCollectionTabStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
        </TopupDataCollectionTabStack.Navigator>
    );
}

export default TopupDataCollectionTabNavigator

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'TopupDataCollectionTab' }]
    });
};

