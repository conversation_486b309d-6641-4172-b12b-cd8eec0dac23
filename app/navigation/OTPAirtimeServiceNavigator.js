import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
const OTPAirtimeServiceStack = createStackNavigator();
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import SaleOrderPayment from '../container/SaleOrderPayment';
import OTPAirtimeService from '../container/InsuranceLoanProtection/OTPAirtimeService';

const OTPAirtimeServiceNavigator = () => {
    return (
        < OTPAirtimeServiceStack.Navigator
            initialRouteName={"OTPAirtimeService"}
            headerMode={"screen"}
        >
            < OTPAirtimeServiceStack.Screen
                name={"OTPAirtimeService"}
                component={OTPAirtimeService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"ĐĂNG KÝ GÓI DATA"}
                        key={"TopupDataService"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            < OTPAirtimeServiceStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
        </ OTPAirtimeServiceStack.Navigator>
    );
}

export default OTPAirtimeServiceNavigator

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'TopupDataCollectionTab' }]
    });
};