import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
const CollectionStack = createStackNavigator();
import { translate } from '@translate';
import PrintSaleOrderDDay from '../container/PrintSaleOrderDDay';

const PrintSaleOrderDDayStack = createStackNavigator();

const PrintSaleOrderDDayNavigator = () => {
    return (
        <PrintSaleOrderDDayStack.Navigator
            initialRouteName={'PrintSaleOrderDDay'}>
            <PrintSaleOrderDDayStack.Screen
                name={'PrintSaleOrderDDay'}
                component={PrintSaleOrderDDay}
                options={{
                    header: ({ navigation }) => (
                        <MenuHeader
                            openDrawer={onOpenDrawer(navigation)}
                            title={"IN ĐƠN HÀNG NGÀY DDAY"}
                            key={'PrintSaleOrderDDay'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
        </PrintSaleOrderDDayStack.Navigator>

    )
}


export default PrintSaleOrderDDayNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
};
