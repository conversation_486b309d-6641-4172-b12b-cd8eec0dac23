import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import SaleOrderManager from '../container/SaleOrderManager';
import SaleOrderPayment from '../container/SaleOrderPayment';
import QRPayment from '../container/SaleOrderPayment/QRPayment';
import SmartCardPayment from '../container/SaleOrderPayment/SmartCardPayment';
import DeleteSO from '../container/SaleOrderManager/DeleteSO';
import CancelSO from '../container/SaleOrderManager/CancelSO';
import EditSaleOrder from '../container/EditSaleOrder';
import LockSaleOrder from '../container/EditSaleOrder/index_lock';
import EditInstallment from '../container/EditSaleOrder/component/InstallmentCart';
import CartDeliveryInfo from '../container/EditSaleOrder/component/CartDeliveryInfo';
import SearchProduct from '../container/EditSaleOrder/component/SearchProduct';
import AddCartProduct from '../container/EditSaleOrder/component/AddCartProduct';
import AddCartPromotion from '../container/EditSaleOrder/component/AddCartPromotion';
import AddCartKeepPromotion from '../container/EditSaleOrder/component/AddCartPromotion/index_keep';
import OrderCartAdjustPrice from '../container/EditSaleOrder/component/AdjustPriceCart';
import OrderCreatePriceCart from '../container/EditSaleOrder/component/CreatePriceCart';
import OrderLoyalty from '../container/EditSaleOrder/component/Loyalty';
import OrderMemberPoint from '../container/EditSaleOrder/component/MemberPoint';
import OrderInstallmentOTP from '../container/EditSaleOrder/component/InstallmentOTP';
import OrderStudentInfo from '../container/ShoppingCart/component/StudentInfo';
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import PreOrderProduct from '../container/SaleOrderManager/PreOrderProduct';
import PreOrderCart from '../container/SaleOrderManager/PreOrderCart';
import PreOrderSimProcess from '../container/SaleOrderManager/PreOrderSimProcess';
import LogInfoSO from '../container/SaleOrderManager/LogInfoSO';
import { MenuHeader, BackHeader, NoneHeader, Header } from "@header";
import OrderPaymentHeader from "../container/SaleOrderPayment/Header";
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import BackCartHeader from "../container/EditSaleOrder/Header/BackCartHeader";
import { translate } from "@translate";
import AnKhangPharmacy from '../container/AnKhangPharmacy';
import LockImage19AndCoupon from '../container/EditSaleOrder/component/LockImage19AndCoupon';
import PackagingBag from '../container/PackagingBag';
import BuyMedicineRegularly from '../container/AnKhangNew/screens/BuyMedicineRegularly'
import { SCREENS } from '../container/AnKhangNew/constants';
import FormPromotion from '../container/ShoppingCart/component/FormPromotion';
import FormCoupon from '../container/ShoppingCart/component/FormPromotion/FormCoupon';

const OrderManagerStack = createStackNavigator();

const OrderManagerStackNavigator = () => {
    return (
        <OrderManagerStack.Navigator
            initialRouteName={"OrderManagement"}
            headerMode={"screen"}
        >
            <OrderManagerStack.Screen
                name={"OrderManagement"}
                component={SaleOrderManager}
                options={{
                    header: ({ navigation }) => (<MenuHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={translate('header.order_manager_uppercase')}
                        key={"OrderManagerHeader"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"QRPayment"}
                component={QRPayment}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"SmartCardPayment"}
                component={SmartCardPayment}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"DeleteSO"}
                component={DeleteSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"CancelSO"}
                component={CancelSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"CancelSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"LogInfoSO"}
                component={LogInfoSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"LogInfoHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"PreOrderProduct"}
                component={PreOrderProduct}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReProductHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"PreOrderCart"}
                component={PreOrderCart}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"PreOrderCartHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"PreOrderSimProcess"}
                component={PreOrderSimProcess}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"PreOrderSimHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"EditSaleOrder"}
                component={EditSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"EditSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"LockSaleOrder"}
                component={LockSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"LockSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"EditInstallment"}
                component={EditInstallment}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"EditInstallmentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"CartDeliveryInfo"}
                component={CartDeliveryInfo}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"EditDeliveryInfoHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"SearchProduct"}
                component={SearchProduct}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"EditSearchHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"AddCartProduct"}
                component={AddCartProduct}
                options={{
                    header: ({ navigation }) => (<BackCartHeader
                        navigation={navigation}
                        key={"AddProductHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"AddCartPromotion"}
                component={AddCartPromotion}
                options={{
                    header: ({ navigation }) => (<BackCartHeader
                        navigation={navigation}
                        key={"AddPromotionHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"AddCartKeepPromotion"}
                component={AddCartKeepPromotion}
                options={{
                    header: ({ navigation }) => (<BackCartHeader
                        navigation={navigation}
                        key={"AddKeepPromotionHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"OrderCartAdjustPrice"}
                component={OrderCartAdjustPrice}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.adjust_price_uppercase')
                        }
                        key={"AdjustPriceSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"OrderCreatePriceCart"}
                component={OrderCreatePriceCart}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.create_price_uppercase')
                        }
                        key={"CreatePriceSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"OrderLoyalty"}
                component={OrderLoyalty}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.order_member_point')
                        }
                        key={"OrderLoyaltyHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"OrderMemberPoint"}
                component={OrderMemberPoint}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.order_member_point')}
                        key={"OrderMemberPointHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"OrderInstallmentOTP"}
                component={OrderInstallmentOTP}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.order_installment_OTP')}
                        key={"OrderMemberPointHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"OrderStudentInfo"}
                component={OrderStudentInfo}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.add_image_uppercase')}
                        key={"OrderStudentInfoHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"SearchAnKhang"}
                component={AnKhangPharmacy}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"EditSearchHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"OrderLockImage19AndCoupon"}
                component={LockImage19AndCoupon}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"HÌNH ẢNH ĐÍNH KÈM"}
                        key={"LockImage19AndCouponHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"PackagingBag"}
                component={PackagingBag}
                options={({ route, navigation }) => ({
                    header: () => (
                        <Header
                            navigation={navigation}
                            key="PackagingBag"
                            title={`${translate(
                                'saleOrderManager.export_request'
                            )} ${route.params.saleOrderID}`}
                        />
                    ),
                    gestureEnabled: false
                })}
            />
            <OrderManagerStack.Screen
                name={SCREENS.BuyMedicineRegularlyScreenName}
                component={BuyMedicineRegularly}
                options={({ route, navigation }) => ({
                    header: () => (
                        <BackHeader
                            onGoBack={() => {
                                navigation.navigate("OrderManagement")
                            }}
                            title={"ĐẶT LỊCH ĐỊNH KỲ"}
                            key={SCREENS.BuyMedicineRegularlyScreenName}
                        />
                    ),
                    gestureEnabled: false
                })}
            />
            <OrderManagerStack.Screen
                name={"FormPromotion"}
                component={FormPromotion}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.add_image_uppercase')}
                        key={"FormPromotion"}
                    />),
                    gestureEnabled: false
                }}
            />
            <OrderManagerStack.Screen
                name={"FormCoupon"}
                component={FormCoupon}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THÔNG TIN ĐÍNH KÈM"}
                        key={"FormCoupon"}
                    />),
                    gestureEnabled: false
                }}
            />
        </OrderManagerStack.Navigator>
    );
};

export default OrderManagerStackNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}