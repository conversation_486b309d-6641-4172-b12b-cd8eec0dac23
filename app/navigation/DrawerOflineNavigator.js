import React, { useEffect } from 'react';
import { Alert } from 'react-native';
import { constants } from "@constants";
import { createDrawerNavigator } from '@react-navigation/drawer';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import messaging from '@react-native-firebase/messaging';
import { NotifService } from "@components";
import { subscribeNotify, storageHelper, helper } from '@common';
import { DEVICE, STORAGE_CONST } from '@constants';
import MenuScreen from "../container/Menu/index_offline";
import SaleNavigator from './SaleOfflineNavigator';
import ReceiptManagerNavigator from "./OrderOfflineManagerNavigator";
import NotifyStackNavigator from './NotifyNavigator';
import * as pouchDBActionCreator from '../container/PouchDB/action';

const widthMenu = constants.width * 0.88;
const Drawer = createDrawerNavigator();
const DrawerNavigator = ({ pouchDBAction, userInfo, navigation }) => {
    /* Handler Notification */
    const onRegister = (token) => {
        console.log("onRegister", token);
    }
    const onNotification = (notification) => {
        console.log("onNotification", notification);
        const { data } = notification;
        if (helper.IsNonEmptyString(data?.custom_notification)) {
            const { title, body } = JSON.parse(data?.custom_notification);
            Alert.alert(title, body);
        }
    }
    const _notify = new NotifService(onRegister, onNotification);

    const getTokenSubscribe = async () => {
        try {
            const subToken = await storageHelper.getItem(STORAGE_CONST.NOTIFY_SUBTOKEN);
            if (!helper.IsNonEmptyString(subToken)) {
                const tokenFCM = await storageHelper.getItem(STORAGE_CONST.NOTIFY_TOKEN_FCM);
                const result = await subscribeNotify({
                    "userToken": userInfo.userName,
                    "session": DEVICE.uniqueId,
                    "token": tokenFCM,
                    "device": DEVICE.deviceNotify
                });
                console.log("getTokenSubscribe success", result);
            }
            else {
                console.log("getTokenSubscribe exits", subToken);
            }
        } catch (error) {
            console.log("getTokenSubscribe error", error);
        }
    }

    /* didMount */
    const didMount = () => {
        pouchDBAction.initPouchDB();
        const unsubscribe = messaging().onMessage(onReceiveForeground);
        getTokenSubscribe();
        return unsubscribe;
    }
    useEffect(didMount, [])

    /* Handler Notification */
    const onReceiveForeground = async (remoteMessage) => {
        const { notification, data } = remoteMessage;
        _notify.localNotify({
            "title": notification.title,
            "message": notification.body,
            "data": data
        });
    }

    return (
        <Drawer.Navigator
            initialRouteName={"Sale"}
            drawerContent={(props) => (<MenuScreen
                {...props}
                width={widthMenu}
            />)}
            drawerType={"front"}
            keyboardDismissMode={"on-drag"}
            drawerStyle={{ width: constants.width, backgroundColor: 'transparent' }}
            screenOptions={{ gestureEnabled: false }}
        >
            <Drawer.Screen
                name={"Sale"}
                component={SaleNavigator}
            />
            <Drawer.Screen
                name={"Notification"}
                component={NotifyStackNavigator}
            />
            <Drawer.Screen
                name={"ReceiptManagement"}
                component={ReceiptManagerNavigator}
            />
        </Drawer.Navigator>
    );
};

const mapStateToProps = (state) => ({
    userInfo: state.userReducer,
});

const mapDispatchToProps = (dispatch) => ({
    pouchDBAction: bindActionCreators(pouchDBActionCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(DrawerNavigator);