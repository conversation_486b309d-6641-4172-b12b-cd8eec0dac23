import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { <PERSON><PERSON>ead<PERSON>, <PERSON>Header, <PERSON><PERSON><PERSON>ead<PERSON>, Header } from "@header";
import SaleScreen from '../container/Sale';
import DetailScreen from '../container/Detail';
import ShoppingCart from '../container/ShoppingCart';
import CartAdjustPrice from '../container/ShoppingCart/component/AdjustPriceCart';
import CreatePriceCart from '../container/ShoppingCart/component/CreatePriceCart';
import MemberPoint from '../container/ShoppingCart/component/MemberPoint';
import InstallmentOTP from '../container/ShoppingCart/component/InstallmentOTP';
import Loyalty from '../container/ShoppingCart/component/Loyalty';
import StudentInfo from '../container/ShoppingCart/component/StudentInfo';
import FormPromotion from '../container/ShoppingCart/component/FormPromotion';
import FormCoupon from '../container/ShoppingCart/component/FormPromotion/FormCoupon';
import SaleOrderCart from '../container/SaleOrderCart';
import SaleOrderPayment from '../container/SaleOrderPayment';
import QRPayment from '../container/SaleOrderPayment/QRPayment';
import SmartCardPayment from '../container/SaleOrderPayment/SmartCardPayment';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import CartHeader from "../container/ShoppingCart/Header";
import SaleHeader from '../container/Sale/Header';
import { translate } from "@translate";
import PackagingBag from '../container/PackagingBag';
import PreOrderProduct from '../container/SaleOrderManager/PreOrderProduct';
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import PreOrderCart from '../container/SaleOrderManager/PreOrderCart';
import { useDispatch } from 'react-redux';
import { reset_map_content_promotion_input, reset_package_services } from "../container/Detail/action"
import SimProcess from '../container/SimProcess';
import BackHeaderSim from '../container/SimProcess/components/BackHeaderSim';
import AppleCarePlus from '../container/AppleCarePlus';
import withAudioRecorder from '../container/Audio/withAudioRecorder';
const SaleStack = createStackNavigator();


const SaleWithAudio = withAudioRecorder(SaleScreen);
const DetailWithAudio = withAudioRecorder(DetailScreen);
const ShoppingCartWithAudio = withAudioRecorder(ShoppingCart);
const LoyaltyWithAudio = withAudioRecorder(Loyalty);
const InstallmentOTPWithAudio = withAudioRecorder(InstallmentOTP);
const SaleOrderCartWithAudio = withAudioRecorder(SaleOrderCart);



const SaleStackNavigator = () => {
    const dispatch = useDispatch();

    return (
        <SaleStack.Navigator
            initialRouteName={"Sale"}
            headerMode={"screen"}
        >
            <SaleStack.Screen
                name={"Sale"}
                component={SaleWithAudio}
                options={{
                    header: ({ navigation }) => (<SaleHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={translate('header.search_product_uppercase')}
                        key={"SaleHeader"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"Detail"}
                component={DetailWithAudio}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={() => {
                            onGoBack(navigation)();
                            dispatch(reset_package_services())
                            dispatch(reset_map_content_promotion_input())
                        }}
                        title={translate('header.information_product_uppercase')}
                        key={"DetailHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"ShoppingCart"}
                component={ShoppingCartWithAudio}
                options={{
                    header: ({ navigation }) => (<CartHeader
                        navigation={navigation}
                        title={translate('header.information_cart_uppercase')}
                        key={"CartHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"CartAdjustPrice"}
                component={CartAdjustPrice}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.adjust_price_uppercase')}
                        key={"CartAdjustHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"CreatePriceCart"}
                component={CreatePriceCart}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.create_price_uppercase')}
                        key={"CartCreateHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"MemberPoint"}
                component={MemberPoint}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.order_member_point')}
                        key={"MemberPointHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"InstallmentOTP"}
                component={InstallmentOTPWithAudio}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.order_installment_OTP')}
                        key={"InstallmentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"Loyalty"}
                component={LoyaltyWithAudio}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.order_member_point')}
                        key={"LoyaltyHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"StudentInfo"}
                component={StudentInfo}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.add_image_uppercase')}
                        key={"StudentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"FormPromotion"}
                component={FormPromotion}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate('header.add_image_uppercase')}
                        key={"FormPromotion"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"FormCoupon"}
                component={FormCoupon}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THÔNG TIN ĐÍNH KÈM"}
                        key={"FormCoupon"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"SaleOrderCart"}
                component={SaleOrderCartWithAudio}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"QRPayment"}
                component={QRPayment}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"SmartCardPayment"}
                component={SmartCardPayment}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"PackagingBag"}
                component={PackagingBag}
                options={({ route, navigation }) => ({
                    header: () => (
                        <Header
                            navigation={navigation}
                            key="PackagingBag"
                            title={`${translate(
                                'saleOrderManager.export_request'
                            )} ${route.params.saleOrderID}`}
                        />
                    ),
                    gestureEnabled: false
                })}
            />
            <SaleStack.Screen
                name={"PreOrderProduct"}
                component={PreOrderProduct}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReProductHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={"PreOrderCart"}
                component={PreOrderCart}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"PreOrderCartHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={'SimProcess'}
                component={SimProcess}
                options={{
                    header: ({ navigation }) => (
                        <BackHeaderSim
                            onGoBack={navigation.goBack}
                            key={'SIMHeader'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <SaleStack.Screen
                name={'AppleCarePlus'}
                component={AppleCarePlus}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"BÁN HÀNG"}
                        key={"AppleCarePlus"}
                    />),
                    gestureEnabled: false
                }}
            />
        </SaleStack.Navigator>
    );
};

export default SaleStackNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'Sale' }],
    });
}