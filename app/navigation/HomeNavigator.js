import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { BackHeader, NoneHeader, MenuHeader } from "@header";
import HomeScreen from '../container/HomeManager';
import { translate } from "@translate";
import MenuCollection from '../container/CollectionTransfer/index';
import CollectionTwo from '../container/CollectionTransfer/Screen/CollectionTwo';
import CollectionOne from '../container/CollectionTransfer/Screen/CollectionOne';
import QueryStatus from '../container/CollectionTransfer/Screen/QueryStatus';
import SaleOrderPayment from '../container/SaleOrderPayment';
import SaleOrderCart from '../container/SaleOrderCart';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import CatalogCollection from '../container/CollectionTransfer/CatalogCollection';
import HealthInsuranceNavigator from './HealthInsuranceNavigator';
import InsurancePVINavigator from './InsurancePVINavigator';
import InsuranceExtendedWarrantyNavigator from './InsuranceExtendedWarrantyNavigator';
import InsuranceBrightsideNavigator from './InsuranceBrightsideNavigator';
import CollectInstallmentNavigator from './CollectInstallmentNavigator';
import InsuranceNavigator from './InsuranceNavigator';

const HomeStack = createStackNavigator();

const HomeNavigator = () => {
  return (
    <HomeStack.Navigator
      initialRouteName={"Home"}
      headerMode={"screen"}
    >
      <HomeStack.Screen
        name={"Home"}
        component={HomeScreen}
        options={{
          header: ({ navigation }) => (<MenuHeader
            openDrawer={onOpenDrawer(navigation)}
            title={translate('header.home_screen_uppercase')}
            key={"HomeHeader"}
            navigation={navigation}
            isHomeScreen={true}
          />),
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"CollectionOne"}
        component={CollectionOne}
        options={{
          header: ({ navigation }) => (<BackHeader
            onGoBack={navigation.goBack}
            title={translate("collection.deposit_money_into_your_account")}
            key={"CollectionOne"}
          />),
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"CollectionTwo"}
        component={CollectionTwo}
        options={{
          header: ({ navigation }) => (<BackHeader
            onGoBack={navigation.goBack}
            title={translate("collection.deposit_money_into_your_account")}
            key={"Collection"}
          />),
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"QueryStatus"}
        component={QueryStatus}
        options={{
          header: ({ navigation }) => (<BackHeader
            onGoBack={navigation.goBack}
            title={translate("collection.deposit_money_into_your_account")}
            key={"Collection"}
          />),
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"SaleOrderCart"}
        component={SaleOrderCart}
        options={{
          header: NoneHeader,
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"SaleOrderPayment"}
        component={SaleOrderPayment}
        options={{
          header: ({ navigation }) => (<OrderPaymentHeader
            navigation={navigation}
            key={"OrderPaymentHeader"}
          />),
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"CatalogCollection"}
        component={CatalogCollection}
        options={{
          header: ({ navigation }) => (<BackHeader
            onGoBack={navigation.goBack}
            title={"DANH MỤC NHÓM DỊCH VỤ"}
            key={"Collection"}
          />),
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"HealthInsuranceNavigator"}
        component={HealthInsuranceNavigator}
        options={{
          headerShown: false,
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"InsuranceNavigator"}
        component={InsuranceNavigator}
        options={{
          headerShown: false,
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"InsurancePVINavigator"}
        component={InsurancePVINavigator}
        options={{
          headerShown: false,
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"InsuranceExtendedWarrantyNavigator"}
        component={InsuranceExtendedWarrantyNavigator}
        options={{
          headerShown: false,
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"InsuranceBrightsideNavigator"}
        component={InsuranceBrightsideNavigator}
        options={{
          headerShown: false,
          gestureEnabled: false
        }}
      />
      <HomeStack.Screen
        name={"CollectInstallmentNavigator"}
        component={CollectInstallmentNavigator}
        options={{
          headerShown: false,
          gestureEnabled: false
        }}
      />
    </HomeStack.Navigator>
  );
};

export default HomeNavigator;

const onOpenDrawer = (navigation) => () => {
  Keyboard.dismiss();
  navigation.openDrawer();
};
