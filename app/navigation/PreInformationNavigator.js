import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { MenuHeader, BackHeader } from '@header';
import PreInformation from '../container/PreInformation';
import CollectInformation from '../container/PreInformation/screens/CollectInformation';
import SendOTP from '../container/PreInformation/screens/SendOTP';
import LookUpInfo from '../container/PreInformation/screens/LookUpInfo';
const { Navigator, Screen } = createStackNavigator();

const PreInformationNavigator = () => {
    return (
        <Navigator initialRouteName="PreInformation" headerMode="screen">
            <Screen
                name="PreInformation"
                component={PreInformation}
                options={{

                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => navigation.goBack()}
                            title="ĐĂNG KÝ NHẬN THÔNG TIN"
                            key={'CollectInformationHeader'}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <Screen
                name="CollectInformation"
                component={CollectInformation}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => navigation.goBack()}
                            title="ĐĂNG KÝ NHẬN THÔNG TIN"
                            key={'CollectInformationHeader'}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <Screen
                name="SendOTP"
                component={SendOTP}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => navigation.goBack()}
                            title="XÁC NHẬN THÔNG TIN OTP"
                            key={'SendOTPHeader'}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <Screen
                name="LookUpInfo"
                component={LookUpInfo}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => navigation.goBack()}
                            title="TRA CỨU THÔNG TIN PHIẾU"
                            key={'LookUpInfoHeader'}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
        </Navigator>
    );
};

export default PreInformationNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
};
