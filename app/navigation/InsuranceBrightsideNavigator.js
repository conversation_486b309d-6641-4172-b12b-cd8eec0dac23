import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard, StyleSheet } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
const InsuranceBrightsideStack = createStackNavigator();
import SaleOrderPayment from '../container/SaleOrderPayment';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import InsuranceBrightside from '../container/InsuranceBrightside';
import CollectInsuranceBrightside from '../container/InsuranceBrightside/screen/CollectInsuranceBrightside';
import ReplacementService from '../container/InsuranceBrightside/screen/ReplacementService';
import CancelService from '../container/InsuranceBrightside/screen/CancelService';
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import DeleteSO from '../container/SaleOrderManager/DeleteSO';
import CancelSO from '../container/SaleOrderManager/CancelSO';
import HistorySell from '../container/InsuranceBrightside/screen/HistorySell';
import RefundHistory from '../container/InsuranceBrightside/screen/RefundHistory';
import PrintCertificate from '../container/InsuranceBrightside/screen/PrintCertificate';
import { connect } from 'react-redux';
import EditEmei from '../container/InsuranceBrightside/screen/EditEmei';
import HistoryEditEmei from '../container/InsuranceBrightside/screen/HistoryEditEmei';

const InsuranceBrightsideNavigator = ({ updateHeaderAirtime }) => {

    const getTitleInsurance = () => {
        if (updateHeaderAirtime.AirTimeTransactionTypeID == 1872) {
            return "BẢO HÀNH 1 ĐỔI 1 LỖI NSX PVI"
        } else {
            console.log("99999")
            return "BẢO HÀNH 1 ĐỔI 1"
        }
    }

    return (
        <InsuranceBrightsideStack.Navigator
            initialRouteName={"Insurance"}
            headerMode={"screen"}
        >
            <InsuranceBrightsideStack.Screen
                name={"InsuranceBrightside"}
                component={InsuranceBrightside}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"BẢO HÀNH 1 ĐỔI 1"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"CollectInsuranceBrightside"}
                component={CollectInsuranceBrightside}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={getTitleInsurance()}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"ReplacementService"}
                component={ReplacementService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ - BẢO HIỂM THIẾT BỊ"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"CancelService"}
                component={CancelService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"DỊCH VỤ BẢO HÀNH 1 ĐỔI 1"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"DeleteSO"}
                component={DeleteSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"CancelSO"}
                component={CancelSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"CancelSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"HistorySell"}
                component={HistorySell}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"XEM LỊCH SỬ BÁN"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"RefundHistory"}
                component={RefundHistory}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"XEM LỊCH SỬ HOÀN TIỀN"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"PrintCertificate"}
                component={PrintCertificate}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"IN GIẤY CHỨNG NHẬN BẢO HIỂM"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"EditEmei"}
                component={EditEmei}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"CHỈNH SỬA IMEI"}
                        key={"EditEmei"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceBrightsideStack.Screen
                name={"HistoryEditEmei"}
                component={HistoryEditEmei}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"GIAO DỊCH CHỈNH SỬA IMEI"}
                        key={"HistoryEditEmei"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
        </InsuranceBrightsideStack.Navigator>
    );
}

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'InsuranceBrightside' }]
    });
};

const mapStateToProps = function (state) {
    return {
        updateHeaderAirtime: state.insuranceBrightsideReducer.updateHeaderAirtime,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {

    }
}

export default connect(mapStateToProps, mapDispatchToProps)(InsuranceBrightsideNavigator);

const styles = StyleSheet.create({})