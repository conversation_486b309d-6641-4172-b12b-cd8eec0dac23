import React, { useState } from 'react';
import { Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import KModal from "react-native-modal";
import SafeAreaView from 'react-native-safe-area-view';
import { constants } from "@constants";
import { helper } from '@common';
import ContentView from './ContentView';
import MaskView from './NewMaskView';
import { Camera, useCameraDevice, useCameraFormat, useCodeScanner } from 'react-native-vision-camera';
import Icon from '../icon';
import { COLORS } from '@styles';
import { launchImageLibrary } from 'react-native-image-picker';
// import jsQR from 'jsqr';
// import { Buffer } from 'buffer';
// import jpeg from 'jpeg-js';
// import { PNG } from 'pngjs/browser';
import { UIActivityIndicator } from "react-native-indicators";
import BaseAnimatedView from '../base/BaseAnimatedView';

const { width } = constants;
const FRAME_WIDTH = width;
const MASK_WIDTH = FRAME_WIDTH - 40;
const MASK_HEIGHT = MASK_WIDTH * 0.55;
const FRAME_HEIGHT = MASK_HEIGHT + 30;

const ScanBarcodeVision = ({ isVisible, closeCamera, resultScanBarcode, allowSelectPicture = false }) => {

    const device = useCameraDevice('back', {
        physicalDevices: [
            'ultra-wide-angle-camera',
            'wide-angle-camera',
            'telephoto-camera'
        ],
        hardwareLevel: 'full',
        supportsFocus: true
    })
    const format = useCameraFormat(device, [
        { videoResolution: { width: 1920, height: 1080 } }
    ])

    const [isActive, setIsActive] = useState(true);
    const [listBarcode, setListBarcode] = useState([]);
    const [isLoading, setIsLoading] = useState(false);


    const codeScanner = useCodeScanner({
        codeTypes: [
            'qr',
            'ean-13',
            'code-128',
            'code-39',
            'code-93',
            'ean-8',
            'itf',
            'upc-e',
            'pdf-417',
            'aztec',
            'data-matrix'
        ],
        onCodeScanned: (codes) => {
            if (helper.IsNonEmptyArray(codes)) {
                setIsActive(false);
                if (codes.length == 1) {
                    resolveBarcode(codes[0].value);
                } else {
                    setListBarcode(codes.map(code => code.value));
                }
            }
        }
    });

    const resolveBarcode = (barcode) => {
        const { BARCODEPATTERNS } = global.config;
        if (helper.IsNonEmptyString(BARCODEPATTERNS)) {
            const jsonData = JSON.parse(BARCODEPATTERNS);
            const RegExCasio = new RegExp(jsonData[0].CheckPattern, 'g');
            if (RegExCasio.test(barcode)) {
                barcode = barcode.slice(-12);
            }
        }
        resultScanBarcode(barcode);
    };


    const scanBarcodeFromImage = () => {

        // launchImageLibrary(
        //     {
        //         mediaType: 'photo',
        //         includeBase64: true, // Lấy dữ liệu base64 của ảnh
        //         selectionLimit: 1,
        //     },
        //     async (response) => {
        //         try {
        //             setIsLoading(true)
        //             if (helper.hasProperty(response, 'uri')) {
        //                 await helper.sleep(50)
        //                 const image = response;
        //                 if (!image.base64) {
        //                     Alert.alert('Lỗi', 'Không thể lấy dữ liệu base64 của ảnh');
        //                     return;
        //                 }
        //                 const base64Buffer = Buffer.from(image.base64, 'base64');

        //                 let pixelData;
        //                 let imageBuffer;

        //                 if (image.type === 'image/jpeg' || image.type === 'image/jpg') {
        //                     pixelData = jpeg.decode(base64Buffer, { useTArray: true });
        //                     if (!pixelData) {
        //                         Alert.alert('Lỗi', 'Không thể giải mã ảnh JPEG');
        //                         return;
        //                     }
        //                     imageBuffer = pixelData.data;
        //                 } else if (image.type === 'image/png') {
        //                     pixelData = PNG.sync.read(base64Buffer);
        //                     if (!pixelData) {
        //                         Alert.alert('Lỗi', 'Không thể giải mã ảnh PNG');
        //                         return;
        //                     }
        //                     imageBuffer = pixelData.data;
        //                 } else {
        //                     Alert.alert('Lỗi', 'Định dạng ảnh không được hỗ trợ');
        //                     return;
        //                 }

        //                 const { width, height } = pixelData;
        //                 const data = new Uint8ClampedArray(imageBuffer);

        //                 const code = jsQR(data, width, height, { inversionAttempts: "attemptBoth" });

        //                 if (code) {
        //                     resultScanBarcode(code.data);
        //                 } else {
        //                     Alert.alert('Thông báo', 'Không tìm thấy mã QR/Barcode trong ảnh');
        //                 }
        //             }

        //         } catch (error) {
        //             Alert.alert('Lỗi', 'Không thể xử lý ảnh: ' + error.message);
        //         }
        //         finally {
        //             setIsLoading(false)
        //         }
        //     }
        // );
    };

    return (
        <KModal
            onRequestClose={null}
            transparent={true}
            isVisible={isVisible}
            onBackdropPress={closeCamera}
            onSwipeComplete={closeCamera}
            swipeDirection={['down']}
            style={{ margin: 0 }}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
        >
            <SafeAreaView style={{ flex: 1 }} >
                <View style={{
                    width: FRAME_WIDTH,
                    height: FRAME_HEIGHT,
                    backgroundColor: 'transparent'
                }}>
                    {device != null && (
                        <Camera
                            key={device.id}
                            style={{
                                width: FRAME_WIDTH,
                                height: FRAME_HEIGHT
                            }}
                            device={device}
                            format={format}
                            fps={format?.maxFps}
                            isActive={isActive}
                            codeScanner={codeScanner}
                            exposure={0}
                            photo={false}
                            video={false}
                            audio={false}
                            zoom={device.neutralZoom}
                        />
                    )}
                    <View style={{
                        position: 'absolute',
                        width: FRAME_WIDTH,
                        height: FRAME_HEIGHT
                    }}>
                        <MaskView bounds={{
                            width: 0,
                            height: 0,
                            top: 0,
                            left: 0
                        }} />
                    </View>
                </View>
                <ContentView
                    data={listBarcode}
                    onClose={closeCamera}
                    onSelected={resultScanBarcode}
                />
                {
                    allowSelectPicture &&
                    <View>
                        <TouchableOpacity
                            style={styles.fab}
                            onPress={scanBarcodeFromImage}
                        >
                            <Icon
                                iconSet={"MaterialCommunityIcons"}
                                name={"image-plus"}
                                color={COLORS.bdFFFFFF}
                                size={25}
                            />
                        </TouchableOpacity>
                    </View>
                }

                <BaseAnimatedView
                    show={isLoading}
                >
                    <UIActivityIndicator
                        size={25}
                        color={COLORS.icFFFFFF}
                    />
                </BaseAnimatedView>
            </SafeAreaView>

        </KModal>
    );
};

export default ScanBarcodeVision;
const styles = StyleSheet.create({
    fab: {
        position: 'absolute',
        width: 45,
        height: 45,
        alignItems: 'center',
        justifyContent: 'center',
        right: 20,
        bottom: 100,
        backgroundColor: COLORS.bg2FB47C,
        borderRadius: 28,
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    fabText: {
        fontSize: 24,
        color: 'white',
    }
});

