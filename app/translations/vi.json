{"fetchAPI": {"error_time_out": "<PERSON><PERSON> lỗi, thời gian phản hồi từ hệ thống chậm. <PERSON><PERSON> lòng kiểm tra lại mạng di động, wifi hoặc liên hệ bên hệ thống.", "error_connect": "<PERSON><PERSON> lỗi, <PERSON><PERSON> dụng không thể kết nối hệ thống. <PERSON><PERSON> lòng kiểm tra lại dữ liệu di động hoặc wifi.", "error_server": "<PERSON><PERSON> thống đang bảo trì hoặc đang mất kết nối. <PERSON>ui lòng thử lại!", "no_connected": "<PERSON><PERSON> lòng kiểm tra lại mạng di động hoặc wifi.", "error_get_data": "Ứng dụng không thể lấy dữ liệu từ hệ thống. <PERSON>ui lòng thử lại!", "data_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu", "unable_connect_system": "<PERSON><PERSON><PERSON><PERSON> kết n<PERSON>i được với hệ thống. <PERSON>ui lòng thử lại!", "fetch_data_server_failed": "<PERSON><PERSON>y dữ liệu từ phía máy chủ thất bại. Vui lòng thử lại!", "error_occurred": "Có lỗi xảy ra. <PERSON><PERSON> lòng thử lại!", "unexpected_error": "<PERSON><PERSON> lỗi không mong muốn. <PERSON><PERSON> lòng thử lại", "login_at_another_device": "<PERSON><PERSON><PERSON> khoản của bạn đã được đăng nhập ở thiết bị khác. <PERSON>ui lòng đăng nhập lại!", "token_expired": "Token đã hết hạn. <PERSON>ui lòng đăng nhập lại!", "please_use_wifi": "<PERSON><PERSON> lòng sử dụng wifi nội bộ để thực hiện chức năng này!", "urs_pwd_incorrect": "Tên đăng nhập hoặc mật khẩu không đúng. Vui lòng kiểm tra lại!", "cannot_token": "<PERSON><PERSON><PERSON><PERSON> thể lấy đ<PERSON> token"}, "login": {"btn_login": "<PERSON><PERSON><PERSON>", "text_input_username": "Mã nhân viên", "text_input_password": "<PERSON><PERSON><PERSON>", "scan_qr_code": "Quét QR code:", "empty_username": "<PERSON><PERSON> lòng nhập mã số nhân viên.", "empty_password": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u của bạn.", "err_login": "Lỗi đăng nhập: ", "authen_error_get_token_1": "<PERSON><PERSON><PERSON><PERSON> thể lấy access_token.", "authen_error_get_token_2": "Có lỗi xảy ra trong quá trình lưu dữ liệu vui lòng đăng nhập lại!", "info_store_default": "Thông tin siêu thị làm việc mặc định của User", "change_login_again": "đã thay đổi. <PERSON><PERSON> lòng đăng nhập lại.", "dont_get_info_default": "<PERSON><PERSON> thống không lấy đư<PERSON><PERSON> thông tin siêu thị mặc định của bạn. <PERSON><PERSON> lòng thử lại.", "buy_store_default": "<PERSON><PERSON><PERSON> không có quyền bán hàng trên siêu thị làm việc mặc định. Vui lòng liên hệ IT để được cấp quyền.", "declare_default_store": "Bạn chưa được khai báo siêu thị làm việc mặc định. Vui lòng liên hệ IT để được cấp quyền.", "urs_pwd_incorrect": "Tên đăng nhập hoặc mật khẩu không đúng. Vui lòng kiểm tra lại!", "info_user_problem": "Thông tin dữ liệu của user có vấn đề, liê<PERSON> hệ bộ phận IT kiểm tra.", "version_old": "<PERSON><PERSON><PERSON> bản của app hiện tại đã cũ. <PERSON><PERSON> lòng cập nhật phiên bản mới nhất", "use_app": "để sử dụng app MWG POS.", "version": "<PERSON><PERSON><PERSON>", "version_old_cus": "của app hiện tại đã cũ. <PERSON><PERSON> lòng cập nhật phiên bản mới nhất", "info_user_not_found": "Thông tin của user không có.", "mwg_verify_info": "MWG đang xác thực thông tin của bạn", "cannot_authen": "<PERSON><PERSON><PERSON><PERSON> có thông tin xác thực, li<PERSON><PERSON> hệ bộ phận IT kiểm tra.", "cannot_information_work": "Thông tin làm việc của user có vấn đề, liê<PERSON> hệ bộ phận IT kiểm tra.", "cannot_information_warehouse": "Thông tin siêu thị làm việc của user có vấn đề, li<PERSON><PERSON> hệ bộ phận IT kiểm tra.", "please_update_new_version": "của app hiện tại đã cũ hoặc lỗi. <PERSON><PERSON> lòng cập nhật phiên bản mới nhất để sử dụng app MWG POS.", "permission_err": "Lỗi l<PERSON>y quyền. <PERSON><PERSON> lòng liên hệ <PERSON>"}, "menu": {"home_screen": "Trang chủ", "store": "<PERSON><PERSON><PERSON> si<PERSON>u thị", "management_order": "<PERSON><PERSON><PERSON><PERSON> lý yêu cầu xuất hàng", "sale": "<PERSON><PERSON>", "inventory_stock": "<PERSON><PERSON><PERSON> kê sản phẩm", "management_sim": "<PERSON><PERSON><PERSON><PERSON> lý yêu cầu xử lý SIM", "complaint_refund": "<PERSON> hoàn tiền khiếu nại", "log_out": "<PERSON><PERSON><PERSON> xu<PERSON>", "installment": "<PERSON><PERSON><PERSON><PERSON> lý hồ sơ trả góp", "cod_pay": "YC điều chỉnh tiền thu hộ COD", "additional_promotion": "<PERSON><PERSON> sung khuyến mãi", "search_imei_history": "<PERSON><PERSON> <PERSON><PERSON><PERSON> l<PERSON>ch sử IMEI", "change_watch_battery": "<PERSON><PERSON> pin đồng hồ", "f88_loan_contract": "<PERSON><PERSON><PERSON> đ<PERSON> vay F88", "screen_sticker": "<PERSON><PERSON> màn hình", "product_returns": "<PERSON><PERSON><PERSON> tr<PERSON> hàng", "view_promotion_info": "<PERSON><PERSON> thông tin khu<PERSON>ến mãi", "search_inout_voucher": "<PERSON>ra c<PERSON>u phiếu thu chi", "scan_qrcode_id": "<PERSON><PERSON><PERSON> mã QR định danh", "new_product_request": "<PERSON><PERSON><PERSON> c<PERSON>u sản phẩm mới", "use_guide": "Mẹo sử dụng", "product_display_management": "<PERSON><PERSON><PERSON><PERSON> lý trưng bày sản phẩm", "print_config_product": "In BCH sản phẩm", "output_receipt_management": "<PERSON><PERSON><PERSON><PERSON> lý phiếu biên nhận", "sale_service_management": "<PERSON><PERSON> c<PERSON><PERSON> p<PERSON><PERSON> d<PERSON><PERSON> vụ", "menu_search_placeholder": "<PERSON><PERSON><PERSON><PERSON> tên chức năng. VD: <PERSON><PERSON>ng", "all_features": "<PERSON><PERSON><PERSON> cả tính năng: ", "button_your_features": "<PERSON><PERSON><PERSON> n<PERSON>ng của bạn", "your_features": "<PERSON><PERSON><PERSON> n<PERSON>ng của bạn: ", "button_view_all": "<PERSON><PERSON> t<PERSON>t cả", "new_features": "<PERSON><PERSON><PERSON> n<PERSON>ng mới: ", "search_result": "<PERSON><PERSON><PERSON> quả tìm kiếm: ", "product_infor": "<PERSON><PERSON><PERSON> c<PERSON>u sản phẩm", "sim_price_report": "In bảng giá Sim", "restock": "<PERSON><PERSON><PERSON> c<PERSON>u mua giùm", "deposit_receipt_management": "<PERSON><PERSON><PERSON><PERSON> lý phi<PERSON>u nộp tiền", "sendbank": "<PERSON><PERSON><PERSON> quỹ và nộp tiền siêu thị"}, "sale": {"place_holder_search_product": "<PERSON><PERSON><PERSON><PERSON> mã SP, Tên SP, IMEI, SIM", "placeholder_search_pharmacy": "<PERSON><PERSON><PERSON><PERSON> mã SP, Tên SP, IMEI", "footer": "Copyright © 2019 MWG. <PERSON><PERSON><PERSON> ty cổ phần Th<PERSON> G<PERSON>ới Di Động", "version": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON> h<PERSON>ng", "delete": "Bạn có chắc muốn xóa ", "favorites_list_question": "khỏi danh sách yêu thích?", "color": "<PERSON><PERSON><PERSON>: ", "no_product_in_cart": "Giỏ hàng hiện tại không có sản phẩm nào", "go_to_cart": "Vào giỏ hàng", "favorites_product": "sản phẩm yêu th<PERSON>ch: ", "you": "Bạn c<PERSON>", "history": "<PERSON><PERSON><PERSON> sử tìm kiếm :"}, "saleOrder": {"title": "GIỎ HÀNG", "description_crate_order_success": "Giỏ hàng đư<PERSON><PERSON> tạo thành công.", "code_shopping_cart": "<PERSON><PERSON> giỏ hàng ", "description_separate_sale_order": "Giỏ hàng trên có 1 yêu cầu xuất", "description_separate_sale_order_1": "Giỏ hàng trên đ<PERSON><PERSON><PERSON> tách thành ", "description_separate_sale_order_2": " y<PERSON>u c<PERSON>u xu<PERSON>t", "description_separate_sale_order_3": "với mã sau:", "code_require_output": "<PERSON><PERSON> yêu cầu xuất: ", "type_require_output": "<PERSON><PERSON><PERSON> yêu cầu xuất: ", "output_store": "<PERSON><PERSON>: ", "delivery_type": "<PERSON><PERSON><PERSON> thứ<PERSON> nh<PERSON>n: ", "address": "Địa chỉ nhận hàng: ", "output_type": "<PERSON><PERSON><PERSON> thức xuất: ", "taxId": "<PERSON><PERSON> số thuế: ", "error_create_order": "Lỗi hệ thống. <PERSON><PERSON><PERSON> tại không thể tạo đơn hàng!", "non_sale_order_detail": "<PERSON><PERSON><PERSON>ng lấy đ<PERSON><PERSON><PERSON> thông tin chi tiết đơn hàng !", "sum_price_sale_order": "<PERSON><PERSON>ng tiền phải thanh toán: ", "pay_type": "<PERSON><PERSON><PERSON> thức thanh to<PERSON>:", "advance": "Tạm <PERSON>ng:", "note": "<PERSON><PERSON> chú:", "sim_requirements": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON> lý S<PERSON>: ", "create_profile": "<PERSON><PERSON><PERSON> hồ sơ:", "export_cash": "<PERSON><PERSON> & <PERSON><PERSON><PERSON> h<PERSON>ng", "opt_exists": "Mã OTP đã tạo trước đó vẫn còn hiệu lực. Vui lòng chờ sau 1 phút để lấy lại mã mới.", "otp_verified": "Mã OTP đã được xác thực trước đó. <PERSON>ui lòng lấy lại mã mới để xác thực.", "otp_expried": "Mã OTP đã hết hiệu lực. <PERSON><PERSON> lòng lấy lại mã mới để xác thực.", "opt_found": "Mã OTP không hợp lệ."}, "common": {"enter_numberphone_check": "<PERSON><PERSON> lòng nhập số điện thoại để kiểm tra", "quanlity": "Số lượng: ", "price": "Giá: ", "stock": "Kho: ", "discount": "Giảm: ", "notification_uppercase": "THÔNG BÁO", "notification": "<PERSON><PERSON><PERSON><PERSON> báo", "btn_notify_try_again": "<PERSON><PERSON><PERSON> lại", "btn_back": "Quay lại", "btn_confirm": "<PERSON><PERSON><PERSON>", "inventory": "Tồn", "store": "<PERSON><PERSON><PERSON> thị", "orderd": "Đã đặt", "state": "Trạng thái: ", "warranty_time": "<PERSON><PERSON><PERSON> hành ch<PERSON>h hãng đến ngày: ", "non_warranty_info": "<PERSON><PERSON><PERSON><PERSON> có thông tin bảo hành ch<PERSON>h hãng", "warranty_at_store": "<PERSON><PERSON><PERSON> hành tại TGDĐ: ", "no_have": "<PERSON><PERSON><PERSON><PERSON> có ", "non_description": "<PERSON><PERSON><PERSON><PERSON> có mô tả", "search_other_product": "Bạn có muốn chắc chắn tìm kiếm sản phẩm khác.", "search_other_product_confirm_search": "<PERSON><PERSON><PERSON>", "btn_save_watch_list": "Lưu vào watch list", "month": "th<PERSON>g", "day": "ng<PERSON>y", "before": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delivery_time": "Thời gian hẹn giao: ", "cost_price": "<PERSON><PERSON><PERSON><PERSON> tiền:", "inventory_status": "Trạng thái:", "view_detail": "<PERSON>em chi tiết", "code_product": "<PERSON>ã sản phẩm: ", "error_load_data": "Quá trình tải dữ liệu đã bị lỗi. Vui lòng nhấn nút thử lại để tải lại!", "btn_close": "Đ<PERSON><PERSON>", "btn_skip": "Bỏ qua", "btn_accept": "Đồng ý", "btn_cancel": "<PERSON><PERSON><PERSON>", "data_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu", "text_input_search_keyword": "<PERSON><PERSON><PERSON><PERSON> từ khóa để tìm kiếm...", "btn_continue": "<PERSON><PERSON><PERSON><PERSON>", "customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>: ", "phone_number": "<PERSON><PERSON> điện thoại: ", "slugon_delete": "<PERSON>óa sản phẩm khỏi giỏ hàng sẽ xóa mã giảm giá và khuyến mãi liên quan sản phẩm. Bạn có muốn xóa: ", "remove_list": "khỏi danh sách bán kè<PERSON>?", "btn_view_more": "<PERSON><PERSON>", "btn_view_less": "<PERSON><PERSON>", "delete": "Xoá", "btn_add_to_cart": "Thêm vào giỏ hàng", "placeholder_search_product": "Mã SP/Tên SP", "customer_accept": "<PERSON><PERSON><PERSON><PERSON> hàng đồng <PERSON>", "customer_decline": "<PERSON><PERSON><PERSON><PERSON> hàng từ chối", "created_date": "<PERSON><PERSON><PERSON> t<PERSON>: "}, "installmentsteps": {"stepone_client_idcardtypecmnd": "Chứng minh nhân dân", "stepone_client_idcardtypecccd": "<PERSON><PERSON><PERSON> c<PERSON> công dân"}, "arrearsResults": {"headerListStaff": "Thông tin nhân viên truy thu"}, "inventory_share": {"func": "<PERSON><PERSON><PERSON>", "manager_area": "<PERSON><PERSON><PERSON><PERSON> lý khu vực", "inventory": "<PERSON><PERSON><PERSON>", "view_inventory_difference": "<PERSON><PERSON> chênh l<PERSON>nh kiểm kê", "view_inventory_result": "<PERSON><PERSON> kết quả kiểm kê", "view_deduction_result": "<PERSON><PERSON> kết quả truy thu", "result_inventory_difference": "<PERSON><PERSON><PERSON> qu<PERSON> chênh lệnh kiểm kê", "create_collection": "<PERSON><PERSON><PERSON> y<PERSON>u cầu truy thu", "manager_inventory_requitment": "<PERSON><PERSON><PERSON><PERSON> lý yêu cầu truy thu", "completed": "<PERSON><PERSON><PERSON> t<PERSON>t", "access_camere": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> cập camera", "app_mwg_acccess_camera": "Ứng dụng MWG cần quyền truy cập vào camera của bạn", "area_list": "<PERSON><PERSON> s<PERSON>ch khu vực", "area_list_auto_create_by_system": "<PERSON><PERSON> thống tạo tự động 2 khu kiểm Showroom và Kho", "applying": "<PERSON><PERSON>", "apply": "<PERSON><PERSON>", "add_area_suceess": "<PERSON><PERSON><PERSON><PERSON> khu vực thành công", "add_new_area": "<PERSON><PERSON><PERSON><PERSON> khu vực mới", "enter_area_name": "<PERSON><PERSON><PERSON><PERSON> tên khu vực", "new_area_name": "<PERSON><PERSON><PERSON> khu vực mới", "add_area": "<PERSON><PERSON><PERSON><PERSON> khu vực", "choose_type_check": "<PERSON><PERSON><PERSON> lo<PERSON> k<PERSON>", "check_suggestion": "<PERSON><PERSON><PERSON>", "check_inspection": "<PERSON><PERSON><PERSON>", "check_proactive": "<PERSON><PERSON><PERSON> chủ động", "not_found_inven_history": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lịch sử kiểm kê", "not_found_period_inven": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kỳ kiểm kê", "no_premission": "<PERSON><PERSON><PERSON> không có quyền", "select_start_time": "<PERSON><PERSON><PERSON> thời gian b<PERSON><PERSON> đầu", "not_completed": "<PERSON><PERSON><PERSON> ho<PERSON>n tất", "get_start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "finished": "<PERSON><PERSON><PERSON>", "view_inven_history": "<PERSON><PERSON> l<PERSON>ch sử kiểm đã hoàn tất", "search": "<PERSON><PERSON><PERSON>", "select_area": "<PERSON><PERSON><PERSON> khu kiểm", "product_imei": "<PERSON><PERSON><PERSON> ph<PERSON>m có IMEI", "product_no_imei": "<PERSON><PERSON><PERSON> ph<PERSON>m không IMEI", "list_product_no_imei": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m không IMEI", "list_product_imei": "<PERSON><PERSON> s<PERSON>ch sản phẩm có IMEI", "input_lots_code_name": "Mã lots/ Mã SP/ Tên SP", "not_found_product_info": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin sản phẩm", "not_found_info_area": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin khu vực kiểm", "not_found_products": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "products_not_declare_in_term": "<PERSON><PERSON>n phẩm này không đ<PERSON><PERSON><PERSON> khai báo trong kỳ kiểm kê", "product_has_checked": "<PERSON><PERSON>n phẩm này đang được kiểm bởi %{usercheck}. <PERSON><PERSON> lòng chọn sản phẩm khác", "period_inven_blocked": "<PERSON><PERSON> kiểm kê đã khóa. Vui lòng liên hệ quản lý!", "see_more": "<PERSON>em thêm...", "not_found_info_about_product": "<PERSON>h<PERSON>ng tìm thấy thông tin IMEI tương ứng với sản phẩm", "not_found_info_product_status": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin trạng thái sản phẩm", "checking": "<PERSON><PERSON>", "checked": "<PERSON><PERSON> k<PERSON>", "change_stock": "<PERSON><PERSON><PERSON><PERSON><PERSON> tồn", "quantity": "Số lượng", "choose_products": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "input_imei_lots_name": "Nhập IMEI/ Mã lots/ Mã SP/ Tên SP", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "p_imei": "IMEI th<PERSON> sản ph<PERSON>m", "please_check_again": "Vui lòng kiểm tra lại!", "list_imei_export": "<PERSON><PERSON> s<PERSON>ch IMEI đã xuất khi kiểm kê", "cancel_operation": "<PERSON><PERSON><PERSON><PERSON> thao tác bạn vừa thực hiện sẽ bị huỷ bỏ?", "filter": "<PERSON><PERSON> lọc tìm kiếm", "product_transit_not_enter": "<PERSON><PERSON>n phẩm đang đi đường không cho phép nhập tay!", "product_status_not_yet_inven": "<PERSON>r<PERSON><PERSON> thái sản phẩm chưa kiểm", "product_has_inven_change": "Sản phẩm bạn đang kiểm có thay đổi tồn kho. Bạn phải kiểm kê lại sản phẩm này ở các khu vực trước khi hoàn tất và xử lý chênh lệch!", "product_status_available": "Tr<PERSON><PERSON> thái sản phẩm đã tồn tại", "number_check": "Số lư<PERSON> k<PERSON>", "inven_detail": "<PERSON> tiết ki<PERSON>m", "status_stock_cus": "TT tồn", "status_check_cus": "TT kiểm", "status_stock": "<PERSON><PERSON><PERSON><PERSON> thái tồn", "status_check": "<PERSON>r<PERSON><PERSON> thái kiểm", "select_inven_status": "<PERSON><PERSON><PERSON> trạng thái kiểm", "pl_select_inven_status": "<PERSON><PERSON> lòng chọn trạng thái kiểm", "input_imei": "Nhập IMEI", "export_while_checking": "Đ<PERSON> xuất khi kiểm", "import_while_checking": "<PERSON><PERSON> nhập khi kiểm", "out_stock": "<PERSON><PERSON><PERSON><PERSON> tồn kho", "processing_product": "<PERSON><PERSON> đi đường", "finish_check": "Lưu! Và kiểm sản phẩm khác", "confirm_inventory_amount": "<PERSON><PERSON><PERSON>n số lượ<PERSON> kiểm", "update": "<PERSON><PERSON><PERSON>", "product_deviation": "<PERSON><PERSON><PERSON> ph<PERSON>m l<PERSON>ch ki<PERSON>m", "product_check_completed": "SP cần kiểm để hoàn tất", "not_fount_info_product": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin sản phẩm", "have_imei": "Có IMEI", "view_disparity_result": "<PERSON><PERSON> kết qu<PERSON> chênh l<PERSON>ch", "no_imei": "Không IMEI", "area": "<PERSON><PERSON> v<PERSON>", "remaining_stock": "SL tồn", "quantity_check": "SL kiểm", "total_check": "Tổng sl kiểm", "product_excess": "<PERSON><PERSON><PERSON> ph<PERSON>m thừa", "product_lack": "<PERSON><PERSON><PERSON> ph<PERSON>m thi<PERSON>u", "deduction_list": "<PERSON>h s<PERSON>ch truy thu", "deduction_expect": "<PERSON><PERSON><PERSON> thu dự kiến", "total_dedution": "<PERSON><PERSON><PERSON> tiền truy thu", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "deduction_amount": "<PERSON><PERSON> tiền truy thu", "choose_inven": "<PERSON><PERSON><PERSON> k<PERSON> kiểm", "company_indemnify": "<PERSON><PERSON><PERSON> ty chịu", "staff_indemnify": "<PERSON><PERSON><PERSON> viên ch<PERSON>u", "external_processing": "<PERSON><PERSON> lý ngoài", "total_amount": "<PERSON><PERSON><PERSON> tiền", "staff_deduction": "<PERSON><PERSON><PERSON> viên truy thu", "input_staff_dedution": "<PERSON><PERSON><PERSON><PERSON> nhân viên truy thu", "not_found_info_dedution_expect": "<PERSON><PERSON>ông tìm thấy thông tin truy thu dự kiến", "not_found_info_dedution_list": "<PERSON><PERSON>ông tìm thấy thông tin danh sách truy thu", "not_found_staff_info": "<PERSON>hông tìm thấy thông tin nhân viên", "not_found_dedution_info_detail": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin chi tiết truy thu", "export_lost": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "change_product": "đ<PERSON><PERSON> hàng", "amount": "<PERSON><PERSON><PERSON><PERSON> tiền", "import_more": "n<PERSON><PERSON><PERSON> thừa", "amount_staff_indemnify": "<PERSON><PERSON> tiền nhân viên chịu", "input_amount": "<PERSON><PERSON><PERSON><PERSON>", "no_process": "<PERSON><PERSON><PERSON> lý", "processed": "Đã xử lý", "cash_receipt": "<PERSON><PERSON><PERSON> thu", "not_save_total_deduction_diference": "Bạn không thể lưu đư<PERSON> vì tổng tiền truy thu của nhân viên khác với tổng số tiền truy thu tại siêu thị", "not_save_total_duduction_less": "Bạn không thể thêm đượ<PERSON> vì tổng tiền truy thu của nhân viên bé hơn tổng số tiền truy thu tại siêu thị", "deduction_processing": "<PERSON><PERSON><PERSON> thu đang xử lý", "access_result_processed": "<PERSON><PERSON><PERSON> quả truy thu đã xử lý", "id_staff": "Mã NV", "name_staff": "Tên NV", "save_info": "<PERSON><PERSON><PERSON> thông tin", "qty": "SL", "id": "Mã", "history": "<PERSON><PERSON> l<PERSON>ch sử tìm kiếm", "choose_end_time": "<PERSON><PERSON><PERSON> thời gian kết thúc", "list_suggest": "Thông tin danh sách gợi ý", "p_list": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "choose": "<PERSON><PERSON><PERSON>", "choose_status": "<PERSON><PERSON><PERSON> trạng thái", "unchecked": "<PERSON><PERSON><PERSON>", "status_diff": "<PERSON><PERSON><PERSON> trạng thái", "pl_input_area_name": "<PERSON><PERSON> lòng nhập tên khu vực mới", "check_area": "<PERSON><PERSON><PERSON>hu", "choose_area": "<PERSON><PERSON><PERSON> khu vực", "choose_time": "<PERSON><PERSON><PERSON> thời gian", "not_add_emp_is": "Bạn không thể thêm được vì tổng tiền truy thu của nhân viên là", "less_than_total_amount_is": "<PERSON><PERSON> hơn tổng số tiền truy thu tại siêu thị là", "not_save_emp_is": "Bạn không thể lưu đ<PERSON> vì tổng tiền truy thu của nhân viên là", "diff_from_total_amount_is": "<PERSON><PERSON><PERSON><PERSON> với tổng số tiền truy thu tại siêu thị là", "amount_paid": "<PERSON><PERSON> tiền chịu", "info_staff_arrears": "Thông tin nhân viên truy thu", "imei_of_product": "IMEI th<PERSON> sản ph<PERSON>m", "cannot_data": "<PERSON><PERSON><PERSON>ng tìm thấy dữ liệu!", "imei_not_in_format": "IMEI nhập không đúng định dạng. <PERSON><PERSON> lòng kiểm tra lại", "imei_outside_system": "IMEI bạn nhập không tồn trong hệ thống, bạn vẫn muốn nhập để kiểm kê?", "begin_term_time": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON>t đầu của kỳ kiểm ", "prdchangesttquantity": "đổi trạng thái"}, "detail": {"no_infomation_pre": "<PERSON><PERSON><PERSON><PERSON> có thông tin Lock hàng pre-order", "color": "Màu: ", "date": "NGÀY", "quantity": "SỐ LƯỢNG", "check_phone_number": "KIỂM TRA SỐ ĐIỆN THOẠI", "phone_number_apply_promotion": "<PERSON><PERSON> điện thoại áp dụng khu<PERSON>ến mãi:", "btn_done": "<PERSON><PERSON>", "btn_add_to_cart": "THÊM VÀO GIỎ HÀNG", "consult_product_service": "<PERSON><PERSON> vấn dịch v<PERSON> sản phẩm", "store_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy siêu thị", "receive_within_5_7_days": "<PERSON><PERSON><PERSON><PERSON> hàng trong 5-7 ng<PERSON>y", "quantity_short": "SL", "size_choice": "SIZE", "promotion_still": "<PERSON>ươ<PERSON> trình k<PERSON>ến mãi còn", "applied": " <PERSON><PERSON><PERSON>", "btn_at_store": "<PERSON><PERSON><PERSON> si<PERSON>u thị", "btn_at_home": "Tại nhà", "btn_create_store": "<PERSON><PERSON> t<PERSON>o", "btn_other_store": "<PERSON><PERSON>", "select_time": "<PERSON><PERSON><PERSON> thời gian:", "old_customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng c<PERSON>", "transfer_store": "<PERSON><PERSON> chuy<PERSON> hàng:", "transfer_po": "PO chuyển hàng:", "output_store": "<PERSON><PERSON>: ", "sale_installment": "MUA TRẢ GÓP", "new": "<PERSON><PERSON><PERSON>", "secondStock": "Đã sử dụng", "display": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>y", "on_sale": "Mới giảm giá", "non_warranty_info": "<PERSON><PERSON><PERSON><PERSON> có thông tin bảo hành ch<PERSON>h hãng", "fifo": "FIFO", "feature_product": "Đặc điểm\nnổi bật", "config_product": "<PERSON><PERSON><PERSON><PERSON> số\nchi tiết", "installment_service": "<PERSON><PERSON> vấn\nlắp đặt", "instalment_service": "<PERSON><PERSON> vấn\ntrả góp", "emei": "IMEI: ", "total_reward": "<PERSON><PERSON><PERSON><PERSON> thưởng chuẩn", "standard_point": "<PERSON><PERSON><PERSON><PERSON> thưởng nóng", "close_member_point": "<PERSON><PERSON><PERSON><PERSON> tích lũy khách hàng thân thiết tạm tính", "cannot_apply_promotion": " - Chương trình khuyến mãi này không áp dụng đồng thời với chương trình: ", "promotion_has": "<PERSON>ươ<PERSON> trình k<PERSON>ến mãi còn ", "time_remained": " <PERSON><PERSON><PERSON> d<PERSON>", "text_input_search_name": "<PERSON><PERSON><PERSON><PERSON> mã SP, Tên SP", "text_input_phone_number": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "select_package": "<PERSON><PERSON><PERSON> g<PERSON> c<PERSON>:", "config_product_uppercase": "THÔNG SỐ CHI TIẾT", "feature_product_uppercase": "ĐẶC ĐIỂM NỔI BẬT", "fifo_uppercase": "THÔNG TIN TỒN KHO FIFO", "instalment_program_uppercase": "CHƯƠNG TRÌNH TRẢ GÓP", "create_date": "<PERSON><PERSON><PERSON>: ", "color_or_quantity": "M<PERSON>u (SL): ", "customer_phone_number": "SĐT: ", "sale_agent": "NV Bán hàng: ", "create_store": "<PERSON><PERSON> tạo: ", "support_phone_number": "SĐT hỗ trợ: ", "create_agent": "NV tạo: ", "lock_information_uppercase": "THÔNG TIN LOCK HÀNG", "text_input_address_to_find_store": "<PERSON><PERSON><PERSON><PERSON> từ khóa để tìm siêu thị", "select_store_uppercase": "CHỌN SIÊU THỊ", "text_input_phone_number_contact": "<PERSON><PERSON><PERSON><PERSON> số điện thoại người liên hệ", "text_input_name_contact": "<PERSON><PERSON><PERSON><PERSON> họ tên ngư<PERSON>i liên hệ", "text_input_address_contact": "<PERSON><PERSON><PERSON><PERSON>ố <PERSON>h<PERSON>, tên đ<PERSON><PERSON>", "text_input_note": "<PERSON><PERSON><PERSON>", "distance": "Kho<PERSON>ng cách: ", "vehicle": "Phương tiện: ", "text_input_street_to_find_store": "<PERSON><PERSON><PERSON><PERSON> tên đường để tìm nhanh siêu thị", "check_box_receive_at_store": "<PERSON><PERSON>n có thể đến lấy hàng tại siêu thị", "check_box_delivery": "<PERSON><PERSON><PERSON> thị chuyển hàng về", "check_box_delivery_po": "PO chuyển hàng về", "contact_address": "Đ<PERSON>a chỉ liên hệ: ", "contact_name": "<PERSON><PERSON> tên ngư<PERSON>i liên hệ: ", "non_imei": "<PERSON><PERSON><PERSON> ph<PERSON>m không có IMEI", "warranty_at_store": "<PERSON><PERSON><PERSON> hành tại TGDĐ: ", "spare": "<PERSON><PERSON> kiện: ", "not_available": "<PERSON><PERSON><PERSON><PERSON> có", "promotion_by_delivery_method": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi theo hình thức giao", "sale_with_delivery_method": "<PERSON><PERSON> kèm theo hình thức giao", "attached_product_promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi sản phẩm bán kèm", "sim_register_procedure": "THỦ TỤC ĐĂNG KÝ SIM", "id_card": "BẢN GỐC CMND (cấp dưới 15 năm) hoặc Căn cước công dân (còn thời hạn) hoặc <PERSON><PERSON> chiếu (còn thời hạn) của chủ thuê bao.", "potrait": "Ảnh chân dung của chủ thuê bao tại thời điểm giao dịch.", "package_information_uppercase": "THÔNG TIN GÓI CƯỚC", "product_information_not_available": "<PERSON><PERSON><PERSON><PERSON> có thông tin sản phẩm", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi:", "attached": "<PERSON><PERSON> k<PERSON>:", "no_store_found": "<PERSON><PERSON><PERSON><PERSON> tìm đ<PERSON><PERSON><PERSON> siêu thị để lấy hàng", "no_store_delivery_found": "<PERSON><PERSON><PERSON><PERSON> tìm đ<PERSON><PERSON><PERSON> siêu thị chuyển hàng về", "expired_time": "Thời gian chuyển hàng về vượt quá Thời gian giữ hàng tối đa cho phép của Loại yêu cầu xuất. Hoặc hệ thống không lấy được tải giao hàng", "no_delivery_information_found": "<PERSON><PERSON><PERSON><PERSON> có thông tin chuyển hàng về", "no_verified_information": "<PERSON><PERSON><PERSON>ng có thông tin xác thực. <PERSON>ui lòng liện hệ IT để kiểm tra!", "no_product_information": "<PERSON><PERSON><PERSON><PERSON> có thông tin sản phẩm.", "no_instalment_partner_information": "<PERSON><PERSON><PERSON><PERSON> có thông tin đối tác trả góp", "no_instalment_information": "<PERSON><PERSON><PERSON><PERSON> có thông tin chương trình trả góp", "no_fifo_information": "Không có thông tin FIFO", "no_detail_information": "<PERSON><PERSON><PERSON>ng có thông tin cấu hình chi tiết", "no_feature_information": "<PERSON><PERSON><PERSON><PERSON> có thông tin đặc điểm nổi bật", "no_lock_information": "<PERSON><PERSON><PERSON><PERSON> có thông tin Lock hàng", "no_package_information": "<PERSON><PERSON><PERSON>ng có thông tin gói cước", "no_sim_price_information_found": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> đ<PERSON><PERSON><PERSON> thông tin giá bán SIM", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "promotions": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "attachments": "<PERSON><PERSON>", "delivery_method": "<PERSON><PERSON><PERSON> thức giao", "delivery_method:": "<PERSON><PERSON><PERSON> thức giao: ", "no_promotion": "<PERSON><PERSON><PERSON><PERSON> có khu<PERSON>ến mãi", "no_attachments": "<PERSON><PERSON><PERSON><PERSON> có bán k<PERSON>m", "please_select_sim_package": "<PERSON><PERSON> lòng chọn gó<PERSON> cước SIM", "please_select_promotion": "<PERSON><PERSON> lòng chọn khuyến mãi BẮT BUỘC chọn:", "dismiss_promotion": "<PERSON><PERSON> chương trình khuyến mãi chưa đượ<PERSON> chọn. Bạn có chắc muốn bỏ qua khuyến mãi ?", "no_product_in_cart": "Giỏ hàng hiện tại không có sản phẩm nào", "view_lock_product": "Xem lock hàng", "go_to_cart": "Vào giỏ hàng", "export_request": "YC xuất: ", "change_store_request": "YC chuyển kho: ", "exchange_request": "YC chuyển đổi hàng hóa: ", "report": "<PERSON><PERSON><PERSON><PERSON> bản hàng hóa: ", "please_enter_phone_number": "<PERSON><PERSON> lòng nhập số điện thoại", "please_follow_phone_number_format": "<PERSON><PERSON> lòng nhập đúng định dạng số điện thoại", "apply": "<PERSON><PERSON>", "select_hour": "<PERSON><PERSON><PERSON> g<PERSON>", "select_date": "<PERSON><PERSON><PERSON>", "please_enter_contact_phone_number": "<PERSON><PERSON> lòng nhập số điện thoại liên hệ", "please_enter_10_digits_phone_number": "<PERSON><PERSON> lòng đúng định dạng số điện thoại 10 số", "please_enter_correct_phone_number_header": "<PERSON><PERSON> lòng nhập đúng đầu số điện thoại", "please_enter_contact_name": "<PERSON><PERSON> lòng nhập họ tên người liên hệ", "please_enter_delivery_address": "<PERSON><PERSON> lòng nhập địa chỉ giao hàng", "please_select_delivery_time": "<PERSON><PERSON> lòng chọn thời gian hẹn giao", "please_select_store": "<PERSON><PERSON> lòng chọn siêu thi đến lấy hàng", "select_province": "Chọn Tỉnh/Thành", "select_district": "<PERSON><PERSON><PERSON>/Huyện", "please_select_delivery_store": "<PERSON><PERSON> lòng chọn siêu thị chuyển hàng về", "not_enough_inventory": "<PERSON><PERSON><PERSON> phẩm không đủ tồn kho", "delivery_at_home": "Giao tại nhà:", "delivery_at_store": "<PERSON><PERSON>o tại siêu thị:", "remove_instalment_information": "Bạn có muốn xóa thông tin trả góp hiện tại?", "export_code": "Mã YC xuất: ", "change_store_code": "Mã YC chuyển kho: ", "exchange_code": "Mã YC chuyển đổi hàng hóa: ", "cancel_code": "<PERSON><PERSON> đề nghị hủy k<PERSON>ch ho<PERSON>: ", "no_installment_support_information": "<PERSON><PERSON><PERSON>ng có thông tin tư vấn lắp đặt", "no_instalment_support_information": "<PERSON><PERSON><PERSON><PERSON> có thông tin tư vấn trả góp", "no_description_information": "<PERSON><PERSON><PERSON>ng có thông tin mô tả", "no_check_information_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu chấm công tại siêu thị", "all_store": "<PERSON><PERSON> tất cả siêu thị", "condition": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>:", "notice": "<PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "deposit_date": "<PERSON><PERSON><PERSON>: ", "lock_order_type_date": "<PERSON><PERSON><PERSON> xếp thứ tự phân bổ: ", "keep_lock_all_day": "Giữ lock h<PERSON><PERSON> <PERSON><PERSON><PERSON> hết ng<PERSON>: ", "output_type": "Loại YCX: ", "full_product": "<PERSON><PERSON> đủ hàng", "no_full_product": "<PERSON><PERSON><PERSON> đủ hàng", "lock_information_pre_order_uppercase": "THÔNG TIN LOCK HÀNG PRE-ORDER", "male": "<PERSON><PERSON>", "female": "Chị", "size": "Chọn size: ", "quantity_promotion": "<PERSON><PERSON> suất áp dụng k<PERSON>n mãi:", "transport": "Phương tiện: ", "motorbike": "<PERSON>e máy", "truck": "<PERSON><PERSON> tải", "warning_get_promotion": "Thông tin khuyến mãi không hợp lệ. <PERSON><PERSON> lòng kiểm tra lại", "deactivated_installment_partner_notice": "<PERSON><PERSON><PERSON> tác {{partnerInstallmentName}} đang có sự cố, tạm thời không thể làm hồ sơ trả chậm. Bạn vui lòng tư vấn khách hàng sang đối tác khác hoặc liên hệ Ngành Hàng Tài Chính Tiêu Dùng nếu cần sự hỗ trợ."}, "cameraDoc": {"pls_document_into": "<PERSON>ui lòng cho tài liệu của bạn vào trong khung hình", "uncorect_document": "<PERSON><PERSON><PERSON> chụp mặt tài liệu không đúng. <PERSON><PERSON> lòng chụp lại.", "blur_picture": "<PERSON><PERSON><PERSON> chụp bị mờ, bị thiếu hoặc bị lóa. <PERSON><PERSON> lòng chụp lại.", "blur_picture1": "<PERSON><PERSON><PERSON> chụp bị mờ, bị thiếu hoặc bị lóa. <PERSON><PERSON> lòng chụp lại.", "face_into": "Đặt khuôn mặt của bạn trong vòng tròn", "move_cam_close": "<PERSON> chuyển camera củ<PERSON> bạn đến gần hơn", "move_cam_far": "Di chuyển camera của bạn ra xa hơn", "more_face": "<PERSON><PERSON> có nhiều khuôn mặt trong vòng tròn", "blur_pls_again": "Ảnh bạn chụp bị mờ hoặc không phải là ảnh chân dung thật. <PERSON><PERSON> lòng thử lại"}, "shoppingCart": {"product_intend": "(<PERSON><PERSON><PERSON> phẩm không tham gia vào khuyến mãi tổng đơn)", "customer_info": "Thông tin khách hàng", "customer_info_uppercase": "THÔNG TIN KHÁCH HÀNG", "customer_print_company_bill": "<PERSON><PERSON><PERSON><PERSON> hàng in hóa đơn công ty", "old_customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng c<PERSON>", "ID_card_number": "Số CMND/CCCD:", "take_ID_and_student_card_pictures": "CHỤP HÌNH ĐÍNH KÈM", "btn_delete_cart": "HỦY GIỎ HÀNG", "btn_create_sale_order": "TẠO ĐƠN HÀNG", "cart_total_price": "<PERSON><PERSON><PERSON> tiền đơn hàng ", "not_rounded": "(<PERSON><PERSON><PERSON> làm tròn)", "total_customer_deposit": "Tổng tiền khách phải trả trước", "additional_fee": "Phụ phí", "provisional_total_membership_score": "Tổng điểm tích lũy KHTT tạm tính", "btn_adjust_price": "Điều chỉnh giá", "btn_create_competitive_price": "<PERSON><PERSON><PERSON> chiến giá", "text_input_customer_tax": "<PERSON><PERSON> số thuế: ", "text_input_customer_company_name": "Tên công ty: ", "text_input_customer_company_address": "Địa chỉ công ty: ", "text_input_customer_company_phone": "<PERSON><PERSON> điện thoại công ty:", "text_input_contact_phone": "<PERSON><PERSON> điện thoại liên hệ:", "text_input_contact_name": "<PERSON><PERSON> tên ngư<PERSON>i liên hệ:", "text_input_contact_address": "Đ<PERSON>a chỉ người liên hệ:", "text_input_phone": "<PERSON><PERSON> điện thoại:", "text_input_name": "<PERSON><PERSON> tên:", "text_input_cccd": "CMND/CCCD c<PERSON><PERSON> kh<PERSON>ch hàng:", "text_input_cccd_hc": "CMND/CCCD/HC", "btn_check": "<PERSON><PERSON><PERSON>", "text_input_address": "Địa chỉ:", "text_input_email": "Email:", "text_input_cost_contract": "<PERSON><PERSON> làm hồ sơ:", "text_input_deposit": "Số tiền trả trước:", "picker_loan_term": "<PERSON><PERSON> hạn vay:", "text_input_payment_per_month": "Số tiền trả hàng tháng (số tiền tham kh<PERSON>, có thể chênh lệnh 10.000đ - ch<PERSON><PERSON> bao gồm phí thu hộ hàng tháng)", "popup_phone_coupon_requires_phone": "<PERSON>ã Coupon yêu cầu nhập số điện thoại", "placeholder_customer_tax": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "placeholder_customer_company_name": "<PERSON><PERSON><PERSON><PERSON> tên công ty", "placeholder_address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "placeholder_email": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "placeholder_cccd": "Nhập CMND/CCCD c<PERSON><PERSON> kh<PERSON>ch hàng", "placeholder_cccd_hc": "Nhập CMND/CCCD/HC của kh<PERSON>ch hàng", "placeholder_phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "placeholder_name": " <PERSON><PERSON><PERSON><PERSON> họ tên", "placeholder_ID_card_number": "Nhập số CMND/CCCD", "total_promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi tổng đơn", "discount_product_amount_promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi mua nhiều gi<PERSON>m nhiều", "use_Coupon": "Sử dụng coupon", "btn_apply": "<PERSON><PERSON>", "delivery_type": "<PERSON><PERSON><PERSON> thức giao: ", "installment_payment": " (Trả góp)", "product_not_valid_for_order_promotion": "(<PERSON><PERSON><PERSON> phẩm không tham gia vào khuyến mãi tổng đơn)", "adjust_price": "Điều chỉnh giá:", "competitive_price": "Chiến giá:", "product_price": "<PERSON><PERSON><PERSON><PERSON> tiền: ", "btn_confirm_uppercase": "XÁC NHẬN", "btn_update_uppercase": "CẬP NHẬT", "btn_update": "<PERSON><PERSON><PERSON>", "btn_skip_verify": "Bỏ qua xác thực", "btn_agree_use": "Đồng ý sử dụng", "btn_disagree_use": "<PERSON><PERSON><PERSON><PERSON> đồng <PERSON>", "confirm_relationship_with_customer": "<PERSON><PERSON><PERSON> nhận mối quan hệ giữa Bạn và <PERSON> hàng:", "type_IMEI_SIM": "Nhập IMEI SIM", "scan_employee_code_adjust_price": "Quét mã nhân viên đư<PERSON><PERSON> phép điều chỉnh giá bán:", "current_price": "<PERSON><PERSON><PERSON> b<PERSON> hiện tại:", "adjust": "Điều chỉnh:", "price_after_adjust": "Giá sau điều chỉnh:", "adjust_price_reason": "<PERSON>ý do điều chỉnh:", "attach_files": "Thêm file đ<PERSON>h kèm: ", "add_files": "Thêm file", "max_attached_files_quantity": "(Max: 3 files)", "delete": "Xóa", "remaining_limit": "<PERSON><PERSON><PERSON> mức còn lại ", "provisional": "(tạm t<PERSON>)", "discounted_value": "<PERSON><PERSON><PERSON> trị giảm:", "price_after_competition": "Giá sau chiến giá:", "guide": "Hướng dẫn:", "employee_type_OTP": "(<PERSON><PERSON><PERSON> viên nhập mã OTP và bấm \"<PERSON><PERSON>c nhận\")", "skip_verify_guide": "<PERSON><PERSON> chọ<PERSON> \"Bỏ qua xác thực\" nhân viên hướng dẫn khách hàng dùng App đã đăng kí SĐT mua hàng, quét mã QR Code trên biên nhận để xác thực.", "employee_type_ID_code": "(<PERSON><PERSON>ân viên nhập hoặc quét \"Mã danh tính\" lấy từ App khách hàng thân thiết và bấm \"Xá<PERSON> nhận\")", "OTP_then_apply": "<PERSON><PERSON><PERSON><PERSON> mã OTP và chọn \"<PERSON>p dụng\"", "OTP_then_confirm": "(<PERSON><PERSON><PERSON> viên nhập mã OTP và bấm \"<PERSON><PERSON>c nhận\")", "caution": "<PERSON><PERSON><PERSON> ý: ", "for_this_order": " (<PERSON>ơn hàng nà<PERSON>)", "total_price_after_discount": "Tổng tiền sau giảm giá: ", "deposit_delivery_cost": "TIỀN CỌC & PHÍ GIAO HÀNG", "delivery_cost": "PHÍ GIAO VÀ PHÍ CHUYỂN HÀNG", "membership_score": "ĐIỂM TÍCH LŨY KHTT", "discount_product_amount": "MUA NHIỀU GIẢM NHIỀU", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "quantity": "SL", "more_discount": "<PERSON><PERSON><PERSON><PERSON> thêm", "total_discount": "Tổng giá trị giảm", "total_more_discount": "Tổng giá trị giảm thêm: ", "more_discount_caution": "Lưu ý: <PERSON><PERSON><PERSON> trị giảm thêm đư<PERSON><PERSON> tính trên giá bán niêm yết (chưa áp dụng khuyến mãi)", "finish": "<PERSON><PERSON>", "choose_a_plan": "<PERSON><PERSON><PERSON> g<PERSON> c<PERSON>:", "apply_times": " <PERSON><PERSON><PERSON> d<PERSON>", "fee": "Phí:", "IMEI": "IMEI:", "quantity_full": "Số lượng:", "output_store": "<PERSON><PERSON>:", "btn_receive_OTP_call": "<PERSON>hận <PERSON> gọi OTP", "btn_receive_OTP_message": "Nhận tin nhắn OTP", "change_phone_number": "Đ<PERSON>i số điện thoại\nmua hàng", "condition": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>:", "promotion_until": "<PERSON>ươ<PERSON> trình k<PERSON>ến mãi còn ", "text_input_contract_code": "<PERSON><PERSON> hợp đồng: ", "lf_contract_employee": "<PERSON><PERSON><PERSON> viên làm hồ sơ: ", "lf_dealer_product_code": "<PERSON><PERSON> đại lý/<PERSON><PERSON> sản phẩm đối tác: ", "at_store": "<PERSON><PERSON><PERSON> si<PERSON>u thị", "at_home": "Tại nhà", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "bundle_sale": "<PERSON><PERSON>", "promotion_delivery_type": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi theo hình thức giao", "bundle_sale_delivery_type": "<PERSON><PERSON> kèm theo hình thức giao", "competitive_price_opponent": "<PERSON><PERSON><PERSON> thủ chiến giá:", "opponent_price": "<PERSON><PERSON><PERSON> đối thủ", "MWG_price": "<PERSON><PERSON><PERSON> bán MWG", "opponent_inStock": "<PERSON><PERSON><PERSON> kho đối thủ", "take_front_ID_picture": "<PERSON><PERSON><PERSON> hình CMND/CCCD mặt trước:", "take_front_student_card_picture": "<PERSON><PERSON><PERSON> hình thẻ sinh viên mặt trước:", "take_front_grab_driver_picture": "<PERSON><PERSON><PERSON> hình <PERSON>ng dụng tài xế:", "take_front_exam_score": "<PERSON><PERSON><PERSON> hình giấy báo điểm hoặc giấy báo dự thi (có đầy đủ Họ tên và Số báo danh):", "placeholder_coupon": "Nhập Coupon", "placeholder_OTP": "<PERSON>hậ<PERSON> mã OTP", "placeholder_ID_code": "<PERSON><PERSON><PERSON><PERSON> mã định danh", "placeholder_IMEI": "Nhập IMEI SIM", "btn_skip_uppercase": "BỎ QUA", "btn_continue_uppercase": "TIẾP TỤC", "picker_opponent": "<PERSON><PERSON><PERSON> đ<PERSON>i thủ", "note": "<PERSON><PERSON> chú: ", "contact_address": "Đ<PERSON>a chỉ liên hệ: ", "employee": "Nhân viên: ", "department": "Bộ phận: ", "position": "<PERSON><PERSON><PERSON> v<PERSON>: ", "ft_name": "<PERSON><PERSON> và tên:", "delivery_fee": "<PERSON><PERSON> chuyển hàng: ", "deposit": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>: ", "deposit_time": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>i cọc trước: ", "deposit_amount": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>i trả trước: ", "deposit_rate": "Tỉ lệ cọc: ", "shipping_fee": "<PERSON><PERSON> giao hàng: ", "accumulated_score": "<PERSON><PERSON><PERSON><PERSON> tích <PERSON>:", "male": "Nam", "female": "<PERSON><PERSON>", "cannot_information_user": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin User điều chỉnh giá", "upload_image_error": "Lỗi upload hình <PERSON>nh", "cannot_data_price": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> đ<PERSON><PERSON><PERSON> dữ liệu chiến giá", "no_verified_information": "<PERSON><PERSON>ông có thông tin xác thực", "code_verified_incorrect": "<PERSON><PERSON> xác thực không đúng", "cannot_information_IMEI_SIM": "Không có thông tin IMEI SIM.", "no_package_information": "<PERSON><PERSON><PERSON>ng có thông tin gói cước", "no_information_payment_month": "<PERSON><PERSON><PERSON><PERSON> có thông tin số tiền trả hàng tháng.", "btn_retry_uppercase": "THỬ LẠI", "delete_information_cart": "Bạn có chắc muốn xóa thông tin giỏ hàng hiện tại?", "please_enter_coupon": "<PERSON><PERSON> lòng nhập mã Coupon", "notification_update_cart": "Giỏ hàng đang được điều chỉnh giá hoặc chiến giá, nên không thể áp dụng mã giảm giá. Nếu muốn áp dụng mã giảm giá n<PERSON>, bạn vui lòng hủy điều chỉnh giá hoặc chiến giá.", "notification_cart_coupon": "Giỏ hàng đã áp dụng mã Coupon giảm giá, nên không thể điều chỉnh giá. Vui lòng hủy Coupon giảm giá để điều chỉnh giá!", "please_enter_tax_code": "<PERSON><PERSON> lòng nhập mã số thuế công ty", "validation_tax": "<PERSON>ã số thuế phải đúng 10 ký tự số, hoặc đúng 14 ký tự, trong đó ký tự thứ 11 là dấu g<PERSON>ch ngang, các ký tự còn lại là số.", "validation_tax_cam": "<PERSON>ã số thuế phải đúng 14 ký tự, trong đó ký tự đầu tiên là chữ, ký tự thứ 5 là dấu gạch ngang, các ký tự còn lại là số.", "validation_company_name": "<PERSON><PERSON> lòng nhập tên <PERSON> ty", "validation_company_address": "<PERSON><PERSON> lòng nhập địa chỉ công ty", "validation_customer_name": "<PERSON><PERSON> lòng nhập họ và tên khách hàng", "validation_phone_number": "<PERSON><PERSON> lòng nhập số điện thoại đúng 10 chữ số", "validation_ID_number": "<PERSON><PERSON> lòng nhập số CMND/CCCD", "validation_phone_number_1": "<PERSON><PERSON> lòng nhập đúng đầu số điện thoại", "validation_gender": "<PERSON><PERSON> lòng chọn giới t<PERSON>h kh<PERSON>ch hàng.", "validation_phone_number_rim": "<PERSON><PERSON> lòng nhập số điện thoại", "validation_CMND_rim": "<PERSON><PERSON> lòng nhập số CMND/CCCD", "validation_CMND_number": "<PERSON><PERSON> lòng nhập số CMND/CCCD đúng 9 hoặc 12 chữ số", "validation_CARD_ID_number": "<PERSON><PERSON> lòng <PERSON> tra số CMND/CCCD trư<PERSON><PERSON> khi tạo đơn hàng.", "validation_image": "<PERSON><PERSON> lòng cập nhật đ<PERSON>y đủ hình <PERSON>nh bổ sung", "validation_prepayment": "<PERSON><PERSON> lòng nhập số tiền trả trước", "validation_email": "<PERSON><PERSON> l<PERSON>.", "please_choose_term_loan": "<PERSON><PERSON> lòng chọn kỳ hạn vay", "please_choose_relation_ship_type": "<PERSON>ui lòng chọn mối quan hệ với khách hàng. <PERSON><PERSON><PERSON> có thắc mắc vui lòng liên hệ với Cấp trên của Bạn hoặc user 17269 - T<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "please_enter_information_IMEI_SIM": "<PERSON><PERSON> lòng nhập thông tin IMEI của sản phẩm SIM", "warning_no_enter_phone_number_customer": "Bạn đang tạo đơn hàng mà KHÔNG NHẬP SỐ ĐIỆN THOẠI của khách hàng. Số điện thoại có thể ảnh hưởng đến thông tin tra cứu bả<PERSON> hành, hóa đơn điện tử, tích lũy điểm thành viên...\nBạn có muốn tiếp tục tạo đơn hàng?", "warning_no_choose_promotion": "<PERSON><PERSON> chương trình khuyến mãi chưa đượ<PERSON> chọn. Bạn có chắc muốn bỏ qua khuyến mãi ?", "sure_create_order": "Bạn có chắc muốn tạo đơn hàng?", "validation_promotion": "<PERSON><PERSON> lòng chọn khuyến mãi BẮT BUỘC chọn:", "warning_delete_order": "<PERSON>óa sản phẩm khỏi giỏ hàng sẽ xóa mã giảm giá và khuyến mãi liên quan sản phẩm. Bạn có muốn xóa:", "out_card": "khỏi giỏ hàng?", "out_list": "khỏi danh sách bán kè<PERSON>?", "no_adjust_price": "không có quyền điều chỉnh giá", "please_enter_adjusted_price": "<PERSON><PERSON> lòng nhập giá điều chỉnh thấp hơn giá bán", "please_enter_reason_adjust_price": "<PERSON><PERSON> lòng nhập nội dung điều chỉnh giá", "reduce": "<PERSON><PERSON><PERSON><PERSON>", "augment": "<PERSON><PERSON><PERSON>", "cannot_adjust_price": "kh<PERSON>ng có quyền tạo chiến giá", "please_upload_mandatory_file": "<PERSON><PERSON> lòng cập nh<PERSON>t đủ 2 file đ<PERSON><PERSON> kèm bắt buộc", "description_scan_identity_1": "(<PERSON><PERSON> lòng yêu cầu nhân viên đượ<PERSON> phép điều chỉnh giá bán mở app MWG, bấm vào logo Thế Giới Di Động bên góc trái màn hình, chọn vào “Mã danh tính”)", "description_scan_identity_2": "(<PERSON><PERSON> lòng yêu cầu nhân viên đư<PERSON><PERSON> phép duyệt chiến giá bán mở app MWG, bấm vào logo Thế Giới Di Động bên góc trái màn hình, chọn vào “Mã danh tính”)", "attachment_must_less_than_3MB": "File đ<PERSON>h kèm phải có dung lượng dưới 3Mb.", "file_format": "<PERSON><PERSON><PERSON> dạng file", "not_supported": "kh<PERSON>ng được hỗ trợ.", "no_information_adjust_price": "<PERSON><PERSON><PERSON><PERSON> có thông tin đối thủ chiến giá", "validate_empty_otp": "<PERSON><PERSON> lòng nh<PERSON>p mã OTP", "validate_otp": "<PERSON><PERSON> lòng nhập mã O<PERSON> đúng 4 chữ số", "validate_otp_4": "<PERSON><PERSON> lòng nhập mã O<PERSON> đúng 4 chữ số", "installment_otp_guide": "Mã OTP này dùng để xác thực việc khách hàng đã đồng ý ủy quyền cho TGDĐ thu thập và chuyển giao thông tin cho công ty tài chính để làm hồ sơ trả góp.", "pin_otp_guide": "Mã OTP này dùng để xác thực việc khách hàng đã đồng ý thay pin Đồng Hồ.", "sticker_otp_guide": "Mã OTP này dùng để xác thực việc khách hàng đã đồng ý dán lại <PERSON>.", "add_sale_otp_guide": "Mã OTP này dùng để xác thực việc khách hàng mua đơn hàng chính đã đồng ý cho việc tạo mới đơn hàng bổ sung mua kèm này.", "otp_sent": "Mã OTP đã đư<PERSON><PERSON> gửi đến số", "please_enter_id_code": "<PERSON><PERSON> lòng nhập mã định danh", "please_enter_4_digits_id_code": "<PERSON><PERSON> lòng nhập mã định danh đúng 4 chữ số", "skip_authentication": "Bạn muốn bỏ qua xác thực mua hàng?", "guide_1": "1. <PERSON><PERSON><PERSON><PERSON> hàng khi mua hàng tại MWG sẽ được tích điểm khách hàng thân thiết.", "guide_2": "2. T<PERSON><PERSON><PERSON> tin mua hàng cần qua bước xác thực để được tích điểm.", "guide_3": "3. <PERSON><PERSON><PERSON> viên cần hỗ trợ khách hàng thực hiện xác thực khi mua hàng để đảm bảo quyền lợi cho khách hàng.", "note_description": "Th<PERSON><PERSON>/xo<PERSON>/sử<PERSON>, <PERSON><PERSON><PERSON> sang tr<PERSON> góp, hay tạo 2 đơn hàng cùng lúc đều có thể dẫn tới khách hàng không được áp dụng điểm !!!", "guide_4": "\t1. <PERSON><PERSON><PERSON> cảm ơn khách hàng đã gắn bó và ủng hộ công ty.", "guide_5": "\t2. <PERSON><PERSON><PERSON> thông tin về ưu đãi chỉ dành riêng cho KH thân thiết (bên dướ<PERSON>). KH sẽ không được áp dụng mức giảm giá này khi thay đổi sản phẩm, gi<PERSON> trị đơn hàng.", "cannot_apply_promotion": " - Chương trình khuyến mãi này không áp dụng đồng thời với chương trình: ", "male_1": "<PERSON><PERSON>", "female_1": "Chị", "yourself": "Đơn hàng của CHÍNH MÌNH", "your_friend": "Đơn hàng mà nhân viên CÓ QUEN BIẾT VỚI KHÁCH HÀNG (<PERSON><PERSON>, <PERSON><PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON> nghi<PERSON>, <PERSON><PERSON><PERSON> nghi<PERSON> c<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> xó<PERSON>...)", "outer_customer": "Đơn hàng của KHÁCH HÀNG BÊN NGOÀI", "please_take_full_info_picture": "<PERSON><PERSON> lòng chụp đủ thông tin hình <PERSON>nh", "please_enter_imei_product": "<PERSON><PERSON> lòng nhập IMEI sản phẩm", "please_enter_serial_product": "<PERSON><PERSON> lòng nhập SERIAL sản phẩm", "imei_already_exists": "IMEI đã tồn tại trong đơn hàng", "placeholder_warranty": "Nhập IMEI Bảo <PERSON>", "pls_enter_total_prepaid": "<PERSON><PERSON> lòng nhập số tiền trả trước.", "please_enter_id": "<PERSON><PERSON> lòng nhập mã đại lý/<PERSON><PERSON> sản phẩm đối tác", "existed_app_loyalty": "<PERSON><PERSON><PERSON>ch hàng đã cài đặt app QUÀ TẶNG VIP.", "no_install_app_loyalty": "<PERSON><PERSON><PERSON><PERSON> hàng chưa cài đặt app QUÀ TẶNG VIP.", "customer_phone": "Bạn đang tạo đơn hàng mà KHÔNG NHẬP SỐ ĐIỆN THOẠI của khách hàng. Số điện thoại có ảnh hưởng đến thông tin tra cứu bả<PERSON> hành, hoá đơn điện tử, tích luỹ điểm thành viên... Bạn có muốn tiếp tục tạo đơn hàng?", "staff_info_uppercase": "THÔNG TIN NHÂN VIÊN", "text_input_buyer_phone": "<PERSON><PERSON> điện thoại người mua:", "text_input_buyer_name": "<PERSON><PERSON> tên ng<PERSON><PERSON>i mua:"}, "provincemain": {"mess_error": "<PERSON>hông có thông tin Tỉnh/Thành", "mess_error1": "<PERSON><PERSON><PERSON><PERSON> có thông tin <PERSON>/Huyện", "mess_error2": "K<PERSON><PERSON>ng có thông tin Phường/Xã", "mess_error3": "<PERSON><PERSON><PERSON><PERSON> có thông tin quốc tịch"}, "editSaleOrder": {"no_find_store": "<PERSON><PERSON><PERSON><PERSON> tìm đ<PERSON><PERSON><PERSON> siêu thị để lấy hàng", "program": "<PERSON><PERSON><PERSON><PERSON> trình ", "exsit_store": "kh<PERSON>ng tồn tại ở kho xuất ", "check_phone_number_uppercase": "KIỂM TRA SỐ ĐIỆN THOẠI", "phone_number_apply_promotion": "<PERSON><PERSON> điện thoại áp dụng khu<PERSON>ến mãi:", "btn_done": "<PERSON><PERSON>", "cannot_apply_promotion": " - Chương trình khuyến mãi này không áp dụng đồng thời với chương trình: ", "promotion_has": "<PERSON>ươ<PERSON> trình k<PERSON>ến mãi còn ", "time_remained": " <PERSON><PERSON><PERSON> d<PERSON>", "text_input_search_name": "<PERSON><PERSON><PERSON><PERSON> mã SP, Tên SP", "text_input_phone_number": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "please_select_promotion": "<PERSON><PERSON> lòng chọn khuyến mãi BẮT BUỘC chọn:", "dismiss_promotion": "<PERSON><PERSON> chương trình khuyến mãi chưa đượ<PERSON> chọn. Bạn có chắc muốn bỏ qua khuyến mãi ?", "please_enter_phone_number": "<PERSON><PERSON> lòng nhập số điện thoại", "please_follow_phone_number_format": "<PERSON><PERSON> lòng nhập đúng định dạng số điện thoại", "btn_apply": "<PERSON><PERSON>", "delivery_at_home": "Giao tại nhà:", "delivery_at_store": "<PERSON><PERSON>o tại siêu thị:", "contact_address": "Đ<PERSON>a chỉ liên hệ: ", "contact_name": "<PERSON><PERSON> tên ngư<PERSON>i liên hệ: ", "promotion_by_delivery_method": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi theo hình thức giao", "sale_with_delivery_method": "<PERSON><PERSON> kèm theo hình thức giao", "attached_product_promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi sản phẩm bán kèm", "quantity_short": "SL", "condition": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>:", "notice": "<PERSON><PERSON><PERSON>", "employer_adjust_price": "Quét mã nhân viên đư<PERSON><PERSON> phép điều chỉnh giá bán:", "description_scan_identity_2": "(<PERSON><PERSON> lòng yêu cầu nhân viên đư<PERSON><PERSON> phép duyệt chiến giá bán mở app MWG, bấm vào logo Thế Giới Di Động bên góc trái màn hình, chọn vào “Mã danh tính”)", "limit_remain": "<PERSON><PERSON><PERSON> mức còn lại ", "temporary": "(tạm t<PERSON>)", "current_price": "<PERSON><PERSON><PERSON> b<PERSON> hiện tại:", "discount_value": "<PERSON><PERSON><PERSON> trị giảm:", "adjusted_price": "Giá sau chiến giá:", "add_file": "Thêm file đ<PERSON>h kèm: ", "max_files": "(Max: 3 files)", "btn_update": "<PERSON><PERSON><PERSON>", "btn_delete": "Xóa", "btn_add_file": "Thêm file", "btn_continue_uppercase": "TIẾP TỤC", "please_enter_adjusted_price": "<PERSON><PERSON> lòng nhập giá điều chỉnh thấp hơn giá bán", "please_upload_mandatory_file": "<PERSON><PERSON> lòng cập nh<PERSON>t đủ 2 file đ<PERSON><PERSON> kèm bắt buộc", "agent": "Nhân viên: ", "department": "Bộ phận: ", "position": "<PERSON><PERSON><PERSON> v<PERSON>: ", "attachment_must_less_than_3MB": "File đ<PERSON>h kèm phải có dung lượng dưới 3Mb.", "picker_opponent": "<PERSON><PERSON><PERSON> đ<PERSON>i thủ", "opponent": "<PERSON><PERSON><PERSON> thủ chiến giá:", "opponent_price": "<PERSON><PERSON><PERSON> đối thủ", "MWG_price": "<PERSON><PERSON><PERSON> bán MWG", "opponent_inventory": "<PERSON><PERSON><PERSON> kho đối thủ", "cannot_adjust_price": "kh<PERSON>ng có quyền tạo chiến giá", "file_format": "<PERSON><PERSON><PERSON> dạng file", "not_supported": "kh<PERSON>ng được hỗ trợ.", "detail_specification_uppercase": "THÔNG SỐ CHI TIẾT", "detail_specification": "<PERSON><PERSON><PERSON><PERSON> số\nchi tiết", "outstanding_features_uppercase": "ĐẶC ĐIỂM NỔI BẬT", "outstanding_features": "Đặc điểm\nnổi bật", "color": "Màu: ", "date_uppercase": "NGÀY", "quantity_uppercase": "SỐ LƯỢNG", "fifo_stock_info_uppercase": "THÔNG TIN TỒN KHO FIFO", "phone_check_uppercase": "KIỂM TRA SỐ ĐIỆN THOẠI", "promotion_phone_number": "<PERSON><PERSON> điện thoại áp dụng khu<PERSON>ến mãi:", "finish": "<PERSON><PERSON>", "btn_add_product": "THÊM SẢN PHẨM", "new": "<PERSON><PERSON><PERSON>", "used": "Đã sử dụng", "display": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>y", "no_official_warranty": "<PERSON><PERSON><PERSON><PERSON> có thông tin bảo hành ch<PERSON>h hãng", "FIFO": "FIFO", "IMEI": "IMEI", "setup_consult": "<PERSON><PERSON> vấn\nlắp đặt", "installment_consult": "<PERSON><PERSON> vấn\ntrả góp", "exact_bonus": "<PERSON><PERSON><PERSON><PERSON> thưởng chuẩn", "btn_update_uppercase": "CẬP NHẬT", "transfer_store": "<PERSON><PERSON> chuy<PERSON> hàng:", "delivery_method": "<PERSON><PERSON><PERSON> thức giao", "delivery_method:": "<PERSON><PERSON><PERSON> thức giao: ", "select_date": "<PERSON><PERSON><PERSON>", "select_time": "<PERSON><PERSON><PERSON> thời gian:", "select_hour": "<PERSON><PERSON><PERSON> g<PERSON>", "expired_time": "Thời gian chuyển hàng về vượt quá Thời gian giữ hàng tối đa cho phép của Loại yêu cầu xuất. Hoặc hệ thống không lấy được tải giao hàng", "old_customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng c<PERSON>", "text_input_name": "<PERSON><PERSON><PERSON><PERSON> họ tên", "text_input_address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "please_enter_contact_phone_number": "<PERSON><PERSON> lòng nhập số điện thoại liên hệ", "please_enter_10_digits_phone_number": "<PERSON><PERSON> lòng đúng định dạng số điện thoại 10 số", "please_enter_correct_phone_number_header": "<PERSON><PERSON> lòng nhập đúng đầu số điện thoại", "please_enter_contact_name": "<PERSON><PERSON> lòng nhập họ tên người liên hệ", "please_enter_delivery_address": "<PERSON><PERSON> lòng nhập địa chỉ giao hàng", "please_select_delivery_time": "<PERSON><PERSON> lòng chọn thời gian hẹn giao", "btn_at_home": "Tại nhà", "output_store": "<PERSON><PERSON>: ", "distance": "Kho<PERSON>ng cách: ", "vehicle": "Phương tiện: ", "shipping_fee": "<PERSON><PERSON> giao hàng: ", "btn_at_store": "<PERSON><PERSON><PERSON> si<PERSON>u thị", "debt_must_be_0": "Số tiền còn nợ phải bằng 0", "change_request_with_expense": "CHỈNH SỬA YÊU CẦU XUẤT CÓ CHI TIỀN", "expense": "THỰC CHI", "voucher": "<PERSON><PERSON><PERSON> mua hàng", "vip_point": "<PERSON><PERSON><PERSON><PERSON> quà tặng VIP", "qr_cash": "Tiền QR", "credit": "Ti<PERSON>n cà thẻ", "receive_cash_at_store": "<PERSON><PERSON> trự<PERSON> tiếp ở siêu thị để nhận tiền", "create_expense_note": "Check vào đ<PERSON>, h<PERSON> thộng sẽ tự động tạo phiếu chi cho bạn", "cash": "Tiền mặt", "debt": "<PERSON><PERSON><PERSON> nợ", "old_output_request": "<PERSON><PERSON><PERSON> c<PERSON>u xuất cũ:", "receipt": "<PERSON><PERSON><PERSON><PERSON> đã thu", "available_cash": "<PERSON><PERSON><PERSON><PERSON> có thể mang qua", "without_voucher": "\n(<PERSON><PERSON><PERSON><PERSON> gồ<PERSON>)", "new_output_request": "<PERSON><PERSON><PERSON> c<PERSON>u xuất mới:", "must_receive": "<PERSON><PERSON> tiền cần thu vào", "expense_for_customer": "<PERSON><PERSON><PERSON> chi cho khách:", "charge": "<PERSON><PERSON><PERSON><PERSON> dư sau khi thu", "voucher_restore": "PMH <PERSON>", "must_expend": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>i chi", "invalid_voucher": "PHIẾU MUA HÀNG ĐÃ SỬ DỤNG", "payment": "<PERSON><PERSON><PERSON><PERSON>h to<PERSON>:", "output_request_applied": "YCX Áp dụng:", "please_select_pos": "<PERSON><PERSON> lòng chọn máy P<PERSON>", "please_enter_expense_code": "<PERSON><PERSON> lòng nhập mã chuẩn chi", "expense_code_must_have_6_letters": "<PERSON><PERSON> chuẩn chi phải đúng 6 ký tự", "please_enter_payment": "<PERSON><PERSON> lòng nhập số tiền thanh toán", "expense_more_than_must_expend": "<PERSON><PERSON><PERSON> thực chi đang lớn hơn số tiền phải chi", "existed_expense_code": "<PERSON><PERSON> chuẩn chi đã tồn tại trong đơn hàng", "pos": "Máy POS: ", "select_pos": "<PERSON><PERSON><PERSON> m<PERSON>", "text_input_appv_code": "Nhập mã APPV", "get_appv_code": "<PERSON><PERSON><PERSON> l<PERSON>y APPV Code", "transaction_confirm": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "add_card": "Thê<PERSON> thẻ", "output_request_short": "YCX", "please_select_cancel_reason": "<PERSON><PERSON> lòng chọn lý do hủy", "please_enter_cancel_content": "<PERSON><PERSON> lòng nh<PERSON>p nội dung hủy", "confirm_instalment_uppercase": "XÁC NHẬN HỦY HỒ SƠ TRẢ GÓP", "cancel_reason": "Lý do hủy:", "select_reason": "Chọn lý do", "text_input_cancel_content": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i dung hủy", "scan_agent_code": "<PERSON><PERSON>t mã nhân viên đ<PERSON><PERSON><PERSON> phép tạo yêu cầu huỷ hồ sơ trả góp:", "invalid_agent_code": "(<PERSON><PERSON> lòng yêu cầu nhân viên có quyền xác nhận tạo yêu cầu huỷ hồ sơ trả góp mở app MWG, bấm vào logo Thế Giới Di Động ở góc trái màn hình, chọn vào “Mã danh tính”)", "agent_information": "Thông tin nhân viên:", "agent_id": "Mã NV: ", "sale_order": "<PERSON><PERSON><PERSON> hàng ", "record": " c<PERSON> hồ sơ ", "state": " đang ở trạng thái ", "confirm_information": ". <PERSON><PERSON><PERSON> cần x<PERSON>c nhận thông tin để đư<PERSON><PERSON> hủy hồ sơ trả góp", "reason_7": "<PERSON><PERSON><PERSON><PERSON> hàng không mua nữa tự động hủy", "reason_4": "<PERSON><PERSON><PERSON><PERSON> hàng thay đổi trả trước hoặc kỳ hạn vay", "reason_5": "Người nhà khách hàng không đồng ý", "reason_6": "<PERSON><PERSON><PERSON> đồng gian lận", "reason_1": "<PERSON><PERSON><PERSON><PERSON> sai thông tin", "reason_2": "<PERSON><PERSON><PERSON><PERSON> hàng thay đổi sản phẩm", "reason_3": "<PERSON><PERSON><PERSON><PERSON> hàng không mua n<PERSON>a", "receipt_expense_information": "THÔNG TIN TẠO PHIẾU THU CHI", "available_expense": "<PERSON><PERSON><PERSON><PERSON> có thể chi ra", "alternative_output_request": "<PERSON><PERSON><PERSON> c<PERSON>u xuất thay thế:", "deposit_delivery_cost_uppercase": "TIỀN CỌC & PHÍ GIAO HÀNG", "product_price": "<PERSON><PERSON><PERSON><PERSON> tiền: ", "delivery_fee": "<PERSON><PERSON> chuyển hàng: ", "deposit": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>: ", "deposit_time": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>i cọc trước: ", "deposit_rate": "Tỉ lệ cọc: ", "deposit_amount": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>i trả trước: ", "delivery_cost_uppercase": "PHÍ GIAO VÀ PHÍ CHUYỂN HÀNG", "additional_fee": "Phụ phí: ", "additional_fee_2": "Phụ phí", "membership_score_uppercase": "ĐIỂM TÍCH LŨY KHTT", "discount_product_amount_uppercase": "MUA NHIỀU GIẢM NHIỀU", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "additional_discount": "<PERSON><PERSON><PERSON><PERSON> thêm", "total_discount": "Tổng giá trị giảm", "additional_discount_caution": "Lưu ý: <PERSON><PERSON><PERSON> trị giảm thêm đư<PERSON><PERSON> tính trên giá bán niêm yết (chưa áp dụng khuyến mãi)", "text_input_IMEI_SIM": "Nhập IMEI SIM", "select_package": "<PERSON><PERSON><PERSON> g<PERSON> c<PERSON>:", "please_enter_otp": "<PERSON><PERSON> lòng nh<PERSON>p mã OTP", "please_enter_6_digits_otp": "<PERSON><PERSON> lòng nhập mã O<PERSON> đ<PERSON>g 6 chữ số", "please_enter_4_digits_otp": "<PERSON><PERSON> lòng nhập mã O<PERSON> đúng 4 chữ số", "please_enter_id_code": "<PERSON><PERSON> lòng nhập mã định danh", "please_enter_4_digits_id_code": "<PERSON><PERSON> lòng nhập mã định danh đúng 4 chữ số", "create_order_successful": "<PERSON><PERSON><PERSON> đơn hàng thành công", "btn_skip_verify": "Bỏ qua xác thực", "customer_info_uppercase": "THÔNG TIN KHÁCH HÀNG", "full_name": "<PERSON><PERSON> và tên:", "customer_phone_number": "<PERSON><PERSON> điện thoại:", "accumulated_score": "<PERSON><PERSON><PERSON><PERSON> tích <PERSON>:", "guide": "Hướng dẫn:", "guide_1": "1. <PERSON><PERSON><PERSON><PERSON> hàng khi mua hàng tại MWG sẽ được tích điểm khách hàng thân thiết.", "guide_2": "2. T<PERSON><PERSON><PERSON> tin mua hàng cần qua bước xác thực để được tích điểm.", "guide_3": "3. <PERSON><PERSON><PERSON> viên cần hỗ trợ khách hàng thực hiện xác thực khi mua hàng để đảm bảo quyền lợi cho khách hàng.", "text_input_id_code": "<PERSON><PERSON><PERSON><PERSON> mã định danh", "confirm_information_uppercase": "XÁC NHẬN THÔNG TIN", "text_input_otp": "<PERSON>hậ<PERSON> mã OTP", "otp_guide_1": "<PERSON><PERSON><PERSON><PERSON> mã OTP và chọn \"<PERSON>p dụng\"", "otp_guide_2": "(<PERSON><PERSON>ân viên nhập hoặc quét \"Mã danh tính\" lấy từ App khách hàng thân thiết và bấm \"Xá<PERSON> nhận\")", "otp_sent": "Mã OTP đã đư<PERSON><PERSON> gửi đến số", "so_code": "\nMã SO:", "btn_change_phone_number": "Đ<PERSON>i số điện thoại\nmua hàng", "skip_authentication": "Bạn muốn bỏ qua xác thực mua hàng?", "btn_call_otp": "<PERSON>hận <PERSON> gọi OTP", "btn_send_otp": "Nhận tin nhắn OTP", "btn_agree_use": "Đồng ý sử dụng", "btn_disagree_use": "<PERSON><PERSON><PERSON><PERSON> đồng <PERSON>", "caution": "<PERSON><PERSON><PERSON> ý: ", "note_description": "Th<PERSON><PERSON>/xo<PERSON>/sử<PERSON>, <PERSON><PERSON><PERSON> sang tr<PERSON> góp, hay tạo 2 đơn hàng cùng lúc đều có thể dẫn tới khách hàng không được áp dụng điểm !!!", "for_this_order": " (<PERSON>ơn hàng nà<PERSON>)", "total_price_after_discount": "Tổng tiền sau giảm giá: ", "guide_success_1": "\t1. <PERSON><PERSON><PERSON> cảm ơn khách hàng đã gắn bó và ủng hộ công ty.", "guide_success_2": "\t2. <PERSON><PERSON><PERSON> thông tin về ưu đãi chỉ dành riêng cho KH thân thiết (bên dướ<PERSON>). KH sẽ không được áp dụng mức giảm giá này khi thay đổi sản phẩm, gi<PERSON> trị đơn hàng.", "enter_otp_then_confirm": "(<PERSON><PERSON><PERSON> viên nhập mã OTP và bấm \"<PERSON><PERSON>c nhận\")", "mr": "<PERSON><PERSON>", "ms": "Chị", "full_payment": "Trả thẳng", "instalment": "Trả góp", "yourself": "Đơn hàng của CHÍNH MÌNH", "your_friend": "Đơn hàng mà nhân viên CÓ QUEN BIẾT VỚI KHÁCH HÀNG (<PERSON><PERSON>, <PERSON><PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON> nghi<PERSON>, <PERSON><PERSON><PERSON> nghi<PERSON> c<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> xó<PERSON>...)", "outer_customer": "Đơn hàng của KHÁCH HÀNG BÊN NGOÀI", "confirm_relationship_with_customer": "<PERSON><PERSON><PERSON> nhận mối quan hệ giữa Bạn và <PERSON> hàng:", "hot_bonus": "<PERSON><PERSON><PERSON><PERSON> thưởng nóng", "provisional_membership_point": "<PERSON><PERSON><PERSON><PERSON> tích lũy khách hàng thân thiết tạm tính", "choose_a_plan": "<PERSON><PERSON><PERSON> g<PERSON> c<PERSON>:", "note": "<PERSON><PERSON><PERSON>", "warranty_at_store": "<PERSON><PERSON><PERSON> hành tại TGDĐ: ", "warranty_time": "<PERSON><PERSON><PERSON> hành ch<PERSON>h hãng đến ngày: ", "accessory": "<PERSON><PERSON> kiện: ", "time_out": "<PERSON><PERSON><PERSON><PERSON> gian hết hạn giữ hàng: ", "non": "<PERSON><PERSON><PERSON><PERSON> có", "btn_confirm_uppercase": "XÁC NHẬN", "adjust": "Điều chỉnh:", "price_after_adjust": "Giá sau điều chỉnh:", "adjust_price_reason": "<PERSON>ý do điều chỉnh:", "total_more_discount": "Tổng giá trị giảm thêm: ", "please_enter_reason_adjust_price": "<PERSON><PERSON> lòng nhập nội dung điều chỉnh giá", "please_select_sim_package": "<PERSON><PERSON> lòng chọn gó<PERSON> cước SIM", "not_enough_inventory": "<PERSON><PERSON><PERSON> phẩm không đủ tồn kho", "MWG_mark": "Copyright © 2019 MWG. <PERSON><PERSON><PERSON> ty cổ phần Th<PERSON> G<PERSON>ới Di Động", "search_history": "<PERSON><PERSON><PERSON> sử tìm kiếm: ", "sale_version": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON> h<PERSON>ng", "you_have": "Bạn c<PERSON>", "fav_products": "sản phẩm yêu th<PERSON>ch: ", "placeholder_search_product": "<PERSON><PERSON><PERSON><PERSON> mã SP, Tên SP, IMEI, SIM", "are_you_sure_delete": "Bạn có chắc muốn xóa", "from_fav_list": "khỏi danh sách yêu thích?", "ft_name": "<PERSON><PERSON> và tên:", "ft_phone": "<PERSON><PERSON> điện thoại:", "installment_otp_guide": "Mã OTP này dùng để xác thực việc khách hàng đã đồng ý ủy quyền cho TGDĐ thu thập và chuyển giao thông tin cho công ty tài chính để làm hồ sơ trả góp.", "pin_otp_guide": "Mã OTP này dùng để xác thực việc khách hàng đã đồng ý thay pin Đồng Hồ.", "sticker_otp_guide": "Mã OTP này dùng để xác thực việc khách hàng đã đồng ý dán lại <PERSON>.", "btn_receive_OTP_call": "<PERSON>hận <PERSON> gọi OTP", "btn_receive_OTP_message": "Nhận tin nhắn OTP", "OTP_then_confirm": "(<PERSON><PERSON><PERSON> viên nhập mã OTP và bấm \"<PERSON><PERSON>c nhận\")", "placeholder_OTP": "<PERSON>hậ<PERSON> mã OTP", "OTP_alert": "<PERSON><PERSON> lòng nh<PERSON>p mã OTP", "OTP_full_alert": "<PERSON><PERSON> lòng nhập mã O<PERSON> đúng 4 chữ số", "create_order_successfully": "<PERSON><PERSON><PERSON> đơn hàng thành công", "SO_code": "Mã SO:", "EP_code": "Mã EP:", "take_front_ID_picture": "<PERSON><PERSON><PERSON> hình CMND/CCCD mặt trước:", "take_front_student_card_picture": "<PERSON><PERSON><PERSON> hình thẻ sinh viên mặt trước:", "take_front_grab_driver_picture": "<PERSON><PERSON><PERSON> hình <PERSON>ng dụng tài xế:", "please_take_full_info_picture": "<PERSON><PERSON> lòng chụp đủ thông tin hình <PERSON>nh", "SIM_registration_procedure_uppercase": "THỦ TỤC ĐĂNG KÝ SIM", "ID_requirement": "BẢN GỐC CMND (cấp dưới 15 năm) hoặc Căn cước công dân (còn thời hạn) hoặc <PERSON><PERSON> chiếu (còn thời hạn) của chủ thuê bao.", "customer_portrait": "Ảnh chân dung của chủ thuê bao tại thời điểm giao dịch.", "SIM_plan_info_uppercase": "THÔNG TIN GÓI CƯỚC", "total_promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi tổng đơn", "discount_product_amount_promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi mua nhiều gi<PERSON>m nhiều", "use_Coupon": "Sử dụng coupon", "installment": " (Trả góp)", "product_not_valid_for_order_promotion": "(<PERSON><PERSON><PERSON> phẩm không tham gia vào khuyến mãi tổng đơn)", "not_rounded": "(<PERSON><PERSON><PERSON> làm tròn)", "type_IMEI_SIM": "Nhập IMEI SIM", "fee": "Phí:", "quantity_full": "Số lượng:", "text_input_contract_code": "<PERSON><PERSON> hợp đồng: ", "lf_contract_employee": "<PERSON><PERSON><PERSON> viên làm hồ sơ: ", "lf_dealer_product_code": "<PERSON><PERSON> đại lý/<PERSON><PERSON> sản phẩm đối tác: ", "lf_cost_contract_colon": "<PERSON><PERSON> làm hồ sơ: ", "lf_deposit": "Số tiền trả trước:", "lf_loan_term": "<PERSON><PERSON> hạn vay: ", "lf_payment_per_month": "<PERSON><PERSON> tiền tr<PERSON> hàng tháng: ", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "bundle_sale": "<PERSON><PERSON>", "promotion_delivery_type": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi theo hình thức giao", "bundle_sale_delivery_type": "<PERSON><PERSON> kèm theo hình thức giao", "placeholder_coupon": "Nhập Coupon", "placeholder_note": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "placeholder_phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "caution_delete_coupon": "<PERSON>óa sản phẩm khỏi giỏ hàng sẽ xóa mã giảm giá và khuyến mãi liên quan sản phẩm. Bạn có muốn xóa:", "from_the_cart": "khỏi giỏ hàng?", "from_the_bundle_list": "khỏi danh sách bán kè<PERSON>?", "no_promotion": "<PERSON><PERSON><PERSON><PERSON> có khu<PERSON>ến mãi", "no_attachments": "<PERSON><PERSON><PERSON><PERSON> có bán k<PERSON>m", "delivery_type": "<PERSON><PERSON><PERSON> thứ<PERSON> nh<PERSON>n: ", "btn_create_order": "TẠO ĐƠN", "btn_adjust_price": "Điều chỉnh giá", "btn_create_competitive_price": "<PERSON><PERSON><PERSON> chiến giá", "output_store_2": "<PERSON><PERSON>:", "amount": "Số tiền: ", "txt_done": "<PERSON><PERSON>", "products_list": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m ", "products_list_2": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "total_price": "<PERSON><PERSON><PERSON> tiền đơn hàng ", "total_customer_deposit": "Tổng tiền khách phải trả trước", "provisional_total_membership_score": "Tổng điểm tích lũy KHTT tạm tính", "customer_information": "Thông tin khách hàng", "payment_method": "<PERSON><PERSON><PERSON> thức thanh toán", "customer_print_company_bill": "<PERSON><PERSON><PERSON><PERSON> hàng in hóa đơn công ty", "text_input_contact_phone": "<PERSON><PERSON> điện thoại liên hệ:", "id_card_number": "Số CMND/CCCD: ", "take_ID_and_student_card_pictures": "CHỤP HÌNH ĐÍNH KÈM", "membership_point": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng thân thiết", "instruction": "<PERSON><PERSON> chọ<PERSON> \"Bỏ qua xác thực\" nhân viên hướng dẫn khách hàng dùng App đã đăng kí SĐT mua hàng, quét mã QR Code trên biên nhận để xác thực.", "text_input_tax_id": "<PERSON><PERSON> số thuế:", "text_input_customer_company_name": "Tên công ty:", "text_input_customer_company_address": "Địa chỉ công ty:", "text_input_customer_company_phone": "<PERSON><PERSON> điện thoại công ty:", "text_input_contact_name": "<PERSON><PERSON> tên ngư<PERSON>i liên hệ:", "text_input_contact_address": "Đ<PERSON>a chỉ người liên hệ:", "text_input_phone": "<PERSON><PERSON> điện thoại:", "text_input_name_2": "<PERSON><PERSON> tên:", "text_input_address_2": "Địa chỉ:", "text_input_id_card_number": "Số CMND/CCCD:", "text_input_cost_contract": "<PERSON><PERSON> làm hồ sơ:", "text_input_deposit": "Số tiền trả trước:", "picker_loan_term": "<PERSON><PERSON> hạn vay:", "text_input_payment_per_month": "Số tiền trả hàng tháng (số tiền tham kh<PERSON>, có thể chênh lệnh 10.000đ - ch<PERSON><PERSON> bao gồm phí thu hộ hàng tháng)", "popup_phone_coupon_requires_phone": "<PERSON>ã Coupon yêu cầu nhập số điện thoại", "color_full": "<PERSON><PERSON><PERSON>: ", "order_information_not_exist": "<PERSON><PERSON><PERSON><PERSON> có thông tin đơn hàng", "cannot_data_price": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> đ<PERSON><PERSON><PERSON> dữ liệu chiến giá", "no_store_found": "<PERSON><PERSON><PERSON><PERSON> tìm đ<PERSON><PERSON><PERSON> siêu thị để lấy hàng", "no_delivery_information_found": "<PERSON><PERSON><PERSON><PERSON> có thông tin chuyển hàng về", "no_delivery_information": "<PERSON><PERSON><PERSON><PERSON> có thông tin tải giao hàng", "placeholder_customer_tax": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "placeholder_phone_number": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "placeholder_CMND": "Nhập số CMND/CCCD", "btn_skip_uppercase": "BỎ QUA", "btn_retry_uppercase": "THỬ LẠI", "please_enter_coupon": "<PERSON><PERSON> lòng nhập mã Coupon", "notification_update_cart": "Giỏ hàng đang được điều chỉnh giá hoặc chiến giá, nên không thể áp dụng mã giảm giá. Nếu muốn áp dụng mã giảm giá n<PERSON>, bạn vui lòng hủy điều chỉnh giá hoặc chiến giá.", "notification_cart_coupon": "Giỏ hàng đã áp dụng mã Coupon giảm giá, nên không thể điều chỉnh giá. Vui lòng hủy Coupon giảm giá để điều chỉnh giá!", "notification_cart_coupon_1": "Giỏ hàng đã áp dụng mã Coupon giảm giá, nên không thể tạo chiến giá. Vui lòng hủy Coupon giảm giá để tạo chiến giá!", "please_enter_tax_code": "<PERSON><PERSON> lòng nhập mã số thuế công ty", "validation_tax": "<PERSON>ã số thuế phải đúng 10 ký tự số, hoặc đúng 14 ký tự, trong đó ký tự thứ 11 là dấu g<PERSON>ch ngang, các ký tự còn lại là số.", "validation_company_name": "<PERSON><PERSON> lòng nhập tên <PERSON> ty", "validation_company_address": "<PERSON><PERSON> lòng nhập địa chỉ công ty", "validation_customer_name": "<PERSON><PERSON> lòng nhập họ và tên khách hàng", "validation_phone_number": "<PERSON><PERSON> lòng nhập số điện thoại đúng 10 chữ số", "validation_phone_number_1": "<PERSON><PERSON> lòng nhập đúng đầu số điện thoại", "validation_phone_number_2": "<PERSON><PERSON> lòng nhập đúng định dạng số điện thoại", "validation_phone_number_rim": "<PERSON><PERSON> lòng nhập số điện thoại", "validation_CMND_rim": "<PERSON><PERSON> lòng nhập số CMND/CCCD", "validation_CMND_number": "<PERSON><PERSON> lòng nhập số CMND/CCCD đúng 9 hoặc 12 chữ số", "validation_image": "<PERSON><PERSON> lòng cập nhật đ<PERSON>y đủ hình <PERSON>nh bổ sung", "validation_prepayment": "<PERSON><PERSON> lòng nhập số tiền trả trước", "please_choose_term_loan": "<PERSON><PERSON> lòng chọn kỳ hạn vay", "please_choose_relation_ship_type": "<PERSON>ui lòng chọn mối quan hệ với khách hàng. <PERSON><PERSON><PERSON> có thắc mắc vui lòng liên hệ với Cấp trên của Bạn hoặc user 17269 - T<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "please_enter_information_IMEI_SIM": "<PERSON><PERSON> lòng nhập thông tin IMEI của sản phẩm SIM", "warning_no_enter_phone_number_customer": "Bạn đang tạo đơn hàng mà KHÔNG NHẬP SỐ ĐIỆN THOẠI của khách hàng. Số điện thoại có thể ảnh hưởng đến thông tin tra cứu bả<PERSON> hành, hóa đơn điện tử, tích lũy điểm thành viên...\nBạn có muốn tiếp tục tạo đơn hàng?", "warning_no_choose_promotion": "<PERSON><PERSON> chương trình khuyến mãi chưa đượ<PERSON> chọn. Bạn có chắc muốn bỏ qua khuyến mãi ?", "sure_create_order": "Bạn có chắc muốn tạo đơn hàng?", "validation_promotion": "<PERSON><PERSON> lòng chọn khuyến mãi BẮT BUỘC chọn:", "warning_update_order_no_phone_number_customer": "Bạn đang cập nhật đơn hàng mà KHÔNG NHẬP SỐ ĐIỆN THOẠI của khách hàng. Số điện thoại có thể ảnh hưởng đến thông tin tra cứu bảo hành, hóa đơn điện tử, tích lũy điểm thành viên...\nBạn có muốn tiếp tục cập nhật đơn hàng?", "sure_update_order": "Bạn có chắc muốn cập nhật thông tin đơn hàng hiện tại?", "update_order_successful": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn hàng thành công", "warning_update_payment": "Bạn không được phép chỉnh sửa từ tiền mặt sang trả góp.\nMuốn chọn lại trả góp vui lòng tìm kiếm lại đơn hàng", "warning_update_pay_ment_1": "Bạn không được phép chỉnh sửa từ trả góp sang tiền mặt.\nMuốn chọn lại tiền mặt vui lòng tìm kiếm lại đơn hàng", "profile_sent_partner": "<PERSON><PERSON> sơ đã gửi đối tác. <PERSON><PERSON>n không đư<PERSON><PERSON> phép đổi chươ<PERSON> sang chương trình trả góp khác.", "adjust_price": "Điều chỉnh giá:", "competitive_price": "Chiến giá:", "balance_price": "Điều chỉnh cân bằng giá khi đổi hàng:", "export_code": "Mã YC xuất: ", "change_store_code": "Mã YC chuyển kho: ", "exchange_code": "Mã YC chuyển đổi hàng hóa: ", "cancel_code": "<PERSON><PERSON> đề nghị hủy k<PERSON>ch ho<PERSON>: ", "commodity_record": "<PERSON><PERSON><PERSON><PERSON> bản hàng hóa: ", "no_description_information": "<PERSON><PERSON><PERSON>ng có thông tin mô tả", "no_adjust_price": "không có quyền điều chỉnh giá", "discount_1": "<PERSON><PERSON><PERSON><PERSON>", "augment": "<PERSON><PERSON><PERSON>", "no_information_adjust_price": "<PERSON><PERSON><PERSON><PERSON> có thông tin đối thủ chiến giá", "text_input_buyer_phone": "<PERSON><PERSON> điện thoại người mua:", "text_input_buyer_name": "<PERSON><PERSON> tên ng<PERSON><PERSON>i mua:", "text_input_buyer_address": "Địa chỉ người mua:"}, "saleOrderPayment": {"no_voucher": "<PERSON><PERSON><PERSON><PERSON> có thông tin ưu đãi", "QR_code_valid_until": "Mã QR có hiệu lực tới: ", "customer_guide": "Hướng dẫn khách hàng:", "step_one": "Bước 1: ", "step_two": "Bước 2: ", "open_app": "Mở app", "app_bank": "App <PERSON><PERSON> h<PERSON>ng", "choose_scan_QR": "<PERSON><PERSON><PERSON> quét QR", "money_amount_colon": "Số tiền: ", "press": "(Bấm ", "btn_go_back_uppercase": "QUAY LẠI", "continue_with_different_payment_method": " để tiếp tục thanh toán bằng hình thức khác)", "if_customer_deny_paying": "(<PERSON><PERSON><PERSON>ch từ chối thanh toán thì chọn ", "btn_cancel_QR_code_uppercase": "HỦY QR CODE", "installment_info": "Thông tin trả góp", "installment_money_uppercase": "TIỀN TRẢ GÓP", "appv_code": "APPV Code: ", "point_to_pay": "<PERSON><PERSON><PERSON><PERSON> dùng <PERSON>h toán", "max_applied_point": "<PERSON><PERSON><PERSON><PERSON> áp dụng tối đa: ", "OTP_will_be_sent_to": "(Mã OTP sẽ được gửi tới số điện thoại ", "point_paid_successfully": "<PERSON><PERSON> điểm đã đư<PERSON><PERSON>h toán thành công", "point": "Số điểm: ", "sec_code_paid_successfully": "<PERSON><PERSON> <PERSON><PERSON> đ<PERSON>h toán thành công", "sec_code": "Mã Sec: ", "valid_point": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON> dụng", "IMEI": "IMEI: ", "QR_code_type_colon": "Chọn ví: ", "QR_code_type": "Loại mã QR", "QR_transaction_ongoing": "<PERSON><PERSON><PERSON>ch QR đang thanh toán:", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "money_amount": "<PERSON><PERSON> tiền", "btn_verify": "<PERSON><PERSON><PERSON> th<PERSON>c", "btn_enter": "<PERSON><PERSON><PERSON><PERSON>", "btn_finish": "HOÀN TẤT", "POS_device": "Máy POS: ", "appv_code_guide": "<PERSON><PERSON><PERSON> l<PERSON>y APPV Code", "btn_payment_confirm": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "btn_add_card": "Thê<PERSON> thẻ", "btn_create_QR_code": "Tạo mã thanh toán", "btn_receive_OTP_call": "<PERSON>hận <PERSON> gọi OTP", "btn_receive_OTP_message": "Nhận tin nhắn OTP", "quantity": "SL", "output": "<PERSON><PERSON><PERSON> h<PERSON>", "products_list": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m ", "attached_sale_list": "<PERSON><PERSON> b<PERSON> ", "promotion_list": "<PERSON><PERSON> s<PERSON><PERSON> mãi ", "charge": "<PERSON><PERSON> t<PERSON>n", "customer_voucher": "Phiếu mua hàng MWG", "membership_point": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng thân thiết", "customer_pay_card": "<PERSON><PERSON><PERSON><PERSON> cà thẻ", "customer_pay_QR": "<PERSON><PERSON><PERSON><PERSON> thanh toán qua VÍ ĐIỆN TỬ", "choose_printer": "<PERSON><PERSON><PERSON> m<PERSON>", "customer_at_store": "<PERSON><PERSON> trự<PERSON> tiếp ở siêu thị để nhận tiền", "check_here": "Check vào đ<PERSON>, h<PERSON> thộng sẽ tự động tạo phiếu chi cho bạn", "text_input_contract_code": "<PERSON><PERSON> hợp đồng: ", "lf_contract_employee": "<PERSON><PERSON><PERSON> viên làm hồ sơ: ", "lf_dealer_product_code": "<PERSON><PERSON> đại lý/<PERSON><PERSON> sản phẩm đối tác: ", "lf_cost_contract_colon": "<PERSON><PERSON> làm hồ sơ:", "lf_deposit": "Số tiền trả trước:", "lf_loan_term": "<PERSON><PERSON> hạn vay:", "lf_payment_per_month": "<PERSON><PERSON> tiền tr<PERSON> hàng tháng:", "pay_by_point": "<PERSON><PERSON><PERSON> điểm thanh toán", "pay_by_point_card": "<PERSON><PERSON><PERSON> phi<PERSON>u điểm thanh toán", "bill_printer": "M<PERSON>y in hóa đơn", "VAT_bill_printer": "M<PERSON>y in hóa đơn VAT", "composition_bill_printer": "<PERSON><PERSON><PERSON> in tổng hợp", "pay_for_output_demand": "<PERSON><PERSON> to<PERSON> cho yêu c<PERSON>u xu<PERSON>t", "output_demand": "YCX:", "must_pay": "<PERSON><PERSON><PERSON> to<PERSON>", "charged": "<PERSON><PERSON> thu", "money_to_charge_customer": "<PERSON><PERSON> tiền phải thu của kh<PERSON>ch", "output_store": "<PERSON><PERSON>:", "customer_phone": "SĐT khách hàng: ", "sale_employee": "NV Bán hàng: ", "cash_customer_pay": "Tiền mặt khách đưa", "in_debt": "<PERSON><PERSON><PERSON> nợ", "customer_change": "<PERSON><PERSON><PERSON><PERSON> thừa tr<PERSON> kh<PERSON>ch", "change_point": "<PERSON><PERSON><PERSON><PERSON> thừa", "total_products_price": "<PERSON><PERSON><PERSON> tiền sản phẩm", "additional_fee": "Phụ phí", "round": "<PERSON><PERSON><PERSON> tròn", "deposit": "<PERSON><PERSON><PERSON><PERSON> trả trước", "cost_contract": "<PERSON><PERSON> làm hồ sơ", "advance_payment": "<PERSON><PERSON><PERSON> trước", "placeholder_enter_form_code": "<PERSON><PERSON>ậ<PERSON> hoặc quét mã phiếu", "placeholder_enter_appv_code": "Nhập mã APPV", "placeholder_enter_ID_code": "<PERSON><PERSON><PERSON><PERSON> hoặc quét mã định danh", "placeholder_enter_point_code": "<PERSON><PERSON><PERSON><PERSON> mã phiếu điểm", "placeholder_enter_OTP": "<PERSON>hậ<PERSON> mã OTP", "placeholder_enter_IMEI": "Nhập IMEI", "placeholder_enter_serial_one": "Nhập Serial 1", "placeholder_enter_serial_two": "Nhập Serial 2", "btn_skip_uppercase": "BỎ QUA", "btn_retry_uppercase": "THỬ LẠI", "please_use_XMPOS": "<PERSON><PERSON> long cài đặt App XMPOS để sử dụng chức năng này.", "please_choose_printer": "<PERSON><PERSON> lòng chọn m<PERSON>", "you_want_finish_order": "Bạn có muốn hoàn tất đơn hàng?", "internal_wifi_needed": "<PERSON><PERSON> lòng sử dụng wifi nội bộ để thực hiện chức năng này.", "cannot_connect_printer": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với máy IN.", "print_successfully": "IN thành công", "you_want_delete_QR_code": "Bạn có chắc muốn hủy QR code này?\nBạn cần đảm bảo khách hàng chưa quét QR code này", "please_select_POS": "<PERSON><PERSON> lòng chọn máy P<PERSON>", "please_enter_authorization_code": "<PERSON><PERSON> lòng nhập mã chuẩn chi", "please_enter_OTP_code": "<PERSON><PERSON> lòng nh<PERSON>p mã OTP", "please_enter_ID_code": "<PERSON><PERSON> lòng nhập mã định danh", "authorization_code_six_letters": "<PERSON><PERSON> chuẩn chi phải đúng 6 ký tự", "OTP_code_4_digits": "<PERSON><PERSON> lòng nhập mã O<PERSON> đúng 4 chữ số", "ID_code_4_digits": "<PERSON><PERSON> lòng nhập mã định danh đúng 4 chữ số", "please_enter_pay_amount": "<PERSON><PERSON> lòng nhập số tiền thanh toán", "pay_amount_larger": "Tổng tiền thanh toán đang lớn hơn số tiền phải thu", "authorization_code_exist": "<PERSON><PERSON> chuẩn chi đã tồn tại trong đơn hàng", "please_enter_point": "<PERSON><PERSON> lòng nhập số điểm sử dụng", "excessive_point": "<PERSON><PERSON>ểm sử dụng đang vượt quá số điểm tối đa", "excessive_total_point": "Tổng điểm dùng thanh toán đang vượt quá số điểm tối đa", "excessive_valid_point": "<PERSON><PERSON>ểm sử dụng đang vượt quá số điểm khả dụng", "please_choose_QR_type": "<PERSON><PERSON> lòng chọn lo<PERSON>i QR thanh toán", "btn_continue": "TIẾP TỤC", "btn_continue_print": "TIẾP TỤC IN", "btn_create_again": "TẠO LẠI", "please_scan_qr_code_to_pay": "<PERSON><PERSON> lòng quét mã QR để thanh toán", "transact_successfully": "<PERSON><PERSON><PERSON> d<PERSON>ch thành công", "transact_fail": "<PERSON><PERSON><PERSON> d<PERSON>ch thất b<PERSON>i", "transact_pending": "<PERSON><PERSON><PERSON> d<PERSON>ch đang đ<PERSON> xử lý...", "system_auto_create": "<PERSON><PERSON> thống đã tự động tạo và cập nhật phiếu thu", "please_create_QR_code_again": "<PERSON>ui lòng tạo lại mã QR code để thực hiện giao dịch", "cash": "Tiền mặt", "order_information_not_exist": "<PERSON><PERSON><PERSON><PERSON> có thông tin đơn hàng", "no_information_printer": "Không có thông tin máy IN", "no_information_IEMI": "Không có thông tin IEMI", "no_information_serial": "<PERSON><PERSON><PERSON>ng có thông tin SERIAL", "no_information_voucher": "<PERSON><PERSON><PERSON>ng có thông tin mã Voucher", "voucher_already_use": "<PERSON><PERSON><PERSON> điểm đã được sử dụng", "voucher_out_date": "<PERSON><PERSON><PERSON> điểm đã hết hạn sử dụng", "voucher_cancel": "<PERSON><PERSON>u điểm đã huỷ", "voucher_lock_up": "<PERSON><PERSON><PERSON> điểm đã bị kh<PERSON>a", "voucher_no_information": "<PERSON><PERSON><PERSON><PERSON> có thông tin phiếu điểm", "no_information_receipts_bill": "<PERSON><PERSON><PERSON><PERSON> có thông tin <PERSON> thu, <PERSON><PERSON><PERSON> xuất", "error_print": "Quá trình IN lỗi!", "cannot_connect_to_printer": "không thể kết nối với máy IN!", "cannot_information_transaction": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> đ<PERSON><PERSON><PERSON> thông tin giao dịch", "cannot_information_QR": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>y đ<PERSON><PERSON><PERSON> thông tin mã QR", "cannot_information_order": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin đơn hàng.", "cannot_content_printer": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nội dung in", "create_receipts": "<PERSON><PERSON><PERSON> phiếu thu thành công:", "create_bill": "<PERSON><PERSON><PERSON> phi<PERSON>u xuất thành công:", "create_payment": "<PERSON><PERSON><PERSON> phi<PERSON>u chi cọc thành công:", "extra_points": "\n<PERSON><PERSON><PERSON><PERSON> thừa ", "pay_vip_account": "sẽ đượ<PERSON> trả vào tài k<PERSON>n VIP GIFT củ<PERSON> kh<PERSON>ch hàng", "customers_contact_manager_report_card": "\n<PERSON><PERSON><PERSON><PERSON> hàng liên hệ Quản lý siêu thị để nhận lại Phiếu điểm của điểm thừa", "description": "Kh<PERSON>ch đã quét mã QR Code\nVui lòng chờ khách xác nhận thanh toán...", "month": " th<PERSON>g", "please_enter_report_card": "<PERSON><PERSON> lòng nhập phi<PERSON>u điểm", "report_card_exists": "<PERSON><PERSON>u điểm đã tồn tại trong đơn hàng", "maximum_score": "Tổng điểm dùng thanh toán đang vượt quá số điểm tối đa", "verify_OTP": "<PERSON><PERSON><PERSON>", "verify_ID": "<PERSON><PERSON><PERSON> thực mã định danh", "unconfirmed_transaction": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>a đ<PERSON><PERSON>c <PERSON>n", "please_confirm_transcation": "<PERSON><PERSON> lòng bấm xác nhận để hoàn tất giao dịch", "please_again": "<PERSON><PERSON> lòng thử lại sau", "IMEI_exists_order": "IMEI đã tồn tại trong đơn hàng", "please_enter_code": "<PERSON><PERSON> lòng nhập mã phiếu mua hàng", "please_enter_IMEI": "<PERSON><PERSON> lòng nhập IMEI sản phẩm", "please_enter_SERIAL": "<PERSON><PERSON> lòng nhập SERIAL sản phẩm", "error_get_partner_vouchers": "PMH <PERSON><PERSON><PERSON> tác không đ<PERSON><PERSON><PERSON> khai báo để sử dụng tại kho này.", "payment_card_transaction_ongoing": "<PERSON><PERSON><PERSON> d<PERSON>ch cà thẻ đang thanh toán:", "please_scan_card_code_to_pay": "<PERSON><PERSON> lòng quét thẻ trên máy thanh toán để thực hiện giao dịch", "please_create_transaction_again": "<PERSON><PERSON> lòng tạo lại mã giao dịch để thực hiện giao dịch"}, "saleOrderManager": {"no_update_informatio_sim": "<PERSON><PERSON><PERSON><PERSON> cập nhật đ<PERSON><PERSON><PERSON> yêu cầu xử lý <PERSON>", "no_information_sim": "<PERSON><PERSON><PERSON><PERSON> có thông tin yêu cầu xử lý SIM", "order_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đơn hàng", "cannot_get_order_information": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> đ<PERSON><PERSON><PERSON> thông tin đơn hàng", "order_information_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin đơn hàng", "order_information_not_exist": "<PERSON><PERSON><PERSON><PERSON> có thông tin đơn hàng", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi:", "attached": "<PERSON><PERSON> k<PERSON>:", "please_enter_cancel_reason": "<PERSON><PERSON> lòng nh<PERSON>p lý do hủy", "debt_must_be_0": "Số tiền còn nợ phải bằng 0", "please_select_printer": "<PERSON><PERSON> lòng chọn m<PERSON>", "btn_skip_uppercase": "BỎ QUA", "btn_continue_uppercase": "TIẾP TỤC", "btn_retry_uppercase": "THỬ LẠI", "cancel_order_success": "<PERSON><PERSON><PERSON> hàng đã hủy khỏi hệ thống thành công", "print_expense_report": "IN PHIẾU CHI", "cannot_connect_printer": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với máy IN.", "cannot_information_printer": "Không có thông tin mẫu IN", "cannot_content_printer": "<PERSON><PERSON><PERSON><PERSON> có nội dung IN", "print_success": "IN thành công", "expense_uppercase": "THỰC CHI", "voucher": "<PERSON><PERSON><PERSON> mua hàng", "vip_point": "<PERSON><PERSON><PERSON><PERSON> quà tặng VIP", "qr_cash": "Tiền QR", "credit": "Ti<PERSON>n cà thẻ", "select_printer": "<PERSON><PERSON><PERSON> m<PERSON>", "confirm_cancel": "Bạn có chắc muốn hủy đơn hàng này?", "additional_receipt_order": "<PERSON><PERSON><PERSON> hàng có tạo thêm phiếu thu khác:", "reason_1": "1 - <PERSON><PERSON> có kh<PERSON>ch xem", "reason_2": "2 - <PERSON><PERSON><PERSON> lỗi không đủ điều kiện bán", "reason_3": "3 - <PERSON><PERSON><PERSON> lại đơn hàng do sai thông tin (Giá, tên KH, SĐT, hình thức giao hàng,...)", "reason_4": "4 - <PERSON><PERSON> đ<PERSON>i thông tin đơn hàng (<PERSON><PERSON><PERSON><PERSON>ian <PERSON>iao, Đ<PERSON> giao,...)", "reason_5": "5 - <PERSON><PERSON><PERSON><PERSON> mua (huỷ ERP)", "delete_order_success": "Đ<PERSON>n hàng đã xoá khỏi hệ thống thành công", "deposit": " ti<PERSON><PERSON> c<PERSON>c ", "detain_from_CO": " tiền giữ lại do CO đã chuyển ", "deposit_term": " <PERSON><PERSON><PERSON><PERSON> hạn cọc: ", "enter_cancel_reason": "<PERSON>ui lòng nhập lý do hủy!", "btn_cancel": "Hủy bỏ", "select_cancel_reason": "<PERSON><PERSON>n lý do hủy", "select_reason": "Chọn lý do", "update_export_request": "<PERSON><PERSON><PERSON> nh<PERSON>t hủy YCX và tạo phiếu chi", "btn_cancel_order": "<PERSON><PERSON><PERSON> đơn hàng", "send_coupon": "Gửi Coupon trị giá 100,000", "product_not_for_sale": "<PERSON><PERSON><PERSON> kh<PERSON>ch sản phẩm không bán đ<PERSON>", "created_request": "<PERSON><PERSON><PERSON> c<PERSON>u bạn tạo", "canceled_request": "<PERSON><PERSON><PERSON> c<PERSON>u đã hủy", "btn_cancel_export_request": "Hủy YCX và tạo phiếu chi", "received": "<PERSON><PERSON> thu", "voucher_fee": "<PERSON><PERSON> sử dụng PMH", "detain": "<PERSON><PERSON>ền gi<PERSON> lại", "voucher_fee_not_return": "Tiền PMH không tr<PERSON> khách", "must_expend": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>i chi", "text_input_export_request_code": "Nhập mã YCX/SĐT/Tên KH/Số HĐ/Mã giỏ hàng", "cart_id": "Giỏ hàng: ", "export_request_type": "<PERSON><PERSON><PERSON> yêu cầu xuất: ", "must_pay": "<PERSON><PERSON><PERSON> to<PERSON>:", "remain": "Còn nợ:", "in_debt": "<PERSON><PERSON><PERSON> nợ", "cash": "Tiền mặt", "confirm_status": "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON>", "payment_status": "<PERSON>r<PERSON><PERSON> thái thu tiền", "export_status": "<PERSON>r<PERSON><PERSON> thái xuất hàng", "shipment_status": "<PERSON><PERSON><PERSON><PERSON> thái giao hàng", "export_cash": "<PERSON><PERSON> ti<PERSON>n xu<PERSON>t hàng", "warning_yellow": "Đơn hàng  thuộc yêu cầu xuất b<PERSON><PERSON> hành miễn phí, vui lòng sử dụng chức năng nhận dán của miếng dán màn hình hoặc xuất hàng trên ERP đối với đơn hàng thay pin đồng hồ.", "reprint": "In lại", "not_exported_request": "<PERSON><PERSON><PERSON> c<PERSON>u ch<PERSON>a xu<PERSON>t", "exported_request": "<PERSON><PERSON><PERSON> cầu đã xuất", "invalid_voucher_uppercase": "PHIẾU MUA HÀNG ĐÃ SỬ DỤNG", "payment": "<PERSON><PERSON><PERSON><PERSON>h to<PERSON>:", "export_request_applied": "YCX Áp dụng:", "please_select_pos": "<PERSON><PERSON> lòng chọn máy P<PERSON>", "please_enter_expense_code": "<PERSON><PERSON> lòng nhập mã chuẩn chi", "expense_code_must_have_6_letters": "<PERSON><PERSON> chuẩn chi phải đúng 6 ký tự", "please_enter_payment": "<PERSON><PERSON> lòng nhập số tiền thanh toán", "expense_more_than_must_expend": "<PERSON><PERSON><PERSON> thực chi đang lớn hơn số tiền phải chi", "existed_expense_code": "<PERSON><PERSON> chuẩn chi đã tồn tại trong đơn hàng", "select_pos": "<PERSON><PERSON><PERSON> m<PERSON>", "text_input_appv_code": "Nhập mã APPV", "amount": "Số tiền: ", "btn_transaction_confirm": "<PERSON><PERSON><PERSON>n <PERSON>h to<PERSON>", "btn_add_card": "Thê<PERSON> thẻ", "retail_printer": "M<PERSON>y in hóa đơn", "VAT_printer": "M<PERSON>y in hóa đơn VAT", "common_printer": "<PERSON><PERSON><PERSON> in tổng hợp", "no_promotion": "<PERSON><PERSON><PERSON><PERSON> có khu<PERSON>ến mãi", "please_select_promotion": "<PERSON><PERSON> lòng chọn khuyến mãi BẮT BUỘC chọn:", "dismiss_promotion": "<PERSON><PERSON> chương trình khuyến mãi chưa đượ<PERSON> chọn. Bạn có chắc muốn bỏ qua khuyến mãi ?", "delivery_at_home": "Giao tại nhà:", "delivery_at_store": "<PERSON><PERSON>o tại siêu thị:", "contact_address": "Đ<PERSON>a chỉ liên hệ: ", "contact_name": "<PERSON><PERSON> tên ngư<PERSON>i liên hệ: ", "please_select_more_reason": "<PERSON>ui lòng thêm lý do hủy đơn hàng!", "receipt_code": "<PERSON><PERSON> phiếu thu", "must_pay_for_export_request": "<PERSON><PERSON><PERSON>h toán cho YCX", "additional_fee": "Phụ phí", "shipping_fee": "(<PERSON><PERSON> h<PERSON>, c<PERSON><PERSON> sản ph<PERSON>m)", "cancel_reason": "Lý do hủy", "mandatory": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>p", "title_cancel_reason": "Lý do hủy:", "not_mandatory": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON> nh<PERSON>p", "product_cannot_sale": "<PERSON><PERSON><PERSON> c<PERSON>o sản phẩm không xuất b<PERSON> đ<PERSON>:", "text_input_reason": "<PERSON><PERSON>ậ<PERSON> lý do", "total_price": "<PERSON><PERSON><PERSON> tiền đơn hàng ", "not_rounded": "(<PERSON><PERSON><PERSON> làm tròn)", "total_customer_deposit": "Tổng tiền khách phải trả trước", "shipping_fee_deposit": "\n(<PERSON><PERSON> h<PERSON>, c<PERSON><PERSON> sản ph<PERSON>m)", "apply_voucher": "\n(PMH <PERSON><PERSON> dụng và<PERSON> ch<PERSON>h đơn phát hành)", "source": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>o", "product_promotion": "KM the<PERSON> sản ph<PERSON>m", "delivery_method": "<PERSON><PERSON><PERSON> thức giao", "amount_new_line": "\n<PERSON><PERSON> tiền", "will_be_returned": "sẽ được hoàn vào tài khoàn SmartPay của quý khách", "deposit_new_line": "\n<PERSON><PERSON><PERSON><PERSON>", "detain_from_CO_new_line": "\nTiền giữ lại do CO đã chuyển:", "deposit_term_new_line": "\n<PERSON><PERSON><PERSON><PERSON> hạn c<PERSON>c", "confirm_cancel_new_line": "\nBạn có chắc muốn hủy đơn hàng này?", "view_more": "<PERSON><PERSON>", "cancel_export_request": "HỦY YCX", "export_request": "YCX", "view_transfer_information": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>", "transfer_validation": "<PERSON><PERSON><PERSON> tra", "transfer_information_uppercase": "THÔNG TIN CHUYỂN KHOẢN", "please_choose_printer": "<PERSON><PERSON> lòng chọn số mẫu in", "number_paper": "Số tờ: ", "continue_printer": "<PERSON><PERSON><PERSON><PERSON>", "get_appv_code": "<PERSON><PERSON><PERSON> l<PERSON>y APPV Code", "non_report_history": "<PERSON><PERSON><PERSON><PERSON> có thông tin lịch sử báo cáo", "non_edit_history": "<PERSON><PERSON><PERSON><PERSON> có thông tin lịch sử đơn hàng", "sale_order_id": "<PERSON><PERSON> đơn hàng: ", "content_update": "<PERSON><PERSON><PERSON> dung cập nhật: ", "employee": "Nhân viên: ", "computer_user": "User m<PERSON><PERSON> t<PERSON>: ", "computer_name": "<PERSON><PERSON><PERSON> m<PERSON>: ", "note": "<PERSON><PERSON> chú: ", "type_report": "Loại báo cáo: ", "printer_name": "<PERSON><PERSON><PERSON> in: ", "result": "<PERSON><PERSON><PERSON> quả: ", "history_report": "<PERSON><PERSON><PERSON><PERSON> ký báo c<PERSON>o", "history_sale_order": "<PERSON><PERSON><PERSON><PERSON> ký đơn hàng"}, "instalmentManager": {"no_hometown_information": "không có thông tin danh sách nguyên quán", "no_information": "<PERSON><PERSON><PERSON>ng có thông tin", "not_select_cancel_reason": "Bạn chưa chọn lý do huỷ của hồ sơ. <PERSON><PERSON> lòng kiểm tra lại", "btn_cancel": "Huỷ", "btn_delete": "Xoá", "cannot_read_id_code": "<PERSON>hông đọc đ<PERSON><PERSON> thông tin mã định danh vui lòng quét lại!", "reason": "Lý do", "cancel_record_reason": "<PERSON><PERSON> do huỷ hồ sơ", "record": "<PERSON><PERSON> s<PERSON>", "delete": "xoá", "cancel": "huỷ", "agent_information": "Thông tin nhân viên", "agent_id": "Mã NV: ", "position": "<PERSON><PERSON><PERSON> v<PERSON>: ", "no_payment_per_month_information": "<PERSON><PERSON><PERSON><PERSON> có thông tin số tiền trả hàng tháng", "invalid_deposit": "Số tiền tr<PERSON> tr<PERSON><PERSON><PERSON> không hợp lệ vui lòng kiểm tra lại.", "loan_information": "THÔNG TIN KHOẢN VAY: ", "total_order_price": "Tổng tiền của đơn hàng: ", "total_order_amount": "<PERSON><PERSON><PERSON> tiền của đơn hàng ", "amount_of_prepayment": "Số tiền trả trước ", "loan_term": "<PERSON><PERSON> hạn vay ", "monthly_payment_amount": "Số tiền trả hàng tháng (số tiền tham kh<PERSON>, có thể chênh lệnh 10.000đ - ch<PERSON><PERSON> bao gồm phí thu hộ hàng tháng) ", "picker_loan_term": "<PERSON><PERSON> hạn vay:", "payment_per_month": "Số tiền trả hàng tháng (số tiền tham kh<PERSON>, có thể chênh lệnh 10.000đ - ch<PERSON><PERSON> bao gồm phí thu hộ hàng tháng)", "record_manager": "<PERSON><PERSON><PERSON><PERSON> lý hồ sơ", "send_success": "<PERSON><PERSON><PERSON> chứng từ cho đối tác thành công. <PERSON><PERSON> lòng kiểm tra lại trạng thái hồ sơ.", "processing_information": "<PERSON><PERSON><PERSON> tác đang xử lý thông tin. <PERSON><PERSON> lòng đợi thêm ", "check_status": " phút sau đó kiểm tra lại trạng thái", "btn_check_record_status": "<PERSON><PERSON><PERSON> tra trạng thái hồ sơ", "btn_transfer_record": "<PERSON><PERSON><PERSON> giao hồ sơ", "check_record_status_success": "<PERSON><PERSON>m tra trạng thái hồ sơ thành công. <PERSON><PERSON> lòng kiểm tra lại trạng thái hồ sơ đã đư<PERSON><PERSON> cập nhật", "btn_confirm_otp": "<PERSON>ác nhận OTP", "btn_send_record": "<PERSON><PERSON><PERSON> h<PERSON> sơ", "btn_prepare_document": "<PERSON><PERSON><PERSON> bị tài liệu", "btn_add_record": "<PERSON><PERSON> sung chứng từ", "btn_capture_contract": "<PERSON><PERSON><PERSON> hình hợp đồng", "btn_send_record_to_partner": "<PERSON><PERSON><PERSON> chứng từ cho đối tác", "btn_print_contract": "In hợp đồng trả góp", "please_enter_information": "<PERSON>ui lòng nhập thông tin [", "at_step": "] ở bước ", "information": "Thông tin [", "invalid_1": " <PERSON><PERSON><PERSON><PERSON> hợp lệ. <PERSON><PERSON> lòng kiểm tra lại(#1)", "invalid_2": " <PERSON><PERSON><PERSON><PERSON> hợp lệ. <PERSON><PERSON> lòng kiểm tra lại(#2)", "invalid_3": " <PERSON><PERSON><PERSON><PERSON> hợp lệ. <PERSON><PERSON> lòng kiểm tra lại(#3)", "invalid_4": " <PERSON><PERSON><PERSON><PERSON> hợ<PERSON> lệ. <PERSON><PERSON> lòng kiểm tra lại(#4)", "invalid_5": " <PERSON><PERSON><PERSON><PERSON> hợ<PERSON> lệ. <PERSON><PERSON> lòng kiểm tra lại(#5)", "invalid": " kh<PERSON><PERSON> hợp lệ. <PERSON><PERSON> lòng kiểm tra lại. Bạn chỉ được nhập [", "later_or_equal": "] lớn hơn hoặc bằng ngày ", "sooner_or_equal": "] nhỏ hơn hoặc bằng ngày ", "wrong_format": " kh<PERSON>ng đúng định dạng. <PERSON><PERSON> lòng kiểm tra lại", "hometown_not_found": "<PERSON><PERSON><PERSON>ng tìm thấy nguyên quán!", "text_input_hometown": "<PERSON><PERSON><PERSON><PERSON> tên nguyên quán", "btn_search_uppercase": "TÌM KIẾM", "created_record": "<PERSON><PERSON> sơ bạn tạo", "deleted_record": "<PERSON>ồ sơ đã xoá", "select_province": "Chọn Tỉnh/Thành", "select_district": "<PERSON><PERSON><PERSON>/Huyện", "select_ward": "Chọn Phường/Xã", "header_marriage_status": "-- <PERSON><PERSON><PERSON> trạng hôn nhân --", "enter": "<PERSON><PERSON><PERSON><PERSON> ", "enter_code": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "front": "mặt trước", "back": "mặt sau:", "enter_content": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "header_academic_standard": "-- <PERSON><PERSON><PERSON><PERSON> độ học vấn --", "header_job": "-- <PERSON><PERSON><PERSON> nghi<PERSON> --", "header_company_type": "-- <PERSON><PERSON><PERSON> lo<PERSON>i công ty --", "company_address": "Địa chỉ công ty", "house_number": "Số nhà", "street": "Đường/Ấp", "enter_street": "<PERSON><PERSON><PERSON><PERSON> tên đ<PERSON>/<PERSON>p", "header_position": "-- <PERSON><PERSON><PERSON> ch<PERSON> vụ --", "date_start": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu làm việc", "please_enter_phone_number": "<PERSON><PERSON> lòng nhập số điện thoại", "current_address": "Địa chỉ thường trú ", "quarter": "Tổ/khu phố", "enter_quarter_name": "<PERSON><PERSON><PERSON><PERSON> tên tổ/khu phố", "enter_information": "<PERSON><PERSON><PERSON>p theo thông tin giấy tờ, nếu giấy tờ không có thông tin này thì nhập “.”", "delivery_address": "Địa chỉ tạm trú", "enter_house_number": "<PERSON><PERSON><PERSON><PERSON> số nhà", "header_resident_type": "-- <PERSON><PERSON><PERSON> lo<PERSON>i cư trú --", "upload_image_error": "Lỗi upload hình <PERSON>nh lên server.", "capture": "<PERSON><PERSON><PERSON>", "back_2": "mặt sau", "id_card_old": "CMND", "id_card_new": "CCCD", "portrait": "<PERSON><PERSON><PERSON> chân dung", "please_enter_otp": "<PERSON><PERSON> lòng nh<PERSON>p mã OTP", "enter_6_or_8_digits_otp": "<PERSON><PERSON> lòng nh<PERSON><PERSON> mã <PERSON><PERSON> 6-8 chữ số", "otp_success": "<PERSON><PERSON><PERSON> nhận O<PERSON> thành công. <PERSON><PERSON> sơ đã đư<PERSON><PERSON> đối tác tiếp nhận", "otp_sent": "Mã OTP đã đượ<PERSON> gửi tới số điện thoại của khách hàng. <PERSON><PERSON> lòng nhập vào ô bên dưới để hoàn tất.", "enter_otp": "<PERSON>hậ<PERSON> mã OTP", "customer_information": "THÔNG TIN KHÁCH HÀNG", "full_name": "<PERSON><PERSON> và tên:", "update_record_success": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin hồ sơ trả góp thành công", "householder": "<PERSON><PERSON> chủ hộ", "relationship": "<PERSON><PERSON><PERSON> quan hệ", "header_relationship": "-- <PERSON><PERSON><PERSON> mối quan hệ --", "appellation": "<PERSON><PERSON>", "header_appellation": "-- <PERSON><PERSON> x<PERSON> --", "householder_full_name": "Họ và tên", "enter_householder_full_name": "<PERSON><PERSON><PERSON><PERSON> họ và tên", "contact_phone_number": "<PERSON><PERSON> điện thoại liên hệ", "enter_contact_phone_number": "<PERSON><PERSON><PERSON><PERSON> điện thoại liên hệ", "id": "Số CMND/CCCD", "enter_id": "Nhập số CMND/CCCD", "conjugal_information": "Thông tin vợ/chồng", "btn_update": "<PERSON><PERSON><PERSON>", "no_id_card": "<PERSON><PERSON><PERSON><PERSON> hàng là chủ hộ / <PERSON><PERSON> hộ không có CMND", "please_enter_record_number": "<PERSON><PERSON> lòng nhập số giấy tờ của [", "record_number": "Số giấy tờ của [", "at_least_5_letters": "] ph<PERSON>i có ít nhất 5 kí tự. Vui lòng kiểm tra lại", "please_enter_record_number_2": "<PERSON><PERSON> lòng nhập số giấy tờ mặt sau của [", "record_number_2": "<PERSON><PERSON> giấy tờ mặt sau của [", "please_capture": "<PERSON><PERSON> lòng ch<PERSON> [", "record_type": "<PERSON>ạ<PERSON> gi<PERSON>y tờ của hồ sơ", "header_record_type": "-- <PERSON><PERSON><PERSON> lo<PERSON>i gi<PERSON>y tờ hồ sơ --", "customer_information_2": "THÔNG TIN KHÁCH HÀNG: ", "text_input_surname": "<PERSON><PERSON><PERSON><PERSON> họ kh<PERSON>ch hàng", "text_input_middle_name": "<PERSON><PERSON><PERSON><PERSON> tên đệm kh<PERSON>ch hàng", "text_input_last_name": "<PERSON><PERSON><PERSON><PERSON> tên kh<PERSON>ch hàng", "header_place": "-- <PERSON><PERSON><PERSON> cấp --", "no_term": "<PERSON><PERSON><PERSON><PERSON> thời hạn", "header_hometown": "-- <PERSON><PERSON><PERSON> nguyên quán --", "text_input_phone_number": "<PERSON><PERSON><PERSON><PERSON> số điện thoại di động", "text_input_email": "Nhập email kh<PERSON>ch hàng", "hometown": "<PERSON><PERSON><PERSON><PERSON> quán", "driving_licence_short": "BLXMT", "update_success": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> b<PERSON> sung hồ sơ chứng từ cho đối tác thành công.", "customer_information_3": "THÔNG TIN KHÁCH HÀNG:", "surname": "Họ: ", "middle_name": "<PERSON><PERSON><PERSON> đ<PERSON>: ", "last_name": "Tên: ", "date_of_birth": "<PERSON><PERSON><PERSON>: ", "gender": "Gi<PERSON>i tính: ", "male": "Nam", "female": "<PERSON><PERSON>", "id_code": "Số CMND/CCCD: ", "place": "<PERSON><PERSON><PERSON> cấp: ", "date": "<PERSON><PERSON><PERSON> cấp: ", "title_hometown": "<PERSON><PERSON><PERSON><PERSON> quán: ", "licence_type": "LOẠI GIẤY TỜ:", "dl_code_front": "Số bằng lái xe mặt trước: ", "dl_code_back": "Số bằng lái xe mặt sau: ", "family_record": "<PERSON><PERSON> hộ khẩu: ", "householder_id_code": "Số CMND/CCCD chủ hộ: ", "address": "ĐỊA CHỈ THƯỜNG TRÚ:", "house_number_2": "Số nhà: ", "street_2": "Đường/Ấp: ", "quarter_2": "Tổ/Khu phố: ", "ward": "Phường/Xã: ", "district": "Quận/Huyện: ", "province": "Tỉnh/Thành: ", "print_success": "In thành công", "please_select_print_format": "Vui lòng chọn mẫu in", "print_format_not_found": "<PERSON><PERSON>ông tìm thấy thông tin mẫu in", "select_printer": "<PERSON><PERSON><PERSON> in", "printer_information_not_found": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin máy in", "print_record": "In hồ sơ", "number_of_print": "Số bản in:", "btn_done": "<PERSON><PERSON>", "send_record_success": "<PERSON><PERSON><PERSON> hồ sơ cho đối tác thành công. <PERSON><PERSON> lòng kiểm tra lại trạng thá<PERSON> hồ sơ.", "check_customer_information_success": "<PERSON><PERSON><PERSON> tra thông tin khách hàng thành công.", "otp_sent_to": "Mã OTP đã đư<PERSON><PERSON> gửi đến số", "cancel_record_uppercase": "HỦY HỒ SƠ", "please_enter_canel_reason": "<PERSON><PERSON> lòng nh<PERSON>p lý do huy hồ sơ", "text_input_cancel_content": "<PERSON><PERSON><PERSON><PERSON> nội dung huỷ", "scan_valid_agent_id": "Quét mã danh tính của nhân viên có quyền xác nhận tạo yêu cầu huỷ hồ sơ trả góp", "request_agent": "(<PERSON><PERSON> lòng yêu cầu nhân viên có quyền xác nhận tạo yêu cầu huỷ hồ sơ trả góp mở app MWG, bấm vào logo Thế Giới Di Động ở góc trái màn hình, chọ<PERSON> và<PERSON> \"Mã danh tính\")", "input_deposit": "Số tiền trả trước:", "min_deposit": "(% trả trước tối thiểu:", "max_deposit": "% trả trước tối đa:", "record_2": "<PERSON><PERSON> sơ", "fee": "Phí:", "price": "Giá:", "signature_register": "<PERSON> đ<PERSON>ng kí chữ ký điện tử", "not_signed_up": "<PERSON><PERSON><PERSON><PERSON> hàng chưa đăng kí sử dụng", "electronic_service": "d<PERSON><PERSON> v<PERSON> điện tử", "please_instruct_customer": "<PERSON><PERSON> lòng hướng dẫn khách hàng truy cập", "link_below": "đường dẫn dưới đây để ký bản", "sign_up_electronic_service": "Đ<PERSON>ng ký sử dụng dịch vụ điện tử", "resend_code": "<PERSON><PERSON>i lại mã x<PERSON>c thực", "phone": "<PERSON><PERSON> điện thoại:", "reference_information": "<PERSON>h<PERSON>ng tin người tham chiếu", "number": "Số ", "contract_number": "<PERSON><PERSON> hợp đồng: ", "id_card": "chứng minh nhân dân", "id_card_2": "c<PERSON><PERSON> c<PERSON><PERSON> công dân", "select_additional_document": "<PERSON><PERSON><PERSON> loại gi<PERSON>y tờ c<PERSON><PERSON> b<PERSON> sung củ<PERSON> hồ sơ: ", "print_contract": "In hợp đồng", "instalment_record_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin hồ sơ trả góp", "no_place": "không có thông tin nơi cấp CMND/CCCD/HC", "no_instalment_information": "không tìm thấy thông tin hồ sơ trả góp.", "no_record_type": "không tìm thấy thông tin loại giấy tờ của hồ sơ", "error_update_instalment_information": "Lỗi cập nhật thông tin hồ sơ trả góp.", "no_cancel_reason": "không có thông tin danh sách lý do huỷ hồ sơ", "no_record_code_sent_to_partner": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mã hồ sơ gửi thông tin qua đối tác. <PERSON><PERSON> lòng thử lại hoặc liên hệ IT để được hỗ trợ.", "error_get_signature": "Lỗi lấy thông tin đăng kí chữ kí điện tử từ đối tác HC. <PERSON><PERSON> lòng thử lại.", "error_resend_otp": "Lỗi gửi lại thông tin OTP Code. <PERSON><PERSON> lòng thử lại.", "cannot_verify_otp": "<PERSON><PERSON><PERSON>ng thể xác thực OTP Code. Vui lòng thử lại!", "no_agent_information_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin nhân viên. <PERSON><PERSON> lòng thử lại.", "error_get_agent_information": "Lỗi lấy thông tin nhân viên. <PERSON><PERSON> lòng liên hệ IT.", "error_add_record_partner": "Lỗi b<PERSON> sung gi<PERSON>y tờ cho đối tác.", "error_send_record": "Lỗi gửi chứng từ cho đối tác. <PERSON><PERSON> lòng thử lại hoặc liên hệ IT để được hỗ trợ", "error_print_contract": "Lỗi in thông tin hợp đồng trả góp.", "no_printer_information": "<PERSON><PERSON><PERSON>ng có thông tin của máy in", "no_print_format_found": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin mẫu in", "error_print": "Quá trình IN lỗi!", "cannot_connect_to_printer": "không thể kết nối với máy IN!", "search_input": "Nhập mã HS/ĐH/SĐT", "export_request": "<PERSON><PERSON><PERSON> c<PERSON>u xu<PERSON>: ", "record_status": "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON> hồ sơ: ", "note": "<PERSON><PERSON> chú: ", "partner_record_code": "<PERSON><PERSON> hồ sơ đối tác: ", "start_date_cannot_later_than_end_date": "<PERSON><PERSON><PERSON> bắt đầu không đư<PERSON>c lớn hơn ngày kết thúc!", "at_most_5_days": "<PERSON><PERSON><PERSON> bắt đầu và ngày kết thúc chỉ được cách nhau tối đa 5 ngày!", "time": "<PERSON><PERSON><PERSON><PERSON> gian ", "from_to": "(<PERSON>ừ ngày - tới ngày)", "guide": "Hướng dẫn:", "review_information_uppercase": "KIỂM TRA LẠI THÔNG TIN", "review_information": "<PERSON><PERSON> lòng kiểm tra đầy đủ thông tin", "hintNoteStep6": "<PERSON><PERSON><PERSON> không có thông tin này thì nhập “.”. Bạn chỉ được nhập tối đa 128 ký tự.", "infoOther": "Thông tin khác"}, "searchStore": {"pick_store": "<PERSON><PERSON><PERSON> siêu thị làm việc", "input_store": "<PERSON><PERSON><PERSON><PERSON> si<PERSON> thị", "no_authority": "(<PERSON><PERSON><PERSON><PERSON> có quyền)", "no_store_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy siêu thị !", "confirm_change_store": "Bạn có chắc muốn đổi siêu thị làm việc?", "no_store_authority": "Bạn không có quyền thao tác trên siêu thị này. Vui lòng liên hệ IT để được cấp quyền!", "understood": "<PERSON><PERSON> hiểu"}, "searchImei": {"product": "Sản phẩm:", "status_type": "Trạng thái:", "haswarranty": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> h<PERSON>:", "placeholder": "Nhập IMEI", "no_find_imei": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin <PERSON>mei", "input_type": "<PERSON><PERSON><PERSON> thức nh<PERSON>p:", "input_store": "<PERSON><PERSON> nh<PERSON>p:", "input_id": "<PERSON><PERSON> phi<PERSON>u nhập:", "input_date": "<PERSON><PERSON><PERSON> nh<PERSON>p:", "input_user_name": "NV nhập:", "output_type": "<PERSON><PERSON><PERSON> thức xuất:", "output_store": "<PERSON><PERSON>:", "output_id": "<PERSON><PERSON> phiếu xuất:", "output_date": "<PERSON><PERSON><PERSON>:", "output_user_name": "NV xuất:"}, "pinOclock": {"customer_name": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>:", "input_store": "<PERSON><PERSON><PERSON> siêu thị:", "product": "Sản phẩm:", "choice_pin": "<PERSON><PERSON><PERSON> lo<PERSON>i pin để thay:", "btn_saleoder": "TẠO ĐƠN HÀNG", "total_money": "Tổng tiền: ", "input_date": "<PERSON><PERSON><PERSON> mua:", "defalt_value": "-- <PERSON><PERSON><PERSON> lo<PERSON>i pin cần thay --", "price": "Giá: ", "placeholder": "-- <PERSON><PERSON><PERSON><PERSON> số điện thoại --", "not_find": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin Đồng Hồ", "no_get_price": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>y đ<PERSON><PERSON><PERSON> giá của pin đồng hồ", "confirm_add_to_cart": "Bạn có chắc muốn thêm vào giỏ hàng ?"}, "cMMoneyComplaint": {"no_expense_voucher_information": "<PERSON><PERSON><PERSON>ng có thông tin mã phiếu chi", "error": "<PERSON><PERSON><PERSON> trình hoàn tất thất bại", "no_authority": "<PERSON>ạn không có quyền để thực hiện chức năng này. <PERSON>ui lòng liện hệ IT để được cấp quyền", "invalid_expense_voucher_information": "Thông tin phiếu chi không hợp lệ. <PERSON><PERSON> lòng kiểm tra lại", "complete_expend": "Bạn có chắc muốn hoàn tất chi tiền?", "expend_success": "<PERSON><PERSON><PERSON> tất chi thành công", "btn_complete": "<PERSON><PERSON><PERSON> tất chi tiền", "customer_information": "Thông tin khách hàng", "customer_id": "<PERSON><PERSON> kh<PERSON>ch hàng:", "customer_name": "<PERSON><PERSON> tên KH:", "gender": "Gi<PERSON>i tính:", "phone_number": "<PERSON><PERSON> điện thoại:", "tax_id": "<PERSON><PERSON> số thuế:", "address": "Địa chỉ:", "district_province": "Huyện/Tỉnh:", "text_input_expense_voucher_id": "<PERSON><PERSON><PERSON><PERSON> mã phiếu chi", "male": "Nam", "female": "<PERSON><PERSON>", "expense": "<PERSON>h<PERSON>c chi", "cash": "Tiền mặt:", "debt_remain": "Còn nợ lại:", "expense_voucher_information": "Thông tin phiếu chi", "store": "Kho:", "voucher_type": "<PERSON><PERSON><PERSON> phi<PERSON>u:", "voucher_id": "Số hóa đơn:", "invoice_symbol": "<PERSON><PERSON>", "voucher_date": "<PERSON><PERSON><PERSON> h<PERSON> đơn:", "content": "Nội dung:", "total_money": "Tổng tiền:"}, "codPay": {"error_inspect_request": "Lỗi duyệt yêu cầu điều chỉnh!", "no_order_information_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mã thông tin đơn hàng/SĐT", "notification_2": "Thông báo!", "adjustment_greater_than_zero": "Số tiền điều chỉnh phải lớn hoặc bằng 0.", "adjustment_must_not_equal_cod": "Số tiền điều chỉnh không được bằng với số tiền thu hộ COD.", "adjustment_must_smaller_than_remain": "Số tiền điều chỉnh phải nhỏ hơn hoặc bằng số tiền còn lại.", "please_enter_adjust_reason": "<PERSON><PERSON> lòng nhập lý do điều chỉnh.", "create_request_success": "Tạo yêu cầu điều chỉnh COD thành công!", "not_exported": "<PERSON><PERSON><PERSON> xu<PERSON>", "exported": "Đã xuất", "order_type": "<PERSON><PERSON><PERSON> đơn hàng: ", "remain": "Số tiền còn lại: ", "cod": "<PERSON><PERSON> tiền thu hộ COD: ", "text_input_adjusted_money": "<PERSON><PERSON><PERSON><PERSON> số tiền sau điều chỉnh: ", "adjust_reason": "<PERSON>ý do điều chỉnh: ", "btn_create_request": "TẠO YÊU CẦU", "adjust_request_cod": "YC điều chỉnh số tiền thu hộ COD", "requests": "<PERSON> y<PERSON><PERSON> c<PERSON>u", "search_input_order_code": "Nhập mã ĐH/SĐT", "btn_find": "<PERSON><PERSON><PERSON>", "please_enter_order_code": "<PERSON><PERSON> lòng nhập mã đơn hàng/SĐT", "create_request": "+ Tạo Y/C", "before_arrive_day": "<PERSON>ui lòng chọn ngày từ nhỏ hơn ngày đến!", "within_30_days": "<PERSON>ui lòng chọn trong khoảng 30 ngày!", "no_information": "<PERSON><PERSON><PERSON>ng có thông tin", "inspect_request_success": "<PERSON>yệ<PERSON> yêu cầu điều chỉnh thành công!", "delete_request_success": "<PERSON><PERSON>a yêu cầu điều chỉnh thành công!", "adjust_request_code": "<PERSON>ã yêu cầu điều chỉnh: ", "agent_created": "<PERSON><PERSON><PERSON> viên tạo: ", "store_created": "<PERSON><PERSON><PERSON> thị tạo: ", "remain_request": "<PERSON><PERSON><PERSON> cầu điều chỉnh còn: ", "btn_delete": "Xóa", "status": "Trạng thái:", "deleted": " ĐÃ XOÁ", "inspect_status": "<PERSON>r<PERSON><PERSON> th<PERSON><PERSON>:", "accept": " ĐỒNG Ý"}, "activeSimManager": {"order_status": "Tr<PERSON><PERSON> thái đơn hàng:", "exported": "<PERSON><PERSON> xuất hàng", "not_exported": "<PERSON><PERSON><PERSON> xu<PERSON> hàng", "imei": "Số IMEI: ", "package": "<PERSON><PERSON><PERSON>: ", "source": "Nguồn tạo", "not_connected": "<PERSON><PERSON><PERSON> đấu n<PERSON>i", "connected": "<PERSON><PERSON> đấu nối", "text_input_keyword": "Mã YC/ĐH, IMEI, SĐT, tên K<PERSON>", "error_1": "<PERSON><PERSON><PERSON><PERSON> tìm thấy yêu cầu xử lý SIM", "error_2": "không có thông tin xử lý SIM", "error_3": "không có thông tin quốc gia", "error_4": "không có thông tin tỉnh thành", "error_5": "không có thông tin quận huyện", "error_6": "không có thông tin phường xã", "error_7": "không có thông tin nơi cấp CMND/CCCD/HC", "error_8": "Không tìm thấy thông tin khách hàng!", "error_9": "SIM serial không tồn tại!", "error_10": "Không tìm thấy thông tin Esim!", "signature_uppercase": "CHỮ KÝ", "delete": "XÓA", "done": "XONG", "sim_process_request": "YCXL SIM ", "id_card_1_short": "CMND", "id_card_2_short": "CCCD", "passport": "<PERSON><PERSON> ch<PERSON>", "recapture": "<PERSON><PERSON><PERSON> l<PERSON>", "portrait": "<PERSON><PERSON><PERSON>nh chân dung", "sign": "<PERSON><PERSON> t<PERSON>n", "resign": "<PERSON><PERSON> l<PERSON>", "request_store": "<PERSON><PERSON> yêu cầu: ", "process_store": "Kho xử lý: ", "sale_agent": "NV bán hàng: ", "search_input_sale_agent": "<PERSON><PERSON><PERSON><PERSON> nhân viên b<PERSON> hàng", "product": "Sản phẩm: ", "search_input_sim_serial": "Nhập SIM Serial", "get_sim_serial": "N<PERSON>ấn thử lại để lấy SIM Serial", "package_2": "<PERSON><PERSON><PERSON> c<PERSON>: ", "select_package": "<PERSON><PERSON><PERSON> g<PERSON>", "customer_information": "Thông tin khách hàng", "select_country": "<PERSON><PERSON><PERSON> quốc tịch", "user": "Đối tượng sử dụng: ", "user_type_1": "<PERSON><PERSON><PERSON> thân", "user_type_2": "Con đẻ", "user_type_3": "Con nuôi dưới 14 tuổi", "user_type_4": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> g<PERSON> hộ", "user_type_5": "<PERSON><PERSON><PERSON><PERSON> bị", "select_user": "<PERSON><PERSON><PERSON> đ<PERSON> t<PERSON>", "record_type": "Loại giấy tờ: ", "id_card_1": "Chứng minh nhân dân", "id_card_2": "<PERSON><PERSON><PERSON> c<PERSON> công dân", "select_record_type": "<PERSON><PERSON><PERSON> lo<PERSON>i gi<PERSON>y tờ", "id_number": "Số CMND/CCCD/Hộ chiếu:", "date": "<PERSON><PERSON><PERSON> cấp: ", "select_date": "<PERSON><PERSON><PERSON>", "place": "<PERSON><PERSON><PERSON> cấp: ", "select_place": "<PERSON><PERSON><PERSON> n<PERSON> cấp", "expired_day": "<PERSON><PERSON><PERSON> hết hạn: ", "visa_number": "Số Visa: ", "id_number_in_id_card": "Số CMND/CCCD trong hộ chiếu: ", "province": "Tỉnh/Thành: ", "select_province": "Chọn Tỉnh/Thành", "district": "Quận/Huyện: ", "select_district": "<PERSON><PERSON><PERSON>/Huyện", "ward": "Phường/Xã: ", "select_ward": "Chọn Phường/Xã", "please_enter_address_on_id_card": "<PERSON><PERSON> lòng nhập số nh<PERSON>, đ<PERSON><PERSON><PERSON>/<PERSON><PERSON>, tổ/khu phố theo <PERSON> thường trú trên CMND (nếu có).", "house_number": "Số nhà: ", "street": "Tên <PERSON>/ấp: ", "quarter": "Tên tổ/khu phố: ", "address": "<PERSON><PERSON><PERSON> thường trú: ", "full_name": "<PERSON><PERSON> và tên: ", "customer_id": "CMND/CCCD c<PERSON><PERSON> kh<PERSON>ch hàng", "customer_hc_id": "CMND/CCCD/HC của kh<PERSON>ch hàng", "date_of_birth": "<PERSON><PERSON><PERSON> tháng năm sinh: ", "btn_update": "<PERSON><PERSON><PERSON>", "btn_connect": "<PERSON><PERSON><PERSON>", "enter": "<PERSON><PERSON><PERSON><PERSON>", "select_pg": "Chọn PG", "photo_and_signature": "<PERSON>ui lòng chụp đủ hình ảnh và chữ ký!", "error_update_image": "Lỗi cập nhật hình <PERSON>nh", "connect_success": "<PERSON><PERSON>u nối  thành công!", "update_success": "<PERSON><PERSON><PERSON> nhật thành công!", "message_1": "<PERSON><PERSON> lòng nhập serial SIM", "message_2": "<PERSON><PERSON> lòng nhập đúng serial SIM", "message_3": "<PERSON><PERSON> lòng nhập họ tên", "message_4": "Vui lòng chọn gói cướ<PERSON> !", "message_5": "<PERSON><PERSON> lòng chọn quốc tịch", "message_6": "<PERSON><PERSON> lòng chọn đối tượng sử dụng", "message_7": "<PERSON><PERSON> lòng chọn Tỉnh/Thành", "message_8": "<PERSON><PERSON> lòng <PERSON>/Huyện", "message_9": "<PERSON><PERSON> lòng chọn <PERSON>/Xã", "message_10": "<PERSON><PERSON> lòng chọn lo<PERSON>i gi<PERSON>y tờ", "message_11": "<PERSON><PERSON> lòng nhập số CMND/CCCD/HC", "message_12": "<PERSON><PERSON> lòng chọn ngày cấp CMND/CCCD/HC", "message_13": "<PERSON><PERSON> lòng chọn n<PERSON>i cấp CMND/CCCD/HC", "message_14": "<PERSON><PERSON> lòng chọn ngày hết hạn CCCD/<PERSON><PERSON> chiếu", "message_15": "<PERSON><PERSON> lòng nh<PERSON>p số <PERSON>", "message_16": "<PERSON><PERSON> lòng nhập số CMND/CCCD trong hộ chiếu", "message_17": "<PERSON><PERSON> lòng chọn ngày tháng năm sinh", "message_18": "<PERSON><PERSON> lòng nhập số điện thoại", "message_19": "Vui lòng chọn PG!", "name": "<PERSON><PERSON> tên: ", "date_of_birth_2": "<PERSON><PERSON><PERSON>: ", "address_2": "Địa chỉ: ", "select_customer_information": "<PERSON><PERSON><PERSON> thông tin khách hàng", "capture": "<PERSON><PERSON><PERSON>", "front": "mặt trước", "back": "mặt sau", "nationality": "Quốc tịch: ", "physical_sim": "SIM trắng vật lý", "esim": "SIM trắng điện tử (eSIM)", "male": "Nam", "female": "<PERSON><PERSON>", "validation_gender": "<PERSON><PERSON> lòng chọn giới t<PERSON>h kh<PERSON>ch hàng."}, "splash": {"token_expired": "Token đã quá thời hạn. <PERSON>ui lòng đăng nhập lại.", "cannot_information_user": "Thông tin dữ liệu của user có vấn đề, liê<PERSON> hệ bộ phận IT kiểm tra.", "cannot_information_work": "Thông tin làm việc của user có vấn đề, liê<PERSON> hệ bộ phận IT kiểm tra.", "cannot_information_warehouse": "Thông tin siêu thị làm việc của user có vấn đề, li<PERSON><PERSON> hệ bộ phận IT kiểm tra.", "impormation_supermarket_defaut": "Th<PERSON>ng tin siêu thị làm việc mặc định của bạn", "info_change": "đã thay đổi. <PERSON><PERSON> lòng đăng nhập lại", "please_update_new_version": "của app hiện tại đã cũ hoặc lỗi. <PERSON><PERSON> lòng cập nhật phiên bản mới nhất để sử dụng app MWG POS.", "btn_notify_log_out": "<PERSON><PERSON><PERSON> xu<PERSON>", "language": "<PERSON><PERSON><PERSON> ng<PERSON>:", "btn_login": "<PERSON><PERSON><PERSON><PERSON> qua SSO", "sso_authenticating": "<PERSON><PERSON> xác thực đ<PERSON>ng nh<PERSON>p SSO", "updating": "Ứng dụng đang đư<PERSON> cập nh<PERSON>t (%{progress}%)", "checking_update": "<PERSON><PERSON> kiểm tra phiên bản cập nh<PERSON>t"}, "components": {"title_camera_access": "<PERSON><PERSON><PERSON><PERSON> truy cập vào camera", "MWG_camera_access": "Ứng dụng MWG cần quyền truy cập vào camera của bạn!", "version": "<PERSON><PERSON><PERSON> b<PERSON>:", "loading": "<PERSON><PERSON> t<PERSON>: ", "completed": "<PERSON><PERSON><PERSON> t<PERSON>t", "data_not_found": "<PERSON><PERSON><PERSON>ng tìm thấy dữ liệu!", "on_loading_data": "<PERSON><PERSON> liệu đang trong quá trình tải xuống", "update_success": "MWG POS cập nhật thành công", "please_close_app": "<PERSON><PERSON> lòng tắt ứng dụng và mở lại để sử dụng phiên bản vừa đượ<PERSON> cập nhật.", "POS_update": "MWG POS cập nh<PERSON>t", "app_new_update": "Ứng dụng đã có phiên bản cập nhật mới.\n<PERSON><PERSON> lòng bấm “Đồ<PERSON> ý” để cập nhật bản phiên bản mới.", "POS_updating": "MWG POS đang cập nh<PERSON>t", "app_sync_data": "Ứng dụng đang đồng bộ dữ liệu. <PERSON><PERSON> lòng chờ cho đến khi hoàn tất.", "select_province": "Chọn Tỉnh/Thành", "select_district": "<PERSON><PERSON><PERSON>/Huyện", "select_ward": "Chọn Phường/Xã"}, "header": {"home_screen_uppercase": "TRANG CHỦ", "cod_pay_uppercase": "ĐIỀU CHỈNH SỐ TIỀN THU HỘ COD", "search_installment_records_uppercase": "TÌM KIẾM HỒ SƠ TRẢ GÓP", "inventory_uppercase": "KIỂM KÊ SẢN PHẨM", "order_manager_uppercase": "QUẢN LÝ YÊU CẦU XUẤT HÀNG", "adjust_price_uppercase": "ĐIỀU CHỈNH GIÁ", "create_price_uppercase": "TẠO CHIẾN GIÁ", "order_member_point": "KHÁCH HÀNG THÂN THIẾT", "order_installment_OTP": "XÁC NHẬN THÔNG TIN OTP", "add_image_uppercase": "HÌNH ẢNH BỔ SUNG", "search_product_uppercase": "TÌM KIẾM SẢN PHẨM", "information_product_uppercase": "THÔNG TIN SẢN PHẨM", "information_cart_uppercase": "THÔNG TIN GIỎ HÀNG", "manager_SIM_uppercase": "QUẢN LÝ YÊU CẦU XỬ LÝ SIM", "choose_supermarket_uppercase": "CHỌN SIÊU THỊ LÀM VIỆC", "CM_money_complaint": "CHI HOÀN TIỀN KHIẾU NẠI", "additional_promotion_uppercase": "BỔ SUNG KHUYẾN MÃI", "sticker_protector_uppercase": "DÁN MÀN HÌNH", "search_imei_history_uppercase": "TRA CỨU LỊCH SỬ IMEI", "cart_warranty_sticker": "GIỎ HÀNG BẢO HÀNH MIẾNG DÁN", "f88_loan_contract": "HỢP ĐỒNG VAY F88", "create_contract_f88": "TẠO HỢP ĐỒNG VAY F88", "print_contract_f88": "IN HỢP ĐỒNG F88", "handover_f88": "BÀN GIAO CHỨNG TỪ", "handover_list": "DANH SÁCH BÀN GIAO", "employee_of_partner": "NHÂN VIÊN LẤY CHỨNG TỪ", "history_handover": "LỊCH SỬ BÀN GIAO CHỨNG TỪ", "print_handover_letter": "IN BIÊN BẢN BÀN GIAO", "detail_handover": "CHI TIẾT BÀN GIAO", "promotion_detail": "CHI TIẾT KHUYẾN MÃI", "view_promotion_info": "XEM THÔNG TIN KHUYẾN MÃI", "search_inout_voucher": "TRA CỨU PHIẾU THU CHI", "scan_qrcode_id": "THAM GIA SỰ KIỆN", "search_output_receipt": "QUẢN LÝ PHIẾU BIÊN NHẬN", "search_sale_service": "TRA CỨU PHIẾU DỊCH VỤ", "area_management_uppercase": "QUẢN LÝ KHU VỰC", "inventory_product": "KIỂM KÊ SẢN PHẨM", "select_inventory_status": "CHỌN TRẠNG THÁI KIỂM", "view_inventory_result": "XEM KẾT QUẢ KIỂM KÊ", "view_arrears_result": "XEM KẾT QUẢ TRUY THU", "processing_arrears": "TRUY THU ĐANG XỬ LÝ", "arrears_staff_info": "THÔNG TIN NHÂN VIÊN TRUY THU", "inventory_no_imei_product": "KIỂM KÊ SẢN PHẨM KHÔNG IMEI", "print_report": "IN BIÊN BẢN", "inventory_has_imei_product": "KIỂM KÊ SẢN PHẨM CÓ IMEI", "add_new_area": "THÊM MỚI KHU VỰC", "select_product_state": "CHỌN LOẠI TÌNH TRẠNG", "product_detail": "CHI TIẾT SẢN PHẨM", "deposit_receipt": "QUẢN LÝ PHIẾU NỘP TIỀN", "send_bank_internal": "KIỂM QUỸ VÀ NỘP TIỀN SIÊU THỊ"}, "additionalPromotion": {"order_not_found": "<PERSON><PERSON><PERSON><PERSON> có đơn hàng nào thỏa điều kiện tìm kiếm", "cannot_get_order_information": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> đ<PERSON><PERSON><PERSON> thông tin đơn hàng", "order_information_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin đơn hàng", "order_information_not_exist": "<PERSON><PERSON><PERSON><PERSON> có thông tin đơn hàng", "text_input_export_request_code": "<PERSON>hậ<PERSON> mã Yêu cầu xuất/SĐT/IMEI", "customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>: ", "phone_number": "<PERSON><PERSON> điện thoại: ", "export_request_type": "<PERSON><PERSON><PERSON> yêu cầu xuất: ", "view_detail": "<PERSON>em chi tiết", "additional_sale": "<PERSON><PERSON> sung bán kèm", "source": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>o", "output_date": "<PERSON><PERSON><PERSON>: ", "output_store": "<PERSON><PERSON>: ", "quantity": "Số lượng: ", "total_price": "<PERSON><PERSON><PERSON> b<PERSON>: ", "debt_promotion_product_list": "<PERSON><PERSON> k<PERSON>n mãi", "debt_sale_product": "BS Bán kèm", "debt_product_list": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m nợ", "committed_delivery_date": "<PERSON><PERSON><PERSON><PERSON> gian cam kết giao hàng: ", "action_pay_debt": "<PERSON> tiền nợ", "action_export_additional_promotion": "<PERSON><PERSON><PERSON> b<PERSON> sung", "debt_promotion_quantity": "Số lượng khu<PERSON>ến mãi nợ: ", "in_stock_quantity": "Số tồn kho: ", "at_least_one_product_selected": "<PERSON><PERSON>n chưa chọn sản phẩm xuất b<PERSON> sung khuyến mãi, vui lòng chọn sản phẩm", "no_debt_product": "Sản phẩm bạn chọn không nằm trong danh sách khai báo tặng của CTKM. Vui lòng chọn sản phẩm khác hoặc liên hệ ngành hàng để được hỗ trợ!", "sale_price": "<PERSON><PERSON><PERSON> b<PERSON>: ", "expired_additional_promotion_program": "<PERSON><PERSON><PERSON><PERSON> trình đã hết thời gian đ<PERSON><PERSON><PERSON> ph<PERSON><PERSON> bổ sung khuyến mãi.", "total_must_expend": "<PERSON><PERSON>ng tiền phải chi", "action_create_debt_payment_voucher": "<PERSON><PERSON><PERSON> phi<PERSON>u chi nợ", "inStock_message": "%{productName} còn tồn kho: %{inStock}", "ask_to_choose_others": "<PERSON><PERSON> lòng chọn sản phẩm kh<PERSON>c", "header_acronym": "BSKM %{SaleOrderID}", "quantity_acronym": "SL: %{quantity}", "create_payment": "<PERSON><PERSON><PERSON> phi<PERSON>u chi thành công:", "promotion_tab": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "receive_method_tab": "<PERSON><PERSON><PERSON> th<PERSON> n<PERSON>n"}, "screenSticker": {"ordered_products_to_be_pasted": "<PERSON><PERSON><PERSON> phẩm nhận dán hôm nay", "waiting_to_be_pasted": "Chờ dán", "waiting_list_to_be_pasted": "<PERSON><PERSON> s<PERSON>ch chờ dán", "ordered_to_be_pasted": "<PERSON><PERSON><PERSON><PERSON> dán", "no_product_to_be_pasted": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm chờ dán", "employee_id": "Mã NV", "imei": "IMEI", "customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>: ", "product_name": "Sản phẩm: ", "output_paste_date": "<PERSON><PERSON><PERSON> d<PERSON>: ", "created_date": "<PERSON><PERSON><PERSON> t<PERSON>: ", "at_store": "<PERSON><PERSON><PERSON> siêu thị: ", "choose_sticker_to_be_warranted": "<PERSON><PERSON><PERSON> miếng dán để bảo hành", "btn_create_sale_order": "<PERSON><PERSON><PERSON> đơn hàng", "sale_order_id": "<PERSON><PERSON> đơn hàng: ", "created_by_user": "<PERSON><PERSON><PERSON> viên tạo: ", "enter_input_placeholder": "Nhập IMEI/Số điện thoại", "paste_screen_sticker": "<PERSON><PERSON> màn hình", "warrant_screen_sticker": "<PERSON><PERSON><PERSON> h<PERSON>nh mi<PERSON>ng dán", "total_amount": "Tổng tiền: ", "warrant_sticker_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin Bảo hành miếng dán", "no_ordered_product_to_be_pasted": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm nhận dán", "failed_to_get_price": "<PERSON><PERSON><PERSON> giá miếng dán không thành công", "failed_to_create_so": "<PERSON><PERSON><PERSON><PERSON> dán không thành công", "btn_get_price": "<PERSON><PERSON>y giá", "product_has_no_warranty": " kh<PERSON>ng tham gia ch<PERSON>h s<PERSON>ch b<PERSON>o h<PERSON>.", "ensure_add_to_cart": "Bạn có chắc muốn thêm vào giỏ hàng?", "at_least_one_product_selected": "<PERSON><PERSON>n chưa chọn miếng dán b<PERSON><PERSON>, vui lòng chọn miếng dán.", "error_get_more_list": "<PERSON><PERSON> lỗi không quá trình lấy thêm danh sách. <PERSON>ui lòng thoát ra chọn lại tính năng", "load_more_fail": "<PERSON><PERSON><PERSON><PERSON> lấy thêm đ<PERSON><PERSON> danh sách nhận dán. <PERSON><PERSON> lòng thử lại", "confirm_customer": "Bạn có chắc muốn nhận dán không?"}, "f88": {"id_number": "S<PERSON> giấy tờ định danh: ", "customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>: ", "bike_number": "<PERSON>i<PERSON>n số xe: ", "contract_status": "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON> hồ sơ: ", "contract_handover_status": "<PERSON>r<PERSON><PERSON> thái bàn giao hồ sơ: ", "partner_contract_id": "<PERSON><PERSON> hồ sơ đối tác: ", "select_contract": "<PERSON><PERSON><PERSON> hồ sơ bàn giao", "select_all": "<PERSON><PERSON><PERSON> tất cả", "released_contract": "<PERSON><PERSON><PERSON> hồ sơ đã giải ngân: ", "selected_contract": "<PERSON><PERSON><PERSON> hồ sơ đã chọn để bàn giao: ", "btn_continue": "<PERSON><PERSON><PERSON><PERSON>", "just_not_handover_contract": "Chỉ l<PERSON>y hợp đồng chưa đư<PERSON><PERSON> bàn giao", "select_contract_status": "<PERSON><PERSON><PERSON> trạng thái hồ sơ", "created_user": "<PERSON><PERSON><PERSON> viên làm hồ sơ: ", "input_user_id": "<PERSON><PERSON><PERSON><PERSON> mã nhân viên", "please_select_printer": "<PERSON><PERSON> lòng chọn m<PERSON> in", "handover_contract_quantity": "<PERSON><PERSON> lượ<PERSON> hồ sơ bàn giao: ", "create_handover_request": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u bàn giao", "please_capture_introduction_letter": "<PERSON><PERSON> lòng chụp hình thư giới thiệu của nhân viên lấy chứng từ", "please_capture_customer_portrait": "<PERSON><PERSON> lòng chụp hình chân dung của khách hàng", "please_capture_agent_card": "<PERSON><PERSON> lòng chụp hình thẻ nhân viên của nhân viên lấy chứng từ", "please_capture_agent_front_id_card": "<PERSON><PERSON> lòng chụp hình mặt trước giấy tờ tùy thân của nhân viên lấy chứng từ", "please_capture_agent_back_id_card": "<PERSON><PERSON> lòng chụp hình mặt sau giấy tờ tùy thân của nhân viên lấy chứng từ", "please_capture_contract_with_signature": "<PERSON><PERSON> lòng chụp bảng kê có chữ ký của nhân viên lấy chứng từ", "please_take_partner_signature": "<PERSON><PERSON> lòng lấy chữ ký của nhân viên lấy chứng từ", "update_handover_request_success": "<PERSON><PERSON><PERSON> nh<PERSON>t yêu cầu bàn giao thành công", "capture_introduction_letter": "<PERSON><PERSON><PERSON> gi<PERSON>y tờ công tác của nhân viên lấy chứng từ", "introduction_letter": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON>i thiệu", "agent_card": "Thẻ nhân viên", "capture_agent_id_card": "<PERSON><PERSON><PERSON> giấy tờ tùy thân của nhân viên lấy chứng từ", "agent_front_id_card": "Mặt trước gi<PERSON>y tờ tùy thân", "agent_back_id_card": "Mặt sau giấy tờ tùy thân", "capture_contract_with_signature": "<PERSON><PERSON><PERSON> bảng kê có chữ ký của nhân viên lấy chứng từ", "contract_with_signature": "<PERSON><PERSON><PERSON> kê có chữ ký", "take_partner_signature": "<PERSON><PERSON><PERSON> viên l<PERSON>y chứng từ ký tên", "partner_signature": "<PERSON>ữ ký của nhân viên lấy chứng từ", "btn_done": "<PERSON><PERSON><PERSON> th<PERSON>", "please_use_pdf_path": "<PERSON>ui lòng đường dẫn file PDF", "pdf_url": "Link file PDF:", "input_url": "<PERSON><PERSON><PERSON><PERSON> địa chỉ URL", "btn_print_contract": "IN HỢP ĐỒNG", "create_contract": "<PERSON><PERSON><PERSON> hồ sơ vay", "create_handover_loan_request": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u bàn giao", "history_transfer": "<PERSON><PERSON><PERSON> sử bàn giao", "enter_loan_customer": "<PERSON><PERSON><PERSON><PERSON> mã hồ sơ vay/biển số xe/tên <PERSON>", "IdentificationNo": "S<PERSON> giấy tờ định danh:", "License_Plate": "<PERSON>i<PERSON>n số xe:", "Contract_Status_Name": "Trạng thái đã giải ngân:", "Partner_ContractID": "Mã số hồ sơ đối tác:", "list_profile": "<PERSON><PERSON> s<PERSON>ch hồ sơ bàn giao", "staff_info": "Thông tin nhân viên lấy chứng từ", "working_papers_staff": "<PERSON><PERSON><PERSON><PERSON> tờ công tác của nhân viên lấy chứng từ", "identification_staff": "<PERSON><PERSON><PERSON><PERSON> tờ tùy thân của nhân viên lấy chứng từ", "signed_statement_staff": "Bằng kê có chữ ký của nhân viên lấy chứng từ", "sign_staff": "<PERSON><PERSON><PERSON> viên l<PERSON>y chứng từ ký tên", "Count_RQContract": "<PERSON>ổng số hồ sơ bàn giao:", "HanoverDate": "<PERSON>h<PERSON><PERSON> gian bàn giao:", "HandoverStatusName": "<PERSON><PERSON><PERSON><PERSON> thái biên bản bàn giao", "continue_print_uppercase": "TIẾP TỤC IN", "choose_day": "<PERSON><PERSON><PERSON>:", "handover_code": "<PERSON><PERSON> biên bản bàn giao", "no_find_contract": "<PERSON><PERSON><PERSON><PERSON> tìm thấy hồ sơ vay", "no_find_exam": "<PERSON>hông có thông tin mẫu in", "no_find_list": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh sách hồ sơ bàn giao", "no_find": "<PERSON><PERSON><PERSON><PERSON> tìm thấ<PERSON> hồ sơ bàn giao chứng từ", "update_fail_retry": "<PERSON><PERSON><PERSON> nh<PERSON>t yêu cầu bàn giao không thành công, vui lòng thử lại", "fail_tryagain": "<PERSON><PERSON><PERSON> yêu cầu bàn giao không thành công, vui lòng thử lại", "fail_conect": "<PERSON>ết nối vào F88 bị lỗi. <PERSON><PERSON> lòng liên hệ IT để được hỗ trợ", "no_find_file": "<PERSON><PERSON><PERSON><PERSON> tìm thấy hồ sơ vay", "customer_portrait": "<PERSON>ân dung khách hàng"}, "searchPicker": {"placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa để tìm kiếm...", "fail_loading": "<PERSON><PERSON><PERSON>ng tìm thấy dữ liệu!"}, "modalFilter": {"search": "<PERSON><PERSON><PERSON>", "search_enter": "<PERSON><PERSON><PERSON> trong lịch sử nhập", "search_export": "<PERSON><PERSON><PERSON> trong lịch sử xuất", "select_promotion_date": "<PERSON><PERSON><PERSON> mãi:", "select_sale_program_id": "Chương trình trả góp:"}, "oldProduct": {"approve": "<PERSON><PERSON><PERSON><PERSON>", "reject": "Bỏ duyệt", "enter_reject_reason": "<PERSON><PERSON> lòng nhập lý do bỏ duyệt", "reject_reason": "<PERSON><PERSON> do bỏ duyệt", "reason_1": "<PERSON><PERSON><PERSON> tê<PERSON>, IMEI, m<PERSON><PERSON> s<PERSON>c", "reason_2": "<PERSON><PERSON><PERSON> thi<PERSON> phụ kiện", "reason_3": "<PERSON><PERSON><PERSON> bể màn hình", "reason_4": "<PERSON><PERSON><PERSON> cho m<PERSON>", "reason_5": "<PERSON><PERSON><PERSON>", "reason_6": "Máy lỗi", "other_reason": "Lý do khác", "lack_of_images": "Bạn đang thiếu hình <PERSON>nh cho: ", "placeholder_search": "<PERSON><PERSON><PERSON><PERSON>, Tên SP", "choices_category": "<PERSON><PERSON><PERSON> ng<PERSON>nh hàng", "choices_status": "<PERSON><PERSON><PERSON> trạng thái", "no_find_oldproduct": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh sách máy cũ", "no_find_image": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh sách hình máy cũ", "no_find_accessories": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh sách máy cũ", "no_find_accessories_oldproduct": "<PERSON><PERSON><PERSON><PERSON> có danh sách phụ kiện sản phẩm cũ", "up_image_suscess": "<PERSON><PERSON><PERSON>nh không thành công", "confirm_update_status": "Bạn có muốn cập nhật trạng thái sản phẩm?", "update_suscess": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "accessories_included": "<PERSON><PERSON> kiện kèm theo: ", "btn_close": "Đ<PERSON><PERSON>", "btn_update": "<PERSON><PERSON><PERSON>", "imei": "IMEI: ", "price": "Giá: ", "warranty": "<PERSON><PERSON><PERSON>: ", "inventory_status_name": "<PERSON>r<PERSON><PERSON> thái sản phẩm: ", "status": "Tình trạng: ", "view_web": "Xem ở web", "upload_oldproduct": "Upload hình máy cũ", "name_product": "Tên SP: ", "import_store": "<PERSON><PERSON> nhập kho đ<PERSON>", "time": "giờ", "view_detail": "<PERSON>em chi tiết", "placeholder_note": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "update_info": "<PERSON><PERSON><PERSON> nhật thông tin", "confirm_delete_image": "Bạn có chắc muốn xóa ảnh?", "point_gift": "<PERSON><PERSON><PERSON>m thưởng Upload hình máy cũ", "staff": "Nhân viên: ", "sum_image": "<PERSON><PERSON><PERSON> hình: ", "reward": "Tổng thưởng: ", "header_point_gift": "ĐIỂM THƯỞNG UPLOAD HÌNH MÁY CŨ", "header_upload_oldproduct": "UPLOAD HÌNH MÁY CŨ", "header_old_product_info": "THÔNG TIN MÁY CŨ", "condition": "<PERSON>ui lòng cập nhật thông tin tình trạng và danh sách phụ kiện trước khi chụp hình!", "direction": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> anh chị kiểm tra model, màu sắc sản phẩm, đủ ảnh và đúng như ảnh mẫu mới đư<PERSON><PERSON> du<PERSON>t", "value_0": "Tr<PERSON>ng thái - All", "value_1": "Chờ duyệt Web", "value_2": "Đã duyệt Web", "value_4": "<PERSON><PERSON> ch<PERSON> kho", "value_7": "<PERSON><PERSON><PERSON> c<PERSON> khách đặt", "value_8": "Chưa up Web", "cagegory_-1": "<PERSON><PERSON><PERSON> – All", "cagegory_42": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "cagegory_44": "Laptop", "cagegory_522": "<PERSON><PERSON><PERSON> b<PERSON>", "cagegory_1942": "Tivi", "cagegory_1882": "Wearable", "cagegory_7077": "<PERSON><PERSON><PERSON> hồ thông minh", "cagegory_166": "<PERSON><PERSON> đ<PERSON>ng", "cagegory_1943": "Tủ lạnh", "cagegory_1944": "Máy giặt", "cagegory_1962": "<PERSON><PERSON><PERSON> n<PERSON>g", "cagegory_2002": "<PERSON><PERSON><PERSON>", "cagegory_2202": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> qu<PERSON>n <PERSON>o", "cagegory_2065": "Micro", "cagegory_622": "Micro", "cagegory_2162": "<PERSON><PERSON>, Dàn <PERSON>h", "cagegory_165": "<PERSON><PERSON><PERSON>", "cagegory_2022": "DVD, Karaoke", "cagegory_3305": "<PERSON><PERSON><PERSON> hồ<PERSON> ng<PERSON>", "cagegory_284": "<PERSON><PERSON><PERSON>", "cagegory_1982": "<PERSON><PERSON><PERSON> từ", "cagegory_7604": "<PERSON><PERSON><PERSON> b<PERSON>", "cagegory_3385": "<PERSON><PERSON><PERSON>", "cagegory_5693": "<PERSON><PERSON><PERSON> in, Fax", "cagegory_5697": "<PERSON><PERSON><PERSON> h<PERSON>nh má<PERSON> t<PERSON>h", "cagegory_5698": "<PERSON><PERSON><PERSON> t<PERSON>h để bàn", "cagegory_5475": "<PERSON><PERSON><PERSON><PERSON>", "cagegory_1": "K<PERSON><PERSON><PERSON>", "cagegory_54": "<PERSON> nghe", "cagegory_2162_1": "Loa", "cagegory_4727": "<PERSON><PERSON><PERSON><PERSON> bị mạng", "cagegory_9499": "<PERSON><PERSON><PERSON> s<PERSON>, chuy<PERSON>n đổi", "cagegory_9118": "TV Box", "cagegory_10618": "Airtag", "cagegory_86": "<PERSON><PERSON><PERSON>", "cagegory_862": "<PERSON><PERSON><PERSON>", "cagegory_57": "Sạc dự phòng", "cagegory_1882_1": "<PERSON><PERSON> kiện tablet", "cagegory_7264": "<PERSON><PERSON><PERSON> hồ thời trang", "cagegory_2222": "<PERSON><PERSON><PERSON> nư<PERSON> nóng lạnh", "cagegory_1989": "<PERSON><PERSON><PERSON> đun siêu tốc", "cagegory_1984": "<PERSON><PERSON><PERSON>", "cagegory_1988": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "cagegory_1992": "Quạt", "cagegory_10139": "<PERSON> hút b<PERSON>i", "cagegory_1985": "<PERSON><PERSON><PERSON> xay sinh tố", "cagegory_1991": "<PERSON><PERSON><PERSON> t<PERSON>c", "cagegory_1922": "<PERSON><PERSON><PERSON> c<PERSON>m đi<PERSON>n", "cagegory_1983": "Bếp gas", "cagegory_1987": "Lò vi sóng", "cagegory_1990": "<PERSON><PERSON><PERSON>", "cagegory_9418": "<PERSON><PERSON><PERSON> chiên không dầu", "cagegory_1986": "<PERSON><PERSON><PERSON>", "cagegory_2062": "<PERSON><PERSON>y Ép Trái <PERSON>", "cagegory_2064": "<PERSON><PERSON><PERSON>", "cagegory_2262": "<PERSON><PERSON><PERSON>", "cagegory_2322": "<PERSON><PERSON><PERSON>", "cagegory_2428": "Quạt Sưởi", "cagegory_5473": "<PERSON><PERSON><PERSON> Không Khí", "cagegory_7498": "<PERSON><PERSON><PERSON><PERSON>", "title_1": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "title_2": "<PERSON><PERSON><PERSON><PERSON>", "title_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turn": "Xoay", "error_turn": "<PERSON><PERSON> lỗi xảy ra trong quá trình xoay hình. Xin bạn vui lòng chụp lại hình ảnh", "error_turn_1": "Lỗi xoay hình", "error_edit": "Lỗi chỉnh sửa hình ảnh. Xin vui lòng thử lại", "error_getinfo": "Lỗi lấy thông tin hình ảnh. <PERSON><PERSON> lòng thử lại", "error_select": "Lỗi chọn hình ảnh. Xin vui lòng thử lại", "error_take": "Lỗi chụp hình ảnh. Xin vui lòng thử lại", "required_accessory_approve": "<PERSON><PERSON> lòng cập nhật danh sách phụ kiện tr<PERSON><PERSON><PERSON> khi duyệt", "inventory_status_name_2": "Đã sử dụng", "inventory_status_name_7": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> (bỏ mẫu)", "choices_status_inventory": "<PERSON><PERSON>n trạng thái tồn kho", "placeholder_description": "<PERSON><PERSON><PERSON><PERSON> thông tin mô tả hiện tại (Nhập mô tả cũ và mới):", "cancel_modal": "<PERSON><PERSON><PERSON>", "approve_modal": "<PERSON><PERSON><PERSON> t<PERSON>t", "modal_title": "<PERSON><PERSON> tả tình trạng", "title_desc_form": "<PERSON><PERSON> tả chi tiết tình trạng máy cũ:", "empty_desc_warning": "<PERSON><PERSON><PERSON> nhập mô tả chi tiết tình trạng máy cũ. <PERSON><PERSON><PERSON> cầu anh chị nhập đầy đủ thông tin mô tả tình trạng máy trư<PERSON><PERSON> khi duyệt", "old_image_title": "<PERSON><PERSON><PERSON> c<PERSON>", "new_image_title": "<PERSON><PERSON><PERSON> m<PERSON>i", "wrong_desc_old_product": "SAI MÔ TẢ", "right_desc_old_product": "ĐÚNG MÔ TẢ", "warning_confirm_status_desc": "Bạn vui lòng xác nhận thông tin mô tả máy cũ trước khi up hình!", "update_desc_err": "Lỗi cập nhật mô tả máy cũ", "condition_copy": "<PERSON>ui lòng cập nhật thông tin tình trạng và danh sách phụ kiện trước khi sao chép hình ảnh!"}, "offlineCart": {"delivery_form": "<PERSON><PERSON><PERSON> thức giao", "btn_continue": "<PERSON><PERSON><PERSON><PERSON>", "pick_up_at_store": "<PERSON><PERSON><PERSON><PERSON> tại siêu thị", "store_deliver": "<PERSON><PERSON><PERSON> thị đi giao", "dedicated_deliver": "<PERSON><PERSON><PERSON>âm đi giao", "choose_delivery_method": "<PERSON><PERSON><PERSON> hình thức giao:"}, "offlineCreateOrder": {"btn_create_order": "TẠO ĐƠN", "info_create_export_request": "THÔNG TIN GIAO HÀNG ĐỂ TẠO YÊU CẦU XUẤT", "create_export_request": "TẠO ĐƠN"}, "offlineOrderManager": {"receipt_delivery": "<PERSON><PERSON>u thu kiêm phiếu giao hàng", "no_find_printed": "<PERSON>hông có thông tin mẫu in", "err_html": "<PERSON><PERSON><PERSON> trình in lỗi. <PERSON><PERSON> lòng vào chức năng \"In lại\" để thao tác tiếp.", "no_vote_info": "<PERSON><PERSON><PERSON>ng có thông tin phiếu", "confirm_delete": "Bạn có chắc muốn xoá thông tin phiếu bán hàng ", "delete_success": "<PERSON><PERSON><PERSON> bán hàng xoá thành công", "delete_err": "<PERSON><PERSON><PERSON> bán hàng xoá không thành công", "form_code": "<PERSON><PERSON><PERSON> ", "form_ordered": "<PERSON><PERSON><PERSON> đã tạo đơn", "form_order_yet": "<PERSON><PERSON><PERSON> ch<PERSON>a tạo đơn", "total_money": "<PERSON><PERSON>ng tiền sản phẩm : ", "collected": "<PERSON><PERSON> thu:", "collected_export_request:": "<PERSON><PERSON> thu trên yêu cầu xuất:", "placeholder": "<PERSON>hập mã phiếu bán hàng/SĐT/Tên khách hàng"}, "offlinePayment": {"vote_generation_failed": "<PERSON><PERSON><PERSON> phi<PERSON>u không thành công", "imei": "IMEI ", "not_in_stock": " kh<PERSON>ng tồn kho ", "not_exist": " kh<PERSON>ng tồn tại ", "pls_enter_imei": "<PERSON><PERSON> lòng nhập IMEI sản phẩm", "please_choose_collect_payment_delivery": "<PERSON><PERSON> lòng chọn thu tiền để giao hàng", "pls_collect_money": "<PERSON><PERSON> lòng thu đủ số tiền phải thanh toán", "pls_collect_owed": "<PERSON><PERSON> lòng thu đủ số tiền còn nợ", "create_form": "TẠO PHIẾU BÁN HÀNG", "imei_exist": "IMEI đã tồn tại trong đơn hàng", "pls_enter_serial": "<PERSON><PERSON> lòng nhập SERIAL sản phẩm", "placeholder_imei": "Nhập IMEI b<PERSON><PERSON>nh", "imei_not_in_stock": "IMEI không tồn kho", "placeholder_search": "Nhập mã SP, IMEI"}, "offlineSale": {"imei_not_in_stock": "IMEI không tồn kho", "placeholder_search": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h x<PERSON>c mã sản phẩm, IMEI"}, "offlineNavigate": {"manager": "QUẢN LÝ PHIẾU BÁN HÀNG", "search": "TÌM KIẾM SẢN PHẨM", "promotion": "THÔNG TIN KHUYẾN MÃI", "cart": "GIỎ HÀNG OFFLINE", "create_form": "TẠO PHIẾU BÁN HÀNG"}, "goodsReturn": {"fee_details": "CHI TIẾT PHÍ THU", "fee_tax_details": "CHI TIẾT MỨC PHIẾU THU", "period": "<PERSON><PERSON><PERSON><PERSON> thời gian", "fee_return_product": "<PERSON><PERSON> tr<PERSON> m<PERSON> (%)", "fee_return_product_on_day": "<PERSON><PERSON> tr<PERSON> máy trên <PERSON> (%)", "corresponding_value": "<PERSON><PERSON><PERSON> trị tương <PERSON>ng", "btn_accept": "OK", "btn_done": "<PERSON><PERSON>", "time_hold_product": "<PERSON>h<PERSON>i gian giữ sản phẩm", "export_price_vat": "Đơn giá xuất có VAT", "fee_return": "<PERSON><PERSON> hoàn tiền", "fee_return_total": "<PERSON>í thu + hoàn tiền", "fee_warranty": "<PERSON><PERSON><PERSON>m phí vi phạm cam kết b<PERSON><PERSON> hành", "fee_total": "Tổng cộng phí", "customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>: ", "phone_number": "<PERSON><PERSON> điện thoại: ", "address": "Địa chỉ: ", "export_date": "<PERSON><PERSON><PERSON>: ", "export_store": "<PERSON><PERSON>: ", "lookup_fee": "<PERSON>ra c<PERSON>u phí", "product_status": "<PERSON><PERSON><PERSON> trạng sản phẩm", "return_quantity": "Số lư<PERSON>ng trả", "select_product_status": "<PERSON><PERSON><PERSON> tình trạng sản phẩm", "refund_method": "<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền", "select_refund_method": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền", "return_accessories": "<PERSON><PERSON> kiện cần trả", "detail": "<PERSON> ti<PERSON>", "residual_value_of_product": "<PERSON><PERSON>á trị còn lại của 1 sản phẩm", "total_fee": "T<PERSON>ng phí thu", "status": "Tình trạng máy: ", "return_product_fee": "<PERSON><PERSON> thu sản ph<PERSON>m khách trả", "note": "<PERSON><PERSON><PERSON>", "adjust_reduce_fees": "Điều chỉnh giảm thu phí", "placeholder_search": "Nhập IMEI/Mã phiếu xuất/Mã YCX/SĐT", "product_id": "<PERSON>ã sản phẩm: ", "imei": "IMEI: ", "quantity": "Số lượng: ", "price": "Giá: ", "return_value": "<PERSON><PERSON><PERSON> trị trả hàng: ", "fees_per_one": "<PERSON><PERSON> thu trên 1 sản phẩm", "fees": "<PERSON><PERSON> thu", "fees_percent": "Tỉ lệ thu phí (%): ", "no_found_goods_return": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin yêu cầu xuất", "please_select_an_product": "<PERSON><PERSON> lòng chọn sản phẩm để tiếp tục.", "seri": "Seri: ", "holding_time": "Thời gian giữ sản phẩm: ", "output_type": "<PERSON><PERSON><PERSON> thức xuất: ", "returned_quantity": "Số lượng đã đổi trả: ", "view_detail": "<PERSON>em chi tiết", "fee_due_to_deposit": "Tiền thu do giữ lại phí cọc", "fee_due_to_surcharge": "Tiền thu do giữ lại phụ phí", "lookup_product_returns": "<PERSON>ra c<PERSON>u đổi tr<PERSON> hàng", "total": "<PERSON><PERSON><PERSON> hóa đơn: ", "payment_method": "<PERSON><PERSON><PERSON> thức thanh to<PERSON>: ", "adjust_fees": "Điều chỉnh phí thu", "no_params_declare_note": "<PERSON><PERSON><PERSON> phẩm chưa khai báo tham số đổi trả hàng. <PERSON><PERSON> lòng liên hệ 18291 - <PERSON><PERSON><PERSON>ễn Đ<PERSON><PERSON> Hào để hỗ trợ khai báo.", "no_params_declare_notification": "Có %{quantity} sản phẩm chưa khai báo tham số trả hàng. Vui lòng xem lý do tại phần thông tin thêm.", "no_cashback_method": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ph<PERSON><PERSON><PERSON> thức hoàn trả", "no_fee_info": "<PERSON><PERSON><PERSON>ng có thông tin giá", "no_invoice_info": "<PERSON><PERSON><PERSON><PERSON> có thông tin hoá đơn", "not_liquidated_yet": "CHƯA THANH LÝ", "liquidated": "THANH LÝ", "liquidation_cancel": "ĐÃ HUỶ", "liquidation_unknown": "KHÔNG RÕ", "contract_status": "<PERSON>r<PERSON><PERSON> thái hợp đồng: ", "full_payment": "Trả thẳng", "installment": "Trả góp", "contract_id": "<PERSON><PERSON> hợp đồng: ", "total_amount": "Tổng tiền: ", "product_list": "DANH SÁCH SẢN PHẨM", "paid_total_amount": "Số tiền đã thanh toán", "total_amount_of_missing_promotion": "Tiền thu do không trả khuyến mãi", "total_amount_of_debt_payment_promotion": "Tiền thu do đã chi nợ khuyến mãi", "total_amount_of_missing_accessories": "Ti<PERSON>n thu do trả thiếu phụ kiện", "other_total_amount": "<PERSON><PERSON><PERSON> phí thu khác", "collection_detail_of_sale_order": "CHI TIẾT THU CỦA YÊU CẦU XUẤT", "cash": "Tiền mặt", "money_from_card": "<PERSON><PERSON><PERSON><PERSON> qua thẻ", "money_from_voucher": "<PERSON><PERSON><PERSON><PERSON> phi<PERSON>u mua hàng", "cash_in_bank": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i ngân hàng", "vip_point": "<PERSON><PERSON><PERSON><PERSON> quà tặng VIP", "money_collected_by_accountant": "Tiền do kế toán thu", "clearing_debt_money": "Tiền tự động cấn trừ công nợ", "no_info_clearing_debt_money": "<PERSON><PERSON><PERSON><PERSON> có chi tiết tiền tự động cấn trừ công nợ.", "paid": "<PERSON><PERSON> thanh toán", "total_final": "<PERSON><PERSON><PERSON> cộng", "notice_content": "<PERSON>ã thanh toán được tính là tổng tiền không bao gồm tiền do kế toán thu.", "notice": "<PERSON><PERSON><PERSON>", "money_amount": "<PERSON><PERSON> tiền", "surcharge_detail": "<PERSON> tiết phụ phí", "delivery_cost": "Phí giao: ", "forward_cost": "<PERSON><PERSON> chuyển: ", "opportunity_cost": "<PERSON><PERSON> c<PERSON> hội: ", "please_select_product_status": "<PERSON><PERSON> lòng chọn Tình trạng sản phẩm", "processed_date": "<PERSON><PERSON><PERSON> chi: ", "processed_store_name": "<PERSON>ho chi: ", "process_voucher_concern": "Mã phiếu chi: ", "debt_promotion_paid": "Số tiền chi: ", "debt_payment_promotion_list_header": "DANH SÁCH SẢN PHẨM CHI NỢ KHUYẾN MÃI", "select_invoice_back_header": "CHỌN HOÁ ĐƠN THU HỒI", "returns_products_list_header": "DANH SÁCH SẢN PHẨM TRẢ HÀNG", "whether_return_record_is_valid": "<PERSON><PERSON><PERSON><PERSON> bản tr<PERSON> hàng hợp lệ", "whether_customer_asks_invoice": "<PERSON><PERSON><PERSON><PERSON> hàng có xuất hoá đơn", "fee_due_to_lost_invoice": "<PERSON><PERSON> do mất hoá đơn", "full_box_fee": "<PERSON><PERSON> đổi Fullbox", "product_required_jobcard": "<PERSON><PERSON><PERSON> phẩm yêu cầu nhập mã <PERSON><PERSON> khi đổi trả", "product_auto_generated_jobcard_push_to_wms": "<PERSON><PERSON><PERSON> phẩm tự động tạo Jobcard đ<PERSON>y qua WMS", "product_required_expertise_from_crm": "<PERSON><PERSON><PERSON> phẩm có yêu cầu phiếu thẩm định từ CRM", "alert_negative_fee": "<PERSON><PERSON><PERSON> trị còn lại của sản phẩm < 0. <PERSON><PERSON><PERSON> không thể nhập trả sản phẩm này. <PERSON>ui lòng liên hệ 3588 - Vương Văn <PERSON> để hỗ trợ!", "alert_input_negative_quantity": "<PERSON><PERSON><PERSON><PERSON> thể nhập tr<PERSON> số lượng nhỏ hơn 0", "alert_input_over_quantity": "<PERSON><PERSON><PERSON><PERSON> thể nhập trả số lượng lớn hơn số lượng cần trả", "surcharge": "Phụ phí: ", "must_collect": "<PERSON><PERSON><PERSON> thu", "must_expense": "<PERSON><PERSON><PERSON> chi", "installment_contract_info_header": "THÔNG TIN HỢP ĐỒNG TRẢ GÓP", "select_exchanged_promotion_header": "CHỌN KHUYẾN MÃI ĐÃ ĐỔI HÀNG CẦN TRẢ LẠI", "no_params_exchange": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tham số đổi trả"}, "switchIMEI": {"placeholder_Search": "<PERSON><PERSON><PERSON><PERSON> mã S<PERSON>, <PERSON>ã Lots", "not_found_product": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin sản phẩm", "choice_status": "<PERSON><PERSON><PERSON> trạng thái sản phẩm", "product_input": "<PERSON><PERSON><PERSON> ph<PERSON>m đã nhập", "btn_export": "<PERSON><PERSON><PERSON> đổi hàng", "switch_Product": "<PERSON><PERSON><PERSON> phẩm chuyển đổi", "placeholder_imei": "Nhập IMEI sản phẩm", "btn_input": "<PERSON><PERSON><PERSON><PERSON>", "pls_choice_status": "<PERSON><PERSON> lòng chọn trạng thái sản phẩm", "enter_and_check_imei": "<PERSON><PERSON> lòng nhập IMEI chuyển đổi ", "confirm_export": "<PERSON><PERSON>n chắc chắn muốn xuất đổi hàng", "notify_success": "<PERSON><PERSON> xuất chuyển đổi thành công", "product_name": "<PERSON><PERSON><PERSON> sản phẩm: ", "product_id": "<PERSON>ã sản phẩm: ", "enter_other_imei": "<PERSON><PERSON> đã tồn tại trong l<PERSON>, <PERSON><PERSON> lò<PERSON>h<PERSON><PERSON> <PERSON>.", "count_imei": "Tổng IMEI đã nhập:", "quantity_imei_instock": "<PERSON><PERSON> lượng tồn kho không bị lock hàng:", "scanned_successfully": "<PERSON><PERSON> quét thành công"}, "searchInOutVoucher": {"text_input_placeholder": "Nhập NV tạo/Mã CM/Chứng từ LQ/SĐT", "search_type_-1": "<PERSON><PERSON><PERSON> <PERSON><PERSON> cả", "search_type_1": "<PERSON><PERSON><PERSON> viên tạo", "search_type_2": "Mã phiếu thu/chi", "search_type_3": "<PERSON><PERSON><PERSON> từ liên quan", "search_type_4": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "source": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>o", "customer_name": "<PERSON><PERSON><PERSON> h<PERSON>ng", "total_liquidate": "<PERSON><PERSON><PERSON><PERSON>h toán", "debt": "<PERSON><PERSON><PERSON><PERSON> nợ", "voucher_type_name": "<PERSON><PERSON><PERSON>", "inout_voucher_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin phiếu thu chi.", "collect_expense": "Đã %{type}", "have_not_history": "<PERSON><PERSON><PERSON> c<PERSON> lịch sử thu chi.", "voucher_info": "Thông tin phiếu", "in_history": "<PERSON><PERSON><PERSON> sử thu", "out_history": "<PERSON><PERSON><PERSON> sử chi", "in_detail_inout": "CHI TIẾT PHIẾU THU", "out_detail_inout": "CHI TIẾT PHIẾU CHI", "staff": "Nhân viên: ", "store": "Kho: ", "in_real": "Thực thu: ", "out_real": "Thực chi: ", "pls_enter_reasson": "<PERSON><PERSON> lòng nh<PERSON>p lý do", "btn_adjust": "<PERSON><PERSON><PERSON> yêu cầu điều chỉnh", "store_create": "<PERSON><PERSON> tạo phi<PERSON>u: ", "style_voucher": "<PERSON><PERSON><PERSON>", "bill_id": "S<PERSON> hóa đơn", "placeholder_bill_id": "<PERSON><PERSON><PERSON><PERSON> số hóa đơn", "bill_sys": "<PERSON><PERSON> <PERSON><PERSON><PERSON> hóa đơn", "placeholder_bill_sys": "<PERSON><PERSON><PERSON><PERSON> kí hiệu hóa đơn", "date_voucher": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>n", "content": "Nội dung:", "placeholder_content": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "totalMoney": "<PERSON><PERSON><PERSON> tiền", "placeholder_totalmoney": "<PERSON><PERSON><PERSON><PERSON> tổng tiền", "cash": "Tiền mặt", "tax_id": "<PERSON><PERSON> số thuế:", "placeholder_taxid": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "attched_file": "<PERSON>h<PERSON><PERSON> file đ<PERSON>h kèm", "note": "<PERSON><PERSON><PERSON>", "detail": "CHI TIẾT"}, "pharmacy": {"no_selected_product": "<PERSON><PERSON> lòng chọn sản phẩm để tiếp tục.", "no_quantity_product": "<PERSON><PERSON> lòng nhập số lượng sản phẩm.", "lot_date": "Lô/Date", "batch_id": "Số lô", "expiration": "Hạn sử dụng", "stock": "SL tồn", "quantity": "SL bán", "lot_date_info": "Thông tin Lô Date", "wrong_total_quantity_msg": "<PERSON><PERSON> lư<PERSON><PERSON> bán đ<PERSON> nhập chưa đúng. <PERSON><PERSON> lòng kiểm tra lại.", "over_total_instock_msg": "<PERSON><PERSON> lượng bán đ<PERSON> nhập quá số lượng tồn còn lại.", "stock_available": "<PERSON><PERSON><PERSON> b<PERSON>", "before_meal": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "after_meal": "<PERSON>u khi <PERSON>n", "instruction_placeholder": "<PERSON><PERSON><PERSON><PERSON> hướng dẫn sử dụng", "morning": "<PERSON><PERSON><PERSON>", "noon": "Trưa", "afternoon": "<PERSON><PERSON><PERSON>", "evening": "<PERSON><PERSON><PERSON>", "quantity_acronym": "SL", "information": "Thông tin", "quantity_header": "Số lượng", "lot_date_header": "Lô Date", "unknown_batch": "<PERSON><PERSON> ch<PERSON>a x<PERSON><PERSON>"}, "insert_new_product": {"name_product": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "active": "<PERSON><PERSON><PERSON> ch<PERSON>", "quantity": "<PERSON><PERSON> l<PERSON> cần", "industry": "<PERSON><PERSON><PERSON>", "supplier": "<PERSON><PERSON><PERSON> cung cấp", "insert": "<PERSON><PERSON><PERSON><PERSON> mới", "complete": "<PERSON><PERSON><PERSON> t<PERSON>t", "delete": "<PERSON><PERSON><PERSON>", "pls_enter_product_name": "<PERSON><PERSON> lòng nhập tên <PERSON>", "pls_enter_active": "<PERSON><PERSON> lòng nh<PERSON><PERSON> ho<PERSON> chất", "pls_choice_industry": "<PERSON><PERSON> lòng chọn ngành hàng", "validate_quantity": "<PERSON><PERSON> lượng bạn nhập chưa hợp lệ. <PERSON>ui lòng nhập lại!", "validate_image": "<PERSON><PERSON> lòng ch<PERSON><PERSON> sản phẩm", "pls_insert_new_product": "<PERSON><PERSON> lòng thêm mới sản phẩm trư<PERSON>c khi hoàn tất."}, "tips": {"use_guide": "Mẹo sử dụng", "article": "Hướng dẫn", "list_of_article": "<PERSON><PERSON> s<PERSON>ch b<PERSON>i vi<PERSON>t", "detail_of_article": "<PERSON> tiết bài viết", "no_use_guide_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin mẹo sử dụng"}, "display_manager": {"all_position_by_product": "1. <PERSON><PERSON> tất cả vị trí theo sản phẩm", "postion_by_shelfgroup": "2. <PERSON><PERSON> vị trí sản phẩm theo nhóm kệ", "confirm_display": "3. <PERSON><PERSON><PERSON> tr<PERSON><PERSON> b<PERSON>y", "printer_setting": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> in", "goods_addition_warning": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> châm hàng", "goods_addition_auto": "<PERSON><PERSON><PERSON> hàng tự động", "goods_addition_by_hand": "<PERSON><PERSON><PERSON> hàng tay", "unfilled_goods_list": "DANH SÁCH CHƯA CHÂM HÀNG", "change_status": "Chuyển trạng thái", "change_store": "<PERSON><PERSON><PERSON><PERSON> kho", "display": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>y", "fill_goods": "<PERSON><PERSON><PERSON>", "pull_goods": "<PERSON><PERSON><PERSON>", "not_found_shelf_group": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhóm kệ", "choose_shelf_group": "<PERSON><PERSON><PERSON> n<PERSON> kệ", "shelf_groups_list": "<PERSON><PERSON> s<PERSON>ch nh<PERSON>m kệ", "trays_list": "<PERSON><PERSON> s<PERSON>ch mâm", "see_more": "<PERSON><PERSON>", "empty_tray": "<PERSON>ện tại các mâm của nhóm kệ đã xác nhận trưng bày đầy đủ.", "display_confirmation_success": "<PERSON><PERSON><PERSON> nhận trưng bày thành công", "confirm_warning_fill_goods": "Bạn có chắc đã kiểm tra thông tin cảnh báo và thực hiện xác nhận trưng bày không?", "cancel": "<PERSON><PERSON><PERSON>", "empty_goods_display": "<PERSON><PERSON> lòng chọn sản phẩm trưng bày", "not_found_barcode": "<PERSON>ã vạch không thuộc nhóm kệ. <PERSON><PERSON> lòng thử lại", "not_found_shelf_group_code": "<PERSON><PERSON> nhóm kệ không đúng. <PERSON><PERSON> lòng thử lại", "full_product": "<PERSON><PERSON><PERSON> phẩm đã đư<PERSON><PERSON> chọn hết", "not_found_product": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "tray_shortdesc": "<PERSON><PERSON><PERSON>", "product_position": "<PERSON><PERSON> trí trưng bày:", "product_order": "<PERSON><PERSON><PERSON> tự sản phẩm:", "product_name": "<PERSON><PERSON><PERSON> sản phẩm:", "not_found_data_filling_goods": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu châm hàng. <PERSON><PERSON> lòng liên hệ IT để hỗ trợ.", "success_filling_goods": "<PERSON><PERSON><PERSON><PERSON> hiện châm hàng thành công", "fail_filling_goods": "<PERSON><PERSON><PERSON><PERSON> hiện châm hàng thất bại", "success_filling_goods_calculation": "<PERSON><PERSON><PERSON><PERSON> hiện t<PERSON>h lại châm hàng thành công", "fail_filling_goods_calculation": "<PERSON><PERSON><PERSON><PERSON> hiện t<PERSON>h lại châm hàng thất bại", "product_list": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "please_use_tool": "<PERSON>ui lòng vào tool xác nhận trưng bày để thực hiện châm hàng và xác nhận trưng bày.", "filling_goods_calculation": "<PERSON><PERSON><PERSON> lại châm hàng", "filling_goods_perform": "<PERSON><PERSON><PERSON><PERSON> hiện châm hàng", "sell": "Bán", "replace": "<PERSON>hay thế", "new": "<PERSON><PERSON><PERSON>", "duplicate": "Tr<PERSON><PERSON> trùng", "filling_products_number": "Số lượng châm:", "decreasing_products_number": "Số lượng g<PERSON>:", "position_pick": "<PERSON><PERSON> trí lấy:", "position": "<PERSON><PERSON> trí:", "facing": "Mặt trưng:", "tray": "Mâm:", "replace_number": "S<PERSON> lượng thay:", "pick_from_inventory": "<PERSON><PERSON><PERSON> từ tồn kho", "please_choose_product_to_require": "<PERSON>ui lòng chọn sản phẩm để tạo yêu cầu chuyển!", "please_choose_max_product": "Bạn chỉ đượ<PERSON> phép chuyển kho tối đa 8 sản phẩm. <PERSON><PERSON> lòng kiểm tra lại.", "number_transition": "<PERSON><PERSON> l<PERSON> ch<PERSON>", "make_request": "<PERSON><PERSON><PERSON> y<PERSON> c<PERSON>u", "successfull_update_IMEI": "<PERSON><PERSON><PERSON> nhật IMEI thành công", "full_IMEI": "<PERSON><PERSON><PERSON> phẩm đã nhập đủ số lượng IMEI", "existed_IMEI": "Đã tồn tại IMEI này", "please_choose_IMEI": "Nhập 8 IMEI muốn chuyển trạng thái từ Mới -> Trưng bày của sản phẩm 1234", "enter_IMEI": "Nhập IMEI", "manipulation": "<PERSON><PERSON>", "make_request_success": "<PERSON><PERSON><PERSON> y<PERSON>u cầu thành công", "error_filling_goods_by_hand": "Lỗi châm hàng tay, vui lòng thử lại", "please_choose_2_shelf_group": "Bạn vui lòng chọn 2 nhóm kệ để tìm thông tin", "please_choose_2_to_4_shelf_group": "<PERSON><PERSON> lòng chọn từ 2 đến 4 nhóm kệ.", "see_info_cluster_shelf_group": "<PERSON>em thông tin cụm nhóm kệ", "search": "<PERSON><PERSON><PERSON>", "please_choose_shelf_group_to_print": "<PERSON><PERSON> lòng chọn nhóm kệ cần in", "please_choose_tray_to_print": "<PERSON><PERSON> lòng chọn mâm cần in", "not_found_tray_of_shelf_group_to_print": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu mâm nhóm kệ cần in", "print_follow_tray": "In theo mâm", "print_follow_shelf_group": "In theo nhóm kệ", "confirm_warning_filling_goods_success": "<PERSON><PERSON><PERSON>n cảnh báo châm hàng thành công", "confirm_warning_filling_goods_fail": "<PERSON><PERSON><PERSON> n<PERSON>n cảnh báo châm hàng thất bại", "please_check_info_warning_filling_goods": "Bạn có chắc đã kiểm tra thông tin cảnh báo và thực hiện châm hàng đầy đủ?", "product_to_filling_goods": "<PERSON><PERSON><PERSON> phẩm cần châm", "export_quantity": "SL xuất", "inventory_quantity": "SL tồn kho", "please_confirm_warning_filling_good": "<PERSON><PERSON> lòng xác nhận thông báo “Cảnh báo châm hàng” và thực hiện châm hàng đẩy đủ cho các sản phẩm đã đư<PERSON>c bán.", "confirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "confirm_notice": "<PERSON><PERSON><PERSON>n cảnh báo", "enter_info_product": "<PERSON>hậ<PERSON> mã SP, tên SP", "title_all_position_by_product": "<PERSON><PERSON> tất cả vị trí theo sản phẩm", "shelf_group": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>:", "In-stock_quantity": "SL tồn:", "display_quantity": "SL trưng bày:", "model": "<PERSON><PERSON> h<PERSON>nh:", "primary_product": "SP chính:", "price": "Giá:", "SPEC": "TSKT: ", "MSR": "NSX: ", "length": "Dài:", "width": ", Rộng:", "height": ", Cao:", "not_found_data": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu", "not_found_unsuccessful_tray": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu mâm chưa hoàn tất", "malformed_value": "<PERSON><PERSON><PERSON> trị nhập vào không đúng định dạng", "unsuccessful_shelf_groups_list": "<PERSON><PERSON> s<PERSON>ch nhóm kệ chưa hoàn tất", "enter_shelf_group_barcode": "<PERSON>hậ<PERSON> mã vạch mâm của nhóm kệ", "please_choose_maingroup": "<PERSON><PERSON> lòng chọn ngành hàng", "please_choose_subgroup": "<PERSON><PERSON> lòng chọn nhóm hàng", "please_choose_max_1_subgroup": "Chỉ đượ<PERSON> chọn tối đa 1 nhóm hàng. <PERSON><PERSON> lòng kiểm tra lại danh sách nhóm hàng đã chọn", "choose_maingroup": "<PERSON><PERSON><PERSON> ng<PERSON>nh hàng", "choose_subgroup": "<PERSON><PERSON><PERSON> n<PERSON> hàng", "find": "<PERSON><PERSON><PERSON>", "enter_info_product_1": "<PERSON><PERSON><PERSON><PERSON> tên/ mã sản phẩm", "enter_shelf_group_code": "<PERSON><PERSON><PERSON><PERSON> mã nhóm kệ", "empty_shelf_groups_list": "<PERSON><PERSON> s<PERSON>ch nhóm kệ trống", "empty_tray_list": "<PERSON><PERSON> s<PERSON>ch mâm trống", "confirm": "<PERSON><PERSON><PERSON>", "enter_product_code": "<PERSON><PERSON><PERSON><PERSON> mã sản phẩm", "empty_products_list": "<PERSON><PERSON> s<PERSON>ch sản phẩm trống", "show_checked_product": "Chỉ hiện thị các sản phẩm đư<PERSON><PERSON> chọn", "sell_quantity": "Số lư<PERSON>ng bán:", "please_choose_IMEI_1": "Bạn phải nhập đủ {0} IMEI cho sản phẩm đã chọn!", "filling_quantity": "<PERSON><PERSON> lượng châm hàng:", "please_choose_IMEI_New_Display": "*** <PERSON><PERSON><PERSON><PERSON> {0} IMEI muốn chuyển trạng thái từ Mới -> Trưng bày của sản phẩm {1}", "product_name_sortdesc": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "not_found_group_to_print": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu nhóm kệ cần in", "update": "<PERSON><PERSON><PERSON>", "print_by_selected_shelf_group": "In nhóm kệ đư<PERSON> chọn", "print_all_shelf_group": "In toàn bộ nhóm kệ", "print_by_all_tray_of_shelf_group": "In toàn bộ mâm nhóm kệ", "print_by_tray_of_shelf_group": "In mâm nhóm kệ đư<PERSON>c chọn", "choose_tray": "<PERSON><PERSON><PERSON> m<PERSON>", "crease_facing": "<PERSON><PERSON><PERSON> facing", "reduce_facing": "<PERSON><PERSON><PERSON><PERSON> facing", "not_found_alternative_product": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm thay thế", "product_not_position": "<PERSON><PERSON><PERSON> ph<PERSON>m không có vị trí trưng bày."}, "sale_service": {"service_voucher_expire": "<PERSON><PERSON><PERSON> dịch vụ đã thu hết tiền", "text_input_search_SV": "Nhập mã SO/ mã SH/ mã SV/ người tạo", "unregistered": "<PERSON><PERSON>a đ<PERSON>ng ký", "registered": "Đ<PERSON> đăng ký", "code_SO": "<PERSON>ứng từ gốc: ", "code_RC": "<PERSON><PERSON><PERSON> bi<PERSON>n nhận: ", "service_group_name": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hi<PERSON>: ", "provider_name": "<PERSON><PERSON>à cung cấp: ", "total_amount": "<PERSON><PERSON><PERSON><PERSON>h to<PERSON>: ", "debt": "<PERSON><PERSON><PERSON><PERSON> nợ: ", "crash_insurance": "<PERSON><PERSON><PERSON> vỡ", "extended_warranty": "<PERSON><PERSON><PERSON> hiểm b<PERSON>o hành mở rộng", "type_service": "<PERSON><PERSON><PERSON> d<PERSON>ch vụ: ", "service_ticket_status": "Tr<PERSON><PERSON> thái phi<PERSON>u dịch vụ: ", "collected_money": "<PERSON><PERSON> thu tiền", "enough_information": "<PERSON><PERSON> thông tin", "delevery": "<PERSON><PERSON><PERSON> từ liên quan đã giao hàng", "time": "<PERSON><PERSON><PERSON><PERSON> gian (từ ngày - tới ngày)", "service_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phi<PERSON>u dịch vụ", "cancel_YCX": "HỦY YCX", "cancel_PDV": "Hủy PDV và tạo phiếu chi", "product_name": "<PERSON><PERSON><PERSON> phẩm tham gia: ", "money_insurance": "<PERSON><PERSON> tiền b<PERSON><PERSON> hi<PERSON>: ", "time_insurance": "<PERSON><PERSON><PERSON><PERSON> gian hi<PERSON> l<PERSON>: ", "insurance_price": "<PERSON><PERSON><PERSON> b<PERSON> b<PERSON>o hi<PERSON>: ", "info_insurance": "Thông tin chương trình bảo hiểm", "insurance_package": "<PERSON><PERSON><PERSON><PERSON> trình b<PERSON><PERSON> hiể<PERSON>: ", "term_insurance": "<PERSON><PERSON><PERSON><PERSON> hạn tham gia bảo hiểm: ", "month": "th<PERSON>g", "info_insurance_packge": "Thông tin gói b<PERSON>o hiểm", "month_from": "tháng kể từ ngày đăng ký thành công", "customer_info": "Thông tin khách hàng", "customer_name": "Họ và tên", "enter_customer_name": "<PERSON><PERSON><PERSON><PERSON> họ tên khách hàng", "numberphone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "enter_numberphone": "<PERSON><PERSON><PERSON><PERSON> số điện tho<PERSON><PERSON> kh<PERSON>ch hàng", "address": "Địa chỉ", "close": "Đ<PERSON><PERSON>", "enter_number_house": "<PERSON><PERSON><PERSON><PERSON> số nhà", "created_day": "<PERSON><PERSON><PERSON> t<PERSON>o giao d<PERSON>: "}, "service_data": {"please_enter_name": "<PERSON><PERSON> lòng nhập tên khách lẻ/Công ty", "choice_brand": "1. <PERSON><PERSON><PERSON> mạng", "info_customer": "2. <PERSON><PERSON><PERSON><PERSON> tin khách hàng", "enter_phoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enter_name": "<PERSON><PERSON><PERSON><PERSON> tên khách lẻ/ Công ty", "note": "<PERSON><PERSON><PERSON>", "enter_note": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "receipt_company": "<PERSON><PERSON><PERSON><PERSON> hàng lấy hóa đơn công ty", "enter_address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ công ty", "enter_tax": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "btn_continue_uppercase": "TIẾP TỤC", "enter_confirm_code": "<PERSON><PERSON><PERSON><PERSON> mã xác thực"}, "saleExpress": {"warning_required_promotions_with_params": "<PERSON><PERSON> lòng chọn <PERSON> mãi bắt buộc:\n%{productNames}", "confirm_clear_all_products": "Bạn muốn xóa toàn bộ sản phẩm?", "product_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm phù hợp", "product_lack_of_in_stock_with_params": "SP %{productName} không đủ số lượng tồn", "warning_required_promotions": "<PERSON><PERSON>n chưa chọn <PERSON>ến mãi b<PERSON><PERSON> buộ<PERSON>.", "not_enough_in_stock": "không đủ số lượng tồn", "not_made_price": "ch<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> làm giá", "warning_validation_product_with_params": "<PERSON><PERSON>n phẩm %{ProductName} %{msgText}.", "error_can_not_create_cart": "<PERSON><PERSON><PERSON><PERSON> thể tạo giỏ hàng, vui lòng kiểm tra lại.", "warning_apply_coupon": "<PERSON><PERSON> lòng chọn khu<PERSON>ến mãi trư<PERSON><PERSON> khi áp dụng Coupon.", "warning_empty_coupon": "<PERSON><PERSON> lòng nh<PERSON>p Coupon.", "warning_not_found_promotions": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khu<PERSON>ến mãi.", "title_usage_guide": "Hướng dẫn sử dụng", "title_promotions": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "title_dosage": "<PERSON><PERSON><PERSON> d<PERSON>", "title_sale_promotions": "<PERSON><PERSON>", "placeholder_coupon": "Mã coupon", "warning_no_dosage": "Trong giỏ hàng đang không có sản phẩm nào thuộc ngành hàng có hướng dẫn sử dụng, li<PERSON><PERSON> dùng", "scan_to_find_product": "Quét Barcode để thêm sản phẩm", "payment_info": "Thông tin thanh toán", "provisional_price": "<PERSON><PERSON><PERSON>", "placeholder_drug_suggestion": "<PERSON><PERSON><PERSON><PERSON> tê<PERSON> b<PERSON>nh, tri<PERSON><PERSON> chứ<PERSON>... (Vd: <PERSON><PERSON><PERSON><PERSON>)", "confirm_replace_products": "Bạn có muốn chọn sản phẩm thay thế không?", "title_auxiliary": "SP Bổ trợ", "title_replaced_products": "<PERSON><PERSON><PERSON> ph<PERSON>m thay thế", "quantity": "Số lượng", "in_stock_quantity_with_params": "SL tồn: %{quantity}", "unit_name": "Đơn vị tính: ", "retry": "<PERSON><PERSON><PERSON> lại", "category": "Phân loại gói", "disease_name": "<PERSON><PERSON><PERSON> b<PERSON>: ", "btn_show_less": "<PERSON><PERSON>", "prescribe": "<PERSON><PERSON> đ<PERSON>n thuốc", "label_provisional_price": "<PERSON><PERSON><PERSON> t<PERSON>: ", "label_ingredient": "Thành phần: ", "confirm_clear_cart": "<PERSON>hao tác này sẽ xóa toàn bộ sản phẩm trong giỏ hàng, bạn có muốn tiếp tục?", "quantity_acronym": "SL: %{quantity}", "total_amount": "<PERSON><PERSON><PERSON> tiền", "extra_discount_amount": "Tổng tiền đã giảm thêm", "discount_by_coupon": "Giảm giá coupon", "discount_by_cart_promotion": "<PERSON><PERSON><PERSON><PERSON> giá tổng đơn", "total_paid": "<PERSON><PERSON><PERSON> to<PERSON>", "tax_id": "<PERSON><PERSON> số thuế", "company_name": "<PERSON><PERSON>n công ty", "company_address": "Địa chỉ công ty", "company_phone": "<PERSON><PERSON> điện tho<PERSON>i công ty", "placeholder_tax_id": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "placeholder_company_address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ công ty", "placeholder_company_phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại công ty", "placeholder_phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "contact_phone": "<PERSON><PERSON> điện thoại liên hệ", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "mr": "<PERSON><PERSON>", "ms": "Chị", "placeholder_customer_name": "<PERSON><PERSON><PERSON><PERSON> họ tên khách hàng", "full_name": "<PERSON><PERSON> tên", "contact_name": "<PERSON><PERSON> tên ng<PERSON><PERSON>i liên hệ", "contact_address": "<PERSON><PERSON><PERSON> chỉ liên hệ", "address": "Địa chỉ", "placeholder_address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "info_upload_take_photo_of_prescription": "Bạn vui lòng tải hình ảnh hoặc chụp lại ảnh đơn thuốc", "upload_or_take_photo": "Chọn/<PERSON><PERSON><PERSON>nh", "info_max_size_photo": "Tối đa 10MB/3 ảnh", "hot_bonus": "<PERSON><PERSON><PERSON><PERSON> thưởng nóng", "lot_date": "Lô/Date", "title_cart_promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi tổng đơn", "choose_promotion": "<PERSON><PERSON><PERSON> mãi", "info_promotion": "Th<PERSON>ng tin khu<PERSON>ến mãi", "total_quantity_of_cart": "Tổng số lượng sản phẩm trong giỏ hàng", "placeholder_search_drugs": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ất", "placeholder_search_ava": "Nhập <PERSON>, Tên SP, IMEI", "search_result": "<PERSON><PERSON><PERSON> qu<PERSON> tìm kiếm", "title_cart": "Giỏ hàng", "title_customer_info": "<PERSON><PERSON><PERSON><PERSON>", "title_prescription": "<PERSON><PERSON>", "title_sale_express": "<PERSON><PERSON> hàng không tư vấn", "title_drug_suggestion": "<PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> li<PERSON>", "title_provisional_cart_list": "Giỏ hàng tạm", "title_combo": "Combo", "title_vicinity": "<PERSON><PERSON>", "title_alternative": "<PERSON><PERSON><PERSON> ý thay thế", "title_receive_at_store": "<PERSON><PERSON><PERSON><PERSON> tại siêu thị", "title_home_delivery": "<PERSON><PERSON><PERSON> t<PERSON>n n<PERSON>", "title_promotion_profit": "<PERSON><PERSON> nhiều gi<PERSON><PERSON> nhiều", "placeholder_search_provisional_cart": "<PERSON><PERSON><PERSON><PERSON> tên giỏ hàng, ng<PERSON><PERSON> tạo (Vd: Giỏ hàng 1)", "notify_provisional_cart_1": "<PERSON><PERSON> sách giỏ hàng sẽ được xoá sau 24 giờ mỗi ngày.", "notify_provisional_cart_2": "Giỏ hàng sẽ không lưu lại thông tin khuyến mãi, thông tin Lô/date", "confirm_delete_all_provisional_carts": "Bạn có muốn xoá tất cả các %{target} không?", "shipping_info": "<PERSON><PERSON>ng giao đến địa chỉ: %{address}", "store_change_info": "<PERSON><PERSON>ng xin từ kho: %{id} - %{name}", "warning_enough_stock": "<PERSON><PERSON><PERSON> kho tại siêu thị đang đủ bán. <PERSON>ạn không thể sử dụng tính năng này.", "required_quantity": "<PERSON><PERSON> l<PERSON> cần tìm", "view_cross_selling_products": "<PERSON><PERSON> ph<PERSON>m b<PERSON> k<PERSON>m", "inventory_status_new": "<PERSON><PERSON><PERSON>", "inventory_status_old_date": "Cận date", "inventory_status_new_with_defect": "M<PERSON>i (Giảm giá)", "no_buy_product_close_date": "<PERSON>ản phẩm với trạng thái cận date không đư<PERSON>c sử dụng chức năng này", "view_promotion": "<PERSON><PERSON> mãi", "info_out_stock_promotion": "<PERSON><PERSON><PERSON><PERSON> trình khu<PERSON>ến mãi đã hết quà tặng.", "title_drug_prescription": "<PERSON><PERSON><PERSON><PERSON> theo toa"}, "inventory": {"add_new_area": "<PERSON><PERSON><PERSON><PERSON> mới khu vực", "area_list": "<PERSON><PERSON> s<PERSON>ch khu vực: ", "delete": "Xoá", "save": "<PERSON><PERSON><PERSON>", "inventory_type": "<PERSON><PERSON>i kiểm kê: ", "list_inventory_period": "<PERSON><PERSON> s<PERSON>ch kỳ kiểm kê: ", "complete_input": "<PERSON><PERSON><PERSON> tất nh<PERSON>p", "inventory_status": "Trạng thái kiểm: ", "select_control_area": "<PERSON><PERSON><PERSON> khu kiểm: ", "select_product_status": "<PERSON><PERSON><PERSON> trạng thái sản phẩm ", "have_emei": "có IMEI", "not_emei": "không IMEI", "collapse": "<PERSON><PERSON>", "see_all": "<PERSON><PERSON> t<PERSON>t cả", "input_while_checking": "<PERSON><PERSON> nhập khi kiểm", "amount": "Số lượng", "product_id": "<PERSON>ã sản phẩm: ", "product_status": "Trạng thái: ", "save_and_go_back": "<PERSON><PERSON><PERSON> và trở về", "save_and_continue_scanning": "<PERSON><PERSON><PERSON> và quét tiếp", "total_checked_quantity": "Tổng SL đã kiểm", "total_checking_quantity": "Tổng SL đang kiểm", "unit_short": "ĐVT", "list_inventory_term": "<PERSON><PERSON> s<PERSON>ch kỳ kiểm kê", "inventory_history": "<PERSON><PERSON><PERSON> sử kiểm kê", "checked_quantity": "SL đã kiểm", "checking_quantity": "SL đang kiểm", "check_quantity": "SL kiểm:", "imei_not_exist_in_system": "IMEI không tồn hệ thống", "time_start": "<PERSON><PERSON><PERSON> đầu: ", "time_end": "<PERSON><PERSON><PERSON> th<PERSON>: ", "not_started": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu", "choose_product": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "product_no_imei": "<PERSON><PERSON><PERSON> ph<PERSON>m không IMEI", "product_has_imei": "<PERSON><PERSON><PERSON> ph<PERSON>m có IMEI", "inventory_product_no_imei_uppercase": "KIỂM KÊ SẢN PHẨM KHÔNG IMEI", "select_inventory_status_product_no_imei": "<PERSON><PERSON>n trạng thái sản phẩm không IMEI", "select_inventory_status_product_with_imei": "<PERSON><PERSON>n trạng thái sản phẩm có IMEI", "exported": "Đã xuất", "quantity": "Số lượng", "inventory_status_2": "<PERSON>r<PERSON><PERSON> thái kiểm", "area_management": "<PERSON><PERSON><PERSON><PERSON> lý khu vực", "inventory": "<PERSON><PERSON><PERSON>", "view_inventory_result": "<PERSON><PERSON> kết quả kiểm kê", "view_arrears_result": "<PERSON><PERSON> kết quả truy thu", "no_inventory_area_found": "<PERSON><PERSON><PERSON><PERSON> có danh sách khu vực", "no_inventory_type_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh sách loại kiểm kê", "no_inventory_history_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lịch sử kiểm kê", "no_inventory_term_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kỳ kiểm kê", "noti_no_inventory_area_found": "<PERSON><PERSON> lòng vào mục \"QUẢN LÝ KHU VỰC\" để \"Thêm mới\" ít nhất 1 khu vực", "no_inventory_status_found": "<PERSON>hông tìm thấy danh sách trạng thái kiểm kê!", "no_valid_data_found": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu thỏa điều kiện tìm kiếm", "product_info_not_found": "Không tìm thấy thông tin sản phẩm!", "lock_product_unsuccessful": "Lock sản phẩm không thành công", "no_arrears_product_found": "<PERSON><PERSON><PERSON><PERSON> có danh sách sản phẩm truy thu", "no_processing_arrears_product_found": "<PERSON><PERSON><PERSON><PERSON> có danh sách sản phẩm truy thu đang xử lý", "no_arrears_staff_info_found": "<PERSON>hông có danh sách thông tin nhân viên truy thu", "no_imei_found": "<PERSON><PERSON><PERSON><PERSON> có danh sách IMEI của sản phẩm", "no_product_found": "<PERSON><PERSON><PERSON><PERSON> có danh sách sản phẩm", "error_delete_inventory_info": "Lỗi xóa thông tin kiểm kê!", "delete_inventory_info_success": "<PERSON><PERSON>a thông tin kiểm kê thành công!", "area_list_auto_create_by_system": "<PERSON><PERSON> thống tạo tự động 2 khu kiểm Showroom và Kho", "enter_area_name": "<PERSON><PERSON><PERSON><PERSON> tên khu vực", "new_area_name": "<PERSON><PERSON><PERSON> khu vực mới", "input_arrears_staff": "<PERSON><PERSON><PERSON><PERSON> nhân viên truy thu", "staff_id": "Mã NV", "paid_amount": "<PERSON><PERSON> tiền chịu", "input_amount": "<PERSON><PERSON><PERSON><PERSON>", "id": "Mã", "no_process": "<PERSON><PERSON><PERSON> lý", "processed": "Đã xử lý", "total_amount": "<PERSON><PERSON><PERSON> tiền", "company_paid": "<PERSON><PERSON><PERSON> ty chịu: ", "extra_paid": "<PERSON><PERSON> lý ngoài: ", "receipt": "<PERSON><PERSON><PERSON> thu: ", "staff_paid": "<PERSON><PERSON><PERSON> viên ch<PERSON>: ", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "cash_amount": "<PERSON><PERSON><PERSON><PERSON> tiền", "arrears_list": "<PERSON>h s<PERSON>ch truy thu", "arrears_expect": "<PERSON><PERSON><PERSON> thu dự kiến", "total_arrears_amount": "Tổng số tiền truy thu:", "print_report": "In biên bản", "save_info": "<PERSON><PERSON><PERSON> thông tin", "update": "<PERSON><PERSON><PERSON>", "staff_id_short": "Mã NV", "staff_name": "Tên NV", "cash": "<PERSON><PERSON><PERSON><PERSON> tiền", "upload_report_image": "<PERSON><PERSON><PERSON> hình biên bản liên đới ", "max_3_files": "(Tối đa: 3 files)", "attach_image": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON> ", "max_5_files": "(Tối đa: 5 files)", "note_print_report": "(<PERSON><PERSON><PERSON> ý: <PERSON><PERSON> lòng in biên bản liên đới trước khi chụp hình)", "please_print_report": "<PERSON><PERSON> lòng in biên bản trư<PERSON><PERSON> khi up hình!", "select_time": "<PERSON><PERSON><PERSON> thời gian:", "select_inventory_term": "<PERSON><PERSON><PERSON> kỳ kiểm:", "checked_product": "<PERSON><PERSON><PERSON> phẩm đã kiểm", "product_needs_to_check": "SP cần kiểm để hoàn tất", "deviant_after_minus_transferring": "<PERSON><PERSON><PERSON> lệch sau khi trừ hàng đi đường:", "no_imei_uppercase": "Không IMEI", "has_imei_uppercase": "Có IMEI", "changed_stock_quantity": "Sản phẩm thay đổi tồn!\nVui lòng xóa dữ liệu và kiểm lại.", "delete_inventory_data": "<PERSON><PERSON><PERSON> dữ liệu", "stock_quantity_short": "SL tồn: ", "not_including_in_transit": "\n(trừ hàng đi đường)", "check_quantity_short": "SL kiểm: ", "view_detail": "<PERSON> ti<PERSON>", "stock_status_short": "TT tồn", "inventory_status_short": "TT kiểm", "inventory_bill_id": "Mã phiếu kiểm: ", "user_id": "Mã user: ", "check_qtt": "Số lượng kiểm: ", "checked_qtt": "SL đã kiểm: ", "adjusted_quantity": "SL sau điều chỉnh", "total_adjusted_quantity": "Tổng SL sau điều chỉnh", "inventory_area": "<PERSON><PERSON> v<PERSON>c kiểm: ", "inventory_result_no_imei_product_uppercase": "KẾT QUẢ KIỂM KÊ SẢN PHẨM KHÔNG IMEI", "selected_inventory_status": "Tr<PERSON>ng thái chọn kiểm: ", "deviated_status": "<PERSON>r<PERSON><PERSON> thái hiển thị chênh lệch: ", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "deviated_product": "SP có ch<PERSON>nh l<PERSON>ch", "changed_stock_qtt_product": "SP có thay đổi tồn", "not_devated_product": "SP không chênh l<PERSON>ch", "from_to": "Từ ngày - <PERSON><PERSON><PERSON> ngày: ", "search": "<PERSON><PERSON><PERSON>", "area": "<PERSON><PERSON> v<PERSON>", "has_been_deleted": "đ<PERSON> đ<PERSON><PERSON><PERSON> xoá", "enter_imei": "Nhập IMEI", "imei_is_belong_to": "IMEI th<PERSON> sản ph<PERSON>m", "please_check_again": "Vui lòng kiểm tra lại!", "modified_products_exist": "<PERSON><PERSON><PERSON> phẩm có thay đổi tồn", "max_5_image": "Chỉ đ<PERSON><PERSON><PERSON> thêm tối đa 5 hình!", "scan_try_again": "<PERSON><PERSON><PERSON>", "select_all": "<PERSON><PERSON><PERSON> tất cả", "report_product_ticket": "<PERSON><PERSON><PERSON> tình trạng hàng hoá", "select_report_product_ticket": "<PERSON><PERSON><PERSON> phiếu tình trạng hàng hoá", "request_arrears_process": "<PERSON><PERSON><PERSON> cầu truy thu đã đư<PERSON>c xử lý", "not_have_access": "<PERSON><PERSON>n chưa đ<PERSON><PERSON><PERSON> cấp quyền sử dụng chức năng này", "report_product_state": "<PERSON><PERSON> báo tình trạng hàng hóa thực tế", "pls_enter_product_before_enter_imei": "<PERSON><PERSON> lòng nhập mã sản phẩm trư<PERSON><PERSON> khi nhập IMEI!", "not_enough_inventory": "không đủ tồn kho, vui lòng kiểm tra lại!", "not_out": "<PERSON><PERSON>n ph<PERSON><PERSON> b<PERSON>m \"Tạo yêu cầu\" trư<PERSON><PERSON> khi thoát màn hình!", "created_success": "Tạo yêu cầu thành công!", "product_list": "DANH SÁCH SẢN PHẨM", "status_1": "Tình trạng: ", "create_request": "<PERSON><PERSON><PERSON> y<PERSON> c<PERSON>u", "differ_process_code": "không cùng mã XLKK với sản phẩm", "import_export": "<PERSON><PERSON><PERSON> xác nhận tạo yêu cầu, và khi xử lý yêu cầu này sẽ Nhập Thừa - <PERSON><PERSON><PERSON>.", "not_product_with_code_inventory": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm có mã yêu cầu kiểm kê hợp lệ!", "product_selected_with_imei": "Sản phẩm bạn chọn là sản phẩm có IMEI. Vui lòng kiểm tra lại!", "product_selected_no_imei": "<PERSON>ản phẩm bạn chọn là sản phẩm không IMEI. Vui lòng kiểm tra lại!", "product_same_product": "Sản phẩm đúng trùng với sản phẩm sai code sai màu, vui lòng kiểm tra lại!", "product_selected_with_no_imei": "<PERSON>ản phẩm bạn chọn là sản phẩm không IMEI. Vui lòng kiểm tra lại!", "enter_product_same_product_shot": "đang nhập trùng với IMEI sản phẩm bắn vào. Vui lòng kiểm tra lại!", "import_inventory": "<PERSON><PERSON> nhập có tồn kho. <PERSON>ui lòng kiểm tra lại", "no_import_export_history": "kh<PERSON><PERSON> có lịch sử nhập xuất và sai định dạng. Vui lòng liên hệ kế toán chi nhánh để xử lý!", "no_history_import": "kh<PERSON><PERSON> có lịch sử nhập xuất. <PERSON><PERSON>n có chắc chắn tạo yêu cầu?", "no_history_systems": "kh<PERSON><PERSON> có lịch sử nhập xuất trên hệ thống. Bạn có chắc chắn tạo yêu cầu?", "in_stock": "đang tồn ở kho", "input_not_allow": "khô<PERSON> đư<PERSON><PERSON> phép nhập thừa. <PERSON>ui lòng kiểm tra lại!", "exists_system": "đã có tồn trong hệ thống, vui lòng kiểm tra lại!", "total_convert": "Tổng số lượng quy đổi", "enter_quantity": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>: ", "true_product_id": "MSP đúng: ", "true_imei": "IMEI đúng: ", "out_system_imei": " (IMEI ngoài)", "note": "<PERSON><PERSON> chú: ", "enter_note": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "unit": "Đơn vị tính: ", "true_product_info": "THÔNG TIN SẢN PHẨM ĐÚNG", "report_quantity": "Số lượng: ", "label_enter_true_productid": "<PERSON>hập <PERSON> đ<PERSON>g", "true_productid_placeholder": "<PERSON><PERSON> sản ph<PERSON>m đ<PERSON>g", "label_enter_true_imei": "IMEI SP đúng: ", "true_imei_placeholder": "Nhập IMEI đúng", "label_enter_spare_imei": "Nhập IMEI: ", "spare_imei_placeholder": "Nhập IMEI thừa", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "not_declared_business_status": "<PERSON><PERSON><PERSON><PERSON> hàng của sản phẩm quét/nhập chưa khai báo trạng thái kinh doanh.", "status_2": "trạng thái", "not_in_stock": "không đủ tồn kho. Vui lòng kiểm tra lại!", "save_report_request_error": "Lỗi lưu yêu cầu khai báo tình trạng sản phẩm", "no_report_requests_found": "<PERSON><PERSON><PERSON>ng có yêu cầu khai báo tình trạng hàng hoá nào!", "cannot_get_report_request_detail": "<PERSON><PERSON><PERSON>ng lấy đ<PERSON><PERSON><PERSON> thông tin chi tiết của phiếu khai báo tình trạng sản phẩm!", "update_failed": "<PERSON><PERSON><PERSON> nhật thất bại", "select_finish": "Bạn ph<PERSON>i chọn hoàn tất nhập trư<PERSON><PERSON> khi thoát màn hình", "list_imei_export": "<PERSON><PERSON> s<PERSON>ch IMEI đã xuất không còn tồn kho: ", "system_cancel_imei": "<PERSON><PERSON> thống sẽ xoá phiếu kiểm của IMEI này. <PERSON><PERSON> lòng bấm OK sau đó L<PERSON> lại", "imei_different_status": "IMEI đang nhập kiểm có trạng thái tồn khác với trạng thái chọn kiểm", "contact_supermarket": "li<PERSON><PERSON> hệ siêu thị", "processing_check": "đ<PERSON> xử lý trư<PERSON><PERSON> khi nhập kiể<PERSON>.", "imei_not_exist": "IMEI không tồn tại kho", "select_max": "Bạn chỉ được chọn tối đa", "day": "ngày!", "different_product": "SẢN PHẨM LỆCH KIỂM KHÔNG IMEI", "attched_file": "Bạn vui lòng đ<PERSON>h kèm file", "list_employees_product": "<PERSON><PERSON> sách nhân viên liên đới trách nhiệm tổn thất tài sản/ hàng hóa", "before_save": "tr<PERSON><PERSON><PERSON>", "not_get_content": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> đ<PERSON><PERSON><PERSON> nội dung biên bản", "not_get_list_img": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON> đ<PERSON><PERSON><PERSON> danh sách hình <PERSON>nh", "total_exchange_quantity": "Tổng số lượng quy đổi: ", "select_status": "<PERSON><PERSON><PERSON> trạng thái", "product_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin sản phẩm", "no_report_state_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh sách các loại tình trạng khai báo!", "select_new_status": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> th<PERSON><PERSON> đổ<PERSON> sang:", "link_camera": "Link camera: ", "link_camera_placeholder": "Link camera", "off_30": "đã nghỉ việc quá", "off_30_2": "ngày. <PERSON><PERSON><PERSON> không được thêm nhân viên đền bù. <PERSON><PERSON> lòng cập nhật nhân viên đền bù khác.", "invalid_expiration_date": "<PERSON><PERSON><PERSON> bạn vừa nhập không tồn tại, vui lòng thử lại!", "not_declare_imei_format": "<PERSON><PERSON><PERSON> phẩm có yêu cầu IMEI nhưng chưa khai báo định dạng"}, "otpConfirm": {"verify_OTP": "<PERSON><PERSON><PERSON> thực mã <PERSON>", "will_sent_OTP": "Mã OTP sẽ được gửi đến SĐT "}, "collection": {"multicat_industry_service": "DỊCH VỤ NGÀNH HÀNG MULTICAT", "multicat_industry_service_nfp": "D<PERSON><PERSON> v<PERSON> ng<PERSON>nh hàng Multicat", "deposit_money_into_your_account": "NẠP TIỀN VÀO SỐ TÀI KHOẢN", "managerment_multicat_industry_transactions": "QUẢN LÝ GD NGÀNH HÀNG MULTICAT", "managerment_multicat_industry_transactions_nfp": "Quản lý GD ngành hàng Multicat", "take_front_id_card": "<PERSON><PERSON> lòng chụp mặt trước CMND/CCCD", "take_back_id_card": "<PERSON><PERSON> lòng ch<PERSON>p mặt sau CMND/CCCD", "enter_card_number": "<PERSON><PERSON> lòng nhập số CMND/CCCD", "enter_load_name": "<PERSON><PERSON> lò<PERSON> nh<PERSON>p Tên ng<PERSON><PERSON> nạp", "enter_load_phone_number": "<PERSON><PERSON> lò<PERSON> nhập <PERSON><PERSON> điện thoại người nạp", "choose_transfer_bank": "<PERSON><PERSON> lòng chọn <PERSON> hàng cần chuyển", "enter_account_number": "<PERSON><PERSON> lòng nh<PERSON>p số tài k<PERSON>n", "enter_amount_to_transfer": "<PERSON><PERSON> lòng nhập <PERSON> tiền cần chuyển", "amount_received_must_multiple_of_1000": "<PERSON><PERSON> tiền nhận phải là bội của 1000", "sender_information": "Thông tin người gửi", "take_a_shot": "<PERSON><PERSON><PERSON>", "front": "mặt trước", "backside": "mặt sau", "id_card": "Chứng minh nhân dân", "id_card_2": "<PERSON><PERSON><PERSON> c<PERSON> công dân", "sym_id_card": "CMND", "sym_id_card_2": "CCCD", "card_number": "Số CMND/CCCD", "placeholder_ID_card_number": "Nhập số CMND/CCCD", "sender_name": "<PERSON><PERSON><PERSON><PERSON>i", "enter_sender_name": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i", "sender_phone": "<PERSON><PERSON> điện thoại người gửi", "enter_sender_phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại người gửi", "receiver_information": "<PERSON>h<PERSON>ng tin người nhận", "bank": "<PERSON><PERSON> h<PERSON>", "choose_bank": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "account_number": "Số tài <PERSON>n", "account_number_required": "<PERSON><PERSON><PERSON><PERSON> số tài <PERSON>n", "recipient_name": "<PERSON><PERSON><PERSON>n", "transaction_amount": "<PERSON><PERSON> tiền giao d<PERSON>ch", "enter_amount_want_to_deposit": "<PERSON><PERSON><PERSON><PERSON> số tiền muốn nạp", "transaction_fee": "<PERSON><PERSON> giao d<PERSON>ch", "retry": "<PERSON><PERSON><PERSON> lại", "btn_accept": "OK", "agree_to_terms": "<PERSON><PERSON><PERSON><PERSON> hàng đồng ý với toàn bộ điều kiện điều khoản đượ<PERSON> quy định tại website ", "customer_signature": "<PERSON><PERSON> ký khách hàng", "continue": "TIẾP TỤC", "recharge_request": "<PERSON><PERSON><PERSON> c<PERSON>u nạp tiền của giao dịch ", "send_to_App_XWork": " của bạn đã đư<PERSON><PERSON> gửi thông báo đến app X-Work của quản lý siêu thị có chấm công trong ca gồm: ", "wait_for_management_to_approve": ". <PERSON><PERSON> lòng chờ quản lý siêu thị xác nhận trên App X-Work!", "ticket_is_valid_for_10_minutes": "Ticket có hiệu lực trong 10 phút", "ticket_status": "Trạng thái ticket:", "sent_to_supermarket_manager": "Đã gửi cho QLST", "create_order": "TẠO ĐƠN HÀNG", "reply_ticket": "GỬI LẠI TICKET", "check_the_result": "KIỂM TRA KẾT QUẢ", "information_check_management": "Nhân viên xác nhận kỹ số tài kho<PERSON>n/ thông tin Khách hàng và số tiền muốn nạp, nhận đủ tiền trước khi nhấn nút Tiếp Tục. <PERSON><PERSON><PERSON> d<PERSON> này sẽ không hỗ trợ hoàn hủy", "transactions_processed": "<PERSON><PERSON><PERSON> d<PERSON>ch đang xử lý: ", "completed": "HOÀN TẤT", "close": "ĐÓNG", "export_request": "<PERSON><PERSON><PERSON> c<PERSON>u xu<PERSON>: ", "transaction_type": "Loại GD: ", "status": "Trạng thái: ", "note": "<PERSON><PERSON> chú: ", "customer_name": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng: ", "amount_of_money": "Số tiền: ", "click_check_result": "<PERSON><PERSON><PERSON> kiểm tra kết quả", "collect_money": "<PERSON><PERSON> t<PERSON>n", "print_receipt": "In biên nhận", "the_transaction_you_create": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> bạn t<PERSON>o", "the_transaction_you_cancel": "<PERSON><PERSON><PERSON> bạn huỷ", "refund_status": "Tr<PERSON><PERSON> thái hoàn tiền: ", "refunds": "<PERSON><PERSON> hoàn tiền", "check_phone": "<PERSON><PERSON> lòng nhập số điện thoại đúng 10 chữ số", "check_id_card": "<PERSON><PERSON> lòng nhập CMND/CCCD đúng 9 hoặc 12 ký tự"}, "sendbank": {"text_input_username_add": "Mã số nhân viên tạo/duyệt", "text_input_voucher_code": "<PERSON><PERSON> y<PERSON>u c<PERSON>", "select_status": "<PERSON><PERSON><PERSON> trạng thái xử lý", "requires_approval_code": "<PERSON><PERSON> y<PERSON>u c<PERSON>", "supermarket_requires_approval": "<PERSON><PERSON><PERSON> thị yêu c<PERSON><PERSON>", "date_creation_approval_request": "<PERSON><PERSON><PERSON> t<PERSON>o yêu c<PERSON><PERSON>", "staff_approved": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "date_approved": "<PERSON><PERSON><PERSON>", "voucher_code": "Mã phiếu chi", "bank_pays_money": "<PERSON><PERSON> hàng n<PERSON> tiền", "total_amount": "<PERSON><PERSON><PERSON> tiền", "payment_date": "<PERSON><PERSON><PERSON> t<PERSON>n", "staff_created": "<PERSON><PERSON><PERSON> viên tạo", "processing_status": "<PERSON><PERSON><PERSON><PERSON> thái", "processing_staff": "<PERSON><PERSON>ân viên xử lý", "processing_times": "<PERSON><PERSON><PERSON><PERSON> gian xử lý", "note": "<PERSON><PERSON><PERSON>", "list_attachments": "<PERSON><PERSON> s<PERSON>ch file đ<PERSON>h k<PERSON>m", "btn_submit": "<PERSON><PERSON><PERSON>", "btn_back": "Bỏ qua", "change_employee_approval_information": "Thay đổi thông tin nhân viên duy<PERSON>t:", "error_employee_information_available": "<PERSON>hông có thông tin nhân viên", "error_employee_file_attach": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu danh sách file đ<PERSON>h kèm", "employee_code_changed": "<PERSON><PERSON> lòng nhập MSNV", "send_bank_id": "ID ngân hàng gửi", "please_select_information": "<PERSON><PERSON> lòng nhập đ<PERSON>y đủ thông tin", "select_bank": "<PERSON><PERSON><PERSON> ngân hàng cần điều chỉnh", "reason_select_bank": "<PERSON><PERSON>n lý do điều chỉnh", "please_enter_total_money": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "please_select_bank": "<PERSON><PERSON> lòng nhập thông tin ngân hàng", "total_money_not_match": "Tổng số tiền xác nhận và số tiền nộp khác nhau. Vui lòng kiểm tra lại!", "total_amount_submit": "Tổng số tiền xác nhận:", "receipt_code_information": "Thông tin phiếu nộp tiền/chi", "replacement_review_staff": "<PERSON><PERSON><PERSON> viên du<PERSON> thay thế", "payment_voucher_code": "<PERSON><PERSON> phi<PERSON>u nộ<PERSON> tiền", "date_of_creation_of_payment_slip": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> p<PERSON><PERSON> nộ<PERSON> tiền", "cashier_staff": "<PERSON><PERSON> ng<PERSON>", "amount_paid": "<PERSON><PERSON> tiền n<PERSON>p", "voucher_creation_date": "<PERSON><PERSON><PERSON> t<PERSON>o phi<PERSON>u chi", "error_only_allowed_to_approve_payment_with_day": "Bạn chỉ đượ<PERSON> phép duyệt yêu cầu chi trong 1 ngày. <PERSON><PERSON> lòng liên hệ kế toán để được xử lý.", "see_more": "<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "at_most_1_month": "<PERSON><PERSON><PERSON> bắt đầu và ngày kết thúc chỉ được cách nhau tối đa 1 tháng!", "please_enter_user_name": "<PERSON><PERSON> lòng nhập nhân viê<PERSON>", "are_you_sure_to_delete_this_record": "Bạn có chắc chắn muốn xóa phiếu tạo đã chọn không?", "download_photo": "<PERSON><PERSON><PERSON>n"}, "feedback": {"feedback": "<PERSON><PERSON><PERSON>", "describe_detail_problem": "<PERSON><PERSON>n đề cần mô tả", "enter_problem": "<PERSON><PERSON><PERSON><PERSON> mô tả phiếu...", "choose_picture": "<PERSON><PERSON><PERSON>", "add_picture": "<PERSON><PERSON><PERSON><PERSON>", "send_feedback": "<PERSON><PERSON><PERSON> yêu cầu hỗ trợ", "limit_3_files": "(Tối đa: 3 files)"}}