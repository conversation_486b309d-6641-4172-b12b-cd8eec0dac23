
import CodePush from 'react-native-code-push';

export const TAG_LANGUAGE = {
    EN: 'en',
    VI: 'vi',
    KHM: 'khm'
};
export const ID_LANGUAGE = {
    en: 1,
    vi: 2,
    khm: 3
};
export const SALE_SCENARIO_TYPE = {
    SALE: 0,
    PRE_ORDER: 1,
    AN_KHANG_PHARMACY: 2,
    STAFF_PROMOTION: 3
};

export const BRAND_NAME = {
    AN_KHANG: 'AN_KHANG',
    AVA: 'AVA'
};

export const BRAND_ID = {
    [BRAND_NAME.AN_KHANG]: 8,
    [BRAND_NAME.AVA]: [14, 15, 17] // SPORT, KIDS, FASHION
};

export const SCREENS = {
    AN_KHANG: 'AnKhangPharmacy',
    SALE: 'Sale',
    PRODUCT_DETAIL: 'Detail',
    SEARCH_MEDICAL: 'SearchMedical',
    PRESCRIPTION: 'PrescriptionScreen',
    PHARMACY: 'Pharmacy'
};

export const ALLOW_STORE_ID = {
    [BRAND_NAME.AVA]: [-1] // -1 là full kho ava
};

export const STATUS_PRE = {
    NO_STOCK_YET: [0, 2],
    MADE_GOODS: 1,
    EXPIRY_OF_SHIPMENT: 3,

};
export const TYPE_QR = {
    INFO: "INFO",
    ESIM: "ESIM",
};

export const COMPANY_ID = {
    BHX: 2
};

export const FILE_PATH = {
    INIT: '/init',
    STUDENT_INFO: '/studentinfo',
    STAFF_ARREARS: '/staffarrears',
    CHANGE_CODE_BUSINESS: '/changecodebusiness',
    SPARE_PRODUCT: '/spareproduct',
    CART_SCREEN: '/cartscreen',
    PRESCRIPTION_IMAGES: '/prescriptionimages',
    COLLECTION_ONE: '/collectionone',
    COLLECTION_TWO: '/collectiontwo',
    ADJUST_PRICE: '/adjustprice',
    STEP_ONE: '/stepone',
    STEP_THREE: '/stepthree',
    ATTACHMENTS_INSTALLMENT: '/attachmentsinstallment',
    ATTACHMENTS: '/attachments',
    INCORRECT_PRODUCT: '/incorrectproduct',
    NEW_PRODUCT_REQUEST: '/newproductrequest',
    PAYMENT_TRANSACTIONS: '/paymenttransaction',
    PRODUCT_EVALUATION: '/productevaluation',
    CREATE_RESTOCK_REQUEST: '/createrestockrequest',
    RESTOCK_REQUEST_DETAIL: '/restockrequestdetail',
    FORM_PROMOTION: '/formpromotion',
    SIM_PROCESS: '/simprocess',
    OLD_IMAGE: '/posused',
};

export const CODE_PUSH_STATUS = {
    ...CodePush.SyncStatus,
    SYNC_IN_BACKGROUND: -1
};
