import { Dimensions, Platform, NativeModules } from "react-native";
import StaticSafeAreaInsets from 'react-native-static-safe-area-insets';
import DeviceInfo from 'react-native-device-info';
import { RNHyperSnapParams } from 'hypersnapsdk_reactnative';
import * as DEVICE from './device';

export const isHasNotch = DEVICE.isIOS && DEVICE.isNotch;
export const isHasDynamicIsland = DEVICE.isIOS && DEVICE.isDynamicIsland;
const heightTopSafeIOS = isHasDynamicIsland ? (StaticSafeAreaInsets.safeAreaInsetsTop - 12) : StaticSafeAreaInsets.safeAreaInsetsTop;
export const heightTopSafe = DEVICE.isIOS
    ? ((isHasNotch || isHasDynamicIsland) ? heightTopSafeIOS : 20)
    : 0;
export const heightBottomSafe = (isHasNotch || isHasDynamicIsland) ? StaticSafeAreaInsets.safeAreaInsetsBottom : 0;
export const heightIndicator = (isHasNotch || isHasDynamicIsland) ? 16 : 0;
export const NAVIGATION_HEADER_HEIGHT = 60;

const ExtraDimensions = DEVICE.isIOS ? null : require("react-native-extra-dimensions-android");

export const width = Dimensions.get("screen").width;

export const height = DEVICE.isIOS ? Dimensions.get("window").height : ExtraDimensions.get("REAL_WINDOW_HEIGHT") - ExtraDimensions.get("SOFT_MENU_BAR_HEIGHT");

export const isIphoneX = function () {
    return (
        DEVICE.isIOS && (height === 812 || width === 812)
    );
};

export const statusBar = function () {
    if (isIphoneX()) {
        return 44;
    }
    return 20;
}

const standardWidth = 375;
const standardHeight = 667;

const scale = size => width / standardWidth * size;

export const getSize = (initSize) => {
    return Math.round(scale(initSize));
}


export const defaultPadding = 4;
export const defaultFontSize = 14;
export const defaultFontColor = "#333333";
export const defaultFont = DEVICE.isIOS ? "Helvetica Neue" : "Roboto";
export const normalWeight = "400";
export const boldWeight = "600";
export const boldWeightExtra = "800";
export const boldWeightMega = "900";
export const defaultPaddingHorizontal = 10;
export const defaultPaddingVertical = 10;
export const defaultMarginHorizontal = 10;
export const defaultMarginVertical = 10;

/*  */
export const MIN_QUANTITY_APPLY = 1;
export const MAX_QUANTITY_APPLY = 99;
export const HYPER_VERGE = {
    DocumentCARD: RNHyperSnapParams.DocumentTypeCard,
    DocumentOTHER: RNHyperSnapParams.DocumentTypePassport,
    DocumentFront: RNHyperSnapParams.DocumentFront,
    DocumentBack: RNHyperSnapParams.DocumentBack,
    DocumentNoneSide: "none.none.none",
    ContentCARD_FRONT: JSON.stringify({
        docCaptureTitle: "Chụp CMND/CCCD mặt trước",
        docCaptureDescription: "Đảm bảo tài liệu của bạn không bị lóa và hoàn toàn đầy đủ",
        docCaptureSubText: "Mặt trước",
        docRetakeButtonText: "Chụp lại",
    }),
    ContentCARD_BACK: JSON.stringify({
        docCaptureTitle: "Chụp CMND/CCCD mặt sau",
        docCaptureDescription: "Đảm bảo tài liệu của bạn không bị lóa và hoàn toàn đầy đủ",
        docCaptureSubText: "Mặt sau",
        docRetakeButtonText: "Chụp lại",
    }),
    ContentOther: JSON.stringify({
        docCaptureTitle: "Chụp giấy tờ",
        docCaptureDescription: "Đảm bảo tài liệu của bạn không bị lóa và hoàn toàn đầy đủ",
        docCaptureSubText: "Tài liệu",
        docReviewRetakeButton: "Chụp lại"
    })
}
export const H_BILL = 1800;
export const H_VOUCHER = 1500;
export const H_BANK = 1500;
export const H_KEY = 1500;
export const H_AIRTIME = 2000;
export const URL_DOWNLOAD_APP = 'xmanager://xmanager/';
export const URL_NET_INFO = 'https://clients3.google.com/generate_204';
export const URL_FIND_IP = 'https://api.ipify.org';
export const URL_OCR_DOC = 'https://vnm-docs.hyperverge.co/v2/nationalID';
export const URL_APPV_CODE = 'https://erpapp.tgdd.vn/mwg-app-media-service/api/media/file/L2VycGNtbmQvY21uZC1kYXRhLzIwMjEvMTEvMTAvcXJvaWtpOGItaW1hZ2U=.jpg';
export const URL_SALE_COMBO = 'https://erpapp.tgdd.vn//mwg-app-pos-media-service/api/media/pos/file/L21lZGlhL3Bvcy8yMDI0LzEwLzA2L3JrYzlkdWRvLWltYWdl.jpg';
export const URL_OLD_PRODUCT = '';
export const URL_THUMBNAIL = ``;
export const PROMOTION_CONFIG = new Set(['16356', '162882', '54413', '182603']);
export const PRINT_CONFIG = new Set([
    '8714', '487', '580', '1135', '2043', '4257', '1365', '1126', '1240',
    '2077', '10636', '1366', '1180', '86', '1244', '1574', '521', '4427', '3474',
    '9849', '1179', '1081', '1392', '1230', '1042', '460', '932', '933', '1034',
    '1649', '1142', '4827', '5738', '1092', '10899', '1018', '1093', '1415',
    '1003', '1130', '1041', '78', '1030'
]);
export const regexEmail = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;

const brands = {
    TGDD: 1,
    DMX: 2,
    BHX: 3,
    AN_KHANG: 8
};

export const MENUID = {
    // [brands.TGDD]: 4784,
    [brands.TGDD]: 4196,
    [brands.DMX]: 4196
}

export const DISTANCE = {
    [brands.AN_KHANG]: 15,
    [brands.TGDD]: 20,
    [brands.DMX]: 20
};

export const ADJUST_PRICE_TYPE = {
    ADJUST_PRICE: 1,
    PRICE_WAR: 2,
    BALANCE_PRICE: 3
};

const { ADJUST_PRICE, BALANCE_PRICE, PRICE_WAR } = ADJUST_PRICE_TYPE;

export const ADJUST_PRICE_LABEL = {
    [ADJUST_PRICE]: 'editSaleOrder.adjust_price', // Điều chỉnh giá
    [PRICE_WAR]: 'editSaleOrder.competitive_price', // Chiến giá
    [BALANCE_PRICE]: 'editSaleOrder.balance_price' // Điều chỉnh cân bằng giá khi đổi hàng
};
// 2 -> Chuyển khoản; 1 -> Tiền mặt
export const PAYMENT_TYPE = {
    CASH: 1,
    BANKING: 2
};

export const PAYMENT_LABEL = {
    [PAYMENT_TYPE.CASH]: 'tiền mặt',
    [PAYMENT_TYPE.BANKING]: 'chuyển khoản'
};

export const TRANSACTION_STATUS = {
    UNKNOWN: 0,
    SUCCESS: 1,
    PROCESSING: 2,
    FAIL: 3,
    REFUND: 4
};

export const DATE_STATUS = {
    UNKOWN_DEPOSIT_DATE: 0,
    UNKOWN_ORDER_DATE: 1,
    UNKOWN_KEEP_DATE: 2,
};

export const SO_TYPE = {
    LOCK_PRE: ********,
};
export const BOUNCY_CHECK = {
    ALL: 2,
    ONE: 0,
    MULTIPLE: 1,
    SELECT_ALL: "SELECT_ALL"
};

export const VOUCHER_PARTNER_TYPE = {
    GOTIT: 9,
    URBOX: 12,
};


export const PROVINCE_OTP = new Set("3", "82", "105", "107", "81", "7", "115", "122", "126", "132", "144", "151", "152", "154");

export const REPORT_NAME = {
    EBillContent: "Biên nhận thanh toán kiêm phiếu giao hàng",
    EBillContentIncome: "Biên nhận thu tiền",
    GiftVCIssueContentPrint: "Phiếu mua hàng",
    KeySoftwareContent: "Key phần mềm"
}

export const BRAND_ID_OF_SIM = {
    VIETTEL: 126,
    VINA: 125,
    ITEL: 6306
};
export const CONNECTED_TYPE_SIM = {
    NOT_CONNECTED: 5,
    DIRECT_CONNECT: 1
};

export const STORE_ALLOW_VIETTEL_SIM = new Set(["777", "889"]);

export const TYPE_PROFILE = {
    CUSTOMER: 1,
    COMPANY: 5,
    ADDRESS_RECEIVE: 2,
    CUSTOMER_RECEIVE: 6
};


export const STATUS_PROMOTION = {
    INIT: -1,
    ACTIVE: 1,
    NON_ACTIVE: 0
};

export const PARTNER_ID = {
    HOME_CREDIT: '2',
    FE_CREDIT: '3',
    M_CREDIT: '10',
    SMART_POS: '23',
    MOMO: '26',
    MIRAE_ASSET: '15',
    HOME_PAY_LATER: '27',
    ACS: '1',
    SHINHAN: '22',
    KREDIVO: '29',
    CAKE: '30',
    QTV: '32',
    SAMSUNG: '33',
    TPBanhEVO: '34',
    QTV: '35',
};

export const PARTNER_REWARD_VALUE = {
    [PARTNER_ID.FE_CREDIT]: { FE_7: 14000, FE_20: 20000 },
    [PARTNER_ID.HOME_CREDIT]: 0,
    [PARTNER_ID.M_CREDIT]: 0
};
// phía Nam (Siêu thị thuộc 4 vùng: Hồ Chí Minh; Duyên Hải; Tây Nam Bộ; Đông Cao Nguyên) southern
export const EREA_APPLY_REWARD_7 = [3922, 3911, 3923, 3927, 3915, 3926, 3916, 3924, 3918, 3921, 3928, 3910, 3914, 3908, 3913, 3920, 3919, 3909, 3925, 3917, 3912, 3870, 3858, 3857, 3861, 3859, 3862, 3860, 3836, 3874, 3837, 3871, 3875, 3873, 3883, 3878, 3841, 3839, 3843, 3842, 3879, 3872, 3835, 3880, 3881, 3840, 3877, 3882, 3838, 3876]
// phía Bắc (Siêu thị thuộc 4 vùng: Hà Nội +; Đông Tây Bắc; Đồng Bằng Sông Hồng; Trung Bộ). North
export const EREA_APPLY_REWARD_20 = [3948, 3949, 3947, 3867, 3934, 3865, 3832, 3833, 3950, 3886, 3864, 3890, 3888, 3868, 3831, 3848, 3932, 3834, 3887, 3885, 3884, 3847, 3829, 3856, 3853, 3855, 3844, 3953, 3852, 3957, 3830, 3854, 3849, 3846, 3863, 3929, 3931, 3845, 3952, 3866, 3944, 3933, 3828, 3940, 3869, 3851, 3889, 3930, 3942, 3946, 3943, 3937, 3850, 3935, 3956, 3936, 3939, 3954, 3941, 3955, 3945, 3938, 3951]

export const RETURN_CODE_VPBANK = {
    CARD_DECLINE: "-2",//: Thẻ bị từ chối
    MAX_AMOUNT_EX: "-1",//: Số tiền tối đa vượt quá
    SUCCESS: "00",//: Được chấp thuận hoặc hoàn thành thành công
    OVER_LIM: "05",///: Không chấp nhận (Vượt hạn mức)  
    ERROR: "06",//: Lỗi  
    PROCESSING: "09",//: Yêu cầu đang được xử lý  
    APPROVED: "11",//: Được chấp thuận (VIP)  
    INVALID_TRAN: "12",//: Giao dịch không hợp lệ  
    INVALID_AMOUNT: "13",//: Số tiền không hợp lệ  
    INVALID_NUM: "14",//: Số thẻ không hợp lệ (không có số này)  
    INVALID_FORMAT: "30",//: Lỗi định dạng  
    NOT_SUFFICIENT: "51",//: Không đủ tiền trong tài khoản  
    EXCEEDS_AMOUNT: "61",//: Vượt quá hạn mức số tiền rút  
    EXCEEDS_FREQUENCY: "65"//: Vượt quá hạn mức tần suất rút
}

export const SIM_PACKAGE_COMPONENTS = {
    PACKAGES: "PACKAGES", // Gói cước
    CONNECT_SERVICE: "CONNECT_SERVICE", // Dịch vụ hòa mạng
    PHONE_NUMBER: "PHONE_NUMBER", // Số SIM
    CARD: "CARD", // Phôi SIM
};

export const RETURN_CODE_VPBANK_VALUE = {
    [RETURN_CODE_VPBANK.CARD_DECLINE]: "Thẻ bị từ chối",
    [RETURN_CODE_VPBANK.MAX_AMOUNT_EX]: "Số tiền tối đa vượt quá",
    [RETURN_CODE_VPBANK.SUCCESS]: "Được chấp thuận hoặc hoàn thành thành công",
    [RETURN_CODE_VPBANK.OVER_LIM]: "Không chấp nhận (Vượt hạn mức)",
    [RETURN_CODE_VPBANK.ERROR]: "Lỗi",
    [RETURN_CODE_VPBANK.PROCESSING]: "Yêu cầu đang được xử lý  ",
    [RETURN_CODE_VPBANK.APPROVED]: " Được chấp thuận (VIP)  ",
    [RETURN_CODE_VPBANK.INVALID_TRAN]: "Giao dịch không hợp lệ ",
    [RETURN_CODE_VPBANK.INVALID_AMOUNT]: " Số tiền không hợp lệ",
    [RETURN_CODE_VPBANK.INVALID_NUM]: "Số thẻ không hợp lệ (không có số này)  ",
    [RETURN_CODE_VPBANK.INVALID_FORMAT]: "Lỗi định dạng  ",
    [RETURN_CODE_VPBANK.NOT_SUFFICIENT]: "Không đủ tiền trong tài khoản",
    [RETURN_CODE_VPBANK.EXCEEDS_AMOUNT]: "Vượt quá hạn mức số tiền rút  ",
    [RETURN_CODE_VPBANK.EXCEEDS_FREQUENCY]: " Vượt quá hạn mức tần suất rút",
};
export const MONEY_CARD_ID = {
    VP_BANK: "284"
};

export const ID_CARD_TYPE = {
    CMND: 1,
    CCCD: 2,
    CCCD_NEW: 4,
    UNKNOWN: 0
};
export const ID_CARD_SIDE = {
    CMND: {
        FRONT: 1,
        BACK: 3,
        UNKNOWN: 0
    },
    CCCD: {
        FRONT: 2,
        BACK: 4,
        UNKNOWN: 0
    },
    CCCD_NEW: {
        FRONT: 11,
        BACK: 12,
        UNKNOWN: 0
    },
};

export const PAYMENT_PARTNER_ID = {
    HOME_PAY_LATER: 13,
    KREDIVO: 15,
    CAKE: 16,
    QTV: 17,
    TPBanhEVO: 18
};

export const CONTRACT_PROCESS_TYPE = {
    SETTLED: 1,
    NOT_SETTLED: 2
};

export const MAX_QUANTITY = {
    "*************": 73,
    "*************": 3,
    "*************": 73,
    "*************": 3,
    "***********": 48,
    "*************": 8,
    "*************": 2,
    "*************": 2080,
    "*************": 208,
    "*************": 1,
    "*************": 34,
    "*************": 476,
    "*************": 200,
    "*************": 2,
    "*************": 41600,
    "*************": 23200,
    "1193666000041": 21240,
    "1193663000383": 19950,
    "1193682000610": 19950,
    "1193687000204": 16600,
    "1193695000081": 14200,
    "1193660000029": 13250,
    "1193695000123": 11100,
    "1195273000027": 11104,
    "1193686000203": 10800,
    "1193683000060": 9960,
    "1193688000943": 8310,
    "1193696000037": 7800,
    "1193666000030": 7600,
    "1193670000318": 7290,
    "1193684000051": 7200,
    "1193688000746": 7140,
    "1193668000446": 7140,
    "1193689000085": 6750,
    "1193682000384": 6660,
    "1193666000029": 6300,
    "1193668000073": 6200,
    "1193683000059": 6060,
    "1193669000148": 5880,
    "1193687000031": 5120,
    "1193687000223": 5050,
    "1193669000695": 4890,
    "1193668000064": 4740,
    "1193686000090": 4540,
    "1193663000122": 4536,
    "1193687000585": 4530,
    "1193688000292": 4530,
    "1193688000470": 4380,
    "1193663000095": 4260,
    "1193684000104": 3990,
    "1193676000009": 3560,
    "1193687000123": 3564,
    "1193668000208": 3570,
    "1193684000037": 3440,
    "1193688000258": 3330,
    "1193682000232": 3300,
    "1193689000443": 3200,
    "1195272000007": 3200,
    "1193663000298": 2900,
    "1193663000040": 2820,
    "1193682000422": 2640,
    "1193688000556": 2660,
    "1195268000015": 2610,
    "1193682000920": 2604,
    "1193672000014": 2520,
    "1193682000359": 2490,
    "1193691000018": 2436,
    "1193682000119": 2430,
    "1193681000113": 2310,
    "1193682000959": 2260,
    "1193682000435": 2220,
    "1193694000050": 2142,
    "1193682000194": 2080,
    "1193684000197": 2000,
    "1193662000010": 1980,
    "1193668000635": 1980,
    "1193682000434": 1932,
    "1193663000524": 1904,
    "1193682000405": 1860,
    "1193682000204": 1840,
    "1193668000124": 1740,
    "1193663000293": 1652,
    "1193666000052": 1650,
    "1193688000106": 1652,
    "1193682000094": 1660,
    "1193668000634": 1620,
    "1193660000050": 1600,
    "1193668000476": 1560,
    "1193682000378": 1580,
    "1195281000049": 1530,
    "1193666000087": 1440,
    "1193666000086": 1440,
    "1193700000053": 1290,
    "1193666000042": 1260,
    "1193693000025": 1260,
    "1193681000313": 1260,
    "1193689000258": 1250,
    "1195268000068": 1230,
    "1193663000309": 1100,
    "1193682000402": 1100,
    "1193688000499": 1092,
    "1193700000034": 1040,
    "1193682001104": 1000,
    "1193688000616": 960,
    "1193688000618": 960,
    "1193688000620": 960,
    "1052850000118": 1000,
    "1193663000420": 952,
    "1193687000144": 930,
    "1196046000304": 900,
    "1193666000051": 924,
    "1193669000480": 860,
    "1193663000645": 720,
    "1052850000075": 740,
    "1193694000020": 728,
    "1193694000026": 728,
    "1193694000024": 728,
    "1193668000447": 714,
    "1193669000534": 700,
    "1193694000067": 700,
    "1193688000160": 686,
    "1195273000028": 694,
    "1193669000672": 688,
    "1193687000029": 667,
    "1193688000033": 667,
    "1193694000056": 660,
    "1193678000102": 625,
    "1193698000136": 625,
    "1193666000091": 570,
    "1193666000032": 560,
    "1193683000011": 560,
    "1193678000197": 556,
    "1193678000091": 526,
    "1193700000078": 510,
    "1193690000044": 510,
    "1195275000007": 500,
    "1193688000285": 476,
    "1193682001100": 476,
    "1193689000198": 476,
    "1193683000052": 448,
    "1193666000037": 448,
    "1193663000233": 450,
    "1193683000008": 448,
    "1195274000009": 430,
    "1193668000258": 417,
    "1193670000290": 390,
    "1193690000136": 390,
    "1193684000110": 400,
    "1193663000385": 400,
    "1193682000615": 400,
    "1193669000544": 380,
    "1193695000125": 370,
    "1193682000106": 350,
    "1193666000069": 355,
    "1193684000070": 345,
    "1193664000056": 303,
    "1193669000556": 300,
    "1193687000125": 298,
    "1193669000152": 294,
    "1193688000947": 278,
    "1193696000058": 260,
    "1196046000023": 250,
    "1193687000033": 256,
    "1193670000319": 243,
    "1193688000759": 238,
    "1193686000152": 227,
    "1193682000385": 222,
    "1193678000117": 217,
    "1193691000027": 203,
    "1193692000002": 200,
    "1193682000437": 185,
    "1193689000066": 172,
    "1193683000062": 167,
    "1193687000206": 167,
    "1193664000070": 164,
    "1193669000697": 163,
    "1193682000436": 161,
    "1193694000057": 154,
    "1193687000586": 152,
    "1193688000297": 152,
    "1193688000479": 147,
    "1196046000109": 143,
    "1193695000100": 143,
    "1193663000421": 137,
    "1193689000089": 135,
    "1193666000057": 127,
    "1193698000173": 125,
    "1193688000047": 125,
    "1193678000180": 123,
    "1193668000270": 119,
    "1193669000705": 116,
    "1193682000960": 114,
    "1193671000006": 111,
    "1193688000267": 111,
    "1193686000249": 109,
    "1193689000039": 105,
    "1193663000187": 105,
    "1193682000242": 104,
    "1193687000225": 101,
    "1193683000061": 101,
    "1193662000015": 100,
    "1193678000185": 95,
    "1193688000583": 95,
    "1193682000921": 94,
    "1193682000252": 93,
    "1193665000038": 93,
    "1193676000017": 89,
    "1195268000016": 88,
    "1193689000087": 87,
    "1193689000073": 87,
    "1193669000509": 87,
    "1193669000674": 86,
    "1193700000097": 85,
    "1193682000360": 83,
    "1193682000157": 83,
    "1193682000182": 81,
    "1193682000379": 79,
    "1193668000126": 79,
    "1193681000207": 78,
    "1193666000058": 77,
    "1193684000084": 73,
    "1193689000070": 71,
    "1193700000033": 71,
    "1193663000115": 71,
    "1193663000528": 69,
    "1193700000046": 67,
    "1193668000637": 67,
    "1193682000407": 63,
    "1193668000133": 63,
    "1193663000301": 60,
    "1193688000107": 60,
    "1193663000306": 59,
    "1193666000080": 56,
    "1193668000636": 55,
    "1193663000126": 54,
    "1193660000044": 53,
    "1193700000061": 53,
    "1193694000052": 52,
    "1195281000050": 51,
    "1193682001106": 51,
    "1052850000127": 50,
    "1193692000003": 50,
    "1193663000066": 48,
    "1193665000056": 47,
    "1193682000423": 44,
    "1195274000010": 43,
    "1193700000073": 43,
    "1193672000022": 43,
    "1193693000046": 43,
    "1193681000358": 42,
    "1195268000069": 42,
    "1193671000011": 40,
    "1193688000515": 39,
    "1193669000547": 38,
    "1052850000092": 38,
    "1193666000079": 33,
    "1193694000061": 33,
    "1193682000280": 33,
    "1193660000056": 32,
    "1193689000446": 32,
    "1195272000008": 32,
    "1193687000145": 32,
    "1193668000182": 30,
    "1193691000123": 27,
    "1193668000477": 27,
    "1193694000038": 26,
    "1193694000041": 26,
    "1193694000039": 26,
    "1193682000169": 26,
    "1193669000804": 26,
    "1193669000540": 26,
    "1193694000068": 26,
    "1193666000095": 25,
    "1193666000094": 24,
    "1193663000312": 23,
    "1193666000070": 21,
    "1193666000060": 21,
    "1193683000012": 20,
    "1193666000099": 20,
    "1193688000288": 17,
    "1193690000054": 17,
    "1193688000632": 17,
    "1193688000634": 17,
    "1193688000636": 17,
    "1193683000054": 17,
    "1193666000065": 17,
    "1193683000010": 16,
    "1196046000439": 16,
    "1193663000234": 15,
    "1193670000291": 14,
    "1193690000137": 13,
    "1193663000650": 13,
    "1193668000533": 12,
    "1193682000403": 11,
    "1193671000027": 10,
    "1193669000561": 10,
    "1193671000018": 8,
    "1193668000531": 7,
    "1193688000161": 7,
    "1193684000059": 7200,
    "1193698000129": 6500,
    "1193668000098": 5000,
    "1193681000103": 3300,
    "1193666000088": 3120,
    "1193698000070": 2800,
    "1193660000016": 2700,
    "1193698000054": 2400,
    "1193682000105": 2340,
    "1193672000012": 2300,
    "1193667000025": 2160,
    "1193662000011": 1800,
    "1193698000071": 1500,
    "1193681000116": 1350,
    "1193668000094": 1300,
    "1193681000154": 1200,
    "1193682000103": 1200,
    "1193663000032": 1200,
    "1193686000096": 1120,
    "1193698000058": 1100,
    "1193681000142": 980,
    "1193681000167": 980,
    "1193681000305": 900,
    "1193666000049": 840,
    "1193666000085": 840,
    "1193698000043": 750,
    "1193681000125": 750,
    "1193700000038": 645,
    "1193663000819": 585,
    "1193681000107": 532,
    "1193678000165": 500,
    "1193692000011": 500,
    "1193692000005": 500,
    "1193686000205": 490,
    "1193688000108": 476,
    "1193688000284": 476,
    "1193683000007": 360,
    "1193691000062": 300,
    "1193690000068": 300,
    "1193664000050": 160,
    "1193688000023": 144,
    "1193700000031": 140,
    "1193668000152": 130,
    "1193689000071": 124,
    "1193700000032": 120,
    "1193663000820": 117,
    "1193664000117": 107,
    "1193664000064": 100,
    "1193668000156": 100,
    "1193678000122": 95,
    "1193662000016": 90,
    "1193682000168": 5,
    "1193698000093": 60,
    "1193663000732": 75,
    "1193667000043": 72,
    "1193698000166": 65,
    "1193678000194": 60,
    "1193682000166": 60,
    "1193698000097": 55,
    "1193681000197": 55,
    "1193666000096": 52,
    "1193684000129": 50,
    "1193701000021": 48,
    "1193660000031": 45,
    "1193681000210": 45,
    "1193700000064": 43,
    "1193681000248": 40,
    "1193684000092": 40,
    "1193686000251": 35,
    "1193681000236": 35,
    "1193681000261": 35,
    "1193666000077": 30,
    "1193666000093": 30,
    "1193681000350": 30,
    "1193698000109": 28,
    "1193698000082": 25,
    "1193681000219": 25,
    "1193663000058": 24,
    "1193672000020": 23,
    "1193681000201": 19,
    "1193688000111": 17,
    "1193688000287": 17,
    "1193663000734": 15,
    "1193698000110": 15,
    "1193686000321": 14,
    "1193688000038": 12,
    "1193683000009": 12,
    "1193701000033": 12,
    "1193691000063": 10,
    "1193690000069": 10,
    "1193678000124": 2,
    "1193678000182": 2,
    "1193701000038": 100,
    "1193701000048": 1,
    "1193699000011": 60,
    "1193699000019": 2,
    "1193693000025": 600,
    "1193693000046": 20,
    "1193682000610": 10000,
    "1193682000615": 200,
    "1193688000106": 840,
    "1193688000107": 30,
    "1193682000106": 182,
    "1193682000169": 13,
    "1193663000028": 30,
    "1193663000054": 1,
    "1193668000098": 2500,
    "1193668000156": 50,
    "1193700000031": 70,
    "1193698000054": 900,
    "1193698000093": 30,
    "1193682000920": 1316,
    "1193682000921": 47,
    "1193700000032": 60,
    "1193681000305": 450,
    "1193681000350": 15,
    "1193689000085": 4500,
    "1193689000089": 70,
    "1193669000480": 430,
    "1193669000509": 43,
    "1193671000006": 2,
    "1193671000010": 2,
    "1193671000017": 0,
    "1193697000072": 24,
    "1196046000460": 10,
    "1193687000692": 2,
    "1193698000058": 600,
    "1193698000097": 30,
    "1193694000050": 1078,
    "1193694000057": 77,
    "1193663000122": 2268,
    "1193663000126": 27,
    "1193669000556": 150,
    "1193669000561": 5,
    "1193670000334": 100,
    "1193670000335": 1,
    "1193697000054": 0,
    "1193697000163": 0,
    "1193697000157": 0,
    "1193697000055": 0,
    "1193695000041": 20,
    "1193695000067": 1,
    "1193700000107": 30,
    "1193700000108": 3,
    "1193663000039": 80,
    "1193663000065": 0,
    "1193678000111": 1,
    "1193667000020": 45,
    "1193667000038": 1,
    "1193660000022": 20,
    "1193660000037": 1,
    "1193670000054": 100,
    "1193670000055": 1,
    "1193670000250": 20,
    "1193670000253": 1,
    "1193695000032": 100,
    "1193695000058": 1,
    "1193684000063": 20,
    "1193684000096": 1,
    "1193687000416": 30,
    "1193687000431": 1,
    "1193687000338": 16,
    "1193687000351": 1,
    "1193670000345": 20,
    "1193670000346": 1,
    "1193687000246": 30,
    "1193687000248": 1,
    "1193684000034": 40,
    "1193684000067": 1,
    "1193687000232": 20,
    "1193687000236": 1,
    "1193687000045": 20,
    "1193687000046": 1,
    "1193687000596": 30,
    "1193687000601": 1,
    "1193687000410": 60,
    "1193687000425": 1,
    "1193670000262": 100,
    "1193670000264": 1,
    "1193671000017": 0,
    "1196046000460": 10,
    "1193687000692": 2,
    "1193670000334": 100,
    "1193670000335": 1,
    "1193670000307": 20,
    "1193670000308": 0,
    "1193670000322": 20,
    "1193670000323": 0,
    "1193668000124": 900,
    "1193668000182": 15,
    "1193678000124": 2,
    "1193678000182": 2,
    "1193667000020": 45,
    "1193667000038": 1,
    "1193671000011": 20,
    "1193671000018": 4,
    "1193663000524": 840,
    "1193663000528": 30,
    "1195273000027": 5552,
    "1195273000028": 347,
    "1193668000231": 1200,
    "1193668000291": 20,
    "1193664000050": 80,
    "1193684000059": 3600,
    "1193684000092": 20,
    "1193666000030": 3800,
    "1193666000058": 38,
    "1195272000007": 1600,
    "1195272000008": 16,
    "1193668000476": 780,
    "1193668000477": 13,
    "1193682000105": 1170,
    "1193682000168": 45,
    "1193692000002": 100,
    "1193692000003": 25,
    "*************": 17,
    "*************": 238,
    "1193682000066": 3332,
    "1193682000130": 238,
    "1193689000071": 62,
    "1193694000056": 340,
    "1193694000061": 17,
    "1196046000304": 480,
    "1196046000439": 8,
    "1193676000009": 1800,
    "1193676000017": 45,
    "1193665000056": 23,
    "1193688000470": 2100,
    "1193688000479": 70,
    "*************": 20800,
    "1193668000258": 208,
    "1193689000070": 36,
    "1193664000070": 82,
    "1193682000359": 1200,
    "1193682000360": 40,
    "1193668000073": 3100,
    "1193668000133": 31,
    "1193663000309": 550,
    "1193663000312": 11,
    "1193694000052": 26,
    "1193663000233": 240,
    "1193663000234": 8,
    "1193678000197": 278,
    "1193682001100": 238,
    "1193694000024": 364,
    "1193694000039": 13,
    "1193694000067": 364,
    "1193694000068": 13,
    "1193694000020": 364,
    "1193694000038": 13,
    "1193694000026": 364,
    "1193694000041": 13,
    "1193683000060": 4800,
    "1193683000062": 80,
    "1193683000059": 3000,
    "1193683000061": 50,
    "1193682000434": 960,
    "1193682000436": 80,
    "1193682000119": 1200,
    "1193682000182": 40,
    "1193682000435": 1200,
    "1193682000437": 100,
    "***********": 48,
    "1193687000031": 2560,
    "1193687000033": 128,
    "1193689000258": 625,
    "1193663000040": 1440,
    "1193663000066": 24,
    "1193660000029": 6750,
    "1193660000044": 27,
    "*************": 8,
    "*************": 2,
    "1193663000383": 10000,
    "1193663000385": 200,
    "1193663000298": 1500,
    "1193663000306": 30,
    "1193664000117": 54,
    "1193667000025": 1200,
    "1193667000043": 40,
    "1193663000819": 585,
    "1193663000820": 117,
    "1193668000208": 1800,
    "1193668000270": 60,
    "1193684000040": 80,
    "1193684000073": 1,
    "1193687000447": 30,
    "1193687000458": 1,
    "1193682000405": 930,
    "1193682000407": 31,
    "1195268000015": 1320,
    "1195268000016": 44,
    "1195268000068": 600,
    "1195268000069": 20,
    "1193688000746": 3570,
    "1193688000759": 119,
    "1193669000534": 364,
    "1193669000540": 13,
    "1193670000261": 100,
    "1193670000263": 1,
    "1193686000203": 5400,
    "1193686000249": 54,
    "1193699000107": 54,
    "1193699000333": 54,
    "1193699000295": 3240,
    "1193689000066": 86,
    "1193688000499": 560,
    "1193688000515": 20,
    "1193695000123": 5550,
    "1193695000125": 185,
    "1193695000081": 7100,
    "1193695000100": 71,
    "1193666000091": 300,
    "1193666000099": 10,
    "1193688000616": 480,
    "1193688000632": 8,
    "1193688000618": 480,
    "1193688000634": 8,
    "1193688000620": 480,
    "1193688000636": 8,
    "1193672000012": 1200,
    "1193672000020": 12,
    "1193687000204": 8000,
    "1193687000206": 80,
    "1193663000032": 600,
    "1193663000058": 12,
    "1193688000160": 392,
    "1193688000161": 4,
    "1193670000290": 210,
    "1193670000291": 7,
    "1193660000017": 30,
    "1193660000032": 1,
    "1193688000108": 252,
    "1193688000111": 9,
    "1193688000284": 252,
    "1193688000287": 9,
    "1193688000285": 252,
    "1193688000288": 9,
    "1193682001104": 500,
    "1193682001106": 25,
    "1193682000422": 1200,
    "1193682000423": 20,
    "1193682000402": 600,
    "1193682000403": 6,
    "1193687000298": 100,
    "1193687000301": 1,
    "1193700000053": 600,
    "1193700000073": 20,
    "1193700000038": 300,
    "1193700000064": 20,
    "1193688000556": 1344,
    "1193688000583": 48,
    "1193681000103": 1680,
    "1193681000197": 28,
    "1193663000293": 840,
    "1193663000301": 30,
    "1193694000036": 1,
    "1193694000048": 0,
    "1193694000033": 1,
    "1193694000046": 0,
    "1193698000099": 250,
    "1193701000066": 0,
    "1193695000041": 20,
    "1193695000067": 1,
    "1193663000065": 0,
    "1193663000039": 80,
    "1193671000012": 5,
    "1193671000019": 1,
    "1193686000065": 5,
    "1193686000127": 0,
    "1193678000111": 1,
    "1193699000014": 60,
    "1193667000020": 45,
    "1193699000022": 2,
    "1193667000038": 1,
    "1196046000460": 10,
    "1193687000692": 2,
    "1193671000017": 0,
    "1193670000054": 100,
    "1193695000032": 100,
    "1193670000262": 100,
    "1193670000334": 100,
    "1193670000261": 100,
    "1193687000298": 100,
    "1193660000049": 100,
    "1193684000040": 80,
    "1193687000410": 60,
    "1193684000034": 40,
    "1193687000416": 30,
    "1193687000246": 30,
    "1193687000596": 30,
    "1193687000447": 30,
    "1193660000017": 30,
    "1193660000022": 20,
    "1193670000250": 20,
    "1193684000063": 20,
    "1193670000345": 20,
    "1193687000232": 20,
    "1193687000045": 20,
    "1193670000307": 20,
    "1193670000322": 20,
    "1193687000338": 16,
    "1193660000037": 1,
    "1193670000055": 1,
    "1193670000253": 1,
    "1193695000058": 1,
    "1193684000096": 1,
    "1193687000431": 1,
    "1193687000351": 1,
    "1193670000346": 1,
    "1193687000248": 1,
    "1193684000067": 1,
    "1193687000236": 1,
    "1193687000046": 1,
    "1193687000601": 1,
    "1193687000425": 1,
    "1193670000264": 1,
    "1193670000335": 1,
    "1193684000073": 1,
    "1193687000458": 1,
    "1193670000263": 1,
    "1193660000032": 1,
    "1193687000301": 1,
    "1193660000055": 1,
    "1193670000308": 0,
    "1193670000323": 0,
    "1193663000033": 60,
    "1193663000059": 0,
    "1193666000043": 60,
    "1193681000318": 30,
    "1193663000028": 30,
    "1193664000096": 3,
    "1193666000071": 2,
    "1193671000010": 2,
    "1193681000363": 1,
    "1193663000054": 1,
    "1193668000231": 1200,
    "1193668000291": 20,
    "1193680000146": 70,
    "1193680000159": 12,
    "1193680000160": 12,
    "1193680000307": 12,
    "1193699000072": 27,
    "1193699000080": 27,
    "1193695000162": 32,
    "1193695000163": 9,
    "1193680000266": 12,
    "1193680000202": 20,
    "1193675000051": 14,
    "1193693000104": 12,
    "1193680000348": 16,
    "1193680000374": 12,
    "1193699000073": 7,
    "1193699000084": 7,
    "1193685000136": 27,
    "1193685000162": 5,
    "1193696000232": 20,
    "1193680000312": 20,
    "1193696000231": 7,
    "1193680000156": 49,
    "1193680000306": 31,
    "1193680000156": 49,
    "1052850000118": 400,
    "1052850000127": 20,
    "1052850000075": 400,
    "1052850000092": 20,
    "1193695000185": 16,
    "1193680000146": 70,
    "1193687000144": 600,
    "1193687000145": 20,
    "1193698000136": 300,
    "1193698000173": 60,
    "1193698000070": 1400,
    "1193698000109": 14,
    "1193698000071": 800,
    "1193698000110": 8,
    "1193687000636": 50,
    "1193687000637": 10,
    "1193699000080": 27,
    "1193699000084": 7,
    "1193701000061": 250,
    "1193701000066": 0,
    "1193682000378": 800,
    "1193682000379": 40,
    "1193682000194": 1000,
    "1193682000242": 50,
    "1193660000049": 100,
    "1193660000055": 1,
    "1193664000096": 3,
    "1193663000028": 30,
    "1193663000054": 1,
    "1193671000010": 2,
    "1193702000274": 2,
    "1193702000273": 100,
    "1193678000297": 2,
    "1193678000296": 100,
    "1193664000091": 2,
    "1193689000344": 2,
    "1195274000034": 90,
    "1195274000037": 3,
    "9253124000188": 2,
    "1193688000617": 140,
    "1193688000633": 10,
    "1193688000619": 140,
    "1193688000635": 10,
    "1193681000191": 10,
    "1193681000097": 300,
    "1193681000220": 1,
    "1193681000126": 98,
    "1193663000734": 3,
    "1193663000733": 3,
    "1193663000731": 15,
    "1193663000732": 15,
    "1193681000305": 150,
    "1193681000350": 5,
    "1193671000006": 5,
    "1193664000116": 3,
    "1193691000269": 20,
    "1193691000270": 1,
    "1193671000018": 1,
    "1193671000011": 5
}